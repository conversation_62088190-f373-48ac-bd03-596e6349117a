# FAR Sighted - Management Scripts Guide

## Overview

This directory contains several batch files for managing the FAR Sighted application with its new Multi-Database Architecture. These scripts provide easy-to-use commands for common operations.

## 🎯 Quick Start

**For first-time setup or system updates:**

```batch
update-system.bat
```

**For daily use:**

```batch
restart-far-app.bat
```

**To check system status:**

```batch
check-status.bat
```

## 📋 Available Scripts

### Core Management Scripts

| Script                   | Purpose                 | When to Use                                                          |
| ------------------------ | ----------------------- | -------------------------------------------------------------------- |
| `restart-far-app.bat`    | **Restart Application** | Daily use - kills all processes and restarts both frontend & backend |
| `start-far-app.bat`      | **Start Application**   | When no processes are running - starts without killing               |
| `kill-all-processes.bat` | **Stop All Services**   | When you need to stop everything cleanly                             |
| `check-status.bat`       | **System Status Check** | To verify system health and configuration                            |

### Setup & Migration Scripts

| Script                 | Purpose                    | When to Use                              |
| ---------------------- | -------------------------- | ---------------------------------------- |
| `update-system.bat`    | **Complete System Update** | First-time setup or major updates        |
| `migrate-database.bat` | **Database Migration**     | Converting from single to multi-database |

## 🔄 Common Usage Scenarios

### Daily Development Work

```batch
# Start your day
restart-far-app.bat

# Check if everything is working
check-status.bat

# When done for the day
kill-all-processes.bat
```

### First Time Setup

```batch
# Complete system setup
update-system.bat

# Verify everything is working
check-status.bat
```

### Troubleshooting

```batch
# Check what's wrong
check-status.bat

# Force clean restart
kill-all-processes.bat
restart-far-app.bat
```

### Database Migration

```batch
# Migrate to multi-database structure
migrate-database.bat

# Verify migration success
check-status.bat
```

## 🌐 Application Access Points

After starting the application:

| Service              | URL                                        | Purpose                     |
| -------------------- | ------------------------------------------ | --------------------------- |
| **Frontend**         | http://localhost:9090                      | Main application interface  |
| **Backend API**      | http://localhost:8090/api                  | REST API endpoints          |
| **Health Check**     | http://localhost:8090/api/health           | System health status        |
| **Migration Status** | http://localhost:8090/api/migration-status | Database migration info     |
| **System Info**      | http://localhost:8090/api/system-info      | Detailed system information |

## 🏗️ Architecture Information

### Multi-Database Structure

- **Master Database**: `backend/database/master.db` (users, companies registry)
- **Company Databases**: `backend/database/companies/*/company.db` (company-specific data)
- **Original Database**: `backend/database/far_sighted.db` (backed up during migration)

### Port Configuration

- **Frontend**: Port 9090 (Vite development server)
- **Backend**: Port 8090 (Express.js API server)

## 🛠️ Script Details

### restart-far-app.bat

**Purpose**: Complete application restart

- Kills all Node.js, npm, and port-specific processes
- Starts backend server (Multi-Database structure)
- Starts frontend development server
- Opens application in browser
- Provides comprehensive status information

### start-far-app.bat

**Purpose**: Clean application startup

- Checks for port conflicts
- Starts backend with Multi-Database support
- Starts frontend Vite development server
- Includes system health verification
- No process killing (assumes clean state)

### kill-all-processes.bat

**Purpose**: Clean process termination

- Kills Node.js processes
- Kills npm and nodemon processes
- Frees up application ports (9090, 8090)
- Cleans up development server processes
- Provides termination confirmation

### check-status.bat

**Purpose**: Comprehensive system status

- Checks file structure integrity
- Verifies database architecture
- Tests service availability
- Validates backend health
- Shows migration status
- Lists available management scripts

### update-system.bat

**Purpose**: Complete system update

- Stops all running processes
- Updates package configurations
- Installs/updates dependencies
- Runs database migration (optional)
- Verifies new file structure
- Starts updated system
- Comprehensive status reporting

### migrate-database.bat

**Purpose**: Database migration management

- Checks current migration status
- Offers migration options (normal, dry-run, force)
- Runs selected migration process
- Verifies migration success
- Provides detailed status information

## 🔧 Technical Requirements

### System Requirements

- **Node.js**: Version 16.0.0 or higher
- **npm**: Latest version
- **Windows**: Batch file support
- **Ports**: 9090 and 3001 must be available

### Dependencies

- All Node.js dependencies are installed automatically
- Backend dependencies include SQLite3, Express, bcrypt
- Frontend dependencies include React, TypeScript, Vite

## 🚨 Troubleshooting

### Common Issues

**Port Already in Use**

```batch
# Kill all processes and restart
kill-all-processes.bat
restart-far-app.bat
```

**Migration Required**

```batch
# Run database migration
migrate-database.bat
```

**Backend Not Responding**

```batch
# Check status and restart
check-status.bat
restart-far-app.bat
```

**Frontend Won't Start**

```batch
# Check Node.js and npm installation
node --version
npm --version

# Clean restart
kill-all-processes.bat
restart-far-app.bat
```

### Log Locations

- **Backend Logs**: Displayed in "FAR Backend v2.0" console window
- **Frontend Logs**: Displayed in "FAR Frontend v2.0" console window
- **Migration Logs**: Displayed in migration script console

## 📚 Additional Documentation

For detailed technical information, see:

- `MULTI_DATABASE_IMPLEMENTATION.md` - Technical implementation details
- `MIGRATION_DEPLOYMENT_GUIDE.md` - Comprehensive migration guide
- `IMPLEMENTATION_STATUS.md` - Current system status and features

## 💡 Best Practices

### Daily Workflow

1. Start with `check-status.bat` to verify system state
2. Use `restart-far-app.bat` for reliable daily startup
3. Monitor console windows for any errors
4. Use `kill-all-processes.bat` before shutting down

### Development Tips

- Keep console windows open to monitor logs
- Use `check-status.bat` to verify changes
- Run migration only when necessary
- Always backup before major changes

### Performance Optimization

- Close unnecessary applications before starting
- Monitor memory usage in Task Manager
- Restart application if performance degrades
- Use separate databases for better isolation

---

_FAR Sighted v2.0 - Professional Asset Management with Multi-Database Architecture_
