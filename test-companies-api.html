<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Companies API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .companies-list {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .company-item {
            padding: 8px;
            margin: 5px 0;
            background-color: white;
            border-radius: 3px;
            border: 1px solid #e9ecef;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🏢 Companies API Test</h1>
        <p>Direct test of the companies API endpoint to verify frontend-backend connectivity.</p>
        
        <div id="status" class="result loading">
            ⏳ Ready to test...
        </div>
        
        <button onclick="testCompaniesAPI()">Test Companies API</button>
        <button onclick="testWithCredentials()">Test with Credentials</button>
        <button onclick="testDirectFetch()">Test Direct Fetch</button>
        
        <div id="companies-container"></div>
        <div id="debug-info"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8090/api';
        
        function updateStatus(message, type = 'loading') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `result ${type}`;
        }
        
        function showDebugInfo(info) {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML = `
                <h3>🔍 Debug Information:</h3>
                <pre>${JSON.stringify(info, null, 2)}</pre>
            `;
        }
        
        function showCompanies(companies) {
            const container = document.getElementById('companies-container');
            if (companies && companies.length > 0) {
                container.innerHTML = `
                    <div class="companies-list">
                        <h3>📊 Companies Found (${companies.length}):</h3>
                        ${companies.map(company => `
                            <div class="company-item">
                                <strong>${company.name}</strong><br>
                                <small>ID: ${company.id}</small>
                            </div>
                        `).join('')}
                    </div>
                `;
            } else {
                container.innerHTML = `
                    <div class="companies-list">
                        <h3>⚠️ No companies found</h3>
                    </div>
                `;
            }
        }
        
        async function testCompaniesAPI() {
            updateStatus('🔍 Testing companies API...', 'loading');
            
            try {
                console.log('Making request to:', `${API_BASE}/companies`);
                
                const response = await fetch(`${API_BASE}/companies`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                if (response.ok) {
                    const companies = await response.json();
                    console.log('Companies data:', companies);
                    
                    updateStatus(`✅ Success! Found ${companies.length} companies`, 'success');
                    showCompanies(companies);
                    
                    showDebugInfo({
                        status: response.status,
                        statusText: response.statusText,
                        headers: Object.fromEntries(response.headers.entries()),
                        companiesCount: companies.length,
                        companies: companies
                    });
                } else {
                    const errorText = await response.text();
                    console.error('Error response:', errorText);
                    
                    updateStatus(`❌ Failed with status: ${response.status}`, 'error');
                    showDebugInfo({
                        status: response.status,
                        statusText: response.statusText,
                        error: errorText
                    });
                }
            } catch (error) {
                console.error('Fetch error:', error);
                updateStatus(`❌ Network error: ${error.message}`, 'error');
                showDebugInfo({
                    error: error.message,
                    stack: error.stack
                });
            }
        }
        
        async function testWithCredentials() {
            updateStatus('🔍 Testing with credentials...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE}/companies`, {
                    method: 'GET',
                    credentials: 'include',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const companies = await response.json();
                    updateStatus(`✅ With credentials: Found ${companies.length} companies`, 'success');
                    showCompanies(companies);
                } else {
                    updateStatus(`❌ With credentials failed: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus(`❌ With credentials error: ${error.message}`, 'error');
            }
        }
        
        async function testDirectFetch() {
            updateStatus('🔍 Testing direct fetch...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE}/companies`);
                
                if (response.ok) {
                    const companies = await response.json();
                    updateStatus(`✅ Direct fetch: Found ${companies.length} companies`, 'success');
                    showCompanies(companies);
                } else {
                    updateStatus(`❌ Direct fetch failed: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus(`❌ Direct fetch error: ${error.message}`, 'error');
            }
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(testCompaniesAPI, 1000);
        });
    </script>
</body>
</html>
