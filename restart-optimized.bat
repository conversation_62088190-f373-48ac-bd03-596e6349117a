@echo off
echo =============================================
echo FAR SIGHTED - RESTART WITH OPTIMIZATIONS
echo Professional Advisory Services - CA  
echo =============================================
echo.

echo 🚀 Quick restart with database optimizations applied
echo.

echo 🔧 Step 1: Stopping all processes...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im npm.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo.
echo 📡 Step 2: Starting optimized backend...
cd /d "E:\Projects\FAR Sighted\backend"
start "FAR Backend (Optimized)" cmd /k "title FAR Backend (Optimized) && echo ======================================= && echo FAR SIGHTED BACKEND (Optimized) && echo Database initialization is now efficient && echo ======================================= && echo. && npm start"

echo.
echo ⏳ Step 3: Waiting for backend (12 seconds)...
timeout /t 12 /nobreak >nul

echo.
echo 🔍 Step 4: Testing optimized API...
curl -s http://localhost:8090/api/companies >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Companies API working!
    echo.
    echo 🖥️  Step 5: Starting frontend...
    cd /d "E:\Projects\FAR Sighted"
    start "FAR Frontend" cmd /k "title FAR Frontend && npm run dev"
    
    echo.
    echo ⏳ Waiting for frontend (8 seconds)...
    timeout /t 8 /nobreak >nul
    
    echo.
    echo 🌐 Opening application...
    start http://localhost:9090
    
    echo.
    echo ✅ FAR Sighted restarted with optimizations!
    echo    • Company dropdown should work immediately
    echo    • Backend startup is now faster
    echo    • No redundant database operations
    
) else (
    echo    ⚠️  Backend may still be starting
    echo    💡 Check the backend window and wait a moment
)

echo.
echo 📋 Optimization Features Active:
echo    ✅ Smart table existence checking
echo    ✅ Shared DatabaseManager instance
echo    ✅ Efficient migration status checks
echo    ✅ Eliminated double initialization
echo.

pause
