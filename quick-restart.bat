@echo off
echo =====================================
echo FAR SIGHTED - QUICK RESTART & TEST
echo Professional Advisory Services - CA
echo =====================================
echo.

echo 🔄 Restarting FAR Sighted with database fixes...
echo.

REM Navigate to project directory
cd /d "E:\Projects\FAR Sighted"

echo 📛 Stopping all running processes...
taskkill /f /im node.exe 2>nul
taskkill /f /im npm.cmd 2>nul

echo.
echo ⏳ Waiting for processes to terminate...
timeout /t 3 /nobreak >nul

echo.
echo 🚀 Starting backend with database initialization fixes...
cd backend
start "FAR Backend (Fixed)" cmd /k "title FAR Backend - Database Fixed && echo ======================================= && echo FAR SIGHTED BACKEND v2.0 (FIXED) && echo Database Initialization Fixed && echo ======================================= && echo. && npm start"

echo.
echo ⏳ Waiting for backend to initialize...
timeout /t 8 /nobreak >nul

echo.
echo 🔍 Testing backend health...
curl -s http://localhost:3001/api/health >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Backend is responding correctly
) else (
    echo    ⚠️  Backend may still be starting...
)

cd ..

echo.
echo 🖥️  Starting frontend...
start "FAR Frontend" cmd /k "title FAR Frontend v2.0 && echo Starting FAR Sighted Frontend... && npm run dev"

echo.
echo ⏳ Waiting for frontend to start...
timeout /t 5 /nobreak >nul

echo.
echo 🔍 Testing endpoints...
echo    📊 Health Check:
curl -s http://localhost:3001/api/health | node -e "
try {
    const data = JSON.parse(require('fs').readFileSync(0));
    console.log('       Status:', data.status);
    console.log('       Version:', data.version);
    console.log('       System Ready:', data.database?.systemReady);
} catch(e) {
    console.log('       Backend may still be starting...');
}" 2>nul

echo.
echo    🔄 Migration Status:
curl -s http://localhost:3001/api/migration-status | node -e "
try {
    const data = JSON.parse(require('fs').readFileSync(0));
    console.log('       System Ready:', data.systemReady);
    console.log('       Migration Needed:', data.needsMigration);
    console.log('       Companies:', data.companiesInNewStructure);
} catch(e) {
    console.log('       Unable to get migration status');
}" 2>nul

echo.
echo 🌐 Opening application...
timeout /t 3 /nobreak >nul
start http://localhost:9090

echo.
echo ✅ FAR Sighted restarted with database fixes!
echo.
echo 📊 Quick Status:
echo    • Frontend: http://localhost:9090
echo    • Backend: http://localhost:3001/api
echo    • Health: http://localhost:3001/api/health
echo    • Migration: http://localhost:3001/api/migration-status
echo.
echo 💡 If migration is needed, run: migrate-database.bat
echo.

pause