/**
 * FIRST YEAR DEPRECIATION VERIFICATION SCRIPT
 * Professional Advisory Services - Chartered Accountant
 * 
 * This script specifically tests first year depreciation calculations
 * to ensure depreciation is properly calculated and displayed.
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const dbPath = join(__dirname, '../database/far_sighted.db');

console.log('🧪 FIRST YEAR DEPRECIATION VERIFICATION TEST');
console.log('==========================================');

const verifyFirstYearDepreciation = async () => {
    const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
            console.error('❌ Error opening database:', err.message);
            process.exit(1);
        }
    });

    const runQuery = (sql, params = []) => {
        return new Promise((resolve, reject) => {
            db.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    };

    // Helper function to calculate inclusive date difference
    const inclusiveDateDiffInDays = (d1, d2) => {
        if (!d1 || !d2) return 0;
        const utc1 = Date.UTC(d1.getUTCFullYear(), d1.getUTCMonth(), d1.getUTCDate());
        const utc2 = Date.UTC(d2.getUTCFullYear(), d2.getUTCMonth(), d2.getUTCDate());
        const msPerDay = 1000 * 60 * 60 * 24;
        return Math.floor((utc2 - utc1) / msPerDay) + 1;
    };

    // Helper function to calculate first year depreciation
    const calculateFirstYearDepreciation = (asset, currentYear) => {
        const [startYearStr, endYearStr] = currentYear.split('-');
        const fyStart = new Date(`${startYearStr}-04-01T00:00:00.000Z`);
        const fyEnd = new Date(`${endYearStr}-03-31T23:59:59.999Z`);
        
        const putToUseDate = new Date(asset.put_to_use_date);
        const grossAmount = asset.gross_amount || 0;
        const lifeInYears = asset.life_in_years || 0;
        const salvagePercentage = asset.salvage_percentage || 0;
        const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));

        // Calculate use days in financial year
        const startOfUseInFY = putToUseDate > fyStart ? putToUseDate : fyStart;
        const endOfUseInFY = fyEnd;
        let useDaysInFY = 0;
        
        if (endOfUseInFY >= startOfUseInFY && putToUseDate <= endOfUseInFY) {
            useDaysInFY = inclusiveDateDiffInDays(startOfUseInFY, endOfUseInFY);
        }

        // Determine depreciation base
        const isCurrentYearAddition = (putToUseDate >= fyStart && putToUseDate <= fyEnd);
        const depreciationBase = grossAmount; // For first year, base is always gross amount

        let depreciationForYear = 0;
        
        if (lifeInYears > 0 && depreciationBase > salvageValue && useDaysInFY > 0) {
            if (asset.depreciation_method === 'SLM') {
                const depreciableAmount = grossAmount - salvageValue;
                const yearlyDepreciation = depreciableAmount / lifeInYears;
                depreciationForYear = (yearlyDepreciation / 365.25) * useDaysInFY;
            } else { // WDV
                if (grossAmount > 0 && salvageValue < grossAmount) {
                    const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
                    const yearlyDepreciation = depreciationBase * rate;
                    depreciationForYear = (yearlyDepreciation / 365.25) * useDaysInFY;
                }
            }
        }

        return {
            grossAmount,
            salvageValue,
            depreciationBase,
            useDaysInFY,
            depreciationForYear: Math.round(depreciationForYear),
            depreciationRate: lifeInYears > 0 ? (1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears))) * 100 : 0
        };
    };

    try {
        console.log('📊 Testing first year depreciation scenarios...\n');

        // Get assets from first year (2022-23)
        const assets = await runQuery(`
            SELECT record_id, asset_particulars, gross_amount, put_to_use_date, 
                   depreciation_method, life_in_years, salvage_percentage
            FROM assets 
            WHERE company_id = 'c1001' 
              AND put_to_use_date >= '2022-04-01'
              AND put_to_use_date <= '2023-03-31'
            ORDER BY record_id
        `);

        console.log(`Found ${assets.length} assets in first year (2022-23) for testing\n`);

        if (assets.length === 0) {
            console.log('⚠️ No first year assets found. Creating test asset...');
            
            // Insert a test asset for first year
            await runQuery(`
                INSERT OR REPLACE INTO assets (
                    company_id, record_id, asset_particulars, book_entry_date, put_to_use_date,
                    basic_amount, duties_taxes, gross_amount, vendor, invoice_no, model_make,
                    location, asset_id, remarks, ledger_name_in_books, asset_group,
                    asset_sub_group, schedule_iii_classification, salvage_percentage,
                    wdv_of_adoption_date, is_leasehold, depreciation_method, life_in_years, scrap_it
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                'c1001', 'FIRST001', 'Test First Year Asset', '2022-06-15', '2022-07-01',
                100000, 18000, 118000, 'Test Vendor', 'TEST/001', 'Test Model',
                'Test Location', 'FIRST001', 'Test first year depreciation', 'Test Equipment',
                'Plant & Machinery', 'General Plant & Machinery', 'Plant & Machinery - General',
                5, null, false, 'WDV', 10, false
            ]);
            
            // Re-fetch assets
            const newAssets = await runQuery(`
                SELECT record_id, asset_particulars, gross_amount, put_to_use_date, 
                       depreciation_method, life_in_years, salvage_percentage
                FROM assets 
                WHERE company_id = 'c1001' 
                  AND record_id = 'FIRST001'
            `);
            
            assets.push(...newAssets);
            console.log('✅ Test asset created successfully\n');
        }

        let allTestsPassed = true;

        for (const asset of assets) {
            console.log(`🧪 TESTING: ${asset.record_id} - ${asset.asset_particulars}`);
            console.log('─'.repeat(70));

            const metrics = calculateFirstYearDepreciation(asset, '2022-2023');

            console.log(`📊 Asset Details:`);
            console.log(`   Gross Amount: ₹${metrics.grossAmount.toLocaleString()}`);
            console.log(`   Put to Use Date: ${asset.put_to_use_date}`);
            console.log(`   Depreciation Method: ${asset.depreciation_method}`);
            console.log(`   Life in Years: ${asset.life_in_years}`);
            console.log(`   Salvage Percentage: ${asset.salvage_percentage}%`);
            
            console.log(`\n📈 Calculated Metrics:`);
            console.log(`   Salvage Value: ₹${metrics.salvageValue.toLocaleString()}`);
            console.log(`   Depreciation Base: ₹${metrics.depreciationBase.toLocaleString()}`);
            console.log(`   Use Days in FY: ${metrics.useDaysInFY} days`);
            console.log(`   Depreciation Rate: ${metrics.depreciationRate.toFixed(2)}%`);
            console.log(`   Depreciation for Year: ₹${metrics.depreciationForYear.toLocaleString()}`);

            // Test 1: Depreciation should be calculated (not zero)
            if (metrics.depreciationForYear > 0) {
                console.log(`\n✅ PASS: Depreciation calculated (₹${metrics.depreciationForYear.toLocaleString()})`);
            } else {
                console.log(`\n❌ FAIL: No depreciation calculated - this should not happen for valid assets`);
                allTestsPassed = false;
            }

            // Test 2: Use days should be reasonable (not full year for mid-year additions)
            const putToUseDate = new Date(asset.put_to_use_date);
            const fyStart = new Date('2022-04-01');
            const expectedMaxDays = putToUseDate >= fyStart ? 
                inclusiveDateDiffInDays(putToUseDate, new Date('2023-03-31')) : 365;
            
            if (metrics.useDaysInFY <= expectedMaxDays && metrics.useDaysInFY > 0) {
                console.log(`✅ PASS: Use days calculation correct (${metrics.useDaysInFY}/${expectedMaxDays} days)`);
            } else {
                console.log(`❌ FAIL: Use days calculation incorrect (${metrics.useDaysInFY}/${expectedMaxDays} days)`);
                allTestsPassed = false;
            }

            // Test 3: Depreciation should not exceed depreciable amount
            const maxDepreciableInYear = (metrics.grossAmount - metrics.salvageValue) / asset.life_in_years;
            if (metrics.depreciationForYear <= maxDepreciableInYear * 1.1) { // Allow 10% margin for partial year
                console.log(`✅ PASS: Depreciation within reasonable limits`);
            } else {
                console.log(`❌ FAIL: Depreciation exceeds reasonable limits`);
                allTestsPassed = false;
            }

            // Test 4: Schedule III specific validations
            console.log(`\n📋 Schedule III Expectations:`);
            console.log(`   Opening Gross: ₹0 (first year addition)`);
            console.log(`   Additions Gross: ₹${metrics.grossAmount.toLocaleString()}`);
            console.log(`   Opening Depreciation: ₹0`);
            console.log(`   Depreciation for Year: ₹${metrics.depreciationForYear.toLocaleString()}`);
            console.log(`   Opening Net Block: ₹0`);
            console.log(`   Closing Net Block: ₹${(metrics.grossAmount - metrics.depreciationForYear).toLocaleString()}`);

            console.log('');
        }

        // Summary
        console.log('🎯 FIRST YEAR DEPRECIATION TEST SUMMARY');
        console.log('═'.repeat(50));

        if (allTestsPassed) {
            console.log('✅ ALL FIRST YEAR TESTS PASSED!');
            console.log('✅ Depreciation is being calculated correctly for first year');
            console.log('✅ Schedule III should show depreciation in "For the Year" column');
            console.log('✅ Use days calculation is accurate');
        } else {
            console.log('❌ SOME FIRST YEAR TESTS FAILED');
            console.log('❌ First year depreciation logic needs review');
        }

        console.log('\n💡 TESTING IN APPLICATION:');
        console.log('1. Generate Schedule III report for FY 2022-23');
        console.log('2. Check "Depreciation - For the Year" column');
        console.log('3. Verify it shows calculated amounts, not zero');
        console.log('4. Confirm partial year calculation for mid-year additions');

        console.log('\n🎯 EXPECTED RESULTS IN SCHEDULE III:');
        console.log('- Opening columns should be ₹0 for first year additions');
        console.log('- Additions Gross should show asset amounts');
        console.log('- Depreciation For the Year should show calculated amounts');
        console.log('- Closing Net Block should be less than Closing Gross');

    } catch (error) {
        console.error('❌ First year verification error:', error);
    } finally {
        db.close();
    }
};

verifyFirstYearDepreciation();