# FAR Sighted Multi-Database Migration and Deployment Guide

## Quick Start

### 1. Backup Current System
```bash
# Backup the current database
cp backend/database/far_sighted.db backend/database/far_sighted.db.backup.$(date +%Y%m%d_%H%M%S)
```

### 2. Update Package Configuration
```bash
cd backend
cp package-new.json package.json
```

### 3. Run Migration
```bash
# Test migration first (dry run)
npm run migrate:dry-run

# Run actual migration
npm run migrate

# Or force migration if needed
npm run migrate:force
```

### 4. Start New Server
```bash
# Replace old server with new multi-database server
cp server-new.js server.js

# Start the new server
npm start
```

## Migration Process

### Pre-Migration Checklist

- [ ] **Backup existing database**: Copy `far_sighted.db` to safe location
- [ ] **Stop application**: Ensure no users are actively using the system
- [ ] **Verify disk space**: Ensure sufficient space for company databases
- [ ] **Test environment**: Run migration in development first
- [ ] **Document companies**: Note down current companies and asset counts

### Migration Steps

#### Step 1: Prepare Environment
```bash
cd E:\Projects\FAR Sighted\backend

# Check current database status
npm run status

# View system health
npm run health
```

#### Step 2: Run Migration Script
```bash
# Preview what will be migrated (recommended first)
npm run migrate:dry-run

# Run the actual migration
npm run migrate

# Check migration results
npm run status
npm run system-info
```

#### Step 3: Verify Migration
```bash
# Test company creation
npm run test:company

# Check system health
npm run health

# Verify all companies migrated
curl -s http://localhost:3001/api/companies | jq '.'
```

#### Step 4: Update Server Configuration
```bash
# Replace old server with new one
cp server-new.js server.js
cp package-new.json package.json

# Restart server
npm start
```

## Verification Checklist

### Database Structure Verification
- [ ] Master database created at `backend/database/master.db`
- [ ] Company folders created in `backend/database/companies/`
- [ ] Each company has its own `company.db` file
- [ ] Original database backed up with timestamp

### Data Integrity Verification
- [ ] All companies migrated successfully
- [ ] Asset counts match between old and new structure
- [ ] User accounts transferred to master database
- [ ] Audit trails preserved in company databases
- [ ] Statutory rates and ledgers migrated correctly

### Functional Verification
- [ ] User login works
- [ ] Company switching works
- [ ] Asset data displays correctly
- [ ] Reports generate properly
- [ ] Backup/restore functions work

## Directory Structure After Migration

```
backend/
├── database/
│   ├── master.db                           # Master database
│   ├── far_sighted.db.migration-backup.*   # Original database backup
│   └── companies/                          # Company-specific databases
│       ├── c1640000001-ABC_Private_Limited/
│       │   └── company.db
│       ├── c1640000002-XYZ_Corporation/
│       │   └── company.db
│       └── ...
├── services/
│   ├── database-manager/
│   │   ├── DatabaseManager.js
│   │   ├── CompanyDatabaseService.js
│   │   └── DatabaseMigrator.js
│   ├── database-new.js                     # New database service
│   └── database.js                         # Original (can be removed)
├── routes/
│   ├── companies-new.js                    # Updated companies routes
│   ├── users-new.js                        # Updated users routes
│   ├── assets-new.js                       # Updated assets routes
│   └── admin.js                            # Migration admin routes
├── scripts/
│   └── migrate-database.js                 # Migration script
├── server-new.js                           # New server with multi-DB support
└── package-new.json                        # Updated package configuration
```

## API Endpoints

### Migration and Admin
```http
GET  /api/migration-status          # Check migration status
POST /api/admin/migrate             # Run migration via API
GET  /api/admin/database-info       # Get database structure info
POST /api/admin/test-company        # Create test company
```

### Company Management
```http
GET  /api/companies                 # List all companies
POST /api/companies                 # Create new company
GET  /api/companies/:id             # Get company with full data
PUT  /api/companies/:id             # Update company info
```

### Asset Management (Company-Specific)
```http
GET  /api/assets/company/:companyId                    # Get company assets
PUT  /api/assets/company/:companyId                    # Update company assets
POST /api/assets/company/:companyId/asset             # Create single asset
PUT  /api/assets/company/:companyId/asset/:recordId   # Update single asset
```

### User Management (Master Database)
```http
GET  /api/users                     # List all users
POST /api/users                     # Create new user
POST /api/users/login               # User authentication
PUT  /api/users/:id                 # Update user
```

## Environment Variables

Create or update `.env` file:
```env
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:5173

# Database settings
DB_MASTER_PATH=./database/master.db
DB_COMPANIES_BASE_PATH=./database/companies
DB_BACKUP_PATH=./database/backups

# Security settings
BCRYPT_ROUNDS=10
SESSION_SECRET=your-secret-key-here

# Logging
LOG_LEVEL=info
ENABLE_AUDIT_LOGGING=true
```

## Troubleshooting

### Migration Issues

#### Issue: "Old database not found"
**Solution**: Ensure `far_sighted.db` exists in `backend/database/` directory

#### Issue: "Permission denied creating company folders"
**Solution**: Check write permissions on `backend/database/` directory
```bash
chmod 755 backend/database
chown -R $USER backend/database
```

#### Issue: "Migration partially completed"
**Solution**: Check migration logs and re-run with force flag
```bash
npm run migrate:force
```

### Runtime Issues

#### Issue: "Company context required"
**Solution**: Ensure company ID is included in API requests
```javascript
// Correct API call
fetch('/api/assets/company/c1640000001')

// Or set company context in request
fetch('/api/assets', {
  headers: { 'X-Company-ID': 'c1640000001' }
})
```

#### Issue: "Database connection failed"
**Solution**: Check if company database exists and is accessible
```bash
# Check company databases
ls -la backend/database/companies/*/company.db

# Test database connectivity
npm run health
```

### Performance Issues

#### Issue: "Slow company switching"
**Solution**: Database connections are cached, but initial connection may be slow
- Monitor connection pool usage
- Consider connection pre-warming for active companies

#### Issue: "High memory usage"
**Solution**: Multiple database connections consume more memory
- Monitor active connections
- Implement connection cleanup for inactive companies

## Rollback Procedure

If migration fails or issues arise:

### Step 1: Stop New Server
```bash
# Stop the new server
pkill -f "node server-new.js"
```

### Step 2: Restore Original Configuration
```bash
# Restore original server
cp server.js.backup server.js  # If you backed it up
# Or manually revert to old routes in server.js

# Restore original package.json if needed
cp package.json.backup package.json
```

### Step 3: Verify Original Database
```bash
# Check if original database is intact
ls -la backend/database/far_sighted.db

# If corrupted, restore from backup
cp backend/database/far_sighted.db.backup.* backend/database/far_sighted.db
```

### Step 4: Start Original Server
```bash
npm start
```

## Performance Monitoring

### Database Metrics
```bash
# Check database file sizes
du -sh backend/database/*

# Monitor connection counts
netstat -an | grep :3001

# Check memory usage
ps aux | grep node
```

### API Performance
```bash
# Test API response times
time curl -s http://localhost:3001/api/health

# Monitor request logs
tail -f backend/logs/access.log
```

## Security Considerations

### Database Security
- Each company database is isolated
- No cross-company data access possible
- Separate encryption keys possible per company

### Access Control
- User authentication in master database
- Company-specific authorization
- Audit trail per company

### Backup Security
- Company-specific backups
- Encrypted backup storage recommended
- Regular backup verification

## Maintenance Tasks

### Weekly Tasks
- [ ] Verify database integrity for all companies
- [ ] Check audit log sizes
- [ ] Monitor disk space usage
- [ ] Review performance metrics

### Monthly Tasks
- [ ] Backup all company databases
- [ ] Clean up old audit logs
- [ ] Update database statistics
- [ ] Review security logs

### Quarterly Tasks
- [ ] Full system backup verification
- [ ] Performance optimization review
- [ ] Security audit
- [ ] Database maintenance (VACUUM, REINDEX)

## Support Contacts

For issues with migration or multi-database implementation:

1. **Check logs**: Review migration script output and server logs
2. **Verify setup**: Ensure all prerequisites are met
3. **Test in development**: Try migration on copy of production data
4. **Document issues**: Note exact error messages and steps to reproduce

## Migration Success Indicators

✅ **Migration Completed Successfully When:**
- All companies appear in new structure
- Asset counts match original database
- Users can login and access company data
- Reports generate correctly
- No error messages in server logs
- Original database is safely backed up

The multi-database implementation provides enhanced security, better performance, and improved scalability for the FAR Sighted application while maintaining full backward compatibility.