# Product Requirements Document (PRD)

## Title: Fixed Asset Register (FAR) Management System

### Platform
- Desktop App (Electron)

### Tech Stack
- Frontend: React, TypeScript/JavaScript, TailwindCSS
- Backend: SQLite
- Packaging: Electron (for Windows executable)

*Note: The current version of the application uses a simulated API layer to mimic a backend service. This architecture is designed to be backend-agnostic, preparing the application for future integration with a live SQLite database without requiring significant changes to the frontend components.*

---

## 1. Core Functional Requirements

### 1.1 Multi-Entity Support
- Separate data folder and SQLite database per entity.
- Entity switching requires re-login.
- Export/Import entity data for use in multiple locations (e.g., office, auditor).

### 1.2 Licensing Control
- Licensing is per entity per financial year.
- If license is invalid:
  - Data entry allowed only for 3 months from start of FY.
  - Report generation is blocked after that.
- Licensing data stored inside the entity database.

### 1.3 Company Master
- Fields:
  - Company Name
  - Corporate Identification Number (CIN)
  - Date of Incorporation
  - Financial Year Start/End (default: Apr 1 – Mar 31)
  - First date of adoption
  - Data Folder Path
  - Address (2 lines)
  - City
  - PIN
  - Email
  - Mobile
  - Contact Person
  - License Valid Upto
- The Asset Classification master is loaded per company.
  - In the `Asset Classification` view, the 'Useful Life' for statutory items (where `Is Statutory` is 'Yes') is non-editable.
  - The 'Useful Life' for non-statutory items is editable, allowing for custom classifications.

---

## 2. Asset Management

### 2.1 Asset Classification Master
- This master data defines the categories for all assets and is managed in the `Asset Classification` view.
- It is based on the statutory requirements of the Companies Act, but can be extended with custom, non-statutory classifications.
- **Key Fields**:
  - `Asset Group`, `Asset Sub-Group`: The primary classification hierarchy.
  - `Useful Life (Yrs)`: The depreciation period for the asset type. This is only editable for non-statutory items.
  - `Extra Shift Depr.`: Indicates if assets in this category are eligible for extra shift depreciation.
  - `Schedule III Classification`: The corresponding reporting category.
  - `Tangibility`: Whether the asset is 'Tangible' or 'Intangible'.
  - `Is Statutory`: Determines if the `Useful Life` is fixed ('Yes') or editable ('No').

### 2.2 Asset Records
- A system-generated, unique "Record ID" will be the primary identifier for each asset.
- Editing of asset data is locked for all financial years except the most recent one. An exception exists for administrators, who can temporarily unlock a past year for editing (see section 2.6).
- Users can filter records based on text input.
- Users can add a new, single asset record directly from the UI.
- A 'Cancel' button will be available, allowing users to discard any pending edits.
- Column order is organized for logical data entry, with disposal-related information placed at the end of the table.
- The 'Gross Amount' is automatically calculated as `Basic Amount` + `Duties, Taxes, etc.` and is not user-editable.
- **Date Validation**:
  - The 'Put to Use Date' will automatically be filled with the 'Book Entry Date' when the latter is entered or changed.
  - The 'Put to Use Date' cannot be earlier than the 'Book Entry Date'. This validation is enforced during both manual entry and CSV import.
  - The 'Disposal Date' must be on or after the 'Put to Use Date' and must fall within the selected financial year.
- **Field Dependencies**:
  - When the 'Asset Group' is changed, dependent fields ('Asset Sub-Group', 'Life in years', 'Schedule III Classification') are automatically cleared to ensure data integrity.
  - The 'Asset Sub-Group' dropdown is dynamically populated based on the selected 'Asset Group' and is disabled until a group is chosen.
  - Upon selecting an 'Asset Sub-Group', the 'Schedule III Classification' (read-only) and 'Life in years' fields are auto-populated based on the master data.
  - The 'Life in years' field is disabled by default. It is only enabled if the user selects a non-statutory 'Asset Sub-Group'.
  - The 'WDV on Adoption Date' field is enabled for a new asset *only if* the company is in its first financial year of adoption. It is disabled for all existing assets. However, it remains a compulsory field for any asset whose 'Put to use Date' is before the company's adoption date.
  - The 'Lease Period' field is disabled and cleared if 'Assets under leasehold' is set to 'No'.
  - Disposal-related fields ('Disposal Date', 'Disposal Amount') are disabled for new assets.
- Compulsory fields will be marked with an asterisk (*) in their column header for user clarity.
- The structure of asset fields is defined in the "Assets Structure.csv" document.

### 2.3 Create Next Financial Year
- The system provides a function to close the current financial year and generate calculations for all assets.
- This process finalizes the data for the closing year and prepares the system for the next financial year.
- **Trigger**: A "Create Next FY" button, which is enabled only when viewing the most recent financial year. Initiating the process requires user confirmation via a non-blocking UI modal.
- **Process**:
  - **Validation & Atomicity**: The entire process is treated as an atomic transaction. It first validates data integrity (e.g., checks for missing dates or historical data) across all assets. If validation passes, it calculates all values in memory. Changes are only committed to the database upon successful completion of all calculations, preventing partial updates and data corruption. If any error occurs, the process halts with a specific message, and no data is changed.
  - Calculates and stores the final depreciation and closing WDV for every asset for the closing year.
  - **Opening WDV**: Determined based on the asset's history. For the first year of adoption, it uses 'WDV of Adoption Date' for old assets or 'Gross Amount' for new ones. For subsequent years, it uses the prior year's 'Closing WDV'.
  - **Depreciation Calculation**: The annual depreciation is calculated based on the method (WDV or SLM), prorated for the exact number of days the asset was in use during the year, and considers any extra shift days applicable. The total depreciation is capped to ensure the asset's WDV does not fall below its salvage value.
  - **Closing WDV**: Calculated as `Opening WDV - Final Depreciation for the Year`.
  - **Data Storage**: The calculated values (`Use Days`, `Depreciation`, `Closing WDV`) for the closing year are stored against each asset record.
  - **New Year Initialization**: A new financial year is added to the company's records, and placeholders for its data (e.g., shift days, calculated values) are created for each asset.

### 2.4 Shift-Based Depreciation
- A dedicated "Extra Shift Days" view will be provided.
- This view lists all assets eligible for extra shift depreciation for the selected financial year.
- Users can input the number of 2nd and 3rd shift days for each asset in this view.
- Changes can be saved on a per-company, per-year basis.

### 2.5 Depreciation Calculation
- **Integer-Based Calculations**: All financial calculations, including depreciation and WDV, will be rounded to the nearest whole number at each step to avoid decimals in all stored and displayed results.
- Based on useful life from “Rates” sheet (Sch II).
- Prorated for acquisition/disposal dates.
  - Prorating is based on an exact, inclusive count of days the asset was in use during the financial year. The day count calculation must be performed in a timezone-agnostic way (e.g., using UTC) to ensure accuracy across leap years and different user locations.
- **Opening WDV Determination**: For assets put to use *before* the company's adoption date, the 'WDV of Adoption Date' is used as the Opening WDV in the first financial year. For all subsequent years and for new assets, the previous year's closing WDV is used as the opening WDV.
- **Depreciation Rate**: The annual depreciation rate is calculated based on the method.
    - **WDV Rate** = `1 - ( (Salvage / Gross) ^ (1 / Life) )`. This rate is applied to the Opening WDV for the year.
    - **SLM Rate** = `1 / Life in Years`. This rate is applied to the original Depreciable Amount (Gross - Salvage).
- **Data Storage**: When a financial year is finalized via the "Create Next FY" process, the key calculated values for that year (`Use Days`, `Depreciation`, `Closing WDV`, etc.) are stored as new properties on each asset's record. The property keys are suffixed with the financial year (e.g., `Depreciation-2023-2024`, `WDV-2023-2024`).

### 2.6 Administrator Override: Unlocking a Financial Year
- **Purpose**: To allow for exceptional corrections to historical data, administrators have the ability to unlock a previously finalized financial year.
- **Security**: This is a high-risk, admin-only function, protected by password re-authentication.
- **Workflow**:
  - The "Unlock Financial Year" function is available as a dedicated view under the `Administration` menu.
  - An administrator selects a past, locked year from the list and clicks the "Unlock" button.
  - A confirmation modal appears, warning of the risks and requiring the admin to re-enter their password.
  - On successful authentication, the system automatically performs a full backup of the database and captures a snapshot of all asset data *before* any changes are made.
  - The year becomes temporarily "unlocked," allowing edits in views like 'Asset Records' and 'Extra Shift Days'. A prominent "Lock & Recalculate" button appears in the main app header, and navigation to other companies or financial years is disabled during this state.
  - After making edits, the administrator clicks the "Lock & Recalculate" button.
  - **Impact Analysis**: The system performs a "dry run" of the recalculation and presents the administrator with an **Impact Analysis modal**. This modal shows a detailed, per-asset breakdown comparing the old vs. new values for `Depreciation` and `Closing WDV` for the unlocked year and all subsequent years. This ensures the admin is fully aware of the consequences of their changes.
  - **Confirmation**: The admin can review the impact and either `Confirm & Recalculate` to proceed or `Cancel` to return to the unlocked state for further edits.
  - **Finalization**: Upon confirmation, the system triggers a full recalculation of all financial data from the unlocked year forward, ensuring all subsequent years are updated and consistent.
  - The year is then re-locked, and the override process is complete.
- **Auditing**: Every critical step (automatic backup, unlock action, impact analysis, recalculation) is recorded in the Audit Trail.

---

## 3. Reporting

### 3.1 Year-Wise Data Storage
- As described in section 2.5, all year-specific calculated data is stored directly on each asset's record using year-suffixed property keys. There is no separate `depreciation_history` table.

### 3.2 Reports
- **Asset Group Report**: A report that aggregates financial data based on "Asset Group" and "Asset Sub-Group". It uses the same column structure as the Schedule III report but intelligently filters out any asset sub-groups where all financial columns are zero.
- **Schedule III Format**: A summary report as per Schedule III that includes a column for the count of underlying assets in each row. The asset count correctly reflects only those assets active during the selected financial year.
- **Ledger-wise**: A report that groups all financial movements by the 'Ledger Name' assigned to the assets, which is useful for reconciling with books of accounts.
- **Method-wise (SLM / WDV)**: A report that separates all asset values based on their depreciation method (WDV or SLM).
- **Tangibility-wise**: A summary report that groups all financial data based on whether the assets are Tangible or Intangible.
- **Drill-Down Feature**: Most summary reports (Schedule III, Ledger-wise, Method-wise, Tangibility-wise) support an interactive drill-down. Double-clicking a summary row opens a detailed modal view of the individual assets comprising that row.
- All report views support column sorting and exporting the current view to Excel.

---

## 4. Ledger Mapping

- The application uses a flexible ledger system managed in the `Ledger Master` view.
- There is no rigid, separate ledger table with IDs. Instead, the master list of ledgers is dynamically compiled from:
  1.  All unique `Ledger Name in Books` values currently used in the `Asset Records`.
  2.  A separate list of "extra ledgers" that may not yet be assigned to any asset.
- From the `Ledger Master` view, users can:
  - Rename existing ledgers. This action will update the `Ledger Name in Books` field for all associated assets.
  - Add new, unassigned ledgers to the list.
  - Delete ledgers that have no assets assigned to them.
  - Import a list of ledgers from a CSV file.

---

## 5. User Management

- Fields:
  - `user_id`, `username`, `password`, `role`
- Roles: Admin, Data Entry, Report Viewer

---

## 6. Backup & Restore

The application provides a dedicated "Backup & Restore" view for all data management operations.

### 6.1 Manual Backup
- **Functionality**: Users can manually trigger a full backup of the application's database.
- **Process**:
  - Clicking "Create Backup" generates a single JSON file.
  - The file is named with a timestamp (e.g., `far_system_backup_YYYY-MM-DD_HH-MM-SS.json`).
  - The user is prompted to save this file to a secure location on their local machine.
- **Content**: The backup file contains all data for all companies, including asset records, company info, and user accounts.

### 6.2 Manual Restore
- **Functionality**: Users can restore the application's state from a previously created backup file.
- **Process**:
  - A "Restore from File" button allows the user to select a `.json` backup file.
  - **Critical Warning**: Before proceeding, a confirmation modal appears, warning the user that restoring will **overwrite all existing data** and is irreversible.
  - Upon confirmation, the data from the file replaces the current database.
  - The application automatically reloads to reflect the restored state.

### 6.3 Operation History
- **Functionality**: The "Backup & Restore" screen includes a log of all recent backup and restore operations.
- **Log Fields**: The log displays the `Timestamp`, `Action` (Backup/Restore), `Initiated By` (Manual), and `Details` (Success/Failure).
- This log is stored in a `backups_log` table within the database.

### 6.4 Automatic Backups
- The application performs automatic backups before executing high-risk operations to ensure data safety. An auto-backup is created before:
  - Restoring data from a manual backup file.
  - Unlocking a finalized financial year for editing.
- These automatic backups are logged in the Operation History.
- A fully automated, scheduled backup system may be considered for a future version.

---

## 7. Audit Trail
- **Purpose**: To maintain a comprehensive and immutable record of all significant activities within the application.
- **Access**: The Audit Trail view is restricted to users with the 'Admin' role.
- **Logged Actions**: The system logs all Create, Update, and Delete (CUD) operations across the application, including but not limited to:
  - Company creation and modification.
  - User creation, modification, and deletion.
  - Asset record creation, updates, deletion, and bulk imports.
  - Changes to Asset Classification, Ledger Master, and Extra Shift Days.
  - Critical system events like manual/automatic backups, restores, financial year creation, and administrator-led year unlocking/recalculation.
- **Log Fields**: Each audit log entry contains:
  - `log_id`: A unique identifier for the log entry.
  - `timestamp`: The exact date and time the action occurred.
  - `user_id` & `username`: Who performed the action.
  - `action`: A short code for the type of action (e.g., `UPDATE_ASSET_RECORDS`).
  - `details`: A human-readable description of the change. For `UPDATE` actions, this description includes specific details about which fields were changed, including their old and new values, to provide a clear and actionable history of modifications.

---

## 8. UI/UX

### 8.1 Layout and Scrolling
- Auto-fit to screen width for most views.
- For data-heavy tables like Asset Records, horizontal scrolling is enabled for readability. Column widths are based on content.
- The first three columns of the Asset Records table ('Record ID', 'Asset Particulars', and 'Actions') will remain fixed (sticky) during horizontal scrolling to improve usability.

### 8.2 Table Controls
- **Filtering**: Most data tables include a text-based filter to quickly find records. A "Clear Filter" button is also provided.
- **Sorting**: All data tables support column-based sorting. Clicking a column header sorts the data ascendingly; a second click sorts descendingly. A visual indicator (e.g., ▲/▼) shows the current sort direction.
- **Export to Excel**: Each table view includes an "Export to Excel" button, allowing users to download the currently displayed data (respecting filters and sorting) as an `.xlsx` file.
- **Cell Wrapping**: Text within table cells will wrap as needed to ensure readability.
- **Interactive Calculated Fields**: In the 'Asset Calculations' view, certain cells are interactive to provide deeper insight:
    - Double-clicking the **Depreciation Rate** cell opens a modal showing the formula (WDV or SLM) and the calculation using the asset's specific values.
    - Double-clicking the **Depreciation for Year** cell opens a detailed modal. This modal displays a full breakdown of the calculation. For existing assets, it shows the `Opening WDV`; for new assets added in the current year, it shows the `Added Date` and `Addition Amount`. It also includes: `Depreciation Rate`, `Use Days`, `Normal Depreciation`, and if applicable, `2nd/3rd Shift Days` and `Extra Shift Depreciation Amount`. If the depreciation for the year is zero, the modal will instead display a clear, specific reason (e.g., asset is fully depreciated, not in use). The modal includes an "Export to Excel" feature.
    - Double-clicking the **Disposal WDV** cell (for disposed assets) opens a detailed modal. This modal displays a full breakdown of the disposal calculation, including: `Opening WDV`, `Use Days` for the year's depreciation, the `Depreciation Amount` applied for the year, the final `Disposal WDV`, the `Disposal Amount` realized, and the resulting `Gain/Loss on disposal`. The modal also includes an "Export to Excel" feature.
- **Report Drill-Down**: In summary reports (Schedule III, Ledger-wise, Method-wise, Tangibility-wise), double-clicking a summary row opens a detailed modal view. This view lists the individual assets that constitute the summary figures, presents the data in the same column format as the main report, and is also exportable to Excel.

### 8.3 Number Formatting
- All monetary values are handled and stored as whole numbers (integers).
- Any calculations resulting in decimals will be rounded to the nearest whole number.
- Displayed monetary values must use the Indian numbering system for comma separation (e.g., 1,23,45,678).

### 8.4 Sidebar Navigation

```
Company Masters
  └ Company Info
  └ Asset Classification
  └ Ledger Master
Assets Data
  └ Extra Shift Days
  └ Asset Records
  └ Asset Calculations
Reports
  └ Asset Group Report
  └ Schedule III
  └ Ledger-wise
  └ Method-wise
  └ Tangibility-wise
Users
  └ User List
  └ Add User
Settings
  └ Theme
  └ Backup & Restore
  └ Export Settings
Administration (Admin-only)
  └ Audit Trail
  └ Unlock Financial Year
Help & About
  └ User Manual
  └ About & Help
```
The sidebar also includes a footer area that displays contextual information:
- **Company Information**: Shows the name of the currently selected company.
- **License Validity**: Displays the "License Valid Upto" date for the selected company, providing at-a-glance status.
- **User Information**: Shows the `username` and `role` of the currently logged-in user.

### 8.5 Visual Cues
- **Disposed Assets**: In data tables such as 'Asset Records' and 'Asset Calculations', rows corresponding to assets that have been disposed of will be visually highlighted (e.g., with a colored border) for easy identification.
- **Extra Shift Depreciation**: In the 'Asset Calculations' view, the 'Depreciation for Year' cell for any asset that has extra shift depreciation applied will be visually highlighted (e.g., with a different background color and border) to draw attention to it.
- **Selected Row Highlight**: In all data tables, clicking on a row will highlight it with a distinct color. This selection persists during scrolling and hovering over other rows, providing a clear visual anchor for the user, especially in tables with horizontal scrolling.
- **Selected Column Highlight**: In all sortable data tables, clicking a column header both sorts the data and highlights the entire column with a distinct background color. This provides a clear vertical visual guide for the user and works in conjunction with row highlighting.
- **Search Term Highlighting**: In all filterable views, any text that matches the current search filter will be visually highlighted (e.g., with a yellow background). This provides immediate feedback on why a particular row is included in the search results.

---

## 10. Constraints

| Constraint | Rule |
|-----------|------|
| New Company Dates | Financial Year Start and Adoption Date must be April 1st. |
| Create Next FY | The "Create Next FY" function is only enabled when the user has selected the most recent financial year in the application. This prevents creating new years from historical data. |
| Admin Year Unlock | A high-risk, administrator-only function. Requires password confirmation and triggers an automatic backup and a full recalculation of subsequent years upon completion. |
| Salvage Value | Defaults to 5%, can be user-edited. |
| Statutory Asset Edit | The 'Useful Life' field for a statutory asset classification cannot be edited. |
| Locked Depreciation Year | Editing is disabled for all financial years except the most recent one, unless explicitly unlocked by an admin. |
| Audit Trail | All changes recorded |
| **Statutory Asset Life** | The 'Life in years' field is disabled by default. It is non-editable and auto-populated if the asset's sub-group is statutory. It becomes editable only when a non-statutory sub-group is selected. |
| Client-side Validation | Client-side validation will be performed before saving asset records to ensure all compulsory fields are filled. |
| Compulsory Asset Fields | Business-critical fields are marked with an asterisk (*) and are required for saving. |
| **WDV of Adoption Date** | This field is compulsory if the asset's 'Put to use Date' is before the company's adoption date. It is disabled for existing assets and for new assets added after the company's first year of adoption. |
| **Date Logic** | 'Put to Use Date' must be >= 'Book Entry Date'. 'Disposal Date' must be >= 'Put to Use Date' and within the current FY. This is enforced on manual entry and import. |
| **Lease Period Logic** | The 'Lease Period' field is disabled if the asset is not under leasehold. |
| **New Asset Disposal** | Disposal fields ('Disposal Date', 'Disposal Amount') are disabled when adding a new asset. |
| Opening WDV Calculation | For assets put to use before the company's adoption date, the 'WDV of Adoption Date' is used as the Opening WDV in the first financial year. For all subsequent years and for new assets, the previous year's closing WDV is used. |

---

## 11. Import/Export

- **Export**: Most data views (e.g., Asset Records, reports) include an "Export to Excel" feature.
- **Import**: The application supports bulk import for Assets and Ledgers from CSV files.
- **Templates**: Downloadable CSV templates are provided to ensure data compatibility.

### 11.1 Asset Import Workflow
To ensure data integrity, the asset import process follows a robust validation and correction workflow:
- **1. File Selection**: The user selects a CSV file for import in the `Asset Records` view.
- **2. Import Preview Modal**: A full-screen modal appears, displaying all records from the CSV in an editable table.
- **3. Live Validation**:
  - Each row and cell is automatically validated against the same business rules as manual data entry (e.g., required fields, date logic, valid dropdown values).
  - Any cell containing an error is visually highlighted (e.g., with a red border). Hovering over the cell reveals a tooltip explaining the specific error.
  - The modal provides a real-time summary of valid rows and rows with errors.
- **4. In-Modal Correction**: Users can directly edit the data within the modal's table. As corrections are made, validation re-runs instantly for the edited row, removing the error highlight if the issue is resolved.
- **5. Master Data Detection**: The system automatically detects new `Ledger Names` or `Asset Classifications` that do not exist in the database and lists them for user review and approval before import.
- **6. Smart Import Options**:
  - **Import**: If all rows are valid, this button commits all records to the database. If errors exist, the user is prompted to either cancel or import only the valid rows.
  - **Download Error Report**: If errors are present, the user can download an Excel file containing only the rejected rows, with an additional "Error" column detailing the specific validation failure for each row. This facilitates easy correction and re-import.

---

## 12. Initial Setup Flow

1. Create New Company
2. Load Schedule II Depreciation Table
3. Import Asset list and Ledger list
4. Define users
5. Configure financial year & backup path
6. Begin operations


## 13. Database Tables
Sch II Deprn.xlsx contains the details. Analyze for tables creation.