
body.dark {
  --background-primary: #1a1d21;
  --background-secondary: #23272d;
  --background-tertiary: #2a2e34;
  --background-hover: #31363d;
  --background-input: var(--background-primary);
  --background-modal-overlay: rgba(0, 0, 0, 0.7);
  --background-selected: rgba(59, 130, 246, 0.25);
  --background-stripe-row: #262a2f;
  --background-stripe-col: #282c31;
  --background-control-highlight: #2c323b;

  --text-primary: #e0e0e0;
  --text-secondary: #a0a0a0;
  --text-disabled: #6c727a;
  --text-on-accent: #ffffff;
  
  --border-primary: #363b42;
  --border-secondary: #4b5563; /* For button hovers etc. */

  --extra-shift-highlight-bg: rgba(249, 115, 22, 0.15);
  --extra-shift-highlight-border: var(--accent-warning);
}

body.light {
  --background-primary: #f9fafb; /* gray-50 */
  --background-secondary: #ffffff;
  --background-tertiary: #f3f4f6; /* gray-100 */
  --background-hover: #e5e7eb; /* gray-200 */
  --background-input: var(--background-secondary);
  --background-modal-overlay: rgba(0, 0, 0, 0.5);
  --background-selected: rgba(59, 130, 246, 0.2);
  --background-stripe-row: #f8f9fa;
  --background-stripe-col: #f6f7f9;
  --background-control-highlight: #eef2ff;

  --text-primary: #111827; /* gray-900 */
  --text-secondary: #6b7280; /* gray-500 */
  --text-disabled: #9ca3af; /* gray-400 */
  --text-on-accent: #ffffff;

  --border-primary: #d1d5db; /* gray-300 */
  --border-secondary: #9ca3af;

  --extra-shift-highlight-bg: rgba(249, 115, 22, 0.1);
  --extra-shift-highlight-border: var(--accent-warning);
}

:root {
  --font-family: 'Inter', sans-serif;
  --sidebar-width: 260px;

  /* Accents are shared between themes */
  --accent-primary: #3b82f6;
  --accent-primary-hover: #60a5fa;
  --accent-info: #6366f1;
  --accent-info-hover: #818cf8;
  --accent-success: #22c55e;
  --accent-success-hover: #4ade80;
  --accent-warning: #f97316;
  --accent-warning-hover: #fb923c;
  --accent-danger: #ef4444;
  --accent-danger-hover: #f87171;
  --accent-excel: #1D6F42;
  --accent-excel-hover: #185C37;
  --accent-csv: #0d9488;
  --accent-csv-hover: #14b8a6;
}


*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--background-primary);
  color: var(--text-primary);
  overflow: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
}

#root {
  height: 100vh;
  width: 100vw;
}

.app-container {
  display: flex;
  height: 100%;
}

/* --- Login & Recovery Screen Styles --- */
.login-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--background-primary);
}

.login-box {
    width: 100%;
    max-width: 400px;
    padding: 2.5rem;
    background-color: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: 0 10px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04);
}

.login-logo {
    width: 80px;
    height: auto;
    margin: 0 auto 1rem;
    display: block;
}

.login-title {
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 0.5rem;
}

.login-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: 2rem;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.login-error {
    color: var(--accent-danger);
    text-align: center;
    font-size: 0.9rem;
}

.login-footer {
    text-align: center;
    margin-top: 1.5rem;
}

.forgot-password-link {
    color: var(--accent-primary);
    font-size: 0.9rem;
    text-decoration: none;
}
.forgot-password-link:hover {
    text-decoration: underline;
}

/* Sidebar */
.sidebar {
  width: var(--sidebar-width);
  background-color: var(--background-secondary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  padding: 1rem;
  transition: width 0.3s ease, background-color 0.3s ease;
  flex-shrink: 0;
  position: relative;
  z-index: 0;
}

.sidebar-header {
  padding: 0.5rem 0.5rem 1.5rem 0.5rem;
}

.sidebar-header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-group {
  display: flex;
  flex-direction: column;
}

.nav-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-family: var(--font-family);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.nav-group-header:hover {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
}

.nav-group-header .chevron {
  width: 1rem;
  height: 1rem;
  transition: transform 0.2s ease;
  stroke: var(--text-secondary);
}

.nav-group-header:hover .chevron {
    stroke: var(--text-primary);
}

.nav-group-header .chevron.open {
  transform: rotate(90deg);
}

.nav-group-content {
  display: flex;
  flex-direction: column;
  padding-left: 1rem;
  margin-top: 0.5rem;
  border-left: 1px solid var(--border-primary);
  gap: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.6rem 1rem;
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-secondary);
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
  text-decoration: none;
}

.nav-link .icon {
  stroke: var(--text-secondary);
  transition: stroke 0.2s ease;
}

.nav-link:hover {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
}

.nav-link:hover .icon {
    stroke: var(--text-primary);
}

.nav-link.active {
  background-color: var(--accent-primary);
  color: var(--text-on-accent);
  font-weight: 600;
}

.nav-link.active .icon {
    stroke: var(--text-on-accent);
}

/* Sidebar Footer for Registration & User Info */
.sidebar-footer {
  margin-top: auto; /* Push to bottom */
  padding-top: 1rem;
  border-top: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sidebar-footer-section {
  /* container for each info block */
}

.sidebar-footer .footer-label {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--text-secondary);
  display: block;
}

.sidebar-footer .footer-value-primary {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-top: 0.25rem;
  line-height: 1.3;
  word-wrap: break-word;
  white-space: normal;
}

.sidebar-footer .footer-value-secondary {
  font-size: 0.85rem;
  margin-top: 0.25rem;
  color: var(--text-secondary);
}

.sidebar-footer .user-role {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.85rem;
}


/* Main Content */
.main-content {
  flex-grow: 1;
  padding: 2rem 3rem;
  display: flex;
  flex-direction: column;
  max-height: 100vh;
  min-width: 0; /* Prevents flex item from overflowing its container */
  overflow-y: auto;
  position: relative;
  z-index: 0;
}

.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-primary);
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.header-controls {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.header-controls label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.header-controls select {
    background-color: var(--background-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-family: var(--font-family);
    font-size: 0.9rem;
    cursor: pointer;
}

.company-select-wrapper {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}


.header-controls select.highlighted-control {
    background-color: var(--background-control-highlight);
    border: 1px solid var(--accent-primary);
    font-weight: 500;
}

.view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.view-header h2 {
    margin: 0;
    padding: 0;
}

.view-header .actions {
    display: flex;
    gap: 1rem;
}

.main-content h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* New Company Info & Settings Styles */
.company-info-container, .settings-container {
    background-color: var(--background-secondary);
    border-radius: 8px;
    padding: 2rem;
    border: 1px solid var(--border-primary);
}

.settings-container section:not(:last-child) {
    padding-bottom: 2rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-primary);
}

.company-info-container p {
    font-size: 1.1rem;
}

.company-info-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem 2.5rem;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-item label {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.info-item p {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    background-color: var(--background-primary);
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid var(--border-primary);
    min-height: 40px; /* Ensure consistent height */
    word-wrap: break-word;
}


/* Table Styles */
.table-container {
  overflow: auto;
  background-color: var(--background-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
  flex-grow: 1;
  min-height: 0;
}

/* Specific styling for AssetRecords to ensure horizontal scroll works */
.asset-records-table-container {
    overflow: auto; /* Allow both horizontal and vertical scrolling */
    background-color: var(--background-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-primary);
    flex-grow: 1;
    min-height: 0;
}

.asset-records-table-container table {
  table-layout: auto;
  width: auto; /* Allow table to grow beyond container width */
}


table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed; 
}

.table-layout-auto {
    table-layout: auto;
}

th {
  background-color: var(--background-tertiary);
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 16px 15px;
  text-align: center;
  white-space: normal; /* Allow headers to wrap */
  vertical-align: middle;
}

td {
  padding: 8px 15px; /* Reduced padding for denser table */
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
  overflow-wrap: break-word;
  color: var(--text-primary);
  font-size: 0.9rem;
  vertical-align: middle;
  white-space: nowrap;
}

/* Zebra & Checkerboard Striping */
tbody tr:nth-of-type(odd) > td {
  background-color: var(--background-stripe-row);
}
tbody tr > td:nth-of-type(odd) {
  background-color: var(--background-stripe-col);
}

tbody tr:hover td {
  background-color: var(--background-hover);
}

tbody tr:last-child td {
  border-bottom: none;
}

tfoot {
    font-weight: 700;
    background-color: var(--background-tertiary);
}

tfoot td {
    border-top: 2px solid var(--border-primary);
    color: var(--text-primary);
}


.text-right {
    text-align: right;
}

.text-right > input,
.text-right > select {
    text-align: right;
}

/* --- Selected Column Highlight --- */
/* Base style for a selected column's cells. */
th.selected-col, tbody td.selected-col {
    background-color: rgba(59, 130, 246, 0.15) !important;
}

/* Hover state for a selected column cell */
tbody tr:hover td.selected-col {
    background-color: rgba(59, 130, 246, 0.20) !important;
}

/*
  Intersection of selected row and column. This selector is more specific
  than the selected row style, so it will apply correctly.
*/
tbody tr.selected-row td.selected-col,
tbody tr.selected-row:hover td.selected-col {
    background-color: rgba(59, 130, 246, 0.35) !important;
}

/* Override for AssetRecords sticky columns which have specific background styles */
.asset-records-table-container th.sticky-col.selected-col,
.asset-records-table-container tbody td.sticky-col.selected-col {
    background-color: rgba(59, 130, 246, 0.15) !important;
}

.asset-records-table-container tbody tr:hover td.sticky-col.selected-col {
    background-color: rgba(59, 130, 246, 0.20) !important;
}

.asset-records-table-container tbody tr.selected-row td.sticky-col.selected-col {
    background-color: rgba(59, 130, 246, 0.35) !important;
}

.td-wrap {
  white-space: normal; /* Allow specific columns to wrap */
}

/* Clickable rows */
.clickable-row {
    cursor: pointer;
}

/* Column Widths */
.col-width-xs { min-width: 100px; }
.col-width-sm { min-width: 150px; }
.col-width-md { min-width: 200px; }
.col-width-lg { min-width: 250px; }
.col-width-xl { min-width: 300px; }


/* --- Sticky Column Styles for AssetRecords --- */
.asset-records-table-container .sticky-col {
    position: -webkit-sticky; /* For Safari */
    position: sticky;
    /* The background is now handled by the checkerboard pattern, hover, and selection styles */
}

.asset-records-table-container th.sticky-col {
    z-index: 20; /* Higher than normal header z-index */
    background-color: var(--background-tertiary);
}

.asset-records-table-container td.sticky-col {
    z-index: 5;
}

.asset-records-table-container .sticky-col-1 {
    left: 0;
}

.asset-records-table-container .sticky-col-2 {
    left: 150px; /* Should match the width of col-1 (.col-width-sm) */
}

.asset-records-table-container .sticky-col-3 {
    left: 400px; /* 150px (col-1) + 250px (col-2) */
}


/* Ensure hover color doesn't override sticky background */
tbody tr:hover .sticky-col {
  background-color: var(--background-hover) !important;
}
/* Ensure sticky header background is correct */
tbody tr:hover th.sticky-col,
th.sticky-col {
  background-color: var(--background-tertiary);
}


.td-record-id {
    font-weight: 500;
    color: var(--text-secondary);
    background-color: var(--background-tertiary);
}

tbody tr:hover td.td-record-id.sticky-col {
    background-color: var(--background-tertiary) !important;
}


.table-input, .table-select {
    width: 100%;
    min-width: 150px; /* Give inputs a reasonable minimum width */
    padding: 0.5rem;
    background-color: var(--background-input);
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 0.9rem;
    font-family: var(--font-family);
}

.table-input:focus, .table-select:focus {
    outline: 1px solid var(--accent-primary);
    border-color: var(--accent-primary);
}

.table-input:disabled, .table-select:disabled {
    background-color: var(--background-tertiary);
    color: var(--text-disabled);
    cursor: not-allowed;
}

.filter-container {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.report-filter-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    background-color: var(--background-tertiary);
    padding: 0.5rem 1rem;
    border-radius: 6px;
}

.report-filter-group label {
    font-weight: 500;
    color: var(--text-secondary);
}

.radio-group {
    display: flex;
    gap: 1rem;
}

.radio-group input[type="radio"] {
    display: none;
}

.radio-group label {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.radio-group input[type="radio"]:checked + label {
    background-color: var(--accent-primary);
    color: var(--text-on-accent);
    font-weight: 500;
}

.radio-group input[type="radio"]:not(:checked) + label:hover {
    background-color: var(--background-hover);
}


.filter-input-wrapper {
    position: relative;
    display: flex;
    flex-grow: 1;
    align-items: center;
}

.filter-input {
    flex-grow: 1;
    padding: 0.75rem;
    padding-right: 2.5rem; /* Make space for clear button */
    background-color: var(--background-input);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 1rem;
    font-family: var(--font-family);
}

.filter-clear-btn {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: opacity 0.2s, color 0.2s, background-color 0.2s;
    visibility: visible;
    opacity: 1;
}

.filter-clear-btn:hover {
    color: var(--text-primary);
    background-color: var(--background-tertiary);
}

.filter-clear-btn.hidden {
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
}

.filter-clear-btn svg {
    width: 16px;
    height: 16px;
}

/* Filter Info Tooltip */
.filter-info {
    position: relative;
    display: flex;
    align-items: center;
}

.filter-info-icon {
    cursor: pointer;
    color: var(--text-secondary);
    border: 1px solid var(--text-secondary);
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-style: normal;
    font-size: 0.9rem;
    user-select: none;
    transition: color 0.2s, border-color 0.2s;
}

.filter-info-icon:hover {
    color: var(--text-primary);
    border-color: var(--text-primary);
}

.filter-info-tooltip {
    visibility: hidden;
    opacity: 0;
    position: absolute;
    bottom: 140%; 
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--background-tertiary);
    color: var(--text-primary);
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid var(--border-primary);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 100;
    width: max-content;
    transition: opacity 0.2s, visibility 0.2s;
    pointer-events: none;
}

.filter-info:hover .filter-info-tooltip {
    visibility: visible;
    opacity: 1;
}

.filter-info-tooltip strong {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    font-weight: 600;
}

.filter-info-tooltip ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.loading-indicator, .error-message {
  color: var(--text-secondary);
  font-size: 1.2rem;
  text-align: center;
  padding: 2rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  border: 1px solid transparent;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s, color 0.2s;
  font-family: var(--font-family);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn:disabled {
    background-color: var(--background-tertiary);
    color: var(--text-disabled);
    cursor: not-allowed;
    border-color: var(--border-primary);
}

.btn-primary {
  background-color: var(--accent-primary);
  color: var(--text-on-accent);
  border-color: var(--accent-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--accent-primary-hover);
  border-color: var(--accent-primary-hover);
}

.btn-secondary {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--background-hover);
  border-color: var(--border-secondary);
}

.btn-success {
  background-color: var(--accent-success);
  color: var(--text-on-accent);
  border-color: var(--accent-success);
}

.btn-success:hover:not(:disabled) {
  background-color: var(--accent-success-hover);
  border-color: var(--accent-success-hover);
}

.btn-warning {
  background-color: var(--accent-warning);
  color: var(--text-on-accent);
  border-color: var(--accent-warning);
}

.btn-warning:hover:not(:disabled) {
  background-color: var(--accent-warning-hover);
  border-color: var(--accent-warning-hover);
}

.btn-excel {
  background-color: var(--accent-excel);
  color: var(--text-on-accent);
  border-color: var(--accent-excel);
}

.btn-excel:hover:not(:disabled) {
  background-color: var(--accent-excel-hover);
  border-color: var(--accent-excel-hover);
}

.btn-csv {
  background-color: var(--accent-csv);
  color: var(--text-on-accent);
  border-color: var(--accent-csv);
}

.btn-csv:hover:not(:disabled) {
  background-color: var(--accent-csv-hover);
  border-color: var(--accent-csv-hover);
}

.btn-info {
  background-color: var(--accent-info);
  color: var(--text-on-accent);
  border-color: var(--accent-info);
}
.btn-info:hover:not(:disabled) {
  background-color: var(--accent-info-hover);
  border-color: var(--accent-info-hover);
}

.btn-outline {
  background-color: transparent;
  color: var(--text-secondary);
  border-color: var(--border-primary);
}
.btn-outline:hover:not(:disabled) {
  background-color: var(--background-tertiary);
  border-color: var(--border-secondary);
  color: var(--text-primary);
}

.btn-logout {
  background-color: transparent;
  color: var(--accent-danger);
  border: 1px solid var(--accent-danger);
}

.btn-logout:hover:not(:disabled) {
  background-color: var(--accent-danger);
  color: var(--text-on-accent);
}

.required-asterisk {
    color: var(--accent-danger);
    margin-left: 0.25rem;
    font-weight: 700;
}

/* Row Action Buttons */
.action-buttons-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-icon {
    background: transparent;
    border: none;
    padding: 0.25rem;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s, color 0.2s;
}

.btn-icon:hover:not(:disabled) {
    background-color: var(--background-tertiary);
    color: var(--text-primary);
}

.btn-icon:disabled {
    color: var(--text-disabled);
    cursor: not-allowed;
}

.btn-icon.btn-revert:not(:disabled) {
    color: var(--accent-warning);
}

.btn-icon.btn-revert:hover:not(:disabled) {
    color: var(--accent-warning-hover);
    background-color: rgba(249, 115, 22, 0.1);
}

.btn-icon.btn-dispose:not(:disabled) {
    color: var(--accent-info);
}

.btn-icon.btn-dispose:hover:not(:disabled) {
    color: var(--accent-info-hover);
    background-color: rgba(99, 102, 241, 0.1);
}

.btn-icon.btn-delete:not(:disabled) {
    color: var(--accent-danger);
}

.btn-icon.btn-delete:hover:not(:disabled) {
    color: var(--accent-danger-hover);
    background-color: rgba(239, 68, 68, 0.1);
}

.btn-icon svg {
    width: 18px;
    height: 18px;
}

/* --- Calculation Modal Styles --- */
.clickable-cell {
    cursor: pointer;
    text-decoration: underline;
    text-decoration-style: dotted;
    text-underline-offset: 3px;
    transition: background-color 0.2s, color 0.2s;
}

.clickable-cell:hover {
    background-color: var(--accent-primary) !important;
    color: var(--text-on-accent) !important;
}

.extra-shift-depreciation {
    background-color: var(--extra-shift-highlight-bg);
    box-shadow: inset 2px 0 0 0 var(--extra-shift-highlight-border);
}


.calc-modal {
    max-width: 600px;
}

.calc-modal-body {
    padding: 1rem 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.calc-asset-name {
    font-size: 1.1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-primary);
}

.calc-section h4 {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.75rem;
}

.calc-formula {
    background-color: var(--background-primary);
    padding: 1rem;
    border-radius: 6px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 1rem; /* Slightly smaller for more text */
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    line-height: 1.6;
}

.calc-detail-list {
    background-color: var(--background-primary);
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.calc-detail-list p {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1rem;
    line-height: 1.4;
    font-family: var(--font-family);
}

.calc-detail-list p strong {
    font-weight: 600;
    font-family: var(--font-family);
    color: var(--text-primary);
}

.calc-note {
    background-color: var(--background-primary);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    padding: 1rem;
    margin-top: -0.5rem;
    font-style: italic;
    color: var(--text-secondary);
    text-align: center;
    font-size: 0.9rem;
}

.calc-section.result {
    margin-top: 1rem;
    text-align: center;
}

.calc-result {
    font-size: 1.5rem;
    font-weight: 700;
}

.calc-result strong {
    color: var(--accent-primary);
}

/* --- Recalculation Impact Modal Styles --- */
.recalc-impact-container {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0.5rem;
    margin: 0 -0.5rem; /* Counteract padding */
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.recalc-impact-asset-card {
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    background-color: var(--background-primary);
}

.recalc-impact-asset-card[open] {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.recalc-impact-asset-card summary {
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    list-style: none;
    color: var(--text-primary);
}
.recalc-impact-asset-card summary::-webkit-details-marker { display: none; }

.recalc-impact-summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.recalc-impact-record-id {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    font-family: 'Courier New', Courier, monospace;
}

.recalc-impact-asset-card .table-container {
    border-top: 1px solid var(--border-primary);
    border-left: none;
    border-right: none;
    border-bottom: none;
    border-radius: 0 0 7px 7px;
}
.recalc-impact-asset-card .table-container table {
    table-layout: fixed;
}


/* --- Theme Settings --- */
.settings-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.settings-description {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.theme-selector {
    display: flex;
    gap: 1rem;
    background-color: var(--background-primary);
    padding: 0.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-primary);
    width: fit-content;
}

.theme-btn {
    padding: 0.75rem 2rem;
    border: 1px solid transparent;
}

.theme-btn.active {
    background-color: var(--accent-primary);
    color: var(--text-on-accent);
    border-color: var(--accent-primary);
}

/* --- Sortable Table Headers --- */
th.sortable {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: relative;
}

th.sortable:hover {
    background-color: var(--background-hover);
}

.sort-indicator {
    display: inline-block;
    vertical-align: middle;
    margin-left: 0.25rem;
    opacity: 0.5;
    width: 1em;
    height: 1em;
    line-height: 1;
    font-size: 0.8em;
}

th.sortable-active .sort-indicator {
    opacity: 1;
}

/* --- Selected Row Highlight --- */
tbody tr.selected-row td,
tbody tr.selected-row:hover td {
  background-color: var(--background-selected) !important;
}

tbody tr.selected-row td.sticky-col,
tbody tr.selected-row:hover td.sticky-col {
    background-color: var(--background-selected) !important;
}

tbody tr.selected-row td.td-record-id.sticky-col {
    background-color: var(--background-selected) !important;
}

/* Highlight for disposed assets */
.disposed-row > td {
    opacity: 0.65;
    /* text-decoration: line-through;
    text-decoration-color: var(--accent-danger);
    text-decoration-thickness: 1.5px; */
}
.disposed-row > td span {
    text-decoration: line-through;
    text-decoration-color: var(--accent-danger);
    text-decoration-thickness: 1.5px;
}

/* Highlight for scrappable assets - BACKGROUND ONLY */
.scrapped-row td {
    background-color: rgba(249, 115, 22, 0.15) !important;
}
tbody tr.scrapped-row:hover td {
    background-color: rgba(249, 115, 22, 0.25) !important;
}


/* --- USER MANUAL STYLES --- */
.manual-container {
    flex: 1 1 auto;
    overflow-y: auto;
    padding-right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.manual-section details {
    background-color: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    transition: background-color 0.2s;
}

.manual-section details[open] {
    background-color: var(--background-tertiary);
}

.manual-section summary {
    font-size: 1.2rem;
    font-weight: 600;
    padding: 1rem 1.5rem;
    cursor: pointer;
    list-style: none; /* Remove default marker */
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--text-primary);
}

.manual-section summary::-webkit-details-marker {
    display: none; /* Hide for Chrome */
}

.manual-section summary::after {
    content: '▼';
    font-size: 0.8rem;
    transition: transform 0.2s ease;
    color: var(--text-secondary);
}

.manual-section details[open] summary::after {
    transform: rotate(-180deg);
}

.manual-content {
    padding: 0 1.5rem 1.5rem 1.5rem;
    border-top: 1px solid var(--border-primary);
    line-height: 1.7;
    color: var(--text-secondary);
}

.manual-content p {
    margin-bottom: 1rem;
}

.manual-content p strong {
    color: var(--text-primary);
    font-weight: 600;
}

.manual-content code {
    background-color: var(--background-primary);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9rem;
    color: var(--accent-warning);
    border: 1px solid var(--border-primary);
}

.manual-list {
    list-style-position: inside;
    padding-left: 0.5rem;
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.manual-list li::marker {
    font-weight: 700;
    color: var(--accent-primary);
}

/* InfoBox Component Styles */
.manual-infobox {
    margin: 1.5rem 0;
    padding: 1.25rem;
    border-radius: 6px;
    border-left-width: 4px;
    border-left-style: solid;
}

.manual-infobox-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.manual-