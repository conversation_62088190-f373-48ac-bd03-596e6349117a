
body.dark {
  --background-primary: #1a1d21;
  --background-secondary: #23272d;
  --background-tertiary: #2a2e34;
  --background-hover: #31363d;
  --background-input: var(--background-primary);
  --background-modal-overlay: rgba(0, 0, 0, 0.7);
  --background-selected: rgba(59, 130, 246, 0.25);
  --background-stripe-row: #262a2f;
  --background-stripe-col: #282c31;
  --background-control-highlight: #2c323b;
  --background-disabled: #1f2328;

  --text-primary: #e0e0e0;
  --text-secondary: #a0a0a0;
  --text-disabled: #6c727a;
  --text-on-accent: #ffffff;
  
  --border-primary: #363b42;
  --border-secondary: #4b5563; /* For button hovers etc. */

  --extra-shift-highlight-bg: rgba(249, 115, 22, 0.15);
  --extra-shift-highlight-border: var(--accent-warning);
}

body.light {
  --background-primary: #f9fafb; /* gray-50 */
  --background-secondary: #ffffff;
  --background-tertiary: #f3f4f6; /* gray-100 */
  --background-hover: #e5e7eb; /* gray-200 */
  --background-input: var(--background-secondary);
  --background-modal-overlay: rgba(0, 0, 0, 0.5);
  --background-selected: rgba(59, 130, 246, 0.2);
  --background-stripe-row: #f8f9fa;
  --background-stripe-col: #f6f7f9;
  --background-control-highlight: #eef2ff;
  --background-disabled: #f3f4f6;

  --text-primary: #111827; /* gray-900 */
  --text-secondary: #6b7280; /* gray-500 */
  --text-disabled: #9ca3af; /* gray-400 */
  --text-on-accent: #ffffff;

  --border-primary: #d1d5db; /* gray-300 */
  --border-secondary: #9ca3af;

  --extra-shift-highlight-bg: rgba(249, 115, 22, 0.1);
  --extra-shift-highlight-border: var(--accent-warning);
}

:root {
  --font-family: 'Inter', sans-serif;
  --sidebar-width: 260px;

  /* Accents are shared between themes */
  --accent-primary: #3b82f6;
  --accent-primary-hover: #60a5fa;
  --accent-info: #6366f1;
  --accent-info-hover: #818cf8;
  --accent-success: #22c55e;
  --accent-success-hover: #4ade80;
  --accent-warning: #f97316;
  --accent-warning-hover: #fb923c;
  --accent-danger: #ef4444;
  --accent-danger-hover: #f87171;
  --accent-excel: #1D6F42;
  --accent-excel-hover: #185C37;
  --accent-csv: #0d9488;
  --accent-csv-hover: #14b8a6;
}


*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--background-primary);
  color: var(--text-primary);
  overflow: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
}

#root {
  height: 100vh;
  width: 100vw;
}

.app-container {
  display: flex;
  height: 100%;
}

/* --- Login & Recovery Screen Styles --- */
.login-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--background-primary);
}

.login-box {
    width: 100%;
    max-width: 400px;
    padding: 2.5rem;
    background-color: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: 0 10px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04);
}

.login-logo {
    width: 360px; /* Enlarged by 3 times from 120px to 360px */
    height: auto;
    margin: 0 auto 2rem; /* Increased margin for better spacing */
    display: block;
    max-width: 90%; /* Ensure it doesn't overflow on small screens */
}

.login-title {
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 0.5rem;
}

.login-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: 2rem;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.login-error {
    color: var(--accent-danger);
    text-align: center;
    font-size: 0.9rem;
}

.login-footer {
    text-align: center;
    margin-top: 1.5rem;
}

.forgot-password-link {
    color: var(--accent-primary);
    font-size: 0.9rem;
    text-decoration: none;
}
.forgot-password-link:hover {
    text-decoration: underline;
}

/* Sidebar */
.sidebar {
  width: var(--sidebar-width);
  background-color: var(--background-secondary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  padding: 1rem;
  transition: width 0.3s ease, background-color 0.3s ease;
  flex-shrink: 0;
  position: relative;
  z-index: 0;
}

.sidebar-header {
  padding: 0.5rem 0.5rem 1.5rem 0.5rem;
}

.sidebar-header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-group {
  display: flex;
  flex-direction: column;
}

.nav-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-family: var(--font-family);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.nav-group-header:hover {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
}

.nav-group-header .chevron {
  width: 1rem;
  height: 1rem;
  transition: transform 0.2s ease;
  stroke: var(--text-secondary);
}

.nav-group-header:hover .chevron {
    stroke: var(--text-primary);
}

.nav-group-header .chevron.open {
  transform: rotate(90deg);
}

.nav-group-content {
  display: flex;
  flex-direction: column;
  padding-left: 1rem;
  margin-top: 0.5rem;
  border-left: 1px solid var(--border-primary);
  gap: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.6rem 1rem;
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-secondary);
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
  text-decoration: none;
}

.nav-link .icon {
  stroke: var(--text-secondary);
  transition: stroke 0.2s ease;
}

.nav-link:hover {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
}

.nav-link:hover .icon {
    stroke: var(--text-primary);
}

.nav-link.active {
  background-color: var(--accent-primary);
  color: var(--text-on-accent);
  font-weight: 600;
}

.nav-link.active .icon {
    stroke: var(--text-on-accent);
}

/* Sidebar Footer for Registration & User Info */
.sidebar-footer {
  margin-top: auto; /* Push to bottom */
  padding-top: 1rem;
  border-top: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sidebar-footer-section {
  /* container for each info block */
}

.sidebar-footer .footer-label {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--text-secondary);
  display: block;
}

.sidebar-footer .footer-value-primary {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-top: 0.25rem;
  line-height: 1.3;
  word-wrap: break-word;
  white-space: normal;
}

.sidebar-footer .footer-value-secondary {
  font-size: 0.85rem;
  margin-top: 0.25rem;
  color: var(--text-secondary);
}

.sidebar-footer .user-role {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.85rem;
}


/* Main Content */
.main-content {
  flex-grow: 1;
  padding: 2rem 3rem;
  display: flex;
  flex-direction: column;
  max-height: 100vh;
  min-width: 0; /* Prevents flex item from overflowing its container */
  overflow-y: auto;
  position: relative;
  z-index: 0;
}

.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-primary);
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.header-controls {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.header-controls label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.header-controls select {
    background-color: var(--background-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-family: var(--font-family);
    font-size: 0.9rem;
    cursor: pointer;
}

.company-select-wrapper {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}


.header-controls select.highlighted-control {
    background-color: var(--background-control-highlight);
    border: 1px solid var(--accent-primary);
    font-weight: 500;
}

.view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.view-header h2 {
    margin: 0;
    padding: 0;
}

.view-header .actions {
    display: flex;
    gap: 1rem;
}

.main-content h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* New Company Info & Settings Styles */
.company-info-container, .settings-container {
    background-color: var(--background-secondary);
    border-radius: 8px;
    padding: 2rem;
    border: 1px solid var(--border-primary);
}

.settings-container section:not(:last-child) {
    padding-bottom: 2rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-primary);
}

/* Form Grid Layout */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background-color: var(--background-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: 0.9rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input:disabled,
.form-group select:disabled,
.form-group textarea:disabled {
    background-color: var(--background-disabled);
    color: var(--text-disabled);
    cursor: not-allowed;
}

.required-asterisk {
    color: var(--accent-danger);
    margin-left: 0.25rem;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--background-modal-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999; /* Increased z-index to ensure it's above all content */
    padding: 1rem;
    overflow-y: auto; /* Allow scrolling if modal is taller than viewport */
}

.modal-content {
    background-color: var(--background-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-primary);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    padding: 1.5rem;
    position: relative;
    /* Ensure modal is properly centered and visible */
    margin: auto;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-primary);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-header-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.modal-close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0.25rem;
    line-height: 1;
    transition: color 0.2s ease;
}

.modal-close-btn:hover {
    color: var(--text-primary);
}

/* Modal Icon Styles */
.icon-warning {
    color: var(--accent-warning);
}

.icon-info {
    color: var(--accent-info);
}

.icon-success {
    color: var(--accent-success);
}

.icon-danger {
    color: var(--accent-danger);
}

/* Modal Actions */
.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-primary);
}

/* Loading Indicator */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    font-size: 1.1rem;
    color: var(--text-secondary);
}

.company-info-container p {
    font-size: 1.1rem;
}

.company-info-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem 2.5rem;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-item label {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.info-item p {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    background-color: var(--background-primary);
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid var(--border-primary);
    min-height: 40px; /* Ensure consistent height */
    word-wrap: break-word;
}


/* Table Styles */
.table-container {
  overflow: auto;
  background-color: var(--background-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
  flex-grow: 1;
  min-height: 0;
}

/* Specific styling for AssetRecords to ensure horizontal scroll works */
.asset-records-table-container {
    overflow: auto; /* Allow both horizontal and vertical scrolling */
    background-color: var(--background-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-primary);
    flex-grow: 1;
    min-height: 0;
}

/* Specific styling for Asset Classification to ensure sticky headers work */
.asset-classification-table-container {
    max-height: 70vh; /* Ensure there's enough height to trigger scrolling */
    overflow-y: auto; /* Enable vertical scrolling */
}

.asset-classification-table-container th {
    position: sticky;
    top: 0;
    z-index: 15; /* Higher than regular headers */
    background-color: var(--background-tertiary);
}

.asset-records-table-container table {
  table-layout: auto;
  width: auto; /* Allow table to grow beyond container width */
}


table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed; 
}

.table-layout-auto {
    table-layout: auto;
}

th {
  background-color: var(--background-tertiary);
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 16px 15px;
  text-align: center;
  white-space: normal; /* Allow headers to wrap */
  vertical-align: middle;
  /* Make all table headers sticky by default */
  position: sticky;
  top: 0;
  z-index: 10;
}

td {
  padding: 8px 15px; /* Reduced padding for denser table */
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
  overflow-wrap: break-word;
  color: var(--text-primary);
  font-size: 0.9rem;
  vertical-align: middle;
  white-space: nowrap;
  height: 40px; /* Standardized row height for consistency */
  box-sizing: border-box;
}

/* Zebra & Checkerboard Striping */
tbody tr:nth-of-type(odd) > td {
  background-color: var(--background-stripe-row);
}
tbody tr > td:nth-of-type(odd) {
  background-color: var(--background-stripe-col);
}

tbody tr:hover td {
  background-color: var(--background-hover);
}

tbody tr:last-child td {
  border-bottom: none;
}

tfoot {
    font-weight: 700;
    background-color: var(--background-tertiary);
}

tfoot td {
    border-top: 2px solid var(--border-primary);
    color: var(--text-primary);
}


.text-right {
    text-align: right;
}

.text-right > input,
.text-right > select {
    text-align: right;
}

/* --- Selected Column Highlight --- */
/* Base style for a selected column's cells. */
th.selected-col, tbody td.selected-col {
    background-color: rgba(59, 130, 246, 0.15) !important;
}

/* Hover state for a selected column cell */
tbody tr:hover td.selected-col {
    background-color: rgba(59, 130, 246, 0.20) !important;
}

/*
  Intersection of selected row and column. This selector is more specific
  than the selected row style, so it will apply correctly.
*/
tbody tr.selected-row td.selected-col,
tbody tr.selected-row:hover td.selected-col {
    background-color: rgba(59, 130, 246, 0.35) !important;
}

/* Override for AssetRecords sticky columns which have specific background styles */
.asset-records-table-container th.sticky-col.selected-col,
.asset-records-table-container tbody td.sticky-col.selected-col {
    background-color: rgba(59, 130, 246, 0.15) !important;
}

.asset-records-table-container tbody tr:hover td.sticky-col.selected-col {
    background-color: rgba(59, 130, 246, 0.20) !important;
}

.asset-records-table-container tbody tr.selected-row td.sticky-col.selected-col {
    background-color: rgba(59, 130, 246, 0.35) !important;
}

.td-wrap {
  white-space: normal; /* Allow specific columns to wrap */
}

/* Clickable rows */
.clickable-row {
    cursor: pointer;
}

/* Column Widths */
.col-width-xs { min-width: 100px; }
.col-width-sm { min-width: 150px; }
.col-width-md { min-width: 200px; }
.col-width-lg { min-width: 250px; }
.col-width-xl { min-width: 300px; }


/* --- Sticky Column Styles for AssetRecords --- */
.asset-records-table-container .sticky-col {
    position: -webkit-sticky; /* For Safari */
    position: sticky;
    /* The background is now handled by the checkerboard pattern, hover, and selection styles */
}

.asset-records-table-container th.sticky-col {
    z-index: 25; /* Higher than sticky headers (15) and regular headers (10) */
    background-color: var(--background-tertiary) !important;
    opacity: 1 !important; /* Ensure completely opaque */
}

.asset-records-table-container td.sticky-col {
    z-index: 5;
    background-color: var(--background-primary) !important; /* Ensure solid background */
    opacity: 1 !important; /* Ensure completely opaque */
}

.asset-records-table-container .sticky-col-1 {
    left: 0;
}

.asset-records-table-container .sticky-col-2 {
    left: 150px; /* Width of col-1 (Record ID) */
}

.asset-records-table-container .sticky-col-3 {
    left: 400px; /* 150px (col-1) + 250px (col-2) */
}


/* Ensure hover color doesn't override sticky background */
.asset-records-table-container tbody tr:hover .sticky-col {
  background-color: var(--background-hover) !important;
  opacity: 1 !important; /* Ensure completely opaque on hover */
}
/* Ensure sticky header background is correct */
.asset-records-table-container tbody tr:hover th.sticky-col,
.asset-records-table-container th.sticky-col {
  background-color: var(--background-tertiary) !important;
  opacity: 1 !important; /* Ensure completely opaque */
}


.td-record-id {
    font-weight: 500;
    color: var(--text-secondary);
    background-color: var(--background-tertiary) !important;
    opacity: 1 !important; /* Ensure completely opaque */
}

.asset-records-table-container tbody tr:hover td.td-record-id.sticky-col {
    background-color: var(--background-tertiary) !important;
    opacity: 1 !important; /* Ensure completely opaque on hover */
}

/* --- Sticky Column Styles for AssetCalculations --- */
.asset-calculations-table-container .sticky-col {
    position: -webkit-sticky; /* For Safari */
    position: sticky;
    background-color: var(--background-primary);
}

.asset-calculations-table-container th.sticky-col {
    z-index: 25; /* Higher than sticky headers (15) and regular headers (10) */
    background-color: var(--background-tertiary) !important;
    opacity: 1 !important; /* Ensure completely opaque */
}

.asset-calculations-table-container td.sticky-col {
    z-index: 5;
    background-color: var(--background-primary) !important; /* Ensure solid background */
    opacity: 1 !important; /* Ensure completely opaque */
}

.asset-calculations-table-container .sticky-col-1 {
    left: 0 !important;
    width: 120px;
    min-width: 120px;
    max-width: 120px;
}

.asset-calculations-table-container .sticky-col-2 {
    left: 120px !important; /* Width of col-1 (Record ID) */
    width: 350px;
    min-width: 350px;
    max-width: 350px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.asset-calculations-table-container .sticky-col-3 {
    left: 470px !important; /* 120px (col-1) + 350px (col-2) */
    width: 120px;
    min-width: 120px;
    max-width: 120px;
}

/* Ensure hover color doesn't override sticky background */
.asset-calculations-table-container tbody tr:hover .sticky-col {
    background-color: var(--background-hover) !important;
    opacity: 1 !important; /* Ensure completely opaque on hover */
}

/* Ensure sticky headers have proper z-index hierarchy */
.asset-calculations-table-container th.sticky-col-1 {
    z-index: 30; /* Highest priority for first column */
}

.asset-calculations-table-container th.sticky-col-2 {
    z-index: 29; /* Second highest priority */
}

.asset-calculations-table-container th.sticky-col-3 {
    z-index: 28; /* Third highest priority */
}

/* Ensure sticky cells have proper z-index hierarchy */
.asset-calculations-table-container td.sticky-col-1 {
    z-index: 15; /* Highest priority for first column */
}

.asset-calculations-table-container td.sticky-col-2 {
    z-index: 14; /* Second highest priority */
}

.asset-calculations-table-container td.sticky-col-3 {
    z-index: 13; /* Third highest priority */
}

/* Disable zebra striping for Asset Calculations */
.asset-calculations-table-container tbody tr:nth-of-type(odd) > td {
    background-color: var(--background-primary);
}

.asset-calculations-table-container tbody tr > td:nth-of-type(odd) {
    background-color: var(--background-primary);
}

.asset-calculations-table-container tbody tr > td {
    background-color: var(--background-primary);
}

/* Welcome Modal Styles */
.welcome-modal {
    background: var(--background-primary);
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

.welcome-content {
    padding: 3rem 2.5rem;
    text-align: center;
}

.welcome-logo {
    width: 180px;
    height: auto;
    margin-bottom: 1.5rem;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-weight: 500;
}

.welcome-description {
    text-align: left;
    margin-bottom: 2rem;
}

.welcome-description p {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.welcome-features {
    background: var(--background-secondary);
    border-radius: 8px;
    padding: 1.5rem;
    border-left: 4px solid var(--accent-primary);
}

.welcome-features h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.welcome-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.welcome-features li {
    padding: 0.5rem 0;
    color: var(--text-primary);
    position: relative;
    padding-left: 1.5rem;
}

.welcome-features li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent-success);
    font-weight: bold;
}

.welcome-actions {
    margin-bottom: 1.5rem;
}

.welcome-btn {
    font-size: 1.1rem;
    padding: 0.75rem 2rem;
    min-width: 150px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.welcome-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.welcome-note {
    background: var(--background-info);
    border-radius: 6px;
    padding: 1rem;
    border-left: 4px solid var(--accent-info);
}

.welcome-note p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-primary);
    line-height: 1.5;
}

.welcome-note strong {
    color: var(--accent-info);
}

/* Welcome Screen Content */
.welcome-sidebar-content {
    padding: 2rem 1.5rem;
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

.welcome-main-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 80px); /* Adjust for header height */
    padding: 2rem;
}

.welcome-message {
    text-align: center;
    max-width: 600px;
}

.welcome-message h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-message p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Ensure sticky header background is correct */
.asset-calculations-table-container tbody tr:hover th.sticky-col,
.asset-calculations-table-container th.sticky-col {
    background-color: var(--background-tertiary) !important;
    opacity: 1 !important; /* Ensure completely opaque */
}

/* Special styling for Record ID column */
.asset-calculations-table-container .td-record-id {
    font-weight: 500;
    color: var(--text-secondary);
    background-color: var(--background-tertiary) !important;
    opacity: 1 !important; /* Ensure completely opaque */
}

.asset-calculations-table-container tbody tr:hover td.td-record-id.sticky-col {
    background-color: var(--background-tertiary) !important;
    opacity: 1 !important; /* Ensure completely opaque on hover */
}

/* --- Column Manager Styles --- */
.column-manager {
    position: relative;
    display: inline-block;
}

.column-manager-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.column-chooser-panel {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1000;
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 320px;
    max-width: 400px;
    max-height: 500px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.column-chooser-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--background-tertiary);
}

.column-chooser-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

.column-chooser-actions {
    display: flex;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--background-secondary);
}

.column-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 0.5rem 0;
}

.column-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    border-bottom: 1px solid var(--border-light);
}

.column-item:last-child {
    border-bottom: none;
}

.column-item:hover {
    background: var(--background-hover);
}

.column-visibility {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.column-label {
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.column-label.disabled {
    color: var(--text-muted);
    text-decoration: line-through;
}

.sticky-indicator {
    font-size: 0.7rem;
    opacity: 0.7;
}

.column-width-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.width-display {
    font-size: 0.8rem;
    color: var(--text-secondary);
    min-width: 45px;
    text-align: center;
}

.column-chooser-footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--border-color);
    background: var(--background-secondary);
    text-align: center;
}

.column-chooser-footer small {
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* Resizable column styles */
.resizable-column {
    position: relative;
}

.column-resizer {
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: transparent;
    cursor: col-resize;
    -webkit-user-select: none;
    user-select: none;
    z-index: 10;
}

.column-resizer:hover,
.column-resizer.resizing {
    background: var(--accent-primary);
}

.column-resizer::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 20px;
    background: var(--border-color);
    opacity: 0;
    transition: opacity 0.2s;
}

.column-resizer:hover::after,
.column-resizer.resizing::after {
    opacity: 1;
}

.table-input, .table-select {
    width: 100%;
    min-width: 150px; /* Give inputs a reasonable minimum width */
    padding: 0.5rem;
    background-color: var(--background-input);
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 0.9rem;
    font-family: var(--font-family);
    height: 32px; /* Standardized input height */
    box-sizing: border-box;
}

.table-input:focus, .table-select:focus {
    outline: 1px solid var(--accent-primary);
    border-color: var(--accent-primary);
}

.table-input:disabled, .table-select:disabled {
    background-color: var(--background-tertiary);
    color: var(--text-disabled);
    cursor: not-allowed;
}

.filter-container {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.report-filter-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    background-color: var(--background-tertiary);
    padding: 0.5rem 1rem;
    border-radius: 6px;
}

.report-filter-group label {
    font-weight: 500;
    color: var(--text-secondary);
}

.radio-group {
    display: flex;
    gap: 1rem;
}

.radio-group input[type="radio"] {
    display: none;
}

.radio-group label {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.radio-group input[type="radio"]:checked + label {
    background-color: var(--accent-primary);
    color: var(--text-on-accent);
    font-weight: 500;
}

.radio-group input[type="radio"]:not(:checked) + label:hover {
    background-color: var(--background-hover);
}


.filter-input-wrapper {
    position: relative;
    display: flex;
    flex-grow: 1;
    align-items: center;
}

.filter-input {
    flex-grow: 1;
    padding: 0.75rem;
    padding-right: 2.5rem; /* Make space for clear button */
    background-color: var(--background-input);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 1rem;
    font-family: var(--font-family);
}

.filter-clear-btn {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: opacity 0.2s, color 0.2s, background-color 0.2s;
    visibility: visible;
    opacity: 1;
}

.filter-clear-btn:hover {
    color: var(--text-primary);
    background-color: var(--background-tertiary);
}

.filter-clear-btn.hidden {
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
}

.filter-clear-btn svg {
    width: 16px;
    height: 16px;
}

/* Filter Info Tooltip */
.filter-info {
    position: relative;
    display: flex;
    align-items: center;
}

.filter-info-icon {
    cursor: pointer;
    color: var(--text-secondary);
    border: 1px solid var(--text-secondary);
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-style: normal;
    font-size: 0.9rem;
    -webkit-user-select: none;
    user-select: none;
    transition: color 0.2s, border-color 0.2s;
}

.filter-info-icon:hover {
    color: var(--text-primary);
    border-color: var(--text-primary);
}

.filter-info-tooltip {
    visibility: hidden;
    opacity: 0;
    position: absolute;
    bottom: 140%; 
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--background-tertiary);
    color: var(--text-primary);
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid var(--border-primary);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 100;
    width: max-content;
    transition: opacity 0.2s, visibility 0.2s;
    pointer-events: none;
}

.filter-info:hover .filter-info-tooltip {
    visibility: visible;
    opacity: 1;
}

.filter-info-tooltip strong {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    font-weight: 600;
}

.filter-info-tooltip ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.loading-indicator, .error-message {
  color: var(--text-secondary);
  font-size: 1.2rem;
  text-align: center;
  padding: 2rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  border: 1px solid transparent;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s, color 0.2s;
  font-family: var(--font-family);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn:disabled {
    background-color: var(--background-tertiary);
    color: var(--text-disabled);
    cursor: not-allowed;
    border-color: var(--border-primary);
}

.btn-primary {
  background-color: var(--accent-primary);
  color: var(--text-on-accent);
  border-color: var(--accent-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--accent-primary-hover);
  border-color: var(--accent-primary-hover);
}

.btn-secondary {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--background-hover);
  border-color: var(--border-secondary);
}

.btn-success {
  background-color: var(--accent-success);
  color: var(--text-on-accent);
  border-color: var(--accent-success);
}

.btn-success:hover:not(:disabled) {
  background-color: var(--accent-success-hover);
  border-color: var(--accent-success-hover);
}

.btn-warning {
  background-color: var(--accent-warning);
  color: var(--text-on-accent);
  border-color: var(--accent-warning);
}

.btn-warning:hover:not(:disabled) {
  background-color: var(--accent-warning-hover);
  border-color: var(--accent-warning-hover);
}

.btn-danger {
  background-color: var(--accent-danger);
  color: var(--text-on-accent);
  border-color: var(--accent-danger);
}

.btn-danger:hover:not(:disabled) {
  background-color: var(--accent-danger-hover);
  border-color: var(--accent-danger-hover);
}

.btn-excel {
  background-color: var(--accent-excel);
  color: var(--text-on-accent);
  border-color: var(--accent-excel);
}

.btn-excel:hover:not(:disabled) {
  background-color: var(--accent-excel-hover);
  border-color: var(--accent-excel-hover);
}

.btn-csv {
  background-color: var(--accent-csv);
  color: var(--text-on-accent);
  border-color: var(--accent-csv);
}

.btn-csv:hover:not(:disabled) {
  background-color: var(--accent-csv-hover);
  border-color: var(--accent-csv-hover);
}

.btn-info {
  background-color: var(--accent-info);
  color: var(--text-on-accent);
  border-color: var(--accent-info);
}
.btn-info:hover:not(:disabled) {
  background-color: var(--accent-info-hover);
  border-color: var(--accent-info-hover);
}

.btn-outline {
  background-color: transparent;
  color: var(--text-secondary);
  border-color: var(--border-primary);
}
.btn-outline:hover:not(:disabled) {
  background-color: var(--background-tertiary);
  border-color: var(--border-secondary);
  color: var(--text-primary);
}

.btn-logout {
  background-color: transparent;
  color: var(--accent-danger);
  border: 1px solid var(--accent-danger);
}

.btn-logout:hover:not(:disabled) {
  background-color: var(--accent-danger);
  color: var(--text-on-accent);
}

.required-asterisk {
    color: var(--accent-danger);
    margin-left: 0.25rem;
    font-weight: 700;
}

/* Row Action Buttons */
.action-buttons-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-icon {
    background: transparent;
    border: none;
    padding: 0.25rem;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s, color 0.2s;
}

.btn-icon:hover:not(:disabled) {
    background-color: var(--background-tertiary);
    color: var(--text-primary);
}

.btn-icon:disabled {
    color: var(--text-disabled);
    cursor: not-allowed;
}

.btn-icon.btn-revert:not(:disabled) {
    color: var(--accent-warning);
}

.btn-icon.btn-revert:hover:not(:disabled) {
    color: var(--accent-warning-hover);
    background-color: rgba(249, 115, 22, 0.1);
}

.btn-icon.btn-dispose:not(:disabled) {
    color: var(--accent-info);
}

.btn-icon.btn-dispose:hover:not(:disabled) {
    color: var(--accent-info-hover);
    background-color: rgba(99, 102, 241, 0.1);
}

.btn-icon.btn-delete:not(:disabled) {
    color: var(--accent-danger);
}

.btn-icon.btn-delete:hover:not(:disabled) {
    color: var(--accent-danger-hover);
    background-color: rgba(239, 68, 68, 0.1);
}

.btn-icon svg {
    width: 18px;
    height: 18px;
}

/* --- Calculation Modal Styles --- */
.clickable-cell {
    cursor: pointer;
    text-decoration: underline;
    text-decoration-style: dotted;
    text-underline-offset: 3px;
    transition: background-color 0.2s, color 0.2s;
}

.clickable-cell:hover {
    background-color: var(--accent-primary) !important;
    color: var(--text-on-accent) !important;
}

.extra-shift-depreciation {
    background-color: var(--extra-shift-highlight-bg);
    box-shadow: inset 2px 0 0 0 var(--extra-shift-highlight-border);
}

/* --- Extra Shift Days Table Specific Styles --- */
.extra-shift-days-table {
    table-layout: fixed;
    width: 100%;
}

.extra-shift-days-table th:nth-child(1) {
    width: 150px; /* Record ID */
}

.extra-shift-days-table th:nth-child(2) {
    width: auto; /* Asset Particulars - takes remaining space */
    min-width: 200px;
}

.extra-shift-days-table th:nth-child(3),
.extra-shift-days-table th:nth-child(4) {
    width: 120px; /* 2nd and 3rd Shift Days columns */
}

.extra-shift-days-table .table-input {
    min-width: 80px; /* Smaller min-width for shift day inputs */
    width: 100%;
    text-align: right;
}

.extra-shift-days-table td {
    padding: 0.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
}

.extra-shift-days-table td:nth-child(2) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}


.calc-modal {
    max-width: 600px;
    min-width: 500px;
    max-height: 85vh; /* Ensure modal doesn't exceed viewport height */
    overflow-y: auto; /* Allow scrolling within modal if content is too tall */
    margin: auto; /* Center the modal */
    position: relative;
    transform: none; /* Ensure no transform issues */
}

.calc-modal-body {
    padding: 1rem 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Ensure calculation modals are always properly positioned */
.calc-modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 10000 !important;
}

/* Prevent body scroll when modal is open */
body.modal-open {
    overflow: hidden;
}

/* Additional styling for calculation modal content */
.calc-modal .modal-header {
    position: sticky;
    top: 0;
    background-color: var(--background-secondary);
    z-index: 1;
    margin-bottom: 1.5rem;
}

/* --- Row Selection Highlighting --- */
.table-row-selectable {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.table-row-selected {
    background-color: var(--background-selected) !important;
    border-left: 3px solid var(--accent-primary);
}

.table-row-selected td {
    background-color: var(--background-selected) !important;
}

/* Ensure selected row is visible even with sticky columns */
.table-row-selected .sticky-col {
    background-color: var(--background-selected) !important;
    border-left: 3px solid var(--accent-primary);
}

/* Maintain hover effect alongside selection */
.table-row-selectable:hover:not(.table-row-selected) {
    background-color: var(--background-hover);
}

.table-row-selectable:hover:not(.table-row-selected) td {
    background-color: var(--background-hover);
}

.table-row-selectable:hover:not(.table-row-selected) .sticky-col {
    background-color: var(--background-hover) !important;
}

/* --- Scroll Anchoring Fix --- */
.main-content {
    position: relative;
    z-index: 1;
    min-width: 0; /* Prevent flex item from overflowing */
}

.table-container {
    position: relative;
    overflow-x: auto;
    overflow-y: auto;
    /* Ensure table container doesn't scroll under sidebar */
    margin-left: 0;
    padding-left: 0;
}

/* Fix for sticky columns not moving under sidebar */
.sticky-col {
    position: sticky;
    left: 0;
    z-index: 10;
    /* Ensure sticky columns stay above regular content */
    background-color: var(--background-primary);
    border-right: 1px solid var(--border-primary);
}

/* Second sticky column positioning */
.sticky-col-2 {
    position: sticky;
    left: var(--first-col-width, 150px); /* Default width, can be overridden */
    z-index: 9;
    background-color: var(--background-primary);
    border-right: 1px solid var(--border-primary);
}

/* Ensure sticky headers work with scroll anchoring */
.table-container thead th.sticky-col {
    position: sticky;
    top: 0;
    left: 0;
    z-index: 20;
    background-color: var(--background-secondary);
}

.table-container thead th.sticky-col-2 {
    position: sticky;
    top: 0;
    left: var(--first-col-width, 150px);
    z-index: 19;
    background-color: var(--background-secondary);
}

.calc-asset-name {
    font-size: 1.1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-primary);
}

.calc-section h4 {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.75rem;
}

.calc-formula {
    background-color: var(--background-primary);
    padding: 1rem;
    border-radius: 6px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 1rem; /* Slightly smaller for more text */
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    line-height: 1.6;
}

.calc-detail-list {
    background-color: var(--background-primary);
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.calc-detail-list p {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1rem;
    line-height: 1.4;
    font-family: var(--font-family);
}

.calc-detail-list p strong {
    font-weight: 600;
    font-family: var(--font-family);
    color: var(--text-primary);
}

.calc-note {
    background-color: var(--background-primary);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    padding: 1rem;
    margin-top: -0.5rem;
    font-style: italic;
    color: var(--text-secondary);
    text-align: center;
    font-size: 0.9rem;
}

.calc-section.result {
    margin-top: 1rem;
    text-align: center;
}

.calc-result {
    font-size: 1.5rem;
    font-weight: 700;
}

.calc-result strong {
    color: var(--accent-primary);
}

/* --- Recalculation Impact Modal Styles --- */
.recalc-impact-container {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0.5rem;
    margin: 0 -0.5rem; /* Counteract padding */
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.recalc-impact-asset-card {
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    background-color: var(--background-primary);
}

.recalc-impact-asset-card[open] {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.recalc-impact-asset-card summary {
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    list-style: none;
    color: var(--text-primary);
}
.recalc-impact-asset-card summary::-webkit-details-marker { display: none; }

.recalc-impact-summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.recalc-impact-record-id {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    font-family: 'Courier New', Courier, monospace;
}

.recalc-impact-asset-card .table-container {
    border-top: 1px solid var(--border-primary);
    border-left: none;
    border-right: none;
    border-bottom: none;
    border-radius: 0 0 7px 7px;
}
.recalc-impact-asset-card .table-container table {
    table-layout: fixed;
}


/* --- Theme Settings --- */
.settings-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.settings-description {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.theme-selector {
    display: flex;
    gap: 1rem;
    background-color: var(--background-primary);
    padding: 0.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-primary);
    width: fit-content;
}

.theme-btn {
    padding: 0.75rem 2rem;
    border: 1px solid transparent;
}

.theme-btn.active {
    background-color: var(--accent-primary);
    color: var(--text-on-accent);
    border-color: var(--accent-primary);
}

/* --- Sortable Table Headers --- */
th.sortable {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: relative;
}

th.sortable:hover {
    background-color: var(--background-hover);
}

.sort-indicator {
    display: inline-block;
    vertical-align: middle;
    margin-left: 0.25rem;
    opacity: 0.5;
    width: 1em;
    height: 1em;
    line-height: 1;
    font-size: 0.8em;
}

th.sortable-active .sort-indicator {
    opacity: 1;
}

/* --- Selected Row Highlight --- */
tbody tr.selected-row td,
tbody tr.selected-row:hover td {
  background-color: var(--background-selected) !important;
}

tbody tr.selected-row td.sticky-col,
tbody tr.selected-row:hover td.sticky-col {
    background-color: var(--background-selected) !important;
}

tbody tr.selected-row td.td-record-id.sticky-col {
    background-color: var(--background-selected) !important;
}

/* Highlight for disposed assets */
.disposed-row > td {
    opacity: 0.65;
    text-decoration: line-through;
    text-decoration-color: var(--accent-danger);
    text-decoration-thickness: 1.5px;
}
.disposed-row > td span {
    text-decoration: line-through;
    text-decoration-color: var(--accent-danger);
    text-decoration-thickness: 1.5px;
}

/* Highlight for scrappable assets - BACKGROUND ONLY */
.scrapped-row td {
    background-color: rgba(249, 115, 22, 0.15) !important;
}
tbody tr.scrapped-row:hover td {
    background-color: rgba(249, 115, 22, 0.25) !important;
}

/* Highlight for end of life assets - BACKGROUND ONLY */
.end-of-life-row td {
    background-color: rgba(168, 85, 247, 0.15) !important;
}
tbody tr.end-of-life-row:hover td {
    background-color: rgba(168, 85, 247, 0.25) !important;
}


/* --- USER MANUAL STYLES --- */
.manual-container {
    flex: 1 1 auto;
    overflow-y: auto;
    padding-right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.manual-section details {
    background-color: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    transition: background-color 0.2s;
}

.manual-section details[open] {
    background-color: var(--background-tertiary);
}

.manual-section summary {
    font-size: 1.2rem;
    font-weight: 600;
    padding: 1rem 1.5rem;
    cursor: pointer;
    list-style: none; /* Remove default marker */
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--text-primary);
}

.manual-section summary::-webkit-details-marker {
    display: none; /* Hide for Chrome */
}

.manual-section summary::after {
    content: '▼';
    font-size: 0.8rem;
    transition: transform 0.2s ease;
    color: var(--text-secondary);
}

.manual-section details[open] summary::after {
    transform: rotate(-180deg);
}

.manual-content {
    padding: 0 1.5rem 1.5rem 1.5rem;
    border-top: 1px solid var(--border-primary);
    line-height: 1.7;
    color: var(--text-secondary);
}

.manual-content p {
    margin-bottom: 1rem;
}

.manual-content p strong {
    color: var(--text-primary);
    font-weight: 600;
}

.manual-content code {
    background-color: var(--background-primary);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9rem;
    color: var(--accent-warning);
    border: 1px solid var(--border-primary);
}

.manual-list {
    list-style-position: inside;
    padding-left: 0.5rem;
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.manual-list li::marker {
    font-weight: 700;
    color: var(--accent-primary);
}

/* InfoBox Component Styles */
.manual-infobox {
    margin: 1.5rem 0;
    padding: 1.25rem;
    border-radius: 6px;
    border-left-width: 4px;
    border-left-style: solid;
}

.manual-infobox-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

/* --- LEASEHOLD ASSETS REPORT STYLES --- */
.lease-status.active {
    color: var(--accent-success);
    font-weight: 600;
}

.lease-status.expired {
    color: var(--accent-danger);
    font-weight: 600;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background-color: var(--accent-success-light);
    color: var(--accent-success);
}

.status-badge.expired {
    background-color: var(--accent-danger-light);
    color: var(--accent-danger);
}

/* --- COMPULSORY FIELD HIGHLIGHTING --- */
.compulsory-field-empty {
    background-color: #fff3cd !important;
    border: 2px solid #ffc107 !important;
}

.compulsory-field-empty input,
.compulsory-field-empty select {
    background-color: #fff3cd;
    border-color: #ffc107;
}

.compulsory-field-empty input:focus,
.compulsory-field-empty select:focus {
    border-color: #ff8c00;
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.25);
}

/* Required asterisk styling */
.required-asterisk {
    color: #dc3545;
    font-weight: bold;
    margin-left: 3px;
}

.missing-value {
    color: #6c757d;
    font-style: italic;
}