# Schedule III Testing Data Guide

## Overview
This guide provides instructions for adding previous years data to test the Schedule III carry-forward functionality in FAR Sighted.

## Manual Data Entry for Testing

### Historical Assets to Add

#### 1. Manufacturing Plant - Line 1 (HIST001)
- **Asset Particulars**: Manufacturing Plant - Line 1
- **Book Entry Date**: 15/05/2020
- **Put to Use Date**: 01/06/2020
- **Basic Amount**: ₹50,00,000
- **Duties/Taxes**: ₹5,00,000
- **Gross Amount**: ₹55,00,000 (auto-calculated)
- **WDV of Adoption Date**: ₹42,00,000 (required for pre-adoption assets)
- **Vendor**: Industrial Equipment Ltd
- **Invoice No**: INV-2020-001
- **Location**: Factory Floor A
- **Asset ID**: PLT-001
- **Ledger Name**: Plant & Machinery
- **Asset Group**: Plant & Machinery
- **Asset Sub-Group**: Manufacturing Equipment
- **Schedule III Classification**: Plant and machinery
- **Depreciation Method**: WDV
- **Life in Years**: 15
- **Salvage %**: 5%

#### 2. Office Building - Main Block (HIST002)
- **Asset Particulars**: Office Building - Main Block
- **Book Entry Date**: 20/03/2019
- **Put to Use Date**: 01/04/2019
- **Basic Amount**: ₹1,50,00,000
- **Duties/Taxes**: ₹15,00,000
- **Gross Amount**: ₹1,65,00,000
- **WDV of Adoption Date**: ₹1,42,00,000
- **Vendor**: Real Estate Developers
- **Invoice No**: SALE-2019-005
- **Location**: Corporate Office
- **Asset ID**: BLDG-001
- **Ledger Name**: Buildings
- **Asset Group**: Buildings
- **Asset Sub-Group**: Factory buildings
- **Schedule III Classification**: Buildings
- **Depreciation Method**: SLM
- **Life in Years**: 30
- **Salvage %**: 5%

#### 3. Computer Server - Data Center (HIST003) - DISPOSED
- **Asset Particulars**: Computer Server - Data Center
- **Book Entry Date**: 10/08/2021
- **Put to Use Date**: 01/09/2021
- **Basic Amount**: ₹8,00,000
- **Duties/Taxes**: ₹1,44,000
- **Gross Amount**: ₹9,44,000
- **WDV of Adoption Date**: ₹7,50,000
- **Vendor**: Tech Solutions Inc
- **Invoice No**: INV-2021-089
- **Location**: Data Center
- **Asset ID**: SRV-001
- **Ledger Name**: Computer Equipment
- **Asset Group**: Office equipment
- **Asset Sub-Group**: Computers
- **Schedule III Classification**: Office equipment
- **Depreciation Method**: WDV
- **Life in Years**: 6
- **Salvage %**: 10%
- **Disposal Date**: 31/12/2023
- **Disposal Amount**: ₹2,00,000

#### 4. Delivery Vehicle - Truck (HIST004)
- **Asset Particulars**: Delivery Vehicle - Truck
- **Book Entry Date**: 15/01/2022
- **Put to Use Date**: 01/02/2022
- **Basic Amount**: ₹12,00,000
- **Duties/Taxes**: ₹1,80,000
- **Gross Amount**: ₹13,80,000
- **WDV of Adoption Date**: Leave blank (purchased after adoption)
- **Vendor**: Commercial Vehicles Ltd
- **Invoice No**: VEH-2022-003
- **Location**: Transport Yard
- **Asset ID**: VEH-001
- **Ledger Name**: Motor Vehicles
- **Asset Group**: Motor vehicles
- **Asset Sub-Group**: Motor cars
- **Schedule III Classification**: Motor vehicles
- **Depreciation Method**: WDV
- **Life in Years**: 8
- **Salvage %**: 5%

### Extra Shift Days (for HIST001 only)
Add extra shift days for the Manufacturing Plant:
- **2022-23**: 2nd Shift Days: 120, 3rd Shift Days: 60
- **2023-24**: 2nd Shift Days: 120, 3rd Shift Days: 60

## Testing Scenarios Covered

### 1. Pre-Adoption Assets
- HIST001, HIST002, HIST003 were purchased before the adoption date
- These assets require WDV of Adoption Date to be filled
- Tests carry-forward from previous system

### 2. Post-Adoption Assets
- HIST004 was purchased after adoption date
- WDV of Adoption Date should be left blank
- Tests normal asset addition flow

### 3. Depreciation Methods
- **WDV Method**: HIST001, HIST003, HIST004
- **SLM Method**: HIST002
- Tests both depreciation calculation methods

### 4. Asset Disposal
- HIST003 is disposed on 31/12/2023
- Tests disposal calculations and gain/loss
- Tests removal from active assets in subsequent years

### 5. Extra Shift Depreciation
- HIST001 has extra shift days
- Tests additional depreciation calculations
- Tests plant & machinery specific features

### 6. Different Asset Categories
- **Plant & Machinery**: Manufacturing equipment
- **Buildings**: Office building
- **Office Equipment**: Computer server
- **Motor Vehicles**: Delivery truck
- Tests Schedule III grouping and classification

## Expected Schedule III Results

### Opening Balance (2024-25)
The Schedule III report should show:
- **Plant & Machinery**: Opening WDV from HIST001 + HIST004
- **Buildings**: Opening WDV from HIST002
- **Office Equipment**: Should be zero (HIST003 disposed)
- **Motor Vehicles**: Opening WDV from HIST004

### Depreciation Calculations
- Each asset should show proper depreciation based on method
- Extra shift depreciation should be included for HIST001
- Disposal year should show depreciation up to disposal date

### Carry-Forward Testing
- Previous years' closing WDV should match next year's opening WDV
- Disposed assets should not appear in subsequent years
- Depreciation should be cumulative and accurate

## Manual Entry Steps

1. **Login to FAR Sighted** as Admin user
2. **Select Company** to add test data to
3. **Go to Asset Records** page
4. **Add each historical asset** using the data above
5. **Add Extra Shift Days** for HIST001
6. **Create Next Financial Year** to generate calculations
7. **Test Schedule III Report** to verify carry-forward

## Verification Checklist

- [ ] All 4 historical assets added successfully
- [ ] WDV of Adoption Date filled for pre-adoption assets
- [ ] Extra shift days added for manufacturing plant
- [ ] Asset disposal recorded for HIST003
- [ ] Schedule III shows proper opening balances
- [ ] Depreciation calculations are accurate
- [ ] Carry-forward between years is correct
- [ ] Disposed assets don't appear in subsequent years

## Troubleshooting

### Common Issues
1. **WDV of Adoption Date field disabled**: Asset was purchased after adoption date
2. **Validation errors**: Check date logic (Put to Use >= Book Entry)
3. **Missing depreciation**: Ensure asset group/sub-group is selected
4. **Schedule III empty**: Run "Create Next FY" to generate calculations

### Data Verification
- Use Asset Calculations page to verify depreciation amounts
- Double-click cells to see calculation details
- Export reports to Excel for detailed analysis
- Check audit trail for all data entry activities

---
**Created**: 2025-07-11
**Purpose**: Schedule III carry-forward functionality testing
**Status**: Ready for manual implementation
