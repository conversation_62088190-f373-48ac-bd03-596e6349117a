@echo off
setlocal enabledelayedexpansion

echo =====================================================
echo FAR SIGHTED - PORT 8090 CONFLICT RESOLVER
echo Professional Advisory Services - CA
echo =====================================================
echo.

echo ❌ ERROR: Port 8090 is already in use
echo    This prevents the backend from starting
echo.

echo 🔍 Finding what's using port 8090...
echo.

REM Find processes using port 8090
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":8090" ^| find "LISTENING"') do (
    set "pid=%%a"
    echo    📊 Port 8090 is used by Process ID: !pid!
    
    REM Get process name
    for /f "tokens=1" %%b in ('tasklist /fi "pid eq !pid!" /fo csv /nh 2^>nul ^| find /v "INFO:"') do (
        set "processname=%%b"
        set "processname=!processname:"=!"
        echo    📂 Process Name: !processname!
    )
    
    echo.
    echo 🎯 Options:
    echo    1. Kill this process and start fresh
    echo    2. Check if it's already our FAR backend
    echo    3. Find alternative port
    echo.
    
    choice /c 123 /m "Select option"
    set "choice_result=!errorlevel!"
    
    if !choice_result!==1 (
        echo.
        echo ⚠️  Killing process !pid! (!processname!)...
        taskkill /pid !pid! /f >nul 2>&1
        if !errorlevel! == 0 (
            echo    ✅ Process killed successfully
            goto :start_backend
        ) else (
            echo    ❌ Failed to kill process (may require admin rights)
            echo    💡 Try running this script as Administrator
            pause
            exit /b 1
        )
    ) else if !choice_result!==2 (
        echo.
        echo 🔍 Testing if this is our FAR backend...
        curl -s http://localhost:8090/api/health >nul 2>&1
        if !errorlevel! == 0 (
            echo    ✅ This IS our FAR backend - it's already running!
            echo    🎯 No need to start it again
            goto :test_api
        ) else (
            echo    ❌ This is NOT our FAR backend
            echo    💡 This process needs to be stopped
            choice /c YN /m "Kill this process (Y/N)?"
            if !errorlevel! == 1 (
                taskkill /pid !pid! /f >nul 2>&1
                echo    ✅ Process killed
                goto :start_backend
            ) else (
                echo    ❌ Cannot continue with port conflict
                pause
                exit /b 1
            )
        )
    ) else (
        echo.
        echo 🔧 Alternative: Use different port (8091)
        echo    This requires code changes - not recommended
        echo    💡 Better to resolve the port conflict
        pause
        exit /b 1
    )
)

echo ❌ No process found using port 8090
echo    This is unexpected - the error suggests port is in use
echo.
echo 💡 Possible causes:
echo    • Port may have just been freed
echo    • Timing issue with netstat
echo    • Permission issue
echo.
echo 🚀 Trying to start backend anyway...

:start_backend
echo.
echo ✅ Port 8090 should now be free
echo.
echo 🚀 Starting FAR Sighted Backend...
cd /d "E:\Projects\FAR Sighted\backend"

if not exist "package.json" (
    echo ❌ Backend package.json not found
    echo    Current directory: %cd%
    pause
    exit /b 1
)

echo    📡 Attempting to start on port 8090...
start "FAR Backend Clean" cmd /k "title FAR Backend Clean && echo ======================================= && echo FAR SIGHTED BACKEND (Clean Start) && echo ======================================= && echo. && npm start"

echo.
echo ⏳ Waiting for backend to start (10 seconds)...
timeout /t 10 /nobreak >nul

:test_api
echo.
echo 🔍 Testing backend status...
curl -s http://localhost:8090/api/health >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Backend is now running successfully!
    echo.
    echo 🏢 Testing companies API...
    curl -s -w "Response Code: %%{http_code}\n" http://localhost:8090/api/companies
    echo.
    echo 📊 Full companies response:
    curl -s http://localhost:8090/api/companies
    echo.
    
    echo ✅ Backend is working! Now you can:
    echo    1. Start frontend: npm run dev
    echo    2. Open browser: http://localhost:9090
    echo    3. Check dropdown should now show companies
    
) else (
    echo    ❌ Backend still not responding
    echo    💡 Check the "FAR Backend Clean" window for error messages
    echo.
    echo 🔧 Additional troubleshooting:
    echo    • Verify Node.js is installed: node --version
    echo    • Check for missing dependencies: npm install
    echo    • Review backend logs in the server window
)

echo.
echo 🎯 Next Steps:
echo    1. If backend is working, start frontend: npm run dev
echo    2. Open http://localhost:9090
echo    3. Company dropdown should now populate
echo.

cd /d "E:\Projects\FAR Sighted"
echo 💡 Want to start frontend now?
choice /c YN /m "Start frontend automatically (Y/N)?"
if %errorlevel% == 1 (
    echo.
    echo 🖥️  Starting frontend...
    start "FAR Frontend Clean" cmd /k "title FAR Frontend Clean && npm run dev"
    echo.
    echo ⏳ Waiting for frontend (8 seconds)...
    timeout /t 8 /nobreak >nul
    echo.
    echo 🌐 Opening application...
    start http://localhost:9090
)

echo.
echo ✅ Port conflict resolved!
echo    Backend: http://localhost:8090
echo    Frontend: http://localhost:9090
echo.

pause
