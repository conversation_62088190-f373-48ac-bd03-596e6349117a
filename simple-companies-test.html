<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Companies Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Simple Companies Test</h1>
        <p>This is the simplest possible test to fetch companies from the API.</p>
        
        <div id="status" class="result">
            ⏳ Click the button to test...
        </div>
        
        <button onclick="testSimpleFetch()">Test Simple Fetch</button>
        
        <div id="results"></div>
    </div>

    <script>
        function updateStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `result ${type}`;
        }
        
        function showResults(data) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <h3>📊 Results:</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        }
        
        async function testSimpleFetch() {
            updateStatus('🔍 Making simple fetch request...', 'success');
            
            try {
                console.log('Starting simple fetch test');
                
                const response = await fetch('http://localhost:8090/api/companies');
                
                console.log('Response received:', response.status, response.statusText);
                
                if (response.ok) {
                    const companies = await response.json();
                    console.log('Companies:', companies);
                    
                    updateStatus(`✅ Success! Found ${companies.length} companies`, 'success');
                    showResults(companies);
                } else {
                    const errorText = await response.text();
                    console.error('Error response:', errorText);
                    
                    updateStatus(`❌ Failed with status: ${response.status}`, 'error');
                    showResults({ status: response.status, error: errorText });
                }
                
            } catch (error) {
                console.error('Request failed:', error);
                updateStatus(`❌ Request failed: ${error.message}`, 'error');
                showResults({ error: error.message, stack: error.stack });
            }
        }
    </script>
</body>
</html>
