@echo off
echo =============================================
echo FAR SIGHTED - CREATE TEST COMPANY
echo Professional Advisory Services - CA
echo =============================================
echo.

echo 🏢 Creating a test company to populate the dropdown...
echo.

echo 📡 Testing if backend can create companies...
curl -X POST http://localhost:8090/api/companies ^
-H "Content-Type: application/json" ^
-d "{\"companyName\":\"Test Company for Dropdown\",\"financialYearStart\":\"2024-04-01\",\"financialYearEnd\":\"2025-03-31\",\"firstDateOfAdoption\":\"2024-04-01\",\"pan\":\"TESTPAN123\",\"addressLine1\":\"Test Address Mumbai\",\"city\":\"Mumbai\",\"pin\":\"400001\",\"email\":\"<EMAIL>\",\"mobile\":\"9876543210\"}"

echo.
echo.
echo ✅ Test company creation attempted.
echo.

echo 🔍 Now testing if companies API shows the new company...
curl -s http://localhost:8090/api/companies
echo.

echo.
echo 💡 If you see the test company above, then:
echo    ✅ Backend is working correctly
echo    ❌ Issue is in frontend - check browser console (F12)
echo.
echo 💡 If you see error or empty response:
echo    ❌ Backend issue - check migration status
echo.

pause
