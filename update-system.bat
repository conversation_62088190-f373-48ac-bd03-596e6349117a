@echo off
setlocal enabledelayedexpansion

echo ==========================================
echo FAR SIGHTED - COMPLETE SYSTEM UPDATE
echo Multi-Database Architecture Implementation
echo Professional Advisory Services - CA
echo ==========================================
echo.

echo 🔄 Updating FAR Sighted to Multi-Database Architecture...
echo.

REM Navigate to project directory
cd /d "E:\Projects\FAR Sighted"

REM Verify we're in correct directory
if not exist "package.json" (
    echo ❌ Error: package.json not found in current directory
    echo    Current directory: %cd%
    echo    Please ensure you're in the correct project directory
    pause
    exit /b 1
)

echo 📋 System Update Checklist:
echo    ✅ New database architecture implemented
echo    ✅ Migration scripts created
echo    ✅ API endpoints updated
echo    ✅ Multi-database support added
echo    ✅ Backend configuration updated
echo    ✅ Frontend API client updated
echo    ✅ Batch scripts created
echo.

echo 🎯 Update Summary:
echo.
echo 📊 Architecture Changes:
echo    • Single Database → Multi-Database (Separate per company)
echo    • Enhanced data isolation and security
echo    • Company-specific audit trails
echo    • Granular backup/restore capabilities
echo    • Improved scalability
echo.
echo 🛠️  Technical Updates:
echo    • Backend: Updated to Multi-Database structure
echo    • Frontend: API client updated for new backend
echo    • Database: Migration system implemented
echo    • Scripts: New batch files for management
echo    • Configuration: Updated ports and endpoints
echo.

echo 🔍 Pre-Update Verification:
echo.

REM Check if backend directory exists
if not exist "backend\" (
    echo ❌ Backend directory not found
    pause
    exit /b 1
) else (
    echo    ✅ Backend directory found
)

REM Check if new files exist
if exist "backend\services\database-new.js" (
    echo    ✅ New database service found
) else (
    echo    ❌ New database service not found
    echo    Please ensure the multi-database implementation files are in place
    pause
    exit /b 1
)

if exist "backend\routes\companies-new.js" (
    echo    ✅ New route files found
) else (
    echo    ❌ New route files not found
    pause
    exit /b 1
)

echo.
echo ⚠️  IMPORTANT: Before proceeding, ensure:
echo    1. You have backed up your current database
echo    2. No critical work is in progress
echo    3. You understand this will update the system architecture
echo.

choice /c YN /m "Proceed with system update (Y/N)?"
if errorlevel 2 (
    echo    ❌ Update cancelled by user
    pause
    exit /b 1
)

echo.
echo 🚀 Starting System Update Process...
echo.

REM Step 1: Kill all running processes
echo 📛 Step 1: Stopping all running processes...
call kill-all-processes.bat

echo.
echo 📦 Step 2: Updating backend dependencies...
cd backend
if exist "package-new.json" (
    echo    ✅ Updating package.json with new configuration...
    copy package-new.json package.json >nul
    echo    ✅ Package configuration updated
) else (
    echo    ⚠️  package-new.json not found, using existing configuration
)

echo    📦 Installing/updating dependencies...
call npm install
if %errorlevel% neq 0 (
    echo    ❌ Failed to install backend dependencies
    pause
    exit /b 1
)

cd ..

echo.
echo 🖥️  Step 3: Updating frontend dependencies...
echo    📦 Installing/updating frontend dependencies...
call npm install
if %errorlevel% neq 0 (
    echo    ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)

echo.
echo 🔄 Step 4: Database Migration Check...
cd backend

echo    🔍 Checking migration status...
node -e "
const fs = require('fs');
if (fs.existsSync('database/far_sighted.db')) {
    console.log('✅ Old database found - migration will be needed');
} else {
    console.log('ℹ️  No old database found - fresh installation');
}
"

echo.
choice /c YN /m "Run database migration now (Y/N)?"
if errorlevel 1 (
    echo    🔄 Running database migration...
    npm run migrate
    if %errorlevel% == 0 (
        echo    ✅ Migration completed successfully
    ) else (
        echo    ❌ Migration failed - check error messages above
        echo    You can run migration later with: migrate-database.bat
    )
) else (
    echo    ⏭️  Migration skipped - you can run it later with: migrate-database.bat
)

cd ..

echo.
echo 🎯 Step 5: System Verification...

echo    🔍 Verifying new file structure...
if exist "backend\services\database-manager\" (
    echo    ✅ Database manager directory exists
) else (
    echo    ❌ Database manager directory missing
)

if exist "backend\routes\companies-new.js" (
    echo    ✅ Updated route files exist
) else (
    echo    ❌ Updated route files missing
)

if exist "backend\scripts\migrate-database.js" (
    echo    ✅ Migration script exists
) else (
    echo    ❌ Migration script missing
)

if exist "kill-all-processes.bat" (
    echo    ✅ Process management scripts exist
) else (
    echo    ❌ Process management scripts missing
)

echo.
echo 🚀 Step 6: Starting Updated System...
echo.
echo    🎨 Starting frontend on port 9090...
start "FAR Frontend v2.0" cmd /k "title FAR Frontend v2.0 (Multi-DB) && echo Starting FAR Sighted Frontend v2.0... && npm run dev"

timeout /t 3 /nobreak >nul

echo    📡 Starting backend on port 3001...
cd backend
start "FAR Backend v2.0" cmd /k "title FAR Backend v2.0 (Multi-DB) && echo Starting FAR Sighted Backend v2.0... && npm start"

cd ..

echo.
echo ⏳ Waiting for services to initialize...
timeout /t 10 /nobreak >nul

echo.
echo 🔍 System Health Check...
curl -s http://localhost:3001/api/health >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Backend health check passed
) else (
    echo    ⚠️  Backend may still be starting...
)

echo.
echo 🌐 Opening application in browser...
start http://localhost:9090

echo.
echo ================================================
echo ✅ FAR SIGHTED SYSTEM UPDATE COMPLETE!
echo ================================================
echo.
echo 📊 System Information:
echo    • Version: 2.0.0 (Multi-Database Architecture)
echo    • Frontend: http://localhost:9090
echo    • Backend: http://localhost:3001/api
echo    • Health: http://localhost:3001/api/health
echo    • Migration: http://localhost:3001/api/migration-status
echo.
echo 🏗️  New Architecture Features:
echo    ✅ Separate database per company
echo    ✅ Enhanced data isolation
echo    ✅ Company-specific audit trails
echo    ✅ Granular backup/restore
echo    ✅ Improved scalability
echo    ✅ Professional compliance standards
echo.
echo 🎯 Available Management Scripts:
echo    • restart-far-app.bat - Restart the application
echo    • start-far-app.bat - Start without killing processes
echo    • kill-all-processes.bat - Stop all processes
echo    • migrate-database.bat - Run database migration
echo    • update-system.bat - This update script
echo.
echo 💡 Next Steps:
echo    1. Test login functionality
echo    2. Verify company data access
echo    3. Run test reports
echo    4. Check migration status if needed
echo    5. Create backups of new structure
echo.
echo 📚 Documentation:
echo    • MULTI_DATABASE_IMPLEMENTATION.md
echo    • MIGRATION_DEPLOYMENT_GUIDE.md
echo    • IMPLEMENTATION_STATUS.md
echo.

echo ✨ FAR Sighted is now running with Professional Multi-Database Architecture!
echo    Press any key to close this window...
pause >nul