@echo off
echo =============================================
echo FAR SIGHTED - QUICK PORT CONFLICT FIX
echo Professional Advisory Services - CA
echo =============================================
echo.

echo 🔧 QUICK FIX: Killing all Node.js processes and starting fresh
echo.

echo 📡 Step 1: Killing all Node.js processes...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im npm.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo.
echo 🔍 Step 2: Verifying port 8090 is free...
netstat -an | find ":8090" | find "LISTENING" >nul 2>&1
if %errorlevel% == 0 (
    echo    ⚠️  Port 8090 still in use - finding and killing process...
    for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":8090" ^| find "LISTENING"') do (
        echo    🎯 Killing PID: %%a
        taskkill /pid %%a /f >nul 2>&1
    )
    timeout /t 2 /nobreak >nul
) else (
    echo    ✅ Port 8090 is now free
)

echo.
echo 🚀 Step 3: Starting backend on port 8090...
cd /d "E:\Projects\FAR Sighted\backend"
start "FAR Backend" cmd /k "title FAR Backend && echo Starting backend server... && npm start"

echo.
echo ⏳ Step 4: Waiting for backend to initialize (12 seconds)...
timeout /t 12 /nobreak >nul

echo.
echo 🔍 Step 5: Testing backend...
curl -s http://localhost:8090/api/health >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Backend is running!
    
    echo.
    echo 🏢 Step 6: Testing companies API...
    curl -s http://localhost:8090/api/companies
    echo.
    
    echo ✅ Backend working! Starting frontend...
    cd /d "E:\Projects\FAR Sighted"
    start "FAR Frontend" cmd /k "title FAR Frontend && npm run dev"
    
    echo.
    echo ⏳ Waiting for frontend (8 seconds)...
    timeout /t 8 /nobreak >nul
    
    echo.
    echo 🌐 Opening application...
    start http://localhost:9090
    
    echo.
    echo ✅ DONE! Company dropdown should now work!
    
) else (
    echo    ❌ Backend failed to start
    echo    💡 Check the "FAR Backend" window for errors
    echo    💡 May need to run migration: cd backend && npm run migrate
)

echo.
pause
