# FAR Sighted API Reference Guide

## 🔗 Base URL
- **Development:** `http://localhost:3001/api`
- **Production:** `https://your-domain.com/api`

## 🏢 Company Management APIs

### List Companies
```http
GET /companies
Response: Array of {id, name}
```

### Get Company Details
```http
GET /companies/:id
Response: Complete company data with assets, financial years, etc.
```

### Create Company
```http
POST /companies
Body: CompanyInfo object
Response: {id, name}
```

### Update Company
```http
PUT /companies/:id
Body: CompanyInfo object
Response: Success message
```

### Activate License
```http
POST /companies/:id/activate-license
Body: {licenseData: {key, validFrom, validUpto}}
Response: Updated company info
```

### Update Statutory Rates
```http
PUT /companies/:id/statutory-rates
Body: {rates: Array of StatutoryRate}
Response: Success message
```

### Update Extra Ledgers
```http
PUT /companies/:id/extra-ledgers
Body: {ledgers: Array of strings}
Response: Success message
```

### Create Financial Year
```http
POST /companies/:id/financial-years
Body: {currentYear: "2023-2024"}
Response: {yearRange: "2024-2025"}
```

## 📊 Asset Management APIs

### Get Company Assets
```http
GET /assets/company/:companyId
Response: Array of Asset objects with yearly data
```

### Update Assets
```http
PUT /assets/company/:companyId
Body: {assets: Array of Asset objects}
Response: Updated assets array
```

### Import Assets
```http
POST /assets/company/:companyId/import
Body: {assets: Array of new Asset objects}
Response: Success message with count
```

## 👥 User Management APIs

### List Users
```http
GET /users
Response: Array of User objects (without sensitive data)
```

### Get User Details
```http
GET /users/:id
Response: User object (without sensitive data)
```

### User Login
```http
POST /users/login
Body: {username, password}
Response: {user: User, showAdminWelcome: boolean}
```

### Create User
```http
POST /users
Body: {username, password, role}
Response: {user: User, recoveryKey: string}
```

### Update User
```http
PUT /users/:id
Body: {password?, role?}
Response: {recoveryKey: string | null}
```

### Delete User
```http
DELETE /users/:id
Response: Success message
```

### Verify Password
```http
POST /users/:id/verify-password
Body: {password}
Response: boolean
```

### Password Recovery
```http
POST /users/:username/recover
Body: {recoveryKey, newPassword}
Response: {recoveryKey: string}
```

### Confirm Recovery Key Saved
```http
POST /users/:id/confirm-recovery-key
Response: Success message
```

## ⚙️ Settings & System APIs

### Get Settings
```http
GET /settings
Response: AppSettings object
```

### Update Settings
```http
PUT /settings
Body: AppSettings object
Response: Success message
```

### Get Audit Logs
```http
GET /audit
Response: Array of AuditLogEntry objects
```

### Add Audit Log
```http
POST /audit
Body: {userId, username, action, details}
Response: Success message
```

### Get Backup Logs
```http
GET /backup/logs
Response: Array of BackupLogEntry objects
```

### Create Auto Backup
```http
POST /backup/auto
Response: {backupName: string}
```

### Export Database
```http
GET /backup/export
Response: Complete database JSON
```

### Import Database
```http
POST /backup/import
Body: Database JSON string
Response: Success message
```

### Health Check
```http
GET /health
Response: {status: "ok", message, timestamp, version}
```

## 📋 Data Models

### CompanyInfo
```typescript
{
  companyName: string
  pan?: string
  cin?: string
  dateOfIncorporation?: string
  financialYearStart: string
  financialYearEnd: string
  firstDateOfAdoption: string
  dataFolderPath?: string
  addressLine1?: string
  addressLine2?: string
  city?: string
  pin?: string
  email?: string
  mobile?: string
  contactPerson?: string
  licenseValidUpto?: string
}
```

### Asset
```typescript
{
  recordId: string
  assetParticulars: string
  bookEntryDate?: string
  putToUseDate?: string
  basicAmount: number
  dutiesTaxes: number
  grossAmount: number
  vendor?: string
  invoiceNo?: string
  modelMake?: string
  location?: string
  assetId?: string
  remarks?: string
  ledgerNameInBooks?: string
  assetGroup?: string
  assetSubGroup?: string
  scheduleIIIClassification?: string
  disposalDate?: string
  disposalAmount?: number
  salvagePercentage: number
  wdvOfAdoptionDate?: number
  isLeasehold: boolean
  depreciationMethod: 'SLM' | 'WDV'
  lifeInYears: number
  leasePeriod?: number
  scrapIt: boolean
}
```

### User
```typescript
{
  id: string
  username: string
  role: 'Admin' | 'Data Entry' | 'Report Viewer'
  hasSavedRecoveryKey: boolean
}
```

### StatutoryRate
```typescript
{
  isStatutory: string
  tangibility: string
  assetGroup: string
  assetSubGroup: string
  extraShiftDepreciation: string
  usefulLifeYears: string
  scheduleIIClassification: string
}
```

## 🔒 Authentication & Authorization

### User Roles & Permissions
- **Admin:** Full system access, user management, all CRUD operations
- **Data Entry:** Asset management, company data entry, limited settings
- **Report Viewer:** Read-only access to reports and data

### Security Headers
All responses include security headers:
- Content Security Policy
- CORS headers for allowed origins
- X-Frame-Options for clickjacking protection

## ❌ Error Responses

### Standard Error Format
```json
{
  "error": "Error Type",
  "message": "Detailed error message"
}
```

### Common HTTP Status Codes
- **200:** Success
- **201:** Created successfully
- **400:** Bad request (validation error)
- **401:** Unauthorized (authentication required)
- **403:** Forbidden (insufficient permissions)
- **404:** Resource not found
- **409:** Conflict (duplicate data)
- **500:** Internal server error

## 🧪 Testing Examples

### Test Company Creation
```bash
curl -X POST http://localhost:3001/api/companies \
  -H "Content-Type: application/json" \
  -d '{
    "companyName": "Test Company Ltd",
    "financialYearStart": "2024-04-01",
    "financialYearEnd": "2025-03-31",
    "firstDateOfAdoption": "2024-04-01"
  }'
```

### Test User Login
```bash
curl -X POST http://localhost:3001/api/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### Test Health Check
```bash
curl http://localhost:3001/api/health
```

---

**For detailed implementation examples and advanced usage, refer to the frontend API client in `/lib/api.ts`**
