import express from 'express';
import dbService from '../services/database.js';

const router = express.Router();

// Get app settings
router.get('/', async (req, res) => {
    try {
        const settings = await dbService.get(
            `SELECT 
                default_export_path as defaultExportPath,
                idle_timeout_minutes as idleTimeoutMinutes
            FROM app_settings WHERE id = 1`
        );
        
        if (!settings) {
            // Return default settings if none exist
            return res.json({
                defaultExportPath: '',
                idleTimeoutMinutes: 60
            });
        }
        
        res.json(settings);
    } catch (error) {
        console.error('Error fetching settings:', error);
        res.status(500).json({ error: 'Failed to fetch settings' });
    }
});

// Update app settings
router.put('/', async (req, res) => {
    try {
        const { defaultExportPath, idleTimeoutMinutes } = req.body;
        
        if (defaultExportPath === undefined || idleTimeoutMinutes === undefined) {
            return res.status(400).json({ 
                error: 'Both defaultExportPath and idleTimeoutMinutes are required' 
            });
        }
        
        await dbService.run(
            `UPDATE app_settings SET 
                default_export_path = ?,
                idle_timeout_minutes = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = 1`,
            [defaultExportPath, idleTimeoutMinutes]
        );
        
        res.json({ message: 'Settings updated successfully' });
    } catch (error) {
        console.error('Error updating settings:', error);
        res.status(500).json({ error: 'Failed to update settings' });
    }
});

export default router;
