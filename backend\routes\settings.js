import express from 'express';
import dbService from '../services/database-new.js';

const router = express.Router();

// Get app settings
router.get('/', async (req, res) => {
    try {
        await dbService.ensureInitialized();
        const dbManager = dbService.getDatabaseManager();

        // Get settings from global_settings table
        const exportPathSetting = await dbManager.getMasterQuery(
            `SELECT value FROM global_settings WHERE key = 'default_export_path'`
        );
        const timeoutSetting = await dbManager.getMasterQuery(
            `SELECT value FROM global_settings WHERE key = 'idle_timeout_minutes'`
        );

        const settings = {
            defaultExportPath: exportPathSetting?.value || '',
            idleTimeoutMinutes: parseInt(timeoutSetting?.value || '60')
        };

        res.json(settings);
    } catch (error) {
        console.error('Error fetching settings:', error);
        res.status(500).json({ error: 'Failed to fetch settings' });
    }
});

// Update app settings
router.put('/', async (req, res) => {
    try {
        const { defaultExportPath, idleTimeoutMinutes } = req.body;

        if (defaultExportPath === undefined || idleTimeoutMinutes === undefined) {
            return res.status(400).json({
                error: 'Both defaultExportPath and idleTimeoutMinutes are required'
            });
        }

        await dbService.ensureInitialized();
        const dbManager = dbService.getDatabaseManager();

        // Update or insert default_export_path
        await dbManager.runMasterQuery(
            `INSERT OR REPLACE INTO global_settings (key, value, updated_at)
             VALUES ('default_export_path', ?, CURRENT_TIMESTAMP)`,
            [defaultExportPath]
        );

        // Update or insert idle_timeout_minutes
        await dbManager.runMasterQuery(
            `INSERT OR REPLACE INTO global_settings (key, value, updated_at)
             VALUES ('idle_timeout_minutes', ?, CURRENT_TIMESTAMP)`,
            [idleTimeoutMinutes.toString()]
        );
        
        res.json({ message: 'Settings updated successfully' });
    } catch (error) {
        console.error('Error updating settings:', error);
        res.status(500).json({ error: 'Failed to update settings' });
    }
});

export default router;
