
/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, FC, useEffect, useMemo, useCallback } from 'react';
import { createRoot } from 'react-dom/client';
import { api } from '../lib/api';
import type { CompanyInfo as CompanyInfoType, Company, User, Asset, AssetImpact } from '../lib/db-server';

// Import types
import { DataViewProps } from '../lib/types';

// Import components
import { AppHeader } from '../AppHeader';
import { CompanyFormModal } from '../CompanyFormModal';
import { NavGroup, NavLink } from '../Navigation';
import { AdminUnlockModal } from '../AdminUnlockModal';
import { RecalculationImpactModal } from './RecalculationImpactModal';
import { RecoveryKeyModal } from '../RecoveryKeyModal';
import { AdminWelcomeModal } from '../AdminWelcomeModal';
import { PasswordChangeModal } from '../PasswordChangeModal';

// Import views
import { Login } from '../Login';
import { PasswordRecovery } from '../PasswordRecovery';
import { WelcomeModal } from '../WelcomeModal';
import { CompanyInfo } from './CompanyInfo';
import { AssetClassification } from './AssetClassification';
import { AssetRecords } from './AssetRecords';
import { ExtraShiftDays } from './ExtraShiftDays';
import { ScheduleIII } from './ScheduleIII';
import { LedgerWise } from './LedgerWise';
import { MethodWise } from './MethodWise';
import { AssetCalculations } from '../AssetCalculations';
import { AssetGroupReport } from '../AssetGroupReport';
import { UserList } from '../UserList';
import { AddUser } from '../AddUser';
import { ThemeSettings, UserManual, About } from './PlaceholderViews';
import { BackupSettings } from '../BackupSettings';
import { LedgerMaster } from '../LedgerMaster';
import { TangibilityReport } from './TangibilityReport';
import { GeneralSettings } from '../GeneralSettings';
import { AuditTrail } from '../AuditTrail';
import { UnlockYearView } from '../UnlockYearView';
import { ScrapAndEndOfLifeReport } from '../ScrapAndEndOfLifeReport';
import { AssetAdditionsReport } from '../AssetAdditionsReport';
import { AssetDeletionsReport } from '../AssetDeletionsReport';
import { LicenseManagement } from '../LicenseManagement';

// Import Icons
import { 
    InfoIcon, AlertTriangleIcon, CheckCircleIcon, XCircleIcon, BriefcaseIcon, FileTextIcon, 
    BookOpenIcon, CalendarIcon, CalculatorIcon, DatabaseIcon, LayersIcon, BarChart2Icon,
    UsersIcon, MoonIcon, SlidersIcon, SettingsIcon, HistoryIcon, LockIcon, UnlockIcon, TrashIcon, LogOutIcon, KeyIcon
} from '../Icons';


// --- Alert Modal Component ---
interface AlertModalProps {
    isOpen: boolean;
    onClose: () => void;
    title: string;
    message: string;
    type: 'success' | 'error' | 'info';
}

const AlertModal: FC<AlertModalProps> = ({ isOpen, onClose, title, message, type }) => {
    if (!isOpen) return null;

    const ICONS = {
        success: <CheckCircleIcon className="icon-success" />,
        error: <XCircleIcon className="icon-error" />,
        info: <InfoIcon className="icon-info" />,
    };

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" style={{maxWidth: '450px'}} onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <div className="modal-header-content">
                        {ICONS[type]}
                        <h2>{title}</h2>
                    </div>
                    <button className="modal-close-btn" onClick={onClose}>&times;</button>
                </div>
                <div style={{padding: '1.5rem 0', fontSize: '1.1rem', lineHeight: '1.5'}}>
                    <p style={{whiteSpace: 'pre-wrap'}}>{message}</p>
                </div>
                <div className="modal-actions">
                    <button type="button" className="btn btn-primary" onClick={onClose}>OK</button>
                </div>
            </div>
        </div>
    );
};

// --- Confirmation Modal Component ---
interface ConfirmationModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    title: string;
    message: string;
}

const ConfirmationModal: FC<ConfirmationModalProps> = ({ isOpen, onClose, onConfirm, title, message }) => {
    if (!isOpen) return null;

    const handleConfirmClick = () => {
        onConfirm();
        onClose(); // Close after confirming
    };

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" style={{maxWidth: '500px'}} onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <div className="modal-header-content">
                        <AlertTriangleIcon className="icon-warning"/>
                        <h2>{title}</h2>
                    </div>
                    <button className="modal-close-btn" onClick={onClose}>&times;</button>
                </div>
                <div style={{padding: '1.5rem 0', fontSize: '1.1rem', lineHeight: '1.5'}}>
                    <p style={{whiteSpace: 'pre-wrap'}}>{message}</p>
                </div>
                <div className="modal-actions">
                    <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
                    <button type="button" className="btn btn-warning" onClick={handleConfirmClick}>Confirm</button>
                </div>
            </div>
        </div>
    );
};


// --- Component Map for Dynamic View Rendering ---
const VIEW_COMPONENTS: { [key: string]: React.FC<any> } = {
    CompanyInfo,
    AssetClassification,
    LedgerMaster,
    ExtraShiftDays,
    AssetRecords,
    AssetCalculations,
    AssetGroupReport,
    ScheduleIII,
    LedgerWise,
    MethodWise,
    TangibilityReport,
    ScrapAndEndOfLifeReport,
    AssetAdditionsReport,
    AssetDeletionsReport,
    UserList,
    AddUser,
    ThemeSettings,
    BackupSettings,
    GeneralSettings,
    LicenseManagement,
    UserManual,
    About,
    AuditTrail,
    UnlockYearView,
};


function App() {
    const [theme, setTheme] = useState<'light' | 'dark'>(
        () => (localStorage.getItem('theme') as 'light' | 'dark') || 'dark'
    );
    const [userPreferencesLoaded, setUserPreferencesLoaded] = useState(false);
    // Show welcome modal on startup, login only when company is selected
    const [viewMode, setViewMode] = useState<'welcome' | 'login' | 'app' | 'recover'>('welcome');
    const [activeView, setActiveView] = useState('CompanyInfo');
    const [showWelcomeModal, setShowWelcomeModal] = useState(true);
    const [pendingCompanyId, setPendingCompanyId] = useState<string | null>(null);

    // --- User Management State ---
    const [editingUserId, setEditingUserId] = useState<string | null>(null);
    // No auto-login, user must login when selecting company
    const [loggedInUser, setLoggedInUser] = useState<User | null>(null);

    // --- Admin Unlock State ---
    const [unlockedYear, setUnlockedYear] = useState<string | null>(null);
    const [yearToUnlock, setYearToUnlock] = useState<string | null>(null);
    const [assetsBeforeUnlock, setAssetsBeforeUnlock] = useState<Asset[] | null>(null);
    const [isUnlockModalOpen, setIsUnlockModalOpen] = useState(false);
    const [unlockError, setUnlockError] = useState<string | null>(null);
    const [isVerifying, setIsVerifying] = useState(false);
    const [isCalculatingImpact, setIsCalculatingImpact] = useState(false);
    
    // --- Recalculation Impact Modal State ---
    const [recalculationImpact, setRecalculationImpact] = useState<AssetImpact[] | null>(null);
    const [isImpactModalOpen, setIsImpactModalOpen] = useState(false);
    
    // --- Recovery Key Modal State ---
    const [recoveryKeyInfo, setRecoveryKeyInfo] = useState<{ isOpen: boolean, key: string | null, userId: string | null, username: string | null }>({ isOpen: false, key: null, userId: null, username: null });

    // --- First-time Admin Welcome Modal State ---
    const [showAdminWelcome, setShowAdminWelcome] = useState(false);

    // --- Password Change Modal State ---
    const [passwordChangeModal, setPasswordChangeModal] = useState<{isOpen: boolean, username: string, isFirstLogin: boolean}>({isOpen: false, username: '', isFirstLogin: false});

    // --- Idle Timeout State ---
    const [idleTimeoutMinutes, setIdleTimeoutMinutes] = useState(0);

    const navStructure = useMemo(() => {
        const structure = [
            { key: 'company', title: 'Company Masters', children: [
                { key: 'CompanyInfo', label: 'Company Info', icon: <BriefcaseIcon/> }, 
                { key: 'AssetClassification', label: 'Asset Classification', icon: <FileTextIcon/> }, 
                { key: 'LedgerMaster', label: 'Ledger Master', icon: <BookOpenIcon/> }
            ]},
            { key: 'assets', title: 'Assets Data', children: [
                { key: 'AssetRecords', label: 'Asset Records', icon: <DatabaseIcon /> },
                { key: 'AssetCalculations', label: 'Asset Calculations', icon: <CalculatorIcon /> },
                { key: 'ExtraShiftDays', label: 'Extra Shift Days', icon: <CalendarIcon /> }
            ]},
            { key: 'reports', title: 'Reports', children: [
                { key: 'ScheduleIII', label: 'Schedule III', icon: <BarChart2Icon /> },
                { key: 'AssetGroupReport', label: 'Asset Group Report', icon: <LayersIcon /> },
                { key: 'LedgerWise', label: 'Ledger-wise', icon: <BarChart2Icon /> },
                { key: 'MethodWise', label: 'Method-wise', icon: <BarChart2Icon /> },
                { key: 'TangibilityReport', label: 'Tangibility-wise', icon: <BarChart2Icon /> },
                { key: 'AssetAdditionsReport', label: 'Asset Additions', icon: <BarChart2Icon /> },
                { key: 'AssetDeletionsReport', label: 'Asset Deletions', icon: <BarChart2Icon /> },
                { key: 'ScrapAndEndOfLifeReport', label: 'Scrap & End of Life', icon: <TrashIcon /> }
            ]},
            { key: 'users', title: 'Users', children: [
                { key: 'UserList', label: 'User List', icon: <UsersIcon /> }, 
                { key: 'AddUser', label: 'Add User', icon: <UsersIcon /> }
            ]},
            { key: 'settings', title: 'Settings', children: [
                { key: 'ThemeSettings', label: 'Theme', icon: <SlidersIcon /> }, 
                { key: 'BackupSettings', label: 'Backup & Restore', icon: <SettingsIcon /> }, 
                { key: 'GeneralSettings', label: 'General Settings', icon: <SettingsIcon /> },
                { key: 'LicenseManagement', label: 'License Management', icon: <KeyIcon /> },
            ]},
        ];

        if (loggedInUser?.role === 'Admin') {
            structure.push({
                key: 'admin',
                title: 'Administration',
                children: [
                    { key: 'AuditTrail', label: 'Audit Trail', icon: <HistoryIcon /> },
                    { key: 'UnlockYearView', label: 'Unlock Financial Year', icon: <UnlockIcon size={18} /> },
                ]
            });
        }
        
        structure.push({ key: 'help', title: 'Help & About', children: [
            { key: 'UserManual', label: 'User Manual', icon: <BookOpenIcon /> },
            { key: 'About', label: 'About & Help', icon: <InfoIcon /> },
        ]});

        return structure;
    }, [loggedInUser]);

    const viewToGroupMap = useMemo(() => {
        const map = new Map<string, string>();
        navStructure.forEach(group => {
            group.children.forEach(child => {
                map.set(child.key, group.key);
            });
        });
        return map;
    }, [navStructure]);
    
    const [openSectionKey, setOpenSectionKey] = useState<string | null>(() => viewToGroupMap.get(activeView) || null);
    
    const [companies, setCompanies] = useState<Company[]>([]);
    const [selectedCompanyId, setSelectedCompanyId] = useState<string | null>(null);
    const [selectedYear, setSelectedYear] = useState('');
    const [financialYears, setFinancialYears] = useState<string[]>([]);
    const [licenseValidUpto, setLicenseValidUpto] = useState<string | null>(null);
    
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalMode, setModalMode] = useState<'add' | 'edit'>('add');
    const [editingCompany, setEditingCompany] = useState<CompanyInfoType | null>(null);

    // --- Alert and Confirmation Modal State ---
    const [alertInfo, setAlertInfo] = useState({ isOpen: false, title: '', message: '', type: 'info' as 'success' | 'error' | 'info' });
    const [confirmationInfo, setConfirmationInfo] = useState<{
        isOpen: boolean;
        title: string;
        message: string;
        onConfirm: () => void;
    }>({ isOpen: false, title: '', message: '', onConfirm: () => {} });


    useEffect(() => {
        localStorage.setItem('theme', theme);
        document.body.className = theme;
    }, [theme]);

    // Load user preferences when user logs in
    useEffect(() => {
        const loadUserPreferences = async () => {
            if (loggedInUser && !userPreferencesLoaded) {
                try {
                    const preferences = await api.getUserPreferences(loggedInUser.id);
                    if (preferences.theme && preferences.theme !== theme) {
                        setTheme(preferences.theme);
                    }
                    setUserPreferencesLoaded(true);
                } catch (error) {
                    console.error('Failed to load user preferences:', error);
                    // Continue with localStorage theme if user preferences fail
                    setUserPreferencesLoaded(true);
                }
            }
        };

        loadUserPreferences();
    }, [loggedInUser, userPreferencesLoaded, theme]);
    
    // This useEffect is to fetch settings on initial load, since login is bypassed.
    useEffect(() => {
        const fetchInitialSettings = async () => {
             try {
                const settings = await api.getSettings();
                setIdleTimeoutMinutes(settings.idleTimeoutMinutes);
            } catch (error) {
                console.error("Failed to fetch idle timeout settings:", error);
                setIdleTimeoutMinutes(60); 
            }
        };
        fetchInitialSettings();
    }, []); // Run only once on mount

    useEffect(() => {
        if (viewMode === 'app') {
            const groupKey = viewToGroupMap.get(activeView);
            if (groupKey && groupKey !== openSectionKey) {
                setOpenSectionKey(groupKey);
            }
        }
    }, [activeView, viewToGroupMap, viewMode]);
    
    const handleThemeChange = async (newTheme: 'light' | 'dark') => {
        setTheme(newTheme);

        // Save to user preferences if user is logged in
        if (loggedInUser) {
            try {
                await api.updateUserTheme(loggedInUser.id, newTheme);
            } catch (error) {
                console.error('Failed to save theme preference:', error);
                // Theme is still saved to localStorage, so this is not critical
            }
        }
    };

    const loadCompanies = async () => {
        const companyList = await api.getCompanies();
        setCompanies(companyList);
        if (companyList.length > 0) {
            // If no company is selected, or the selected one no longer exists, select the first one.
            const currentCompanyExists = companyList.some(c => c.id === selectedCompanyId);
            if (!selectedCompanyId || !currentCompanyExists) {
                 await handleCompanyChange(companyList[0].id);
            } else {
                 // Refresh data for the currently selected company
                 const companyData = await api.getCompanyData(selectedCompanyId);
                 if (companyData) {
                    setFinancialYears(companyData.financialYears);
                    setLicenseValidUpto(companyData.info.licenseValidUpto);
                 }
            }
        } else {
            // No companies exist
            setSelectedCompanyId(null);
            setFinancialYears([]);
            setSelectedYear('');
            setLicenseValidUpto(null);
        }
    };
    
    useEffect(() => {
        if (viewMode === 'app' || viewMode === 'welcome') {
            loadCompanies();
        }
    }, [loggedInUser, viewMode]);

    const handleCompanyChange = async (id: string) => {
        if (unlockedYear) return;

        // If user is not logged in, show login modal first
        if (!loggedInUser) {
            setPendingCompanyId(id);
            setViewMode('login');
            return;
        }

        // User is logged in, proceed with company selection
        setUnlockedYear(null);
        setSelectedCompanyId(id);
        const companyData = await api.getCompanyData(id);
        if (companyData) {
            setFinancialYears(companyData.financialYears);
            setSelectedYear(companyData.financialYears[companyData.financialYears.length - 1]);
            setLicenseValidUpto(companyData.info.licenseValidUpto);
        } else {
            setLicenseValidUpto(null);
            setFinancialYears([]);
            setSelectedYear('');
        }
    };

    const handleYearChange = (year: string) => {
        if (unlockedYear) return;
        setUnlockedYear(null);
        setSelectedYear(year);
    }

    const handleToggleSection = (sectionKey: string) => {
        setOpenSectionKey(prevKey => {
            if (prevKey === sectionKey) {
                return null;
            }
            const group = navStructure.find(g => g.key === sectionKey);
            if (group && group.children.length > 0) {
                const firstChild = group.children[0];
                if (firstChild.key === 'AddUser') {
                    handleAddUserNav();
                } else {
                    setActiveView(firstChild.key);
                }
            }
            return sectionKey;
        });
    };

    const handleOpenAddModal = () => {
        setModalMode('add');
        setEditingCompany(null);
        setIsModalOpen(true);
    };

    const handleOpenEditModal = async () => {
        if (!selectedCompanyId) return;
        try {
            const companyData = await api.getCompanyInfo(selectedCompanyId);

            // Check if company has a valid license
            const isLicensed = !!(companyData.licenseValidUpto && new Date(companyData.licenseValidUpto) > new Date());

            if (isLicensed) {
                showAlert('Edit Restrictions',
                    'Core company master details (Name, PAN, CIN, Date of Incorporation, First Date of Adoption) cannot be edited because a valid license is active. ' +
                    'Only contact information, address, and license validity can be modified.', 'info');
            }

            setEditingCompany(companyData);
            setModalMode('edit');
            setIsModalOpen(true);
        } catch (error) {
            showAlert("Error", "Could not load company data for editing.", 'error');
            console.error(error);
        }
    };

    const handleDeleteCompany = async (companyId: string) => {
        if (!companyId) return;

        try {
            const companyData = await api.getCompanyInfo(companyId);
            const companyName = companyData.companyName;

            // No browser confirmation needed - modal confirmation is handled in CompanyFormModal
            await api.deleteCompany(companyId);
            showAlert('Success', `Company "${companyName}" has been deleted successfully.`, 'success');

            // Refresh companies list and reset selection
            await loadCompanies();

            // If the deleted company was selected, reset selection
            if (selectedCompanyId === companyId) {
                setSelectedCompanyId('');
                setFinancialYears([]);
                setSelectedYear('');
            }
        } catch (error) {
            showAlert("Error", "Could not delete company. Please try again.", 'error');
            console.error(error);
        }
    };

    const handleCompanyFormSubmit = async (formData: CompanyInfoType) => {
        const isValidFYDate = (dateString: string) => /^\d{4}-04-01$/.test(dateString);

        if (modalMode === 'add' && (!isValidFYDate(formData.financialYearStart) || !isValidFYDate(formData.firstDateOfAdoption))) {
            showAlert('Invalid Date Format', 'For new companies, Financial Year Start and Adoption Date must be on April 1st (format: YYYY-04-01).', 'error');
            return;
        }

        if (loggedInUser) {
            const action = modalMode === 'add' ? 'CREATE_COMPANY' : 'UPDATE_COMPANY';
            const details = `${modalMode === 'add' ? 'Created' : 'Updated'} company: "${formData.companyName}".`;
            api.addAuditLog({ userId: loggedInUser.id, username: loggedInUser.username, action, details });
        }

        if (modalMode === 'add') {
            const newCompany = await api.addCompany(formData);
            await loadCompanies(); 
            await handleCompanyChange(newCompany.id);
        } else if (selectedCompanyId) {
            await api.updateCompany(selectedCompanyId, formData);
            await loadCompanies(); 
        }
        setIsModalOpen(false);
    };

    const handleCreateNextFY = async () => {
        if (!selectedCompanyId) return;
        const companyInfo = await api.getCompanyInfo(selectedCompanyId);
        if (!companyInfo) {
            showAlert("Error", "Cannot find company information.", 'error');
            return;
        }

        showConfirmation(
            'Confirm: Create Next Financial Year',
            `Are you sure you want to create the next financial year for ${companyInfo.companyName}? This action will finalize calculations for the current year and cannot be undone.`,
            async () => {
                try {
                    const newYear = await api.createNextFinancialYear(selectedCompanyId, selectedYear);
                    if (loggedInUser) {
                        api.addAuditLog({
                            userId: loggedInUser.id,
                            username: loggedInUser.username,
                            action: 'CREATE_FINANCIAL_YEAR',
                            details: `Created new financial year ${newYear} for company "${companyInfo.companyName}".`
                        });
                    }
                    showAlert("Success", `Successfully created financial year ${newYear}.`, 'success');
                    await loadCompanies();
                    setSelectedYear(newYear);
                } catch(err: any) {
                    console.error("Error during 'Create Next FY' process:", err);
                    showAlert("Error", `Failed to create next financial year:\n\n${err.message}`, 'error');
                }
            }
        );
    };

    // --- Modal Handlers ---
    const showAlert = useCallback((title: string, message: string, type: 'success' | 'error' | 'info' = 'info') => {
        setAlertInfo({ isOpen: true, title, message, type });
    }, []);

    const showConfirmation = useCallback((title: string, message: string, onConfirm: () => void) => {
        setConfirmationInfo({ isOpen: true, title, message, onConfirm });
    }, []);
    
    // --- Login/Logout & User Management Handlers ---
    const handleLoginSuccess = async (loginResponse: {user: User, showAdminWelcome: boolean, mustChangePassword?: boolean}) => {
        // Check if user must change password first
        if (loginResponse.mustChangePassword) {
            setPasswordChangeModal({
                isOpen: true,
                username: loginResponse.user.username,
                isFirstLogin: true
            });
            return; // Don't proceed with normal login flow
        }

        setLoggedInUser(loginResponse.user);
        setViewMode('app');

        // If there was a pending company selection, handle it now
        if (pendingCompanyId) {
            const companyData = await api.getCompanyData(pendingCompanyId);
            if (companyData) {
                setSelectedCompanyId(pendingCompanyId);
                setFinancialYears(companyData.financialYears);
                setSelectedYear(companyData.financialYears[companyData.financialYears.length - 1]);
                setLicenseValidUpto(companyData.info.licenseValidUpto);
            }
            setPendingCompanyId(null);
        }

        if (loginResponse.showAdminWelcome) {
            setShowAdminWelcome(true);
        }
        try {
            const settings = await api.getSettings();
            setIdleTimeoutMinutes(settings.idleTimeoutMinutes);
        } catch (error) {
            console.error("Failed to fetch idle timeout settings:", error);
            // Use a default or handle the error appropriately
            setIdleTimeoutMinutes(60);
        }
    };

    const handleWelcomeClose = () => {
        setShowWelcomeModal(false);
        setViewMode('app');
    };

    const handleManualLogout = () => {
        showConfirmation(
            'Confirm Logout',
            'Are you sure you want to log out?',
            () => {
                setLoggedInUser(null);
                setViewMode('welcome');
                setShowWelcomeModal(true);
                setSelectedCompanyId(null);
                setFinancialYears([]);
                setSelectedYear('');
                setUnlockedYear(null);
                setLicenseValidUpto(null);
                setPendingCompanyId(null);
                setIdleTimeoutMinutes(0);
            }
        );
    };

    const handleIdleLogout = () => {
        setLoggedInUser(null);
        setViewMode('login');
        setSelectedCompanyId(null);
        setIdleTimeoutMinutes(0);
        showAlert('Session Expired', 'You have been logged out due to inactivity.', 'info');
    };

    const handleEditUser = (userId: string) => {
        setEditingUserId(userId);
        setActiveView('AddUser');
    };

    const handleAddUserNav = () => {
        setEditingUserId(null);
        setActiveView('AddUser');
    };

    const handleUserFormSuccess = (recoveryKey: string | null, userId: string, username: string) => {
        setEditingUserId(null);
        if (recoveryKey) {
            setRecoveryKeyInfo({ isOpen: true, key: recoveryKey, userId: userId, username: username });
        } else {
            setActiveView('UserList');
        }
    };
    
    const handleRecoveryKeySaved = async (userId: string) => {
        await api.setHasSavedRecoveryKey(userId);
        setRecoveryKeyInfo({ isOpen: false, key: null, userId: null, username: null });
        setActiveView('UserList');
    };

    const handleConfirmAdminWelcome = () => {
        setShowAdminWelcome(false);
    };

    const handlePasswordChangeSuccess = () => {
        setPasswordChangeModal({isOpen: false, username: '', isFirstLogin: false});
        // After successful password change, proceed with normal login
        setViewMode('app');
        showAlert('Password Changed', 'Your password has been changed successfully. Welcome to FAR Sighted!', 'success');
    };

    const handlePasswordChangeCancel = () => {
        if (passwordChangeModal.isFirstLogin) {
            // For first login, user cannot cancel - they must change password
            showAlert('Password Change Required', 'You must change your password before continuing.', 'info');
            return;
        }
        setPasswordChangeModal({isOpen: false, username: '', isFirstLogin: false});
    };
    
    const companyName = useMemo(() => {
        return companies.find(c => c.id === selectedCompanyId)?.name || null;
    }, [companies, selectedCompanyId]);

     // --- Admin Unlock Handlers ---
    const handleUnlockYear = (year: string) => {
        if (loggedInUser?.role !== 'Admin') return;
        setYearToUnlock(year);
        setIsUnlockModalOpen(true);
        setUnlockError(null);
    };

    const handleConfirmUnlock = async (password: string) => {
        if (!loggedInUser || !selectedCompanyId || !companyName || !yearToUnlock) return;
        setIsVerifying(true);
        setUnlockError(null);

        try {
            const isVerified = await api.verifyAdminPassword(loggedInUser.id, password);
            if (!isVerified) {
                setUnlockError("Incorrect password. Please try again.");
                setIsVerifying(false);
                return;
            }

            const assetsSnapshot = await api.getAssets(selectedCompanyId);
            setAssetsBeforeUnlock(assetsSnapshot);

            const backupName = await api.createAutoBackup();
            api.addAuditLog({ userId: loggedInUser.id, username: loggedInUser.username, action: 'AUTO_BACKUP', details: `Automatic backup created: ${backupName} before unlocking year ${yearToUnlock}.` });
            api.addAuditLog({ userId: loggedInUser.id, username: loggedInUser.username, action: 'UNLOCK_YEAR', details: `Unlocked financial year ${yearToUnlock} for company "${companyName}".` });

            setUnlockedYear(yearToUnlock);
            setSelectedYear(yearToUnlock);
            setIsUnlockModalOpen(false);
            setYearToUnlock(null);
            showAlert("Year Unlocked", `Financial year ${yearToUnlock} is now unlocked for editing.`, 'info');

        } catch (error) {
            console.error(error);
            setUnlockError("An unexpected error occurred during verification.");
            showAlert("Error", `Failed to unlock year: ${error instanceof Error ? error.message : "Unknown error"}`, 'error');
        } finally {
            setIsVerifying(false);
        }
    };
    
    const handleLockAndRecalculate = async () => {
        if (!unlockedYear || !companyName || !selectedCompanyId || !assetsBeforeUnlock) return;
        
        setIsCalculatingImpact(true);
        try {
            const currentAssets = await api.getAssets(selectedCompanyId);
            const impactData = await api.calculateRecalculationImpact(
                selectedCompanyId,
                unlockedYear,
                assetsBeforeUnlock,
                currentAssets
            );
            
            if (impactData.length === 0) {
                 showConfirmation(
                    'Lock & Recalculate',
                    'No changes detected that would alter calculations. Do you still want to re-lock the year?',
                    handleConfirmRecalculation
                );
            } else {
                setRecalculationImpact(impactData);
                setIsImpactModalOpen(true);
            }
        } catch (error) {
             showAlert("Impact Analysis Failed", `An error occurred during impact analysis: ${error instanceof Error ? error.message : "Unknown error"}`, 'error');
             console.error(error);
        } finally {
            setIsCalculatingImpact(false);
        }
    };
    
    const handleConfirmRecalculation = async () => {
        if (!loggedInUser || !selectedCompanyId || !unlockedYear || !companyName) return;
        
        try {
            setIsImpactModalOpen(false);
            setRecalculationImpact(null);
            await api.recalculateFromYear(selectedCompanyId, unlockedYear);
            
            await api.addAuditLog({ 
                userId: loggedInUser.id, 
                username: loggedInUser.username, 
                action: 'LOCK_AND_RECALCULATE', 
                details: `Locked year ${unlockedYear} and recalculated all subsequent years for company "${companyName}".` 
            });
            
            showAlert("Recalculation Complete", "All data has been successfully recalculated and the year is now locked.", 'success');
            
            setUnlockedYear(null);
            setAssetsBeforeUnlock(null);
            await loadCompanies(); 
            
        } catch (error) {
             showAlert("Recalculation Failed", `An error occurred during recalculation: ${error instanceof Error ? error.message : "Unknown error"}`, 'error');
             console.error(error);
        }
    };
    
    // --- IDLE TIMEOUT EFFECT ---
    useEffect(() => {
        if (viewMode !== 'app' || !idleTimeoutMinutes || idleTimeoutMinutes <= 0) {
            return; // Don't run timer if not in app, or if timeout is disabled (0 or less)
        }

        let idleTimer: number;

        const resetTimer = () => {
            clearTimeout(idleTimer);
            idleTimer = window.setTimeout(() => {
                handleIdleLogout();
            }, idleTimeoutMinutes * 60 * 1000); // convert minutes to ms
        };

        const events: (keyof WindowEventMap)[] = ['mousemove', 'mousedown', 'keypress', 'scroll', 'touchstart'];
        events.forEach(event => window.addEventListener(event, resetTimer));

        resetTimer(); // Initial setup

        return () => {
            clearTimeout(idleTimer);
            events.forEach(event => window.removeEventListener(event, resetTimer));
        };
    }, [viewMode, idleTimeoutMinutes]);


    const dataViewProps: DataViewProps = {
        companyId: selectedCompanyId,
        companyName: companyName,
        year: selectedYear,
        financialYears,
        theme,
        onThemeChange: handleThemeChange,
        editingUserId,
        onEditUser: handleEditUser,
        onAddUserNav: handleAddUserNav,
        onUserFormSuccess: handleUserFormSuccess,
        showAlert,
        showConfirmation,
        loggedInUser,
        unlockedYear,
        onUnlockYear: handleUnlockYear,
        onLockAndRecalculate: handleLockAndRecalculate,
        onDataChange: loadCompanies,
    };
    
    // --- RENDER LOGIC ---
    const renderContent = () => {
        switch (viewMode) {
            case 'welcome':
                return (
                    <div className="app-container">
                        <aside className="sidebar">
                            <header className="sidebar-header"><h1>FAR Sighted</h1></header>
                            <div className="welcome-sidebar-content">
                                <p>Welcome to FAR Sighted! Select a company from the header to begin.</p>
                            </div>
                        </aside>
                        <main className="main-content">
                            <AppHeader
                                companies={companies}
                                selectedCompanyId={selectedCompanyId}
                                onCompanyChange={handleCompanyChange}
                                selectedYear={selectedYear}
                                onYearChange={handleYearChange}
                                years={financialYears}
                                onAddCompany={handleOpenAddModal}
                                onEditCompany={handleOpenEditModal}
                                onCreateNextFY={handleCreateNextFY}
                                activeView={activeView}
                                loggedInUser={loggedInUser}
                                unlockedYear={unlockedYear}
                                onLockAndRecalculate={handleLockAndRecalculate}
                                isCalculatingImpact={isCalculatingImpact}
                                licenseValidUpto={licenseValidUpto}
                            />
                            <div className="welcome-main-content">
                                <div className="welcome-message">
                                    <h2>Welcome to FAR Sighted</h2>
                                    <p>Please select a company from the dropdown above to get started.</p>
                                </div>
                            </div>
                        </main>
                    </div>
                );
            case 'login':
                return <Login onLoginSuccess={handleLoginSuccess} onForgotPassword={() => setViewMode('recover')} />;
            case 'recover':
                return <PasswordRecovery onBackToLogin={() => setViewMode('login')} />;
            case 'app':
                const CurrentView = VIEW_COMPONENTS[activeView];
                return (
                     <div className="app-container">
                        <aside className="sidebar">
                            <header className="sidebar-header"><h1>FAR Sighted</h1></header>
                            <nav className="sidebar-nav">
                                {navStructure.map(group => (
                                    <NavGroup key={group.key} title={group.title} isOpen={openSectionKey === group.key} onToggle={() => handleToggleSection(group.key)}>
                                        {group.children.map(link => (
                                            <NavLink 
                                                key={link.key}
                                                icon={link.icon}
                                                activeView={activeView} 
                                                viewName={link.key} 
                                                label={link.label} 
                                                onClick={() => {
                                                    if (link.key === 'AddUser') handleAddUserNav();
                                                    else setActiveView(link.key);
                                                }} 
                                            />
                                        ))}
                                    </NavGroup>
                                ))}
                            </nav>
                            <footer className="sidebar-footer">
                                <div className="sidebar-footer-section">
                                    {companyName && (
                                        <>
                                            <span className="footer-label">Registered To:</span>
                                            <p className="footer-value-primary">{companyName}</p>
                                            {licenseValidUpto && (
                                                <p className="footer-value-secondary">
                                                    License Valid Upto: {new Date(licenseValidUpto).toLocaleDateString()}
                                                </p>
                                            )}
                                        </>
                                    )}
                                </div>
                                {loggedInUser && (
                                    <div className="sidebar-footer-section">
                                        <div>
                                            <span className="footer-label">Logged In As:</span>
                                            <p className="footer-value-primary">
                                                {loggedInUser.username}
                                                <span className="user-role"> ({loggedInUser.role})</span>
                                            </p>
                                        </div>
                                        <button 
                                            className="btn btn-logout" 
                                            style={{width: '100%', marginTop: '0.75rem'}} 
                                            onClick={handleManualLogout}>
                                            <LogOutIcon /> Logout
                                        </button>
                                    </div>
                                )}
                            </footer>
                        </aside>
                        <main className="main-content">
                            <AppHeader
                                companies={companies}
                                selectedCompanyId={selectedCompanyId}
                                onCompanyChange={handleCompanyChange}
                                selectedYear={selectedYear}
                                onYearChange={handleYearChange}
                                years={financialYears}
                                onAddCompany={handleOpenAddModal}
                                onEditCompany={handleOpenEditModal}
                                onCreateNextFY={handleCreateNextFY}
                                activeView={activeView}
                                loggedInUser={loggedInUser}
                                unlockedYear={unlockedYear}
                                onLockAndRecalculate={handleLockAndRecalculate}
                                isCalculatingImpact={isCalculatingImpact}
                                licenseValidUpto={licenseValidUpto}
                            />
                            {CurrentView && <CurrentView {...dataViewProps} />}
                        </main>
                    </div>
                );
            default:
                return <Login onLoginSuccess={handleLoginSuccess} onForgotPassword={() => setViewMode('recover')} />;
        }
    };

    return (
        <>
            {renderContent()}
            
            <WelcomeModal
                isOpen={showWelcomeModal}
                onClose={handleWelcomeClose}
            />
            <CompanyFormModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onSubmit={handleCompanyFormSubmit}
                onDelete={handleDeleteCompany}
                mode={modalMode}
                initialData={editingCompany}
                showAlert={showAlert}
            />
            <AlertModal
                isOpen={alertInfo.isOpen}
                onClose={() => setAlertInfo({ ...alertInfo, isOpen: false })}
                title={alertInfo.title}
                message={alertInfo.message}
                type={alertInfo.type}
            />
            <ConfirmationModal
                isOpen={confirmationInfo.isOpen}
                onClose={() => setConfirmationInfo({ ...confirmationInfo, isOpen: false })}
                onConfirm={confirmationInfo.onConfirm}
                title={confirmationInfo.title}
                message={confirmationInfo.message}
            />
            <AdminUnlockModal
                isOpen={isUnlockModalOpen}
                onClose={() => setIsUnlockModalOpen(false)}
                onConfirm={handleConfirmUnlock}
                isVerifying={isVerifying}
                error={unlockError}
            />
            <RecalculationImpactModal
                isOpen={isImpactModalOpen}
                onClose={() => setIsImpactModalOpen(false)}
                onConfirm={handleConfirmRecalculation}
                impactData={recalculationImpact}
                companyName={companyName}
                year={unlockedYear}
                showAlert={showAlert}
            />
            <RecoveryKeyModal
                isOpen={recoveryKeyInfo.isOpen}
                recoveryKey={recoveryKeyInfo.key}
                onConfirm={() => recoveryKeyInfo.userId && handleRecoveryKeySaved(recoveryKeyInfo.userId)}
                usernameFor={recoveryKeyInfo.username}
            />
            <AdminWelcomeModal
                isOpen={showAdminWelcome}
                onConfirm={handleConfirmAdminWelcome}
            />
            <PasswordChangeModal
                isOpen={passwordChangeModal.isOpen}
                username={passwordChangeModal.username}
                isFirstLogin={passwordChangeModal.isFirstLogin}
                onSuccess={handlePasswordChangeSuccess}
                onCancel={handlePasswordChangeCancel}
                showAlert={showAlert}
            />
        </>
    );
};

const container = document.getElementById('root');
if (container) {
    const root = createRoot(container);
    root.render(<React.StrictMode><App /></React.StrictMode>);
}