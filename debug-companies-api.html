<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Companies API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .log-entry {
            margin: 5px 0;
            padding: 8px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-info {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
        }
        .log-error {
            background-color: #ffe7e7;
            border-left: 4px solid #dc3545;
        }
        .log-success {
            background-color: #e7ffe7;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Companies API</h1>
        <p>This page replicates the exact API call that the main application makes to load companies.</p>
        
        <div id="status" class="result loading">
            ⏳ Ready to test...
        </div>
        
        <button onclick="testExactAPICall()">Test Exact API Call</button>
        <button onclick="testSimpleFetch()">Test Simple Fetch</button>
        <button onclick="clearLogs()">Clear Logs</button>
        
        <div id="logs"></div>
        <div id="results"></div>
    </div>

    <script>
        // Replicate the exact API configuration from the main app
        const BACKEND_PORT = 8090;
        const API_BASE_URL = `http://localhost:${BACKEND_PORT}/api`;
        
        let logContainer;
        
        function log(message, type = 'info') {
            if (!logContainer) {
                logContainer = document.getElementById('logs');
                if (!logContainer.innerHTML) {
                    logContainer.innerHTML = '<h3>📋 Debug Logs:</h3>';
                }
            }
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            
            // Also log to console
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type = 'loading') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `result ${type}`;
        }
        
        function showResults(data) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <h3>📊 Results:</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        }
        
        function clearLogs() {
            const logsDiv = document.getElementById('logs');
            logsDiv.innerHTML = '';
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            logContainer = null;
        }
        
        // Replicate the exact apiRequest function from the main app
        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE_URL}${endpoint}`;
            
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include', // Include cookies for session management
            };
            
            const requestOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers,
                },
            };
            
            try {
                log(`Making request: ${requestOptions.method || 'GET'} ${endpoint}`, 'info');
                log(`Full URL: ${url}`, 'info');
                log(`Request options: ${JSON.stringify(requestOptions, null, 2)}`, 'info');
                
                const response = await fetch(url, requestOptions);
                
                log(`Response status: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                log(`Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`, 'info');
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    
                    // Handle migration required error specifically
                    if (response.status === 503 && errorData.error === 'System migration required') {
                        log('Migration required error detected', 'error');
                        throw new Error('MIGRATION_REQUIRED');
                    }
                    
                    const errorMsg = errorData.error || `HTTP ${response.status}: ${response.statusText}`;
                    log(`API Error: ${errorMsg}`, 'error');
                    throw new Error(errorMsg);
                }
                
                const data = await response.json();
                log(`Response data received: ${JSON.stringify(data, null, 2)}`, 'success');
                return data;
                
            } catch (error) {
                log(`Request failed: ${error.message}`, 'error');
                log(`Error stack: ${error.stack}`, 'error');
                throw error;
            }
        }
        
        // Test the exact API call that the main app makes
        async function testExactAPICall() {
            updateStatus('🔍 Testing exact API call...', 'loading');
            clearLogs();
            
            try {
                log('Starting exact API call test', 'info');
                log('This replicates: api.getCompanies()', 'info');
                
                // This is exactly what api.getCompanies() does
                const companies = await apiRequest('/companies');
                
                log(`Success! Received ${companies.length} companies`, 'success');
                updateStatus(`✅ Success! Found ${companies.length} companies`, 'success');
                showResults(companies);
                
            } catch (error) {
                log(`Test failed: ${error.message}`, 'error');
                updateStatus(`❌ Failed: ${error.message}`, 'error');
                showResults({ error: error.message, stack: error.stack });
            }
        }
        
        // Test simple fetch without the wrapper
        async function testSimpleFetch() {
            updateStatus('🔍 Testing simple fetch...', 'loading');
            clearLogs();
            
            try {
                log('Starting simple fetch test', 'info');
                
                const url = `${API_BASE_URL}/companies`;
                log(`Fetching: ${url}`, 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });
                
                log(`Response: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const companies = await response.json();
                    log(`Success! Received ${companies.length} companies`, 'success');
                    updateStatus(`✅ Simple fetch success! Found ${companies.length} companies`, 'success');
                    showResults(companies);
                } else {
                    const errorText = await response.text();
                    log(`Error response: ${errorText}`, 'error');
                    updateStatus(`❌ Simple fetch failed: ${response.status}`, 'error');
                    showResults({ status: response.status, error: errorText });
                }
                
            } catch (error) {
                log(`Simple fetch failed: ${error.message}`, 'error');
                updateStatus(`❌ Simple fetch error: ${error.message}`, 'error');
                showResults({ error: error.message, stack: error.stack });
            }
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            log('Page loaded, starting auto-test in 2 seconds', 'info');
            setTimeout(testExactAPICall, 2000);
        });
    </script>
</body>
</html>
