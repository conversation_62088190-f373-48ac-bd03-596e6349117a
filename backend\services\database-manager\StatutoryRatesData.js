/**
 * Statutory Rates Data - Centralized source for asset classification
 * This data is sourced from the official Schedule II of Companies Act 2013
 */

// Statutory CSV data from db-server.ts
const statutoryCSVData = `Statutory,Tangibility,Asset Group,Asset Sub-Group,Extra Shift Depreciation,Useful Life Years,Schedule III Classification
Yes,Tangible,Buildings,Buildings (other than factory buildings) RCC Frame Structure,No,60,Buildings - Freehold
Yes,Tangible,Buildings,Buildings (other than factory buildings) other than RCC Frame Structure,No,30,Buildings - Freehold
Yes,Tangible,Buildings,Factory building,No,30,Buildings - Freehold
Yes,Tangible,Buildings,"Fences, wells, tube wells",No,5,Buildings - Freehold
Yes,Tangible,Buildings,"Others (including temporary structure, etc.)",No,3,Buildings - Freehold
Yes,Tangible,"Bridges, culverts, bunders, etc.","Bridges, culverts, bunders, etc.",No,30,Buildings - Freehold
Yes,Tangible,Plant and Machinery,General plant and machinery,Yes,15,Plant and machinery - General
Yes,Tangible,Plant and Machinery,Continuous process plant,Yes,25,Plant and machinery - Continuous process
Yes,Tangible,Plant and Machinery,"Dies, jigs, patterns, etc.",No,8,Plant and machinery - Dies, jigs, patterns
Yes,Tangible,Furniture and fittings,Furniture and fittings,No,10,Furniture and fittings
Yes,Tangible,Office equipment,Office equipment,No,5,Office equipment
Yes,Tangible,Computer and data processing units,Computer including computer software,No,3,Computer including computer software
Yes,Tangible,Computer and data processing units,Computer software,No,3,Computer including computer software
Yes,Tangible,Motor car,Motor car,No,8,Motor car
Yes,Tangible,Motor vehicles other than motor car,Motor vehicles other than motor car,No,8,Motor vehicles other than motor car
Yes,Tangible,Ships,Ships,No,20,Ships
Yes,Tangible,Aircraft,Aircraft,No,20,Aircraft
Yes,Tangible,Railway sidings,Railway sidings,No,15,Railway sidings
Yes,Tangible,Laboratory equipment,Laboratory equipment,No,10,Laboratory equipment
Yes,Tangible,Medical equipment,Medical equipment,No,13,Medical equipment
Yes,Tangible,Pollution control equipment,Pollution control equipment,No,15,Pollution control equipment
Yes,Tangible,Fire fighting equipment,Fire fighting equipment,No,15,Fire fighting equipment
Yes,Tangible,Electrical installations and equipments,Electrical installations and equipments,No,10,Electrical installations and equipments
Yes,Intangible,Patents,Patents,No,8,Patents
Yes,Intangible,Copyrights,Copyrights,No,6,Copyrights
Yes,Intangible,Trademarks,Trademarks,No,5,Trademarks
Yes,Intangible,Licenses and franchise,Licenses and franchise,No,10,Licenses and franchise
No,Tangible,Land,Freehold land,No,,Land - Freehold
No,Tangible,Land,Leasehold land,No,,Land - Leasehold
No,Tangible,Buildings,Temporary erections,No,3,Buildings - Temporary
No,Tangible,Plant and Machinery,Moulds and dies,No,8,Plant and machinery - Moulds and dies
No,Tangible,Plant and Machinery,Jigs and fixtures,No,8,Plant and machinery - Jigs and fixtures
No,Tangible,Plant and Machinery,Patterns,No,8,Plant and machinery - Patterns
No,Tangible,Plant and Machinery,Tools and tackles,No,5,Plant and machinery - Tools and tackles
No,Tangible,Plant and Machinery,Loose tools,No,3,Plant and machinery - Loose tools
No,Tangible,Plant and Machinery,Spare parts,No,5,Plant and machinery - Spare parts
No,Tangible,Plant and Machinery,Catalysts,No,3,Plant and machinery - Catalysts
No,Tangible,Plant and Machinery,Refractories,No,5,Plant and machinery - Refractories
No,Tangible,Plant and Machinery,Pollution control equipment,No,15,Plant and machinery - Pollution control
No,Tangible,Plant and Machinery,Fire fighting equipment,No,15,Plant and machinery - Fire fighting
No,Tangible,Plant and Machinery,Safety equipment,No,10,Plant and machinery - Safety equipment
No,Tangible,Plant and Machinery,Laboratory equipment,No,10,Plant and machinery - Laboratory equipment
No,Tangible,Plant and Machinery,Medical equipment,No,13,Plant and machinery - Medical equipment
No,Tangible,Plant and Machinery,Electrical installations,No,10,Plant and machinery - Electrical installations
No,Tangible,Plant and Machinery,Weighing machines,No,15,Plant and machinery - Weighing machines
No,Tangible,Plant and Machinery,Measuring instruments,No,10,Plant and machinery - Measuring instruments
No,Tangible,Plant and Machinery,Testing equipment,No,10,Plant and machinery - Testing equipment
No,Tangible,Plant and Machinery,Research and development equipment,No,10,Plant and machinery - R&D equipment
No,Tangible,Plant and Machinery,Quality control equipment,No,10,Plant and machinery - Quality control
No,Tangible,Plant and Machinery,Communication equipment,No,8,Plant and machinery - Communication
No,Tangible,Plant and Machinery,Data processing equipment,No,3,Plant and machinery - Data processing
No,Tangible,Plant and Machinery,Office equipment,No,5,Plant and machinery - Office equipment
No,Tangible,Furniture and fittings,Office furniture,No,10,Furniture and fittings - Office
No,Tangible,Furniture and fittings,Factory furniture,No,10,Furniture and fittings - Factory
No,Tangible,Furniture and fittings,Residential furniture,No,10,Furniture and fittings - Residential
No,Tangible,Vehicles,Two wheelers,No,10,Vehicles - Two wheelers
No,Tangible,Vehicles,Three wheelers,No,8,Vehicles - Three wheelers
No,Tangible,Vehicles,Commercial vehicles,No,8,Vehicles - Commercial
No,Tangible,Vehicles,Earth moving equipment,No,8,Vehicles - Earth moving
No,Tangible,Vehicles,Construction equipment,No,8,Vehicles - Construction
No,Tangible,Vessels,Pressure vessels,No,20,Vessels - Pressure
No,Tangible,Vessels,Storage tanks,No,20,Vessels - Storage
No,Tangible,Vessels,Reactors,No,20,Vessels - Reactors
No,Tangible,Intangible,Goodwill,No,,Intangible - Goodwill
No,Intangible,Intangible,Technical know-how,No,10,Intangible - Technical know-how
No,Intangible,Intangible,Software,No,3,Intangible - Software
No,Intangible,Intangible,Designs,No,10,Intangible - Designs
No,Intangible,Intangible,Drawings,No,10,Intangible - Drawings
No,Intangible,Intangible,Franchises,No,10,Intangible - Franchises`;

/**
 * Parse CSV data into structured format
 */
function parseStatutoryCSV(csvString) {
    const lines = csvString.trim().split('\n');
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const result = [];
    
    for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(/,(?=(?:[^"]*"[^"]*")*[^"]*$)/);
        const obj = {};
        
        for (let j = 0; j < headers.length; j++) {
            const value = values[j] ? values[j].trim().replace(/^"|"$/g, '') : '';
            obj[headers[j]] = value;
        }
        result.push(obj);
    }
    return result;
}

/**
 * Remove duplicates based on asset group and sub-group combination
 */
function removeDuplicates(rates) {
    const seen = new Set();
    return rates.filter(rate => {
        const key = `${rate.assetGroup}|${rate.assetSubGroup}`;
        if (seen.has(key)) {
            return false;
        }
        seen.add(key);
        return true;
    });
}

/**
 * Get statutory rates data in the format expected by the database
 */
function getStatutoryRatesData() {
    const rawData = parseStatutoryCSV(statutoryCSVData).map(item => ({
        isStatutory: item['Statutory'],
        tangibility: item['Tangibility'],
        assetGroup: item['Asset Group'],
        assetSubGroup: item['Asset Sub-Group'],
        extraShiftDepreciation: item['Extra Shift Depreciation'],
        usefulLifeYears: item['Useful Life Years'],
        scheduleIIClassification: item['Schedule III Classification'],
    }));

    // Remove duplicates to prevent constraint violations
    return removeDuplicates(rawData);
}

/**
 * Get only statutory rates (Statutory = 'Yes')
 */
function getStatutoryOnlyRates() {
    return getStatutoryRatesData().filter(rate => rate.isStatutory === 'Yes');
}

/**
 * Get all rates (both statutory and non-statutory)
 */
function getAllRates() {
    return getStatutoryRatesData();
}

/**
 * Get rates by tangibility
 */
function getRatesByTangibility(tangibility) {
    return getStatutoryRatesData().filter(rate => rate.tangibility === tangibility);
}

/**
 * Get rates by asset group
 */
function getRatesByAssetGroup(assetGroup) {
    return getStatutoryRatesData().filter(rate => rate.assetGroup === assetGroup);
}

export {
    getStatutoryRatesData,
    getStatutoryOnlyRates,
    getAllRates,
    getRatesByTangibility,
    getRatesByAssetGroup,
    parseStatutoryCSV,
    statutoryCSVData
};
