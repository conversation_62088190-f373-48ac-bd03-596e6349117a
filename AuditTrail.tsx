/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo, FC } from 'react';
import { api } from './lib/api';
import type { AuditLogEntry } from './lib/db-server';
import { DataViewProps } from './lib/types';
import { sortData, exportToExcel } from './lib/utils';
import { DownloadIcon } from './Icons';
import { Highlight } from './pages/PlaceholderViews';

type SortableKeys = keyof AuditLogEntry;

export const AuditTrail: FC<DataViewProps> = ({ companyName, year, showAlert, loggedInUser }) => {
    const [logs, setLogs] = useState<AuditLogEntry[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [sortConfig, setSortConfig] = useState<{ key: SortableKeys; direction: 'asc' | 'desc' } | null>({ key: 'timestamp', direction: 'desc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);
    const [filterText, setFilterText] = useState('');

    useEffect(() => {
        const fetchLogs = async () => {
            setLoading(true);
            setError(null);
            try {
                const data = await api.getAuditLog();
                setLogs(data);
            } catch (err) {
                setError("Failed to fetch audit trail data.");
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        if (loggedInUser?.role === 'Admin') {
            fetchLogs();
        } else {
            setLoading(false);
        }
    }, [loggedInUser]);

    const searchableFields: (keyof AuditLogEntry)[] = ['username', 'action', 'details'];

    const sortedLogs = useMemo(() => {
        return sortData(logs, sortConfig);
    }, [logs, sortConfig]);

    const filteredLogs = useMemo(() => {
        if (!filterText) return sortedLogs;
        const searchTerm = filterText.trim().toLowerCase();
        return sortedLogs.filter(log =>
            searchableFields.some(field =>
                String(log[field] ?? '').toLowerCase().includes(searchTerm)
            )
        );
    }, [sortedLogs, filterText]);

    const requestSort = (key: SortableKeys) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: SortableKeys) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };
    
    const getHeaderClass = (key: SortableKeys) => {
        const classes = ['sortable'];
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        if (key === 'details') classes.push('td-wrap');
        return classes.join(' ');
    };

    const getColumnClass = (key: SortableKeys) => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if (key === 'details') classes.push('td-wrap');
        return classes.join(' ');
    };

    const handleExport = () => {
        if (!logs || logs.length === 0) {
            showAlert("Export Failed", "There is no data to export.", 'error');
            return;
        }
        
        const dataToExport = filteredLogs.map(log => ({
            "Timestamp": new Date(log.timestamp).toLocaleString(),
            "User": log.username,
            "Action": log.action,
            "Details": log.details,
        }));
        
        exportToExcel({
            data: dataToExport,
            companyName: companyName || "System",
            year: year || new Date().getFullYear().toString(),
            reportName: 'Audit_Trail'
        });
    };

    if (loggedInUser?.role !== 'Admin') {
        return (
            <div className="settings-container">
                <h3 className="settings-title">Access Denied</h3>
                <p className="settings-description">You must be an Administrator to view the audit trail.</p>
            </div>
        );
    }
    
    if (loading) return <div className="loading-indicator">Loading Audit Trail...</div>;
    if (error) return <div className="error-message">{error}</div>;

    return (
        <>
            <div className="view-header">
                <h2>Audit Trail</h2>
                <div className="actions">
                    <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export to Excel</button>
                </div>
            </div>
            <div className="filter-container">
                 <div className="filter-input-wrapper">
                    <input
                        type="text"
                        placeholder="Filter by User, Action, or Details..."
                        className="filter-input"
                        value={filterText}
                        onChange={e => setFilterText(e.target.value)}
                        aria-label="Filter audit logs"
                    />
                    <button
                        className={`filter-clear-btn ${!filterText ? 'hidden' : ''}`}
                        onClick={() => setFilterText('')}
                        aria-label="Clear filter"
                        title="Clear filter"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </div>
                <div className="filter-info">
                    <span className="filter-info-icon">ⓘ</span>
                    <div className="filter-info-tooltip">
                        <strong>Filtering on:</strong>
                        <ul>
                            <li>User</li>
                            <li>Action</li>
                            <li>Details</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div className="table-container">
                <table>
                    <thead>
                        <tr>
                            <th className={getHeaderClass('timestamp')} onClick={() => requestSort('timestamp')}>Timestamp{getSortIndicator('timestamp')}</th>
                            <th className={getHeaderClass('username')} onClick={() => requestSort('username')}>User{getSortIndicator('username')}</th>
                            <th className={getHeaderClass('action')} onClick={() => requestSort('action')}>Action{getSortIndicator('action')}</th>
                            <th className={getHeaderClass('details')} onClick={() => requestSort('details')}>Details{getSortIndicator('details')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredLogs.map(log => (
                            <tr key={log.id} onClick={() => setSelectedRowId(log.id)} className={log.id === selectedRowId ? 'selected-row' : ''}>
                                <td className={getColumnClass('timestamp')}>{new Date(log.timestamp).toLocaleString()}</td>
                                <td className={getColumnClass('username')}><Highlight text={log.username} highlight={filterText} /></td>
                                <td className={getColumnClass('action')}><Highlight text={log.action} highlight={filterText} /></td>
                                <td className={getColumnClass('details')} style={{whiteSpace: 'pre-wrap'}}><Highlight text={log.details} highlight={filterText} /></td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </>
    );
};
