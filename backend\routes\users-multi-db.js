/**
 * UPDATED USER ROUTES
 * Professional Advisory Services - Chartered Accountant
 * 
 * Updated to use multi-database architecture with users in master database
 * and company-specific access controls.
 */

import express from 'express';
import bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import multiDbService from '../services/multi-database.js';

const router = express.Router();

// Get all users
router.get('/', async (req, res) => {
    try {
        const users = await multiDbService.masterAll(
            `SELECT id, username, role, is_active, created_at 
             FROM users 
             WHERE is_active = true 
             ORDER BY username`
        );
        
        res.json(users);
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ error: 'Failed to fetch users' });
    }
});

// Get user by ID with company access
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const user = await multiDbService.masterGet(
            'SELECT id, username, role, is_active, created_at FROM users WHERE id = ?',
            [id]
        );
        
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        // Get user's company access
        const companyAccess = await multiDbService.masterAll(
            `SELECT uca.company_id, c.company_name, uca.access_level, uca.granted_at
             FROM user_company_access uca
             JOIN companies c ON uca.company_id = c.id
             WHERE uca.user_id = ? AND c.is_active = true`,
            [id]
        );
        
        res.json({
            ...user,
            companyAccess: companyAccess
        });
    } catch (error) {
        console.error('Error fetching user:', error);
        res.status(500).json({ error: 'Failed to fetch user' });
    }
});

// Create new user
router.post('/', async (req, res) => {
    try {
        const { username, password, role, companyAccess } = req.body;
        
        // Validate required fields
        if (!username || !password || !role) {
            return res.status(400).json({ 
                error: 'Missing required fields: username, password, role' 
            });
        }
        
        // Validate role
        if (!['Admin', 'Data Entry', 'Report Viewer'].includes(role)) {
            return res.status(400).json({ 
                error: 'Invalid role. Must be Admin, Data Entry, or Report Viewer' 
            });
        }
        
        // Check if username already exists
        const existingUser = await multiDbService.masterGet(
            'SELECT id FROM users WHERE username = ?',
            [username]
        );
        
        if (existingUser) {
            return res.status(409).json({ error: 'Username already exists' });
        }
        
        // Hash password
        const passwordHash = await bcrypt.hash(password, 10);
        const recoveryKey = `rk_${Math.random().toString(36).substring(2, 15)}`;
        const recoveryKeyHash = await bcrypt.hash(recoveryKey, 10);
        
        const userId = uuidv4();
        
        // Create user
        await multiDbService.masterRun(
            `INSERT INTO users (id, username, password_hash, role, recovery_key_hash, has_saved_recovery_key)
             VALUES (?, ?, ?, ?, ?, ?)`,
            [userId, username, passwordHash, role, recoveryKeyHash, false]
        );
        
        // Grant company access if provided
        if (companyAccess && Array.isArray(companyAccess)) {
            for (const access of companyAccess) {
                await multiDbService.masterRun(
                    `INSERT INTO user_company_access (id, user_id, company_id, access_level, granted_by)
                     VALUES (?, ?, ?, ?, ?)`,
                    [uuidv4(), userId, access.companyId, access.accessLevel, 'system']
                );
            }
        }
        
        res.status(201).json({
            id: userId,
            username: username,
            role: role,
            recoveryKey: recoveryKey,
            message: 'User created successfully'
        });
    } catch (error) {
        console.error('Error creating user:', error);
        res.status(500).json({ error: 'Failed to create user' });
    }
});

// Update user
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { username, role, isActive } = req.body;
        
        const user = await multiDbService.masterGet('SELECT id FROM users WHERE id = ?', [id]);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        await multiDbService.masterRun(
            `UPDATE users SET username = ?, role = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
             WHERE id = ?`,
            [username, role, isActive, id]
        );
        
        res.json({ message: 'User updated successfully' });
    } catch (error) {
        console.error('Error updating user:', error);
        if (error.message.includes('UNIQUE constraint failed')) {
            res.status(409).json({ error: 'Username already exists' });
        } else {
            res.status(500).json({ error: 'Failed to update user' });
        }
    }
});

// Change user password
router.put('/:id/password', async (req, res) => {
    try {
        const { id } = req.params;
        const { currentPassword, newPassword } = req.body;
        
        if (!currentPassword || !newPassword) {
            return res.status(400).json({ 
                error: 'Current password and new password are required' 
            });
        }
        
        const user = await multiDbService.masterGet(
            'SELECT id, password_hash FROM users WHERE id = ?',
            [id]
        );
        
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        // Verify current password
        const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
        if (!isValidPassword) {
            return res.status(401).json({ error: 'Current password is incorrect' });
        }
        
        // Hash new password
        const newPasswordHash = await bcrypt.hash(newPassword, 10);
        
        await multiDbService.masterRun(
            'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [newPasswordHash, id]
        );
        
        res.json({ message: 'Password updated successfully' });
    } catch (error) {
        console.error('Error updating password:', error);
        res.status(500).json({ error: 'Failed to update password' });
    }
});

// Grant company access to user
router.post('/:id/company-access', async (req, res) => {
    try {
        const { id } = req.params;
        const { companyId, accessLevel, grantedBy } = req.body;
        
        if (!companyId || !accessLevel) {
            return res.status(400).json({ 
                error: 'Company ID and access level are required' 
            });
        }
        
        // Validate access level
        if (!['full', 'read_only', 'reports_only'].includes(accessLevel)) {
            return res.status(400).json({ 
                error: 'Invalid access level. Must be full, read_only, or reports_only' 
            });
        }
        
        // Check if user exists
        const user = await multiDbService.masterGet('SELECT id FROM users WHERE id = ?', [id]);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        // Check if company exists
        const company = await multiDbService.masterGet('SELECT id FROM companies WHERE id = ?', [companyId]);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // Check if access already exists
        const existingAccess = await multiDbService.masterGet(
            'SELECT id FROM user_company_access WHERE user_id = ? AND company_id = ?',
            [id, companyId]
        );
        
        if (existingAccess) {
            // Update existing access
            await multiDbService.masterRun(
                'UPDATE user_company_access SET access_level = ?, granted_by = ?, granted_at = CURRENT_TIMESTAMP WHERE user_id = ? AND company_id = ?',
                [accessLevel, grantedBy, id, companyId]
            );
        } else {
            // Create new access
            await multiDbService.masterRun(
                `INSERT INTO user_company_access (id, user_id, company_id, access_level, granted_by)
                 VALUES (?, ?, ?, ?, ?)`,
                [uuidv4(), id, companyId, accessLevel, grantedBy]
            );
        }
        
        res.json({ message: 'Company access granted successfully' });
    } catch (error) {
        console.error('Error granting company access:', error);
        res.status(500).json({ error: 'Failed to grant company access' });
    }
});

// Revoke company access from user
router.delete('/:id/company-access/:companyId', async (req, res) => {
    try {
        const { id, companyId } = req.params;
        
        const result = await multiDbService.masterRun(
            'DELETE FROM user_company_access WHERE user_id = ? AND company_id = ?',
            [id, companyId]
        );
        
        if (result.changes === 0) {
            return res.status(404).json({ error: 'Company access not found' });
        }
        
        res.json({ message: 'Company access revoked successfully' });
    } catch (error) {
        console.error('Error revoking company access:', error);
        res.status(500).json({ error: 'Failed to revoke company access' });
    }
});

// User login (authenticate)
router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        
        if (!username || !password) {
            return res.status(400).json({ 
                error: 'Username and password are required' 
            });
        }
        
        const user = await multiDbService.masterGet(
            'SELECT id, username, password_hash, role, is_active FROM users WHERE username = ?',
            [username]
        );
        
        if (!user || !user.is_active) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        const isValidPassword = await bcrypt.compare(password, user.password_hash);
        if (!isValidPassword) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        // Get user's company access
        const companyAccess = await multiDbService.masterAll(
            `SELECT uca.company_id, c.company_name, uca.access_level
             FROM user_company_access uca
             JOIN companies c ON uca.company_id = c.id
             WHERE uca.user_id = ? AND c.is_active = true`,
            [user.id]
        );
        
        // Log the login in global audit logs
        await multiDbService.masterRun(
            `INSERT INTO global_audit_logs (id, user_id, username, action, details)
             VALUES (?, ?, ?, ?, ?)`,
            [uuidv4(), user.id, user.username, 'LOGIN', 'User logged in successfully']
        );
        
        res.json({
            id: user.id,
            username: user.username,
            role: user.role,
            companyAccess: companyAccess,
            message: 'Login successful'
        });
    } catch (error) {
        console.error('Error during login:', error);
        res.status(500).json({ error: 'Login failed' });
    }
});

// Check user access to specific company
router.get('/:id/access/:companyId', async (req, res) => {
    try {
        const { id, companyId } = req.params;
        
        const access = await multiDbService.masterGet(
            `SELECT uca.access_level, c.company_name
             FROM user_company_access uca
             JOIN companies c ON uca.company_id = c.id
             WHERE uca.user_id = ? AND uca.company_id = ? AND c.is_active = true`,
            [id, companyId]
        );
        
        if (!access) {
            return res.status(403).json({ error: 'Access denied to this company' });
        }
        
        res.json(access);
    } catch (error) {
        console.error('Error checking user access:', error);
        res.status(500).json({ error: 'Failed to check access' });
    }
});

// Deactivate user
router.put('/:id/deactivate', async (req, res) => {
    try {
        const { id } = req.params;
        
        const user = await multiDbService.masterGet('SELECT id FROM users WHERE id = ?', [id]);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        await multiDbService.masterRun(
            'UPDATE users SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [id]
        );
        
        res.json({ message: 'User deactivated successfully' });
    } catch (error) {
        console.error('Error deactivating user:', error);
        res.status(500).json({ error: 'Failed to deactivate user' });
    }
});

export default router;