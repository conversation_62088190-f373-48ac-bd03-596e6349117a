import express from 'express';
import dbService from '../services/database-new.js';

const router = express.Router();

// Middleware to check admin role (simplified - in real app you'd check JWT)
const requireAdmin = (req, res, next) => {
    // This would normally check user role from JW<PERSON> token
    // For now, we'll just proceed - implement proper auth in production
    next();
};

// Get migration status
router.get('/migration-status', requireAdmin, async (req, res) => {
    try {
        const status = await dbService.getMigrationStatus();
        
        res.json({
            needsMigration: status.needsMigration,
            hasOldDatabase: status.hasOldDatabase,
            companiesInNewStructure: status.companiesInNewStructure,
            oldDatabasePath: status.oldDatabasePath,
            systemReady: await dbService.isSystemReady()
        });
    } catch (error) {
        console.error('Error checking migration status:', error);
        res.status(500).json({ error: 'Failed to check migration status' });
    }
});

// Run migration
router.post('/migrate', requireAdmin, async (req, res) => {
    try {
        const { force = false } = req.body;
        
        // Check if migration is needed
        const status = await dbService.getMigrationStatus();
        
        if (!status.hasOldDatabase) {
            return res.status(400).json({ 
                error: 'No old database found',
                message: 'There is no old database to migrate from.'
            });
        }

        if (!status.needsMigration && !force) {
            return res.status(400).json({
                error: 'Migration not needed',
                message: 'System already has companies in new structure. Use force=true to migrate anyway.',
                companiesInNewStructure: status.companiesInNewStructure
            });
        }

        console.log('🔄 Starting database migration via API...');
        
        // Run migration
        const result = await dbService.runMigration();
        
        if (result.success) {
            res.json({
                success: true,
                message: 'Migration completed successfully',
                results: result.results
            });
        } else {
            res.status(500).json({
                success: false,
                message: result.message,
                error: result.error?.message
            });
        }
        
    } catch (error) {
        console.error('Error running migration:', error);
        res.status(500).json({ 
            success: false,
            error: 'Migration failed',
            message: error.message 
        });
    }
});

// Get database structure info
router.get('/database-info', requireAdmin, async (req, res) => {
    try {
        const companies = await dbService.getAllCompanies();
        const users = await dbService.getUsers();
        
        const info = {
            masterDatabase: {
                companies: companies.length,
                users: users.length
            },
            companyDatabases: [],
            totalCompanies: companies.length
        };

        // Get info for each company database
        for (const company of companies) {
            try {
                await dbService.setCompanyContext(company.id);
                
                const assets = await dbService.all('SELECT COUNT(*) as count FROM assets');
                const financialYears = await dbService.all('SELECT COUNT(*) as count FROM financial_years');
                const statutoryRates = await dbService.all('SELECT COUNT(*) as count FROM statutory_rates');
                
                info.companyDatabases.push({
                    id: company.id,
                    name: company.name,
                    assets: assets[0]?.count || 0,
                    financialYears: financialYears[0]?.count || 0,
                    statutoryRates: statutoryRates[0]?.count || 0
                });
            } catch (error) {
                console.error(`Error getting info for company ${company.id}:`, error);
                info.companyDatabases.push({
                    id: company.id,
                    name: company.name,
                    error: error.message
                });
            }
        }
        
        res.json(info);
    } catch (error) {
        console.error('Error getting database info:', error);
        res.status(500).json({ error: 'Failed to get database info' });
    }
});

// Test company database creation
router.post('/test-company', requireAdmin, async (req, res) => {
    try {
        const testCompanyData = {
            companyName: 'Test Company ' + Date.now(),
            financialYearStart: '2024-04-01',
            financialYearEnd: '2025-03-31',
            firstDateOfAdoption: '2024-04-01',
            pan: 'TEST123456',
            cin: 'U12345TEST2024PLC123456',
            addressLine1: 'Test Address Line 1',
            city: 'Test City',
            pin: '123456',
            email: '<EMAIL>',
            mobile: '9876543210',
            contactPerson: 'Test Manager'
        };

        const newCompany = await dbService.createCompany(testCompanyData);
        
        // Test database operations
        await dbService.setCompanyContext(newCompany.id);
        
        // Add a test financial year
        await dbService.run(
            'INSERT INTO financial_years (year_range) VALUES (?)',
            ['2024-2025']
        );
        
        // Verify the database works
        const financialYears = await dbService.all('SELECT * FROM financial_years');
        
        res.json({
            success: true,
            message: 'Test company created successfully',
            company: newCompany,
            testData: {
                financialYears: financialYears
            }
        });
        
    } catch (error) {
        console.error('Error creating test company:', error);
        res.status(500).json({ 
            error: 'Failed to create test company',
            message: error.message 
        });
    }
});

// Backup all companies
router.post('/backup-all', requireAdmin, async (req, res) => {
    try {
        const companies = await dbService.getAllCompanies();
        const users = await dbService.getUsers();
        
        const systemBackup = {
            timestamp: new Date().toISOString(),
            version: '2.0',
            masterData: {
                companies: companies,
                users: users.map(user => ({
                    ...user,
                    password: '[REDACTED]' // Don't include passwords in backup
                }))
            },
            companyData: {}
        };

        // Backup each company
        for (const company of companies) {
            try {
                await dbService.setCompanyContext(company.id);
                const companyBackup = await dbService.createCompanyBackup();
                systemBackup.companyData[company.id] = companyBackup;
            } catch (error) {
                console.error(`Error backing up company ${company.id}:`, error);
                systemBackup.companyData[company.id] = { error: error.message };
            }
        }

        res.json({
            success: true,
            message: 'System backup created successfully',
            backup: systemBackup
        });
        
    } catch (error) {
        console.error('Error creating system backup:', error);
        res.status(500).json({ 
            error: 'Failed to create system backup',
            message: error.message 
        });
    }
});

export default router;