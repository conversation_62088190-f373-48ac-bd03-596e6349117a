@echo off
setlocal enabledelayedexpansion

echo =====================================================
echo FAR SIGHTED - CORS FIX APPLIED & RESTART
echo Professional Advisory Services - CA
echo =====================================================
echo.

echo ✅ CORS ISSUE FIXED!
echo.
echo 📋 What was fixed:
echo    • CORS configuration moved BEFORE routes (critical!)
echo    • Added frontend port 9090 to CORS origins
echo    • Added proper headers and methods
echo    • Added CORS debugging to health endpoint
echo.

cd /d "E:\Projects\FAR Sighted"

echo 🔧 Step 1: Stopping all processes...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im npm.exe >nul 2>&1
timeout /t 3 /nobreak >nul

echo.
echo 🚀 Step 2: Starting backend with CORS fix...
cd backend
start "FAR Backend (CORS Fixed)" cmd /k "title FAR Backend (CORS Fixed) && echo ======================================= && echo FAR SIGHTED BACKEND (CORS FIXED) && echo CORS is now properly configured! && echo ======================================= && echo. && npm start"
cd ..

echo.
echo ⏳ Step 3: Waiting for backend with CORS fix (12 seconds)...
timeout /t 12 /nobreak >nul

echo.
echo 🔍 Step 4: Testing CORS configuration...
echo 📊 Health check with CORS headers:
curl -s -H "Origin: http://localhost:9090" http://localhost:8090/api/health
echo.
echo ========================

echo.
echo 🏢 Step 5: Testing companies API with CORS...
curl -s -H "Origin: http://localhost:9090" -H "Content-Type: application/json" http://localhost:8090/api/companies
echo.
echo ========================

if %errorlevel% == 0 (
    echo ✅ CORS is now working! Starting frontend...
    echo.
    echo 🖥️  Step 6: Starting frontend...
    start "FAR Frontend" cmd /k "title FAR Frontend && npm run dev"
    
    echo.
    echo ⏳ Waiting for frontend (8 seconds)...
    timeout /t 8 /nobreak >nul
    
    echo.
    echo 🌐 Opening application...
    start http://localhost:9090
    
    echo.
    echo ✅ CORS FIX COMPLETE!
    echo.
    echo 🎯 Expected Results:
    echo    • No more CORS errors in browser console
    echo    • Company dropdown should now populate
    echo    • All API calls from frontend should work
    echo.
    echo 📋 Check browser Developer Tools (F12):
    echo    • Network tab should show successful /api/companies request
    echo    • Console should have no CORS errors
    echo    • Response should contain company data
    
) else (
    echo ❌ Backend may still be starting
    echo 💡 Check the "FAR Backend (CORS Fixed)" window
    echo 💡 Look for "CORS Origins configured" message in logs
)

echo.
echo 🔧 CORS Configuration Details:
echo    Backend Port: 8090
echo    Frontend Port: 9090 (and others)
echo    CORS Origins: http://localhost:9090, etc.
echo    Methods: GET, POST, PUT, DELETE, OPTIONS
echo    Headers: Content-Type, Authorization, Accept
echo.

echo 💡 If dropdown still empty, check:
echo    1. Browser console for any remaining errors
echo    2. Network tab - companies API should return data
echo    3. Backend logs for any API errors
echo.

pause
