@echo off
echo =============================================
echo FAR SIGHTED - STEP BY STEP DROPDOWN FIX
echo Professional Advisory Services - CA
echo =============================================
echo.

echo 🎯 SYSTEMATIC APPROACH TO FIX DROPDOWN
echo.

echo Step 1: Check if backend is running
echo ====================================
curl -s http://localhost:8090/api/health >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Backend is running
) else (
    echo ❌ Backend NOT running
    echo 💡 FIX: cd backend && npm start
    echo.
    pause
    exit /b 1
)

echo.
echo Step 2: Test companies API directly
echo ===================================
echo 📊 API Response:
curl -s http://localhost:8090/api/companies
echo.

REM Save response to check it
curl -s http://localhost:8090/api/companies > api_response.tmp

echo.
echo Step 3: Analyze the response
echo ============================
findstr "migration required" api_response.tmp >nul 2>&1
if %errorlevel% == 0 (
    echo ❌ ISSUE: Migration required
    echo 🔧 FIXING: Running migration...
    cd backend
    npm run migrate
    echo.
    echo ✅ Migration complete. Testing API again...
    cd ..
    curl -s http://localhost:8090/api/companies
    echo.
    goto :step4
)

findstr "^\[\]$" api_response.tmp >nul 2>&1
if %errorlevel% == 0 (
    echo ❌ ISSUE: Empty database
    echo 🔧 FIXING: Creating test company...
    curl -X POST http://localhost:8090/api/companies -H "Content-Type: application/json" -d "{\"companyName\":\"Test Company Ltd\",\"financialYearStart\":\"2024-04-01\",\"financialYearEnd\":\"2025-03-31\",\"firstDateOfAdoption\":\"2024-04-01\",\"pan\":\"TEST123456\",\"city\":\"Mumbai\"}" >nul 2>&1
    echo ✅ Test company created. Testing API again...
    curl -s http://localhost:8090/api/companies
    echo.
    goto :step4
)

findstr "\"id\":" api_response.tmp >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ GOOD: API returns company data
    echo 📊 Companies found in API response
    goto :step4
) else (
    echo ❌ ISSUE: Unexpected API response
    echo 📋 Response content:
    type api_response.tmp
    echo.
    echo 💡 Check backend logs for errors
    pause
    exit /b 1
)

:step4
echo.
echo Step 4: Test frontend
echo ====================
curl -s http://localhost:9090 >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Frontend is running
) else (
    echo ❌ Frontend NOT running
    echo 🔧 FIXING: Starting frontend...
    start "FAR Frontend" cmd /k "npm run dev"
    echo ⏳ Waiting for frontend to start...
    timeout /t 10 /nobreak >nul
)

echo.
echo Step 5: Test CORS (Cross-Origin Request)
echo ========================================
curl -s -H "Origin: http://localhost:9090" http://localhost:8090/api/companies >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ CORS is working
) else (
    echo ❌ CORS issue detected
    echo 💡 Backend may not be configured for frontend port
)

echo.
echo Step 6: Open browser and check
echo ==============================
echo 🌐 Opening application...
start http://localhost:9090

echo.
echo 📋 MANUAL CHECKS TO DO IN BROWSER:
echo 1. Press F12 to open Developer Tools
echo 2. Go to Network tab
echo 3. Refresh page
echo 4. Look for request to /api/companies
echo 5. Check if it returns data or error
echo 6. Go to Console tab - look for JavaScript errors
echo.

echo Step 7: Final verification
echo ==========================
echo 🔍 Final API test...
curl -s http://localhost:8090/api/companies
echo.

findstr "\"id\":" api_response.tmp >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ BACKEND: Companies API is working correctly
    echo.
    echo 💡 If dropdown is still empty, the issue is in FRONTEND:
    echo    • Check browser console (F12) for errors
    echo    • Check Network tab for failed requests  
    echo    • Verify frontend is calling the right URL
    echo    • Look for CORS or JavaScript errors
    echo.
    echo 🎯 The backend is working - focus on frontend debugging
) else (
    echo ❌ BACKEND: Still has issues
    echo 💡 Check backend console for errors
    echo 💡 Verify migration completed successfully
)

echo.
echo ✅ DIAGNOSIS COMPLETE
echo 💡 Follow the guidance above to resolve the remaining issues
echo.

REM Cleanup
if exist api_response.tmp del api_response.tmp

pause
