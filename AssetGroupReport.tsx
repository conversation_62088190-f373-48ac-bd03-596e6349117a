/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo } from 'react';
import { api } from './lib/api';
import type { Asset } from './lib/db-server';
import { DataViewProps, AssetGroupReportRow } from './lib/types';
import { formatIndianNumber, inclusiveDateDiffInDays, sortData, exportToExcel } from './lib/utils';
import { DownloadIcon } from './Icons';

// This calculation logic is very similar to ScheduleIII, but tailored for this report's grouping
const calculateAssetMetrics = (asset: Asset, fyStart: Date, fyEnd: Date) => {
    const putToUseDate = new Date(asset.putToUseDate);
    const disposalDate = asset.disposalDate ? new Date(asset.disposalDate) : null;
    const grossAmount = asset.grossAmount || 0;
    const lifeInYears = asset.lifeInYears || 0;

    const openingGross = (putToUseDate < fyStart && (!disposalDate || disposalDate >= fyStart)) ? grossAmount : 0;
    const additionsGross = (putToUseDate >= fyStart && putToUseDate <= fyEnd) ? grossAmount : 0;
    const deletionsGross = (disposalDate && disposalDate >= fyStart && disposalDate <= fyEnd) ? grossAmount : 0;

    let openingDepreciation = 0;
    let additionsDepreciation = 0;
    let deletionsDepreciation = 0;
    
    if (lifeInYears > 0) {
        const salvageValue = Math.round(grossAmount * ((asset.salvagePercentage || 0) / 100));
        const depreciableBase = grossAmount - salvageValue;
        const dailyDepreciation = depreciableBase > 0 ? depreciableBase / (lifeInYears * 365.25) : 0;
        
        if (putToUseDate < fyStart) {
            const effectiveEndDate = disposalDate && disposalDate < fyStart ? disposalDate : new Date(fyStart.getTime() - 86400000);
            const daysOfDepreciationBeforeFY = inclusiveDateDiffInDays(putToUseDate, effectiveEndDate);
            openingDepreciation = Math.round(Math.min(depreciableBase, Math.max(0, daysOfDepreciationBeforeFY * dailyDepreciation)));
        }

        const startOfUseInFY = putToUseDate > fyStart ? putToUseDate : fyStart;
        const endOfUseInFY = disposalDate && disposalDate < fyEnd ? disposalDate : fyEnd;
        
        if (endOfUseInFY >= startOfUseInFY && putToUseDate <= endOfUseInFY) {
            const useDaysInFY = inclusiveDateDiffInDays(startOfUseInFY, endOfUseInFY);
            const currentYearDepreciation = useDaysInFY * dailyDepreciation;
            additionsDepreciation = Math.round(Math.min(currentYearDepreciation, Math.max(0, depreciableBase - openingDepreciation)));
        }

        if (deletionsGross > 0 && disposalDate) {
            deletionsDepreciation = openingDepreciation + additionsDepreciation;
        }
    }
    
    return { openingGross, additionsGross, deletionsGross, openingDepreciation, additionsDepreciation, deletionsDepreciation };
}


export function AssetGroupReport({ companyId, companyName, year, showAlert }: DataViewProps) {
    const [reportData, setReportData] = useState<AssetGroupReportRow[]>([]);
    const [totals, setTotals] = useState<AssetGroupReportRow | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [sortConfig, setSortConfig] = useState<{ key: keyof AssetGroupReportRow; direction: 'asc' | 'desc' } | null>({ key: 'assetGroup', direction: 'asc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);
    
    useEffect(() => {
        const calculateReport = async () => {
            if (!companyId || !year) {
                setReportData([]); setTotals(null); setLoading(false); return;
            }
            setLoading(true); 
            setError(null);
            setSelectedRowId(null);
            setSelectedColumnKey(null);

            try {
                const assets = await api.getAssets(companyId);
                if (!assets || assets.length === 0) {
                    setReportData([]); setTotals(null); setLoading(false); return;
                }

                const [startYearStr, endYearStr] = year.split('-');
                const fyStart = new Date(`${startYearStr}-04-01T00:00:00.000Z`);
                const fyEnd = new Date(`${endYearStr}-03-31T23:59:59.999Z`);

                const reportMap: Record<string, AssetGroupReportRow> = {};

                for (const asset of assets) {
                    if (!asset.assetGroup || !asset.assetSubGroup || !asset.putToUseDate) continue;
                    
                    const groupKey = `${asset.assetGroup}|${asset.assetSubGroup}`;
                    if (!reportMap[groupKey]) {
                        reportMap[groupKey] = { assetGroup: asset.assetGroup, assetSubGroup: asset.assetSubGroup, openingGross: 0, additionsGross: 0, deletionsGross: 0, closingGross: 0, openingDepreciation: 0, additionsDepreciation: 0, deletionsDepreciation: 0, closingDepreciation: 0, openingNetBlock: 0, closingNetBlock: 0 };
                    }
                    
                    const metrics = calculateAssetMetrics(asset, fyStart, fyEnd);
                    reportMap[groupKey].openingGross += metrics.openingGross;
                    reportMap[groupKey].additionsGross += metrics.additionsGross;
                    reportMap[groupKey].deletionsGross += metrics.deletionsGross;
                    reportMap[groupKey].openingDepreciation += metrics.openingDepreciation;
                    reportMap[groupKey].additionsDepreciation += metrics.additionsDepreciation;
                    reportMap[groupKey].deletionsDepreciation += metrics.deletionsDepreciation;
                }

                let finalReportData: AssetGroupReportRow[] = Object.values(reportMap).map(row => {
                    row.closingGross = row.openingGross + row.additionsGross - row.deletionsGross;
                    row.closingDepreciation = row.openingDepreciation + row.additionsDepreciation - row.deletionsDepreciation;
                    row.openingNetBlock = row.openingGross - row.openingDepreciation;
                    row.closingNetBlock = row.closingGross - row.closingDepreciation;
                    return row;
                });
                
                // Filter out rows where all financial columns are zero
                finalReportData = finalReportData.filter(row => 
                    row.openingGross !== 0 || row.additionsGross !== 0 || row.deletionsGross !== 0 ||
                    row.openingDepreciation !== 0 || row.additionsDepreciation !== 0 || row.deletionsDepreciation !== 0
                );

                const totalRow: AssetGroupReportRow = { assetGroup: 'Total', assetSubGroup: '', openingGross: 0, additionsGross: 0, deletionsGross: 0, closingGross: 0, openingDepreciation: 0, additionsDepreciation: 0, deletionsDepreciation: 0, closingDepreciation: 0, openingNetBlock: 0, closingNetBlock: 0 };
                finalReportData.forEach(row => {
                    (Object.keys(row) as Array<keyof AssetGroupReportRow>).forEach(key => {
                        if (key !== 'assetGroup' && key !== 'assetSubGroup') {
                            totalRow[key] += row[key] as number;
                        }
                    });
                });
                
                setReportData(finalReportData);
                setTotals(totalRow);
                
            } catch (err) {
                setError('Failed to generate Asset Group report.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        calculateReport();
    }, [companyId, year]);


    const sortedReportData = useMemo(() => {
        // Custom sort to keep asset groups together
        const dataToSort = [...reportData];
        if (sortConfig) {
            dataToSort.sort((a, b) => {
                if (sortConfig.key === 'assetGroup') {
                    if (a.assetGroup < b.assetGroup) return sortConfig.direction === 'asc' ? -1 : 1;
                    if (a.assetGroup > b.assetGroup) return sortConfig.direction === 'asc' ? 1 : -1;
                    if (a.assetSubGroup < b.assetSubGroup) return -1; // always sort sub-group asc within group
                    if (a.assetSubGroup > b.assetSubGroup) return 1;
                    return 0;
                }

                // Fallback to generic sort for other columns
                const aValue = (a as any)[sortConfig.key];
                const bValue = (b as any)[sortConfig.key];

                if (aValue === null || aValue === undefined) return 1;
                if (bValue === null || bValue === undefined) return -1;

                if (typeof aValue === 'number' && typeof bValue === 'number') {
                    if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
                    if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
                    return 0;
                }
                if(typeof aValue === 'string' && typeof bValue === 'string') {
                    return sortConfig.direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
                }
                return 0;
            });
        }
        return dataToSort;

    }, [reportData, sortConfig]);

    const requestSort = (key: keyof AssetGroupReportRow) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        } else {
             direction = 'asc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: keyof AssetGroupReportRow) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };
    
    const handleExport = () => {
        if (!companyId || !companyName || !year) return;
        
        const dataToExport = sortedReportData.map(row => ({
            "Asset Group": row.assetGroup,
            "Asset Sub-Group": row.assetSubGroup,
            "Gross Block - Opening": row.openingGross,
            "Gross Block - Additions": row.additionsGross,
            "Gross Block - Deletions": row.deletionsGross,
            "Gross Block - Closing": row.closingGross,
            "Depreciation - Opening": row.openingDepreciation,
            "Depreciation - For the Year": row.additionsDepreciation,
            "Depreciation - On Disposals": row.deletionsDepreciation,
            "Depreciation - Closing": row.closingDepreciation,
            "Net Block - Opening": row.openingNetBlock,
            "Net Block - Closing": row.closingNetBlock,
        }));

        if (totals) {
            dataToExport.push({
                "Asset Group": totals.assetGroup,
                "Asset Sub-Group": '',
                "Gross Block - Opening": totals.openingGross,
                "Gross Block - Additions": totals.additionsGross,
                "Gross Block - Deletions": totals.deletionsGross,
                "Gross Block - Closing": totals.closingGross,
                "Depreciation - Opening": totals.openingDepreciation,
                "Depreciation - For the Year": totals.additionsDepreciation,
                "Depreciation - On Disposals": totals.deletionsDepreciation,
                "Depreciation - Closing": totals.closingDepreciation,
                "Net Block - Opening": totals.openingNetBlock,
                "Net Block - Closing": totals.closingNetBlock,
            });
        }
        
        if (!exportToExcel({ data: dataToExport, companyName, year, reportName: 'Asset_Group_Report' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    if (loading) return <div className="loading-indicator">Generating Asset Group Report...</div>;
    if (error) return <div className="error-message">{error}</div>;
    if (!companyId) return null;
    if (reportData.length === 0) return <div className="company-info-container"><p>No asset data available to generate the report for the selected year.</p></div>;

    const getHeaderClass = (key: keyof AssetGroupReportRow) => {
        const classes = ['sortable'];
        if (key !== 'assetGroup' && key !== 'assetSubGroup') classes.push('text-right');
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        return classes.join(' ');
    };

    const getColumnClass = (key: keyof AssetGroupReportRow) => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if (key !== 'assetGroup' && key !== 'assetSubGroup') classes.push('text-right');
        if (['assetGroup', 'assetSubGroup'].includes(key)) classes.push('td-wrap');
        return classes.join(' ');
    };

    return (
        <div className="report-view">
            <div className="view-header">
                <h2>Asset Group Report for FY {year}</h2>
                <div className="actions">
                    <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export to Excel</button>
                </div>
            </div>
            <div className="table-container">
                <table>
                    <thead>
                        <tr>
                            <th className={getHeaderClass('assetGroup')} onClick={() => requestSort('assetGroup')}>Asset Group{getSortIndicator('assetGroup')}</th>
                            <th className={getHeaderClass('assetSubGroup')} onClick={() => requestSort('assetSubGroup')}>Asset Sub-Group{getSortIndicator('assetSubGroup')}</th>
                            <th className={getHeaderClass('openingGross')} onClick={() => requestSort('openingGross')}>Gross Block - Opening{getSortIndicator('openingGross')}</th>
                            <th className={getHeaderClass('additionsGross')} onClick={() => requestSort('additionsGross')}>Gross Block - Additions{getSortIndicator('additionsGross')}</th>
                            <th className={getHeaderClass('deletionsGross')} onClick={() => requestSort('deletionsGross')}>Gross Block - Deletions{getSortIndicator('deletionsGross')}</th>
                            <th className={getHeaderClass('closingGross')} onClick={() => requestSort('closingGross')}>Gross Block - Closing{getSortIndicator('closingGross')}</th>
                            <th className={getHeaderClass('openingDepreciation')} onClick={() => requestSort('openingDepreciation')}>Depreciation - Opening{getSortIndicator('openingDepreciation')}</th>
                            <th className={getHeaderClass('additionsDepreciation')} onClick={() => requestSort('additionsDepreciation')}>Depreciation - For the Year{getSortIndicator('additionsDepreciation')}</th>
                            <th className={getHeaderClass('deletionsDepreciation')} onClick={() => requestSort('deletionsDepreciation')}>Depreciation - On Disposals{getSortIndicator('deletionsDepreciation')}</th>
                            <th className={getHeaderClass('closingDepreciation')} onClick={() => requestSort('closingDepreciation')}>Depreciation - Closing{getSortIndicator('closingDepreciation')}</th>
                            <th className={getHeaderClass('openingNetBlock')} onClick={() => requestSort('openingNetBlock')}>Net Block - Opening{getSortIndicator('openingNetBlock')}</th>
                            <th className={getHeaderClass('closingNetBlock')} onClick={() => requestSort('closingNetBlock')}>Net Block - Closing{getSortIndicator('closingNetBlock')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {sortedReportData.map((row) => {
                            const rowKey = `${row.assetGroup}-${row.assetSubGroup}`;
                            return (
                                <tr key={rowKey} onClick={() => setSelectedRowId(rowKey)} className={rowKey === selectedRowId ? 'selected-row' : ''}>
                                    <td className={getColumnClass('assetGroup')}>{row.assetGroup}</td>
                                    <td className={getColumnClass('assetSubGroup')}>{row.assetSubGroup}</td>
                                    <td className={getColumnClass('openingGross')}>{formatIndianNumber(row.openingGross)}</td>
                                    <td className={getColumnClass('additionsGross')}>{formatIndianNumber(row.additionsGross)}</td>
                                    <td className={getColumnClass('deletionsGross')}>{formatIndianNumber(row.deletionsGross)}</td>
                                    <td className={getColumnClass('closingGross')}>{formatIndianNumber(row.closingGross)}</td>
                                    <td className={getColumnClass('openingDepreciation')}>{formatIndianNumber(row.openingDepreciation)}</td>
                                    <td className={getColumnClass('additionsDepreciation')}>{formatIndianNumber(row.additionsDepreciation)}</td>
                                    <td className={getColumnClass('deletionsDepreciation')}>{formatIndianNumber(row.deletionsDepreciation)}</td>
                                    <td className={getColumnClass('closingDepreciation')}>{formatIndianNumber(row.closingDepreciation)}</td>
                                    <td className={getColumnClass('openingNetBlock')}>{formatIndianNumber(row.openingNetBlock)}</td>
                                    <td className={getColumnClass('closingNetBlock')}>{formatIndianNumber(row.closingNetBlock)}</td>
                                </tr>
                            );
                        })}
                    </tbody>
                    {totals && (
                        <tfoot>
                            <tr>
                                <td colSpan={2} className={getColumnClass('assetGroup')}>{totals.assetGroup}</td>
                                <td className={getColumnClass('openingGross')}>{formatIndianNumber(totals.openingGross)}</td>
                                <td className={getColumnClass('additionsGross')}>{formatIndianNumber(totals.additionsGross)}</td>
                                <td className={getColumnClass('deletionsGross')}>{formatIndianNumber(totals.deletionsGross)}</td>
                                <td className={getColumnClass('closingGross')}>{formatIndianNumber(totals.closingGross)}</td>
                                <td className={getColumnClass('openingDepreciation')}>{formatIndianNumber(totals.openingDepreciation)}</td>
                                <td className={getColumnClass('additionsDepreciation')}>{formatIndianNumber(totals.additionsDepreciation)}</td>
                                <td className={getColumnClass('deletionsDepreciation')}>{formatIndianNumber(totals.deletionsDepreciation)}</td>
                                <td className={getColumnClass('closingDepreciation')}>{formatIndianNumber(totals.closingDepreciation)}</td>
                                <td className={getColumnClass('openingNetBlock')}>{formatIndianNumber(totals.openingNetBlock)}</td>
                                <td className={getColumnClass('closingNetBlock')}>{formatIndianNumber(totals.closingNetBlock)}</td>
                            </tr>
                        </tfoot>
                    )}
                </table>
            </div>
        </div>
    );
}
