
/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { api } from './lib/api';
import type { User } from './lib/db-server';
import { DataViewProps } from './lib/types';
import { sortData, exportToExcel } from './lib/utils';
import { PlusIcon, DownloadIcon, EditIcon, TrashIcon } from './Icons';

export function UserList({ companyId, companyName, year, onEditUser, onAddUserNav, showAlert, showConfirmation, loggedInUser }: DataViewProps) {
    const [users, setUsers] = useState<User[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [sortConfig, setSortConfig] = useState<{ key: keyof User; direction: 'asc' | 'desc' } | null>({ key: 'username', direction: 'asc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);

    const fetchUsers = useCallback(async () => {
        if (!companyId) {
            setLoading(false);
            return;
        }

        setLoading(true);
        setError(null);
        setSelectedRowId(null);
        setSelectedColumnKey(null);
        try {
            const userList = await api.getUsers(companyId);
            setUsers(userList);
        } catch (err) {
            setError('Failed to fetch users.');
            console.error(err);
        } finally {
            setLoading(false);
        }
    }, [companyId]);

    useEffect(() => {
        fetchUsers();
    }, [fetchUsers]);

    const sortedUsers = useMemo(() => {
        // Filter out users with malformed IDs - accept both 'u' and 'user_' prefixes
        const validUsers = users.filter(user => user.id && (user.id.startsWith('u') || user.id.startsWith('user_') || user.id.includes('-')));
        return sortData(validUsers, sortConfig);
    }, [users, sortConfig]);

    const requestSort = (key: keyof User) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: keyof User) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };

    const getHeaderClass = (key: keyof User | 'actions') => {
        const classes = [];
        if(key !== 'actions') classes.push('sortable');
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        return classes.join(' ');
    };

    const getColumnClass = (key: keyof User | 'actions') => {
        return selectedColumnKey === key ? 'selected-col' : '';
    };

    const handleDelete = async (userId: string, username: string) => {
        showConfirmation(
            'Confirm Deletion',
            `Are you sure you want to delete the user "${username}"?`,
            async () => {
                try {
                    if (loggedInUser && companyId) {
                        await api.addAuditLog(companyId, { userId: loggedInUser.id, username: loggedInUser.username, action: 'DELETE_USER', details: `Deleted user "${username}" (ID: ${userId}).` });
                    }
                    await api.deleteUser(userId);
                    fetchUsers(); // Refresh the list
                } catch (err) {
                    showAlert('Error', 'Failed to delete user.', 'error');
                    console.error(err);
                }
            }
        );
    };
    
    const handleExport = () => {
        const dataToExport = sortedUsers.map(user => ({
            "Username": user.username,
            "Role": user.role,
        }));
        
        if (!exportToExcel({ data: dataToExport, companyName: companyName || "System", year, reportName: "User_List" })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    if (loading) return <div className="loading-indicator">Loading Users...</div>;
    if (error) return <div className="error-message">{error}</div>;

    return (
        <>
            <div className="view-header">
                <h2>User List</h2>
                <div className="actions">
                    <button type="button" className="btn btn-primary" onClick={onAddUserNav}><PlusIcon /> Add User</button>
                    <button type="button" className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export to Excel</button>
                </div>
            </div>
            <div className="table-container">
                <table>
                    <thead>
                        <tr>
                            <th className={getHeaderClass('username')} onClick={() => requestSort('username')}>Username{getSortIndicator('username')}</th>
                            <th className={getHeaderClass('role')} onClick={() => requestSort('role')}>Role{getSortIndicator('role')}</th>
                            <th className={`text-right ${getColumnClass('actions')}`}>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {sortedUsers.map(user => (
                            <tr key={user.id} onClick={() => setSelectedRowId(user.id)} className={user.id === selectedRowId ? 'selected-row' : ''}>
                                <td className={getColumnClass('username')}>{user.username}</td>
                                <td className={getColumnClass('role')}>{user.role}</td>
                                <td className={`text-right ${getColumnClass('actions')}`}>
                                    <div className="action-buttons-cell action-buttons-right">
                                        <button type="button" className="btn btn-secondary" onClick={() => onEditUser && onEditUser(user.id)}><EditIcon /> Edit</button>
                                        <button type="button" className="btn-icon btn-delete" title="Delete user" onClick={() => handleDelete(user.id, user.username)}>
                                           <TrashIcon />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </>
    );
}