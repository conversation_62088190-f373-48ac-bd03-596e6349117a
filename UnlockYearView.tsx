/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { FC } from 'react';
import { DataViewProps } from './types';
import { UnlockIcon, AlertTriangleIcon } from './Icons';

export const UnlockYearView: FC<DataViewProps> = ({ companyId, financialYears, unlockedYear, onUnlockYear, showAlert }) => {
    if (!companyId) {
        return (
            <>
                <div className="view-header"><h2>Unlock Financial Year</h2></div>
                <div className="company-info-container"><p>Please select a company to manage financial years.</p></div>
            </>
        );
    }

    if (unlockedYear) {
        return (
            <>
                <div className="view-header"><h2>Unlock Financial Year</h2></div>
                <div className="settings-container">
                    <h3 className="settings-title">A Year is Currently Unlocked</h3>
                    <p className="settings-description">
                        Financial year <strong>{unlockedYear}</strong> is currently unlocked for editing.
                        You must use the "Lock & Recalculate" button in the header to finalize your changes before you can unlock another year.
                    </p>
                </div>
            </>
        );
    }

    const latestYear = financialYears.length > 0 ? financialYears[financialYears.length - 1] : null;
    const unlockableYears = financialYears.filter(fy => fy !== latestYear);

    const handleUnlockClick = (yearToUnlock: string) => {
        if (onUnlockYear) {
            onUnlockYear(yearToUnlock);
        } else {
            showAlert?.('Error', 'Unlock function is not available.', 'error');
        }
    };

    return (
        <>
            <div className="view-header">
                <h2>Unlock Financial Year</h2>
            </div>
            <div className="settings-container" style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
                <section>
                    <h3 className="settings-title">Unlock a Past Financial Year for Editing</h3>
                     <div className="manual-infobox" style={{ margin: '1rem 0', borderColor: 'var(--accent-warning)', backgroundColor: 'rgba(249, 115, 22, 0.1)' }}>
                        <div className="manual-infobox-header">
                            <AlertTriangleIcon size={20} className="icon-warning" />
                            <h4>High-Risk Admin Action</h4>
                        </div>
                        <div className="manual-infobox-content">
                            <p>Unlocking a finalized year allows for historical data corrections. This action will:</p>
                            <ul className="manual-list" style={{ marginTop: '0.5rem' }}>
                                <li>Require administrator password verification.</li>
                                <li>Automatically create a full system backup before proceeding.</li>
                                <li>Lock navigation to other companies/years until the unlocked year is re-locked.</li>
                                <li>Require you to use the "Lock & Recalculate" button to save changes, which will recalculate all subsequent years.</li>
                            </ul>
                        </div>
                    </div>
                    {unlockableYears.length > 0 ? (
                        <div className="table-container" style={{maxHeight: '400px', marginTop: '1.5rem'}}>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Financial Year</th>
                                        <th className="text-right">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {unlockableYears.map(fy => (
                                        <tr key={fy}>
                                            <td>{fy}</td>
                                            <td className="text-right">
                                                <button className="btn btn-warning" onClick={() => handleUnlockClick(fy)}>
                                                    <UnlockIcon /> Unlock
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        <p className="settings-description" style={{marginTop: '1.5rem'}}>There are no past financial years available to unlock.</p>
                    )}
                </section>
            </div>
        </>
    );
};
