/**
 * MULTI-COMPANY DATABASE MANAGER
 * Professional Advisory Services - Chartered Accountant
 * 
 * This service manages separate databases for each company,
 * providing data isolation, security, and compliance.
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class DatabaseManager {
    constructor() {
        this.masterDb = null;
        this.companyDbs = new Map(); // Cache of company database connections
        this.dbPath = join(__dirname, '../database');
        this.masterDbPath = join(this.dbPath, 'master.db');
        this.initializeMaster();
    }

    // Initialize master database
    async initializeMaster() {
        try {
            // Ensure database directory exists
            await fs.mkdir(this.dbPath, { recursive: true });
            
            this.masterDb = new sqlite3.Database(this.masterDbPath, (err) => {
                if (err) {
                    console.error('❌ Error connecting to master database:', err.message);
                    throw err;
                }
                console.log('✅ Connected to master database');
            });

            // Enable foreign key constraints
            await this.runOnMaster('PRAGMA foreign_keys = ON');
            
            // Create master database tables
            await this.createMasterTables();
            
        } catch (error) {
            console.error('❌ Failed to initialize master database:', error);
            throw error;
        }
    }

    // Create master database tables
    async createMasterTables() {
        const tables = [
            // Companies registry
            `CREATE TABLE IF NOT EXISTS companies (
                id TEXT PRIMARY KEY,
                company_name TEXT NOT NULL,
                database_name TEXT NOT NULL UNIQUE,
                pan TEXT,
                cin TEXT,
                date_of_incorporation DATE,
                first_date_of_adoption DATE,
                financial_year_start DATE,
                financial_year_end DATE,
                address_line1 TEXT,
                address_line2 TEXT,
                city TEXT,
                pin TEXT,
                email TEXT,
                mobile TEXT,
                contact_person TEXT,
                license_valid_upto DATE,
                status TEXT DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Global user management
            `CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL,
                recovery_key_hash TEXT,
                has_saved_recovery_key BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // User-Company access mapping
            `CREATE TABLE IF NOT EXISTS user_company_access (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                company_id TEXT NOT NULL,
                access_level TEXT DEFAULT 'read',
                granted_by TEXT,
                granted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
                UNIQUE(user_id, company_id)
            )`,

            // System-wide audit logs
            `CREATE TABLE IF NOT EXISTS system_audit_logs (
                id TEXT PRIMARY KEY,
                user_id TEXT,
                username TEXT,
                company_id TEXT,
                action TEXT NOT NULL,
                details TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (company_id) REFERENCES companies(id)
            )`,

            // License management
            `CREATE TABLE IF NOT EXISTS license_history (
                id TEXT PRIMARY KEY,
                company_id TEXT NOT NULL,
                license_key TEXT NOT NULL,
                valid_from DATE NOT NULL,
                valid_upto DATE NOT NULL,
                activated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
            )`
        ];

        for (const table of tables) {
            await this.runOnMaster(table);
        }

        console.log('✅ Master database tables created/verified');
    }

    // Get or create company database connection
    async getCompanyDb(companyId) {
        // Check if connection exists in cache
        if (this.companyDbs.has(companyId)) {
            return this.companyDbs.get(companyId);
        }

        // Verify company exists in master database
        const company = await this.getFromMaster(
            'SELECT * FROM companies WHERE id = ? AND status = ?', 
            [companyId, 'active']
        );

        if (!company) {
            throw new Error(`Company ${companyId} not found or inactive`);
        }

        // Create database connection
        const dbPath = join(this.dbPath, company.database_name);
        const companyDb = new sqlite3.Database(dbPath, (err) => {
            if (err) {
                console.error(`❌ Error connecting to company database ${companyId}:`, err.message);
                throw err;
            }
            console.log(`✅ Connected to company database: ${company.company_name}`);
        });

        // Enable foreign key constraints
        await this.promiseWrapper(companyDb, 'run', 'PRAGMA foreign_keys = ON');

        // Create company database tables if they don't exist
        await this.createCompanyTables(companyDb);

        // Cache the connection
        this.companyDbs.set(companyId, companyDb);

        return companyDb;
    }

    // Create company database tables
    async createCompanyTables(db) {
        const tables = [
            // Company information
            `CREATE TABLE IF NOT EXISTS company_info (
                id TEXT PRIMARY KEY DEFAULT 'main',
                company_name TEXT NOT NULL,
                pan TEXT,
                cin TEXT,
                date_of_incorporation DATE,
                first_date_of_adoption DATE,
                financial_year_start DATE,
                financial_year_end DATE,
                data_folder_path TEXT,
                address_line1 TEXT,
                address_line2 TEXT,
                city TEXT,
                pin TEXT,
                email TEXT,
                mobile TEXT,
                contact_person TEXT,
                license_valid_upto DATE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Financial years
            `CREATE TABLE IF NOT EXISTS financial_years (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                year_range TEXT NOT NULL UNIQUE,
                is_locked BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Assets
            `CREATE TABLE IF NOT EXISTS assets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                record_id TEXT NOT NULL UNIQUE,
                asset_particulars TEXT NOT NULL,
                book_entry_date DATE,
                put_to_use_date DATE,
                basic_amount REAL DEFAULT 0,
                duties_taxes REAL DEFAULT 0,
                gross_amount REAL DEFAULT 0,
                vendor TEXT,
                invoice_no TEXT,
                model_make TEXT,
                location TEXT,
                asset_id TEXT,
                remarks TEXT,
                ledger_name_in_books TEXT,
                asset_group TEXT,
                asset_sub_group TEXT,
                schedule_iii_classification TEXT,
                disposal_date DATE,
                disposal_amount REAL,
                salvage_percentage REAL DEFAULT 5,
                wdv_of_adoption_date REAL,
                is_leasehold BOOLEAN DEFAULT FALSE,
                depreciation_method TEXT DEFAULT 'WDV',
                life_in_years INTEGER,
                lease_period INTEGER,
                scrap_it BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Statutory rates
            `CREATE TABLE IF NOT EXISTS statutory_rates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                is_statutory TEXT DEFAULT 'Yes',
                tangibility TEXT,
                asset_group TEXT,
                asset_sub_group TEXT,
                extra_shift_depreciation TEXT,
                useful_life_years TEXT,
                schedule_ii_classification TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Asset yearly data
            `CREATE TABLE IF NOT EXISTS asset_yearly_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                asset_id INTEGER NOT NULL,
                year_range TEXT NOT NULL,
                opening_wdv REAL DEFAULT 0,
                use_days INTEGER DEFAULT 0,
                depreciation_amount REAL DEFAULT 0,
                closing_wdv REAL DEFAULT 0,
                second_shift_days INTEGER DEFAULT 0,
                third_shift_days INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
                UNIQUE(asset_id, year_range)
            )`,

            // Company-specific audit logs
            `CREATE TABLE IF NOT EXISTS audit_logs (
                id TEXT PRIMARY KEY,
                user_id TEXT,
                username TEXT,
                action TEXT NOT NULL,
                details TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Company-specific backup logs
            `CREATE TABLE IF NOT EXISTS backup_logs (
                id TEXT PRIMARY KEY,
                action TEXT NOT NULL,
                initiated_by TEXT,
                details TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Extra ledgers
            `CREATE TABLE IF NOT EXISTS extra_ledgers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ledger_name TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (const table of tables) {
            await this.promiseWrapper(db, 'run', table);
        }
    }

    // Create new company database
    async createCompanyDatabase(companyInfo) {
        const companyId = companyInfo.id;
        const dbName = `company_${companyId}.db`;
        
        try {
            // Insert company into master database
            await this.runOnMaster(
                `INSERT INTO companies (
                    id, company_name, database_name, pan, cin, date_of_incorporation,
                    first_date_of_adoption, financial_year_start, financial_year_end,
                    address_line1, address_line2, city, pin, email, mobile, contact_person,
                    license_valid_upto
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    companyId, companyInfo.company_name, dbName, companyInfo.pan,
                    companyInfo.cin, companyInfo.date_of_incorporation, companyInfo.first_date_of_adoption,
                    companyInfo.financial_year_start, companyInfo.financial_year_end,
                    companyInfo.address_line1, companyInfo.address_line2, companyInfo.city,
                    companyInfo.pin, companyInfo.email, companyInfo.mobile, companyInfo.contact_person,
                    companyInfo.license_valid_upto
                ]
            );

            // Get company database connection (this will create the database file)
            const companyDb = await this.getCompanyDb(companyId);

            // Insert company info into company database
            await this.runOnCompanyDb(companyId,
                `INSERT INTO company_info (
                    id, company_name, pan, cin, date_of_incorporation, first_date_of_adoption,
                    financial_year_start, financial_year_end, address_line1, address_line2,
                    city, pin, email, mobile, contact_person, license_valid_upto
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    'main', companyInfo.company_name, companyInfo.pan, companyInfo.cin,
                    companyInfo.date_of_incorporation, companyInfo.first_date_of_adoption,
                    companyInfo.financial_year_start, companyInfo.financial_year_end,
                    companyInfo.address_line1, companyInfo.address_line2, companyInfo.city,
                    companyInfo.pin, companyInfo.email, companyInfo.mobile, companyInfo.contact_person,
                    companyInfo.license_valid_upto
                ]
            );

            console.log(`✅ Created company database for: ${companyInfo.company_name}`);
            return companyDb;

        } catch (error) {
            console.error(`❌ Failed to create company database for ${companyId}:`, error);
            throw error;
        }
    }

    // Master database operations
    async runOnMaster(sql, params = []) {
        return this.promiseWrapper(this.masterDb, 'run', sql, params);
    }

    async getFromMaster(sql, params = []) {
        return this.promiseWrapper(this.masterDb, 'get', sql, params);
    }

    async allFromMaster(sql, params = []) {
        return this.promiseWrapper(this.masterDb, 'all', sql, params);
    }

    // Company database operations
    async runOnCompanyDb(companyId, sql, params = []) {
        const db = await this.getCompanyDb(companyId);
        return this.promiseWrapper(db, 'run', sql, params);
    }

    async getFromCompanyDb(companyId, sql, params = []) {
        const db = await this.getCompanyDb(companyId);
        return this.promiseWrapper(db, 'get', sql, params);
    }

    async allFromCompanyDb(companyId, sql, params = []) {
        const db = await this.getCompanyDb(companyId);
        return this.promiseWrapper(db, 'all', sql, params);
    }

    // User-Company access management
    async grantUserAccess(userId, companyId, accessLevel = 'read', grantedBy = null) {
        return this.runOnMaster(
            `INSERT OR REPLACE INTO user_company_access 
             (user_id, company_id, access_level, granted_by) 
             VALUES (?, ?, ?, ?)`,
            [userId, companyId, accessLevel, grantedBy]
        );
    }

    async checkUserAccess(userId, companyId) {
        return this.getFromMaster(
            'SELECT access_level FROM user_company_access WHERE user_id = ? AND company_id = ?',
            [userId, companyId]
        );
    }

    async getUserCompanies(userId) {
        return this.allFromMaster(
            `SELECT c.*, uca.access_level 
             FROM companies c 
             JOIN user_company_access uca ON c.id = uca.company_id 
             WHERE uca.user_id = ? AND c.status = 'active'
             ORDER BY c.company_name`,
            [userId]
        );
    }

    // Transaction support
    async beginTransaction(companyId) {
        if (companyId) {
            return this.runOnCompanyDb(companyId, 'BEGIN TRANSACTION');
        } else {
            return this.runOnMaster('BEGIN TRANSACTION');
        }
    }

    async commit(companyId) {
        if (companyId) {
            return this.runOnCompanyDb(companyId, 'COMMIT');
        } else {
            return this.runOnMaster('COMMIT');
        }
    }

    async rollback(companyId) {
        if (companyId) {
            return this.runOnCompanyDb(companyId, 'ROLLBACK');
        } else {
            return this.runOnMaster('ROLLBACK');
        }
    }

    // Utility: Promise wrapper for database operations
    promiseWrapper(db, method, sql, params = []) {
        return new Promise((resolve, reject) => {
            if (method === 'run') {
                db.run(sql, params, function(err) {
                    if (err) reject(err);
                    else resolve({ id: this.lastID, changes: this.changes });
                });
            } else if (method === 'get') {
                db.get(sql, params, (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                });
            } else if (method === 'all') {
                db.all(sql, params, (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                });
            } else {
                reject(new Error(`Unknown method: ${method}`));
            }
        });
    }

    // Close all database connections
    async closeAll() {
        const closePromises = [];

        // Close company databases
        for (const [companyId, db] of this.companyDbs) {
            closePromises.push(
                new Promise((resolve) => {
                    db.close((err) => {
                        if (err) console.error(`Error closing database for ${companyId}:`, err);
                        resolve();
                    });
                })
            );
        }

        // Close master database
        if (this.masterDb) {
            closePromises.push(
                new Promise((resolve) => {
                    this.masterDb.close((err) => {
                        if (err) console.error('Error closing master database:', err);
                        resolve();
                    });
                })
            );
        }

        await Promise.all(closePromises);
        this.companyDbs.clear();
        this.masterDb = null;
        console.log('🔌 All database connections closed');
    }
}

// Export singleton instance
const dbManager = new DatabaseManager();

export default dbManager;