/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React from 'react';
import { CalculationRow } from '../lib/types';
import { formatIndianNumber } from '../lib/utils';

interface DepreciationRateModalProps {
    isOpen: boolean;
    onClose: () => void;
    data: CalculationRow | null;
}

export function DepreciationRateModal({ isOpen, onClose, data }: DepreciationRateModalProps) {
    if (!isOpen || !data) return null;

    // Add body class to prevent scrolling
    React.useEffect(() => {
        document.body.classList.add('modal-open');
        return () => document.body.classList.remove('modal-open');
    }, []);

    const {
        assetParticulars,
        depreciationMethod,
        depreciationRate,
        grossAmount,
        salvageAmount,
        lifeInYears
    } = data;

    let formula = '';
    let calculation = '';

    if (depreciationMethod === 'WDV') {
        formula = 'Rate = 1 - ( (Salvage / Gross) ^ (1 / Life) )';
        calculation = `Rate = 1 - ( (${formatIndianNumber(salvageAmount)} / ${formatIndianNumber(grossAmount)}) ^ (1 / ${lifeInYears}) )`;
    } else { // SLM
        formula = 'Rate = 1 / Life in Years';
        calculation = `Rate = 1 / ${lifeInYears}`;
    }

    return (
        <div className="modal-overlay calc-modal-overlay" onClick={onClose}>
            <div className="modal-content calc-modal" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>Depreciation Rate Calculation</h2>
                    <button className="modal-close-btn" onClick={onClose}>&times;</button>
                </div>
                <div className="calc-modal-body">
                    <p className="calc-asset-name"><strong>Asset:</strong> {assetParticulars}</p>
                    <div className="calc-section">
                        <h4>Formula ({depreciationMethod})</h4>
                        <p className="calc-formula">{formula}</p>
                    </div>
                    <div className="calc-section">
                        <h4>Calculation</h4>
                        <p className="calc-formula">{calculation}</p>
                    </div>
                    <div className="calc-note">
                        {depreciationMethod === 'WDV'
                            ? 'Note: The WDV rate is applied to the Opening WDV for the year.'
                            : 'Note: The SLM rate is applied to the Depreciable Amount (Gross - Salvage).'
                        }
                    </div>
                     <div className="calc-section result">
                        <h4>Result</h4>
                        <p className="calc-result">Rate = <strong>{(depreciationRate).toFixed(2)} %</strong></p>
                    </div>
                </div>
            </div>
        </div>
    );
}

