/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo, FC } from 'react';
import { api } from './lib/api';
import type { CompanyData, Asset } from './lib/db-server';
import { DataViewProps, CalculationRow } from './lib/types';
import { formatIndianNumber, inclusiveDateDiffInDays, sortData, exportToExcel } from './lib/utils';
import { DepreciationRateModal } from './pages/DepreciationRateModal';
import { DownloadIcon } from './Icons';
import { Highlight } from './pages/PlaceholderViews';

// --- Disposal WDV Calculation Modal ---
interface DisposalWDVModalProps {
    isOpen: boolean;
    onClose: () => void;
    data: CalculationRow | null;
    companyName: string;
    year: string;
    showAlert: (title: string, message: string, type: 'success' | 'error' | 'info') => void;
}

const DisposalWDVModal: FC<DisposalWDVModalProps> = ({ isOpen, onClose, data, companyName, year, showAlert }) => {
    if (!isOpen || !data) return null;

    const { 
        assetParticulars, openingWDV, useDays, depreciationForYear, 
        disposalWDV, disposalAmount, gainLossOnDisposal 
    } = data;
    
    const handleExport = () => {
        const exportData = [
            { Field: 'Asset Particulars', Value: assetParticulars },
            { Field: 'Opening WDV for the year', Value: formatIndianNumber(openingWDV) },
            { Field: 'Depreciation for the year', Value: formatIndianNumber(depreciationForYear) },
            { Field: '(Depreciation applied for days)', Value: String(useDays) },
            { Field: 'WDV on Disposal Date', Value: formatIndianNumber(disposalWDV) },
            { Field: 'Disposal Amount Realized', Value: formatIndianNumber(disposalAmount) },
            { Field: 'Profit / (Loss) on Disposal', Value: formatIndianNumber(gainLossOnDisposal) },
        ];
        if (!exportToExcel({ data: exportData, companyName, year, reportName: `Disposal_WDV_Calc_${data.recordId}`, sheetName: 'Disposal_Calc' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content calc-modal" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>Disposal Calculation Details</h2>
                    <div className="actions">
                        <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export</button>
                        <button className="modal-close-btn" style={{ marginLeft: '1rem' }} onClick={onClose}>&times;</button>
                    </div>
                </div>
                <div className="calc-modal-body">
                    <p className="calc-asset-name"><strong>Asset:</strong> {assetParticulars}</p>
                    <div className="calc-section">
                        <h4>WDV Calculation</h4>
                        <div className="calc-detail-list">
                           <p><span>Opening WDV:</span> <strong>{formatIndianNumber(openingWDV)}</strong></p>
                           <p><span>(-) Depreciation for Year ({useDays} days):</span> <strong>{formatIndianNumber(depreciationForYear)}</strong></p>
                           <p><span>(=) WDV on Disposal Date:</span> <strong>{formatIndianNumber(disposalWDV)}</strong></p>
                        </div>
                    </div>
                    <div className="calc-section">
                        <h4>Gain / Loss Calculation</h4>
                         <div className="calc-detail-list">
                           <p><span>Disposal Amount Realized:</span> <strong>{formatIndianNumber(disposalAmount)}</strong></p>
                           <p><span>(-) WDV on Disposal Date:</span> <strong>{formatIndianNumber(disposalWDV)}</strong></p>
                        </div>
                    </div>
                    <div className="calc-section result">
                        <h4>Profit / (Loss) on Disposal</h4>
                        <p className="calc-result">
                            <strong>{formatIndianNumber(gainLossOnDisposal)}</strong>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};

// --- Depreciation For Year Calculation Modal ---
interface DepreciationDetailModalProps {
    isOpen: boolean;
    onClose: () => void;
    data: CalculationRow | null;
    year: string;
    companyName: string;
    showAlert: (title: string, message: string, type: 'success' | 'error' | 'info') => void;
}

const DepreciationDetailModal: FC<DepreciationDetailModalProps> = ({ isOpen, onClose, data, year, companyName, showAlert }) => {
    if (!isOpen || !data) return null;
    
    const { 
        assetParticulars, baseDepreciation, extraShiftDepreciation, depreciationForYear, 
        isExtraShiftApplicable, openingWDV, depreciationRate, useDays,
        extraShiftDays2nd, extraShiftDays3rd, putToUseDate, grossAmount, zeroDepreciationReason
    } = data;
    
    const [startYearStr] = year.split('-');
    const fyStart = new Date(`${startYearStr}-04-01T00:00:00.000Z`);
    const isAddition = new Date(putToUseDate) >= fyStart;

    const handleExport = () => {
        const exportData = [
            { Field: 'Asset Particulars', Value: assetParticulars },
        ];
        
        if (depreciationForYear === 0 && zeroDepreciationReason) {
            exportData.push(
                { Field: 'Total Depreciation for Year', Value: '0' },
                { Field: 'Reason', Value: zeroDepreciationReason }
            );
        } else {
            if (isAddition) {
                 exportData.push({ Field: `Added on ${new Date(putToUseDate).toLocaleDateString()}`, Value: formatIndianNumber(grossAmount) });
            } else {
                 exportData.push({ Field: 'Opening WDV', Value: formatIndianNumber(openingWDV) });
            }
            
            exportData.push(
                { Field: 'Depreciation Rate Applied', Value: `${depreciationRate.toFixed(2)} %` },
                { Field: 'Use Days in Year', Value: String(useDays) },
                { Field: 'Normal Depreciation', Value: formatIndianNumber(baseDepreciation) }
            );

            if (isExtraShiftApplicable) {
                 exportData.push(
                     { Field: '2nd Shift Days', Value: String(extraShiftDays2nd) },
                     { Field: '3rd Shift Days', Value: String(extraShiftDays3rd) },
                     { Field: 'Extra Shift Depreciation Amount', Value: formatIndianNumber(extraShiftDepreciation) },
                 );
            }
            exportData.push({ Field: 'Total Depreciation for Year', Value: formatIndianNumber(depreciationForYear) });
        }

        if (!exportToExcel({ data: exportData, companyName, year, reportName: `Depreciation_Calc_${data.recordId}`, sheetName: 'Depreciation_Calc' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };
    
    const renderZeroDepreciation = () => (
        <div className="calc-modal-body">
            <p className="calc-asset-name"><strong>Asset:</strong> {assetParticulars}</p>
            <div className="calc-section result">
                <h4>Total Depreciation for Year</h4>
                <p className="calc-result"><strong>0</strong></p>
            </div>
            <div className="calc-section">
                <h4>Reason</h4>
                <div className="calc-note" style={{ fontStyle: 'normal', textAlign: 'left', background: 'var(--background-primary)' }}>
                    <p>{zeroDepreciationReason}</p>
                </div>
            </div>
        </div>
    );
    
    const renderDetailedDepreciation = () => (
        <div className="calc-modal-body">
            <p className="calc-asset-name"><strong>Asset:</strong> {assetParticulars}</p>
            <div className="calc-section">
                <h4>Calculation Breakdown</h4>
                <div className="calc-detail-list">
                    {isAddition ? (
                        <p><span>Added on {new Date(putToUseDate).toLocaleDateString()}:</span> <strong>{formatIndianNumber(grossAmount)}</strong></p>
                    ) : (
                        <p><span>Opening WDV:</span> <strong>{formatIndianNumber(openingWDV)}</strong></p>
                    )}
                    <p><span>Depreciation Rate:</span> <strong>{depreciationRate.toFixed(2)}%</strong></p>
                    <p><span>Normal Depreciation:</span> <strong>{formatIndianNumber(baseDepreciation)}</strong></p>
                    <p><span>Use Days in Year:</span> <strong>{useDays}</strong></p>
                </div>
            </div>
            {isExtraShiftApplicable && (
                <div className="calc-section">
                    <h4>Extra Shift Calculation</h4>
                    <div className="calc-detail-list">
                        <p><span>2nd Shift Days:</span> <strong>{extraShiftDays2nd}</strong></p>
                        <p><span>3rd Shift Days:</span> <strong>{extraShiftDays3rd}</strong></p>
                        <p><span>Extra Shift Depreciation:</span> <strong>{formatIndianNumber(extraShiftDepreciation)}</strong></p>
                    </div>
                    <div className="calc-note" style={{marginTop: '1rem'}}>
                        Extra shift depreciation is based on normal depreciation, calculated only for the days the extra shifts were worked.
                    </div>
                </div>
            )}
            <div className="calc-section result">
                <h4>Total Depreciation for Year</h4>
                <p className="calc-result">
                    <strong>{formatIndianNumber(depreciationForYear)}</strong>
                </p>
            </div>
        </div>
    );

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content calc-modal" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>Depreciation for Year Calculation</h2>
                    <div className="actions">
                        <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export</button>
                        <button className="modal-close-btn" style={{ marginLeft: '1rem' }} onClick={onClose}>&times;</button>
                    </div>
                </div>
                {(depreciationForYear === 0 && zeroDepreciationReason) ? renderZeroDepreciation() : renderDetailedDepreciation()}
            </div>
        </div>
    );
};


export function AssetCalculations({ companyId, companyName, year, financialYears, showAlert }: DataViewProps) {
    const [reportData, setReportData] = useState<CalculationRow[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [rateModalData, setRateModalData] = useState<CalculationRow | null>(null);
    const [depreciationModalData, setDepreciationModalData] = useState<CalculationRow | null>(null);
    const [disposalModalData, setDisposalModalData] = useState<CalculationRow | null>(null);
    const [sortConfig, setSortConfig] = useState<{ key: keyof CalculationRow; direction: 'asc' | 'desc' } | null>({ key: 'recordId', direction: 'asc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);
    const [filterText, setFilterText] = useState('');


    useEffect(() => {
        const calculateReport = async () => {
            if (!companyId || !year) {
                setLoading(false); return;
            }

            setLoading(true);
            setError(null);
            setSelectedRowId(null);
            setSelectedColumnKey(null);
            setFilterText('');
            
            try {
                const companyData = await api.getCompanyData(companyId);

                if (!companyData || !companyData.assets) {
                    setReportData([]); setLoading(false);
                    return;
                }

                const { assets, statutoryRates } = companyData;
                
                const statutoryRatesMap = new Map(statutoryRates.map(r => [r.assetSubGroup, r]));

                const [startYearStr, endYearStr] = year.split('-');
                const fyStart = new Date(`${startYearStr}-04-01T00:00:00.000Z`);
                const fyEnd = new Date(`${endYearStr}-03-31T23:59:59.999Z`);
                const firstAdoptionDate = new Date(companyData.info.firstDateOfAdoption);

                const isFirstYearInSystem = companyData.financialYears.indexOf(year) === 0;

                const calculatedRows: CalculationRow[] = [];

                for (const asset of assets) {
                    const putToUseDate = new Date(asset.putToUseDate);
                    const disposalDate = asset.disposalDate ? new Date(asset.disposalDate) : null;
                    const grossAmount = asset.grossAmount || 0;
                    const lifeInYears = asset.lifeInYears || 0;
                    const salvagePercentage = asset.salvagePercentage || 0;
                    const depreciationMethod = asset.depreciationMethod;

                    const salvageAmount = Math.round(grossAmount * (salvagePercentage / 100));
                    const depreciableAmount = grossAmount - salvageAmount;
                    
                    let endOfLifeDateStr = 'N/A';
                    if (lifeInYears > 0) {
                        const eolDate = new Date(putToUseDate.getTime());
                        eolDate.setFullYear(eolDate.getFullYear() + lifeInYears);
                        endOfLifeDateStr = eolDate.toLocaleDateString();
                    }

                    let depreciationRate = 0;
                    if (lifeInYears > 0 && grossAmount > 0) {
                        if (depreciationMethod === 'WDV' && salvageAmount < grossAmount) {
                            depreciationRate = 1 - Math.pow((salvageAmount / grossAmount), (1 / lifeInYears));
                        } else if (depreciationMethod === 'SLM') {
                            depreciationRate = 1 / lifeInYears;
                        }
                    }

                    let openingWDV: number;
                    
                    // CORRECTED: Use consistent calculation with Schedule III
                    // Remove the conflicting backend field approach and use unified logic
                    const currentYearIndex = companyData.financialYears.indexOf(year);
                    const isFirstYearInSystem = currentYearIndex === 0;
                    const previousYears = companyData.financialYears.slice(0, currentYearIndex);
                    let openingAccumulatedDepreciation = 0;
                    
                    if (isFirstYearInSystem) {
                        if (putToUseDate < firstAdoptionDate && asset.wdvOfAdoptionDate != null) {
                            // Historical asset - use WDV at adoption date
                            openingWDV = asset.wdvOfAdoptionDate;
                        } else if (putToUseDate >= firstAdoptionDate && putToUseDate < fyStart) {
                            // Asset added before first FY but after adoption
                            const partialDepreciation = calculatePartialDepreciation(asset, firstAdoptionDate, fyStart);
                            openingAccumulatedDepreciation = partialDepreciation;
                            openingWDV = grossAmount - openingAccumulatedDepreciation;
                        } else {
                            // Asset exists at beginning of first year (not historical, not fresh addition)
                            if (putToUseDate < fyStart) {
                                // Asset was added in previous period within same first year
                                const partialDepreciation = calculatePartialDepreciation(asset, putToUseDate, fyStart);
                                openingAccumulatedDepreciation = partialDepreciation;
                                openingWDV = Math.max(0, grossAmount - openingAccumulatedDepreciation);
                            } else {
                                // Fresh addition in current year
                                openingWDV = 0; // Asset will be added during the year
                            }
                        }
                    } else {
                        if (putToUseDate < fyStart) {
                            // Asset existed in previous years - calculate accumulated depreciation
                            if (putToUseDate < firstAdoptionDate && asset.wdvOfAdoptionDate != null) {
                                openingAccumulatedDepreciation = grossAmount - asset.wdvOfAdoptionDate;
                            } else if (putToUseDate >= firstAdoptionDate && putToUseDate < new Date(`${companyData.financialYears[0].split('-')[0]}-04-01`)) {
                                openingAccumulatedDepreciation = calculatePartialDepreciation(asset, firstAdoptionDate, new Date(`${companyData.financialYears[0].split('-')[0]}-04-01`));
                            }
                            
                            // Add depreciation from all completed years
                            for (const prevYear of previousYears) {
                                openingAccumulatedDepreciation += (asset[`Depreciation-${prevYear}`] || 0);
                            }
                            
                            // CRITICAL FIX: If no yearly depreciation data exists, calculate fallback depreciation
                            if (openingAccumulatedDepreciation === 0 && previousYears.length > 0) {
                                // Calculate fallback accumulated depreciation based on asset age and method
                                const assetAgeInYears = previousYears.length;
                                const salvagePercentage = asset.salvagePercentage || 0;
                                const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));
                                
                                if (asset.depreciationMethod === 'SLM') {
                                    // Straight Line Method: (Gross - Salvage) / Life * Years elapsed
                                    const depreciableAmount = grossAmount - salvageValue;
                                    const yearlyDepreciation = depreciableAmount / lifeInYears;
                                    openingAccumulatedDepreciation = Math.min(yearlyDepreciation * assetAgeInYears, depreciableAmount);
                                } else if (asset.depreciationMethod === 'WDV' && lifeInYears > 0 && salvageValue < grossAmount) {
                                    // Written Down Value Method: Apply rate for each year
                                    const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
                                    let currentWDV = grossAmount;
                                    
                                    for (let year = 0; year < assetAgeInYears; year++) {
                                        const yearlyDepreciation = currentWDV * rate;
                                        openingAccumulatedDepreciation += yearlyDepreciation;
                                        currentWDV -= yearlyDepreciation;
                                        
                                        // Don't depreciate below salvage value
                                        if (currentWDV <= salvageValue) {
                                            openingAccumulatedDepreciation = grossAmount - salvageValue;
                                            break;
                                        }
                                    }
                                }
                                
                                // Ensure we don't exceed maximum depreciable amount
                                const maxDepreciable = grossAmount - salvageValue;
                                openingAccumulatedDepreciation = Math.min(openingAccumulatedDepreciation, maxDepreciable);
                            }
                            
                            // CRITICAL FIX: If no yearly depreciation data exists, calculate fallback depreciation
                            if (openingAccumulatedDepreciation === 0 && previousYears.length > 0) {
                                // Calculate fallback accumulated depreciation based on asset age and method
                                const assetAgeInYears = previousYears.length;
                                const salvagePercentage = asset.salvagePercentage || 0;
                                const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));
                                
                                if (asset.depreciationMethod === 'SLM') {
                                    // Straight Line Method: (Gross - Salvage) / Life * Years elapsed
                                    const depreciableAmount = grossAmount - salvageValue;
                                    const yearlyDepreciation = depreciableAmount / lifeInYears;
                                    openingAccumulatedDepreciation = Math.min(yearlyDepreciation * assetAgeInYears, depreciableAmount);
                                } else if (asset.depreciationMethod === 'WDV' && lifeInYears > 0 && salvageValue < grossAmount) {
                                    // Written Down Value Method: Apply rate for each year
                                    const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
                                    let currentWDV = grossAmount;
                                    
                                    for (let year = 0; year < assetAgeInYears; year++) {
                                        const yearlyDepreciation = currentWDV * rate;
                                        openingAccumulatedDepreciation += yearlyDepreciation;
                                        currentWDV -= yearlyDepreciation;
                                        
                                        // Don't depreciate below salvage value
                                        if (currentWDV <= salvageValue) {
                                            openingAccumulatedDepreciation = grossAmount - salvageValue;
                                            break;
                                        }
                                    }
                                }
                                
                                // Ensure we don't exceed maximum depreciable amount
                                const maxDepreciable = grossAmount - salvageValue;
                                openingAccumulatedDepreciation = Math.min(openingAccumulatedDepreciation, maxDepreciable);
                            }
                            
                            openingWDV = Math.max(0, grossAmount - openingAccumulatedDepreciation);
                        } else {
                            // Asset to be added in current year
                            openingWDV = 0;
                        }
                    }

                    // Helper function for partial depreciation calculation
                    function calculatePartialDepreciation(asset: any, startDate: Date, endDate: Date): number {
                        const grossAmount = asset.grossAmount || 0;
                        const lifeInYears = asset.lifeInYears || 0;
                        const salvagePercentage = asset.salvagePercentage || 0;
                        const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));
                        
                        if (lifeInYears <= 0 || grossAmount <= salvageValue) return 0;
                        
                        const useDays = inclusiveDateDiffInDays(startDate, endDate);
                        
                        if (asset.depreciationMethod === 'SLM') {
                            const depreciableAmount = grossAmount - salvageValue;
                            const yearlyDepreciation = depreciableAmount / lifeInYears;
                            return Math.round((yearlyDepreciation / 365.25) * useDays);
                        } else { // WDV
                            if (grossAmount > 0 && salvageValue < grossAmount) {
                                const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
                                const yearlyDepreciation = grossAmount * rate;
                                return Math.round((yearlyDepreciation / 365.25) * useDays);
                            }
                        }
                        return 0;
                    }

                    const startOfUseInFY = putToUseDate > fyStart ? putToUseDate : fyStart;
                    const endOfUseInFY = disposalDate && disposalDate < fyEnd ? disposalDate : fyEnd;
                    let useDaysInFY = 0;
                    if (endOfUseInFY >= startOfUseInFY && putToUseDate <= endOfUseInFY) {
                        useDaysInFY = inclusiveDateDiffInDays(startOfUseInFY, endOfUseInFY);
                    }

                    let baseDepreciation = 0;
                    
                    // COMPREHENSIVE FIX: Ensure depreciation is calculated for all valid scenarios
                    const isCurrentYearAddition = (putToUseDate >= fyStart && putToUseDate <= fyEnd);
                    
                    // For additions in current year, use gross amount as base
                    // For existing assets, use opening WDV (which should be > 0 for valid assets)
                    let depreciationBase = 0;
                    if (isCurrentYearAddition) {
                        depreciationBase = grossAmount;
                    } else if (openingWDV > 0) {
                        depreciationBase = openingWDV;
                    } else {
                        // Fallback: if openingWDV is 0 but asset exists, use gross amount
                        depreciationBase = grossAmount;
                    }
                    
                    if (lifeInYears > 0 && depreciationBase > salvageAmount && useDaysInFY > 0) {
                         if (depreciationMethod === 'SLM') {
                            const yearlyDepreciation = depreciableAmount / lifeInYears;
                            baseDepreciation = (yearlyDepreciation / 365.25) * useDaysInFY;
                        } else { // WDV
                            const yearlyDepreciation = depreciationBase * depreciationRate;
                            baseDepreciation = (yearlyDepreciation / 365.25) * useDaysInFY;
                        }
                        
                        // Ensure we don't depreciate below salvage value
                        const maxDepreciationAllowed = depreciationBase - salvageAmount;
                        baseDepreciation = Math.min(baseDepreciation, maxDepreciationAllowed);
                    }
                    
                    let extraShiftDepreciation = 0;
                    const statutoryInfo = statutoryRatesMap.get(asset.assetSubGroup);
                    const isShiftApplicableStatutory = statutoryInfo?.extraShiftDepreciation === 'Yes';
                    const secondShiftDays = asset[`2nd Shift Days-${year}`] || 0;
                    const thirdShiftDays = asset[`3rd Shift Days-${year}`] || 0;
                    const hasExtraShifts = secondShiftDays > 0 || thirdShiftDays > 0;
                    const isExtraShiftApplicable = isShiftApplicableStatutory && hasExtraShifts && depreciableAmount > 0;

                    if(isExtraShiftApplicable) {
                         const singleShiftNormalDepr = (depreciableAmount / lifeInYears) / 365.25;
                         extraShiftDepreciation = singleShiftNormalDepr * ((secondShiftDays * 0.5) + (thirdShiftDays * 1.0));
                    }

                    let depreciationForYear = baseDepreciation + extraShiftDepreciation;
                    const maxDepreciation = openingWDV - salvageAmount;
                    depreciationForYear = Math.round(Math.max(0, Math.min(depreciationForYear, maxDepreciation)));

                    let zeroDepreciationReason: string | null = null;
                    if (depreciationForYear === 0) {
                        if (openingWDV <= salvageAmount) {
                            zeroDepreciationReason = 'Asset is fully depreciated (Opening WDV <= Salvage Value).';
                        } else if (lifeInYears <= 0) {
                            zeroDepreciationReason = 'Asset has zero or negative useful life.';
                        } else if (useDaysInFY <= 0) {
                            if (putToUseDate > fyEnd) {
                                zeroDepreciationReason = 'Asset was not put to use in this financial year.';
                            } else if (disposalDate && disposalDate < fyStart) {
                                zeroDepreciationReason = 'Asset was disposed of before this financial year began.';
                            } else {
                                zeroDepreciationReason = 'Asset had zero days of use in this financial year.';
                            }
                        } else {
                            zeroDepreciationReason = 'Calculation resulted in zero depreciation. This might be due to rounding or a very small remaining depreciable value.';
                        }
                    }

                    let disposalWDV = null;
                    let totalAccumulatedDepreciationOnDisposal = null;
                    let gainLossOnDisposal = null;
                    if (disposalDate && disposalDate >= fyStart && disposalDate <= fyEnd) {
                        totalAccumulatedDepreciationOnDisposal = openingAccumulatedDepreciation + depreciationForYear;
                        disposalWDV = grossAmount - totalAccumulatedDepreciationOnDisposal;
                        if(asset.disposalAmount != null) {
                            gainLossOnDisposal = asset.disposalAmount - disposalWDV;
                        }
                    }
                    
                    const closingWDV = openingWDV - depreciationForYear;

                    calculatedRows.push({
                        recordId: asset.recordId, assetParticulars: asset.assetParticulars,
                        depreciationMethod, depreciationRate: depreciationRate * 100,
                        endOfLifeDate: endOfLifeDateStr, salvageAmount, depreciableAmount,
                        openingWDV, useDays: useDaysInFY, depreciationForYear, disposalWDV,
                        closingWDV, grossAmount, lifeInYears, putToUseDate: asset.putToUseDate,
                        disposalDate: asset.disposalDate,
                        disposalAmount: asset.disposalAmount, gainLossOnDisposal,
                        isExtraShiftApplicable, baseDepreciation: Math.round(baseDepreciation),
                        extraShiftDepreciation: Math.round(extraShiftDepreciation),
                        extraShiftDays2nd: secondShiftDays,
                        extraShiftDays3rd: thirdShiftDays,
                        scrapIt: asset.scrapIt,
                        totalAccumulatedDepreciationOnDisposal,
                        zeroDepreciationReason
                    });
                }
                
                setReportData(calculatedRows);
            } catch (err) {
                setError('Failed to generate Asset Calculations report.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        calculateReport();
    }, [companyId, year, financialYears]);

    const sortedReportData = useMemo(() => {
        return sortData(reportData, sortConfig);
    }, [reportData, sortConfig]);

    const searchableFields: (keyof CalculationRow)[] = ['recordId', 'assetParticulars'];
    
    const filteredReportData = useMemo(() => {
        if (!filterText) return sortedReportData;
        const searchTerm = filterText.trim().toLowerCase();
        return sortedReportData.filter(row =>
            searchableFields.some(field =>
                String(row[field] ?? '').toLowerCase().includes(searchTerm)
            )
        );
    }, [sortedReportData, filterText]);

    const requestSort = (key: keyof CalculationRow) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: keyof CalculationRow) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };

    const getHeaderClass = (key: keyof CalculationRow) => {
        const classes = ['sortable'];
        if (!['recordId', 'assetParticulars', 'depreciationMethod', 'endOfLifeDate'].includes(key)) {
            classes.push('text-right');
        }
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        return classes.join(' ');
    };

    const getColumnClass = (key: keyof CalculationRow) => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if (!['recordId', 'assetParticulars', 'depreciationMethod', 'endOfLifeDate'].includes(key)) {
            classes.push('text-right');
        }
        return classes.join(' ');
    };

    const handleExport = () => {
        if (!companyId || !companyName || !year) return;
        
        const dataToExport = filteredReportData.map(row => ({
            "Record ID": row.recordId,
            "Asset Particulars": row.assetParticulars,
            "Depr. Method": row.depreciationMethod,
            "Depr. Rate (%)": row.depreciationRate.toFixed(2),
            "End of Life Date": row.endOfLifeDate,
            "Salvage Amount": row.salvageAmount,
            "Depreciable Amount": row.depreciableAmount,
            "Opening WDV": row.openingWDV,
            "Use Days": row.useDays,
            "Depreciation for Year": row.depreciationForYear,
            "Disposal WDV": row.disposalWDV,
            "Closing WDV": row.closingWDV,
        }));
        
        if (!exportToExcel({ data: dataToExport, companyName, year, reportName: 'Asset_Calculations' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    if (loading) return <div className="loading-indicator">Generating Asset Calculations Report...</div>;
    if (error) return <div className="error-message">{error}</div>;
    if (!companyId) return null;
    if (reportData.length === 0) return <div className="company-info-container"><p>No asset data available to generate the report for the selected year.</p></div>;

    return (
        <div className="report-view">
            <div className="view-header">
                <h2>Asset Calculations for FY {year}</h2>
                <div className="actions">
                    <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export to Excel</button>
                </div>
            </div>
            <div className="filter-container">
                <div className="filter-input-wrapper">
                    <input
                        type="text"
                        placeholder="Filter by Record ID or Particulars..."
                        className="filter-input"
                        value={filterText}
                        onChange={e => setFilterText(e.target.value)}
                        aria-label="Filter calculation data"
                    />
                    <button
                        className={`filter-clear-btn ${!filterText ? 'hidden' : ''}`}
                        onClick={() => setFilterText('')}
                        aria-label="Clear filter"
                        title="Clear filter"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </div>
                <div className="filter-info">
                    <span className="filter-info-icon">ⓘ</span>
                    <div className="filter-info-tooltip">
                        <strong>Filtering on:</strong>
                        <ul>
                           <li>Record ID</li>
                           <li>Asset Particulars</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div className="table-container">
                <table>
                    <thead>
                        <tr>
                            <th className={getHeaderClass('recordId')} onClick={() => requestSort('recordId')}>Record ID{getSortIndicator('recordId')}</th>
                            <th className={getHeaderClass('assetParticulars')} onClick={() => requestSort('assetParticulars')}>Asset Particulars{getSortIndicator('assetParticulars')}</th>
                            <th className={getHeaderClass('depreciationMethod')} onClick={() => requestSort('depreciationMethod')}>Depr. Method{getSortIndicator('depreciationMethod')}</th>
                            <th className={getHeaderClass('depreciationRate')} onClick={() => requestSort('depreciationRate')}>Depr. Rate (%){getSortIndicator('depreciationRate')}</th>
                            <th className={getHeaderClass('endOfLifeDate')} onClick={() => requestSort('endOfLifeDate')}>End of Life{getSortIndicator('endOfLifeDate')}</th>
                            <th className={getHeaderClass('salvageAmount')} onClick={() => requestSort('salvageAmount')}>Salvage Amt.{getSortIndicator('salvageAmount')}</th>
                            <th className={getHeaderClass('depreciableAmount')} onClick={() => requestSort('depreciableAmount')}>Depreciable Amt.{getSortIndicator('depreciableAmount')}</th>
                            <th className={getHeaderClass('openingWDV')} onClick={() => requestSort('openingWDV')}>Opening WDV{getSortIndicator('openingWDV')}</th>
                            <th className={getHeaderClass('useDays')} onClick={() => requestSort('useDays')}>Use Days{getSortIndicator('useDays')}</th>
                            <th className={getHeaderClass('depreciationForYear')} onClick={() => requestSort('depreciationForYear')}>Depr. for Year{getSortIndicator('depreciationForYear')}</th>
                            <th className={getHeaderClass('disposalWDV')} onClick={() => requestSort('disposalWDV')}>Disposal WDV{getSortIndicator('disposalWDV')}</th>
                            <th className={getHeaderClass('closingWDV')} onClick={() => requestSort('closingWDV')}>Closing WDV{getSortIndicator('closingWDV')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredReportData.map(row => (
                            <tr key={row.recordId} onClick={() => setSelectedRowId(row.recordId)} className={`${row.disposalDate ? 'disposed-row' : ''} ${row.scrapIt ? 'scrapped-row' : ''} ${row.recordId === selectedRowId ? 'selected-row' : ''}`}>
                                <td className={getColumnClass('recordId')}><Highlight text={row.recordId} highlight={filterText} /></td>
                                <td className={getColumnClass('assetParticulars')}><Highlight text={row.assetParticulars} highlight={filterText} /></td>
                                <td className={getColumnClass('depreciationMethod')}>{row.depreciationMethod}</td>
                                <td className={`${getColumnClass('depreciationRate')} clickable-cell`} onDoubleClick={() => setRateModalData(row)}>{row.depreciationRate.toFixed(2)}</td>
                                <td className={getColumnClass('endOfLifeDate')}>{row.endOfLifeDate}</td>
                                <td className={getColumnClass('salvageAmount')}>{formatIndianNumber(row.salvageAmount)}</td>
                                <td className={getColumnClass('depreciableAmount')}>{formatIndianNumber(row.depreciableAmount)}</td>
                                <td className={getColumnClass('openingWDV')}>{formatIndianNumber(row.openingWDV)}</td>
                                <td className={getColumnClass('useDays')}>{row.useDays}</td>
                                <td className={`${getColumnClass('depreciationForYear')} clickable-cell ${row.isExtraShiftApplicable ? 'extra-shift-depreciation' : ''}`} onDoubleClick={() => setDepreciationModalData(row)}>{formatIndianNumber(row.depreciationForYear)}</td>
                                <td className={`${getColumnClass('disposalWDV')} ${row.disposalWDV !== null ? 'clickable-cell' : ''}`} onDoubleClick={() => row.disposalWDV !== null && setDisposalModalData(row)}>{row.disposalWDV !== null ? formatIndianNumber(row.disposalWDV) : 'N/A'}</td>
                                <td className={getColumnClass('closingWDV')}>{formatIndianNumber(row.closingWDV)}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <DepreciationRateModal isOpen={!!rateModalData} onClose={() => setRateModalData(null)} data={rateModalData} />
            <DepreciationDetailModal isOpen={!!depreciationModalData} onClose={() => setDepreciationModalData(null)} data={depreciationModalData} year={year} companyName={companyName || ''} showAlert={showAlert} />
            <DisposalWDVModal isOpen={!!disposalModalData} onClose={() => setDisposalModalData(null)} data={disposalModalData} companyName={companyName || ''} year={year} showAlert={showAlert} />
        </div>
    );
}
