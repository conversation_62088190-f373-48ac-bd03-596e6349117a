#!/usr/bin/env node

/**
 * FAR Sighted License Generator
 * Generates license files for FAR Sighted Asset Management System
 */

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import readline from 'readline';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// License configuration
const LICENSE_CONFIG = {
    version: '1.0',
    algorithm: 'aes-256-cbc',
    secretKey: 'FAR-SIGHTED-2025-SECRET-KEY-FOR-LICENSE-GENERATION-SYSTEM',
    ivLength: 16
};

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Helper function to prompt user input
const prompt = (question) => {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
};

// Generate unique license key
function generateLicenseKey(companyInfo, applicationName = 'FAR Sighted Asset Management System') {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    const companyCode = companyInfo.companyName
        .replace(/[^A-Za-z]/g, '')
        .substring(0, 4)
        .toUpperCase()
        .padEnd(4, 'X');

    // Application prefix based on application type
    const appPrefix = applicationName.includes('FAR Sighted') ? 'FAR' :
                     applicationName.includes('Asset') ? 'AST' :
                     applicationName.substring(0, 3).toUpperCase();

    return `${appPrefix}-${new Date().getFullYear()}-${companyCode}-${random}-${timestamp.toString(36).toUpperCase()}`;
}

// Encrypt license data
function encryptLicenseData(data) {
    const key = crypto.scryptSync(LICENSE_CONFIG.secretKey, 'salt', 32);
    const iv = crypto.randomBytes(LICENSE_CONFIG.ivLength);
    const cipher = crypto.createCipheriv(LICENSE_CONFIG.algorithm, key, iv);

    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return {
        encrypted,
        iv: iv.toString('hex')
    };
}

// Decrypt license data (for validation)
function decryptLicenseData(encryptedData, ivHex) {
    try {
        const key = crypto.scryptSync(LICENSE_CONFIG.secretKey, 'salt', 32);
        const iv = Buffer.from(ivHex, 'hex');
        const decipher = crypto.createDecipheriv(LICENSE_CONFIG.algorithm, key, iv);

        let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
        decrypted += decipher.final('utf8');

        return JSON.parse(decrypted);
    } catch (error) {
        throw new Error('Invalid license file or corrupted data');
    }
}

// Generate license from request file
async function generateFromRequestFile() {
    console.log('📄 GENERATE LICENSE FROM REQUEST FILE');
    console.log('====================================\n');

    try {
        const requestFilePath = await prompt('Enter license request file path: ');

        if (!fs.existsSync(requestFilePath)) {
            throw new Error('Request file not found');
        }

        const requestData = JSON.parse(fs.readFileSync(requestFilePath, 'utf8'));

        // Validate request file structure
        if (!requestData.company || !requestData.license) {
            throw new Error('Invalid request file format');
        }

        console.log('✅ Request file loaded successfully');
        console.log(`📋 Request ID: ${requestData.requestId}`);
        console.log(`🏢 Company: ${requestData.company.name}`);
        console.log(`📅 Requested Period: ${requestData.license.requestedValidFrom} to ${requestData.license.requestedValidUpto}`);

        // Confirm generation
        const confirm = await prompt('\nGenerate license for this request? (yes/no): ');
        if (confirm.toLowerCase() !== 'yes') {
            console.log('❌ License generation cancelled');
            return;
        }

        // Use data from request file
        const companyInfo = {
            companyName: requestData.company.name,
            pan: requestData.company.pan,
            cin: requestData.company.cin,
            contactPerson: requestData.company.contact.person,
            email: requestData.company.contact.email,
            mobile: requestData.company.contact.mobile,
            address: `${requestData.company.address.line1}, ${requestData.company.address.line2}, ${requestData.company.address.city} - ${requestData.company.address.pin}`
        };

        const licenseDetails = {
            validFrom: requestData.license.requestedValidFrom,
            validUpto: requestData.license.requestedValidUpto,
            maxUsers: requestData.license.maxUsers || 5,
            maxCompanies: requestData.license.maxCompanies || 1,
            features: requestData.license.features || {
                multiDatabase: true,
                reports: true,
                backup: true,
                audit: true
            }
        };

        await generateLicenseFile(companyInfo, licenseDetails, requestData.requestId);

    } catch (error) {
        console.error('❌ Error generating license from request file:', error.message);
    }
}

// Generate license file (manual entry)
async function generateLicense() {
    console.log('🔐 FAR SIGHTED LICENSE GENERATOR');
    console.log('================================\n');

    try {
        // Collect application information
        const applicationType = await prompt('Application Type (1: FAR Sighted, 2: Other): ');
        const applicationName = applicationType === '1' ? 'FAR Sighted Asset Management System' : await prompt('Application Name: ');

        // Collect company information
        const companyInfo = {
            companyName: await prompt('Company Name: '),
            pan: await prompt('PAN Number: '),
            cin: await prompt('CIN Number (optional, press Enter to skip): '),
            contactPerson: await prompt('Contact Person: '),
            email: await prompt('Email: '),
            mobile: await prompt('Mobile: '),
            address: await prompt('Address: ')
        };

        // Validate that either PAN or CIN is provided
        if (!companyInfo.pan && !companyInfo.cin) {
            throw new Error('Either PAN or CIN must be provided');
        }

        // Collect license details
        const licenseDetails = {
            validFrom: await prompt('Valid From (YYYY-MM-DD): '),
            validUpto: await prompt('Valid Upto (YYYY-MM-DD): '),
            maxUsers: parseInt(await prompt('Maximum Users: ')) || 5,
            maxCompanies: parseInt(await prompt('Maximum Companies: ')) || 1,
            features: applicationType === '1' ? {
                multiDatabase: true,
                reports: true,
                backup: true,
                audit: true,
                extraShiftCalculation: true,
                scheduleIIIReports: true
            } : {
                basicFeatures: true,
                reports: true,
                backup: true
            }
        };

        await generateLicenseFile(companyInfo, licenseDetails, null, applicationName);

    } catch (error) {
        console.error('❌ Error generating license:', error.message);
    }
}

// Core license generation function
async function generateLicenseFile(companyInfo, licenseDetails, requestId = null, applicationName = 'FAR Sighted Asset Management System') {

    try {
        // Generate license
        const licenseKey = generateLicenseKey(companyInfo, applicationName);
        const licenseData = {
            version: LICENSE_CONFIG.version,
            key: licenseKey,
            application: applicationName,
            company: companyInfo,
            license: licenseDetails,
            requestId: requestId,
            generatedAt: new Date().toISOString(),
            generatedBy: 'Universal License Generator v1.0'
        };

        // Encrypt license data
        const encrypted = encryptLicenseData(licenseData);
        
        // Create license file content
        const licenseFile = {
            version: LICENSE_CONFIG.version,
            key: licenseKey,
            data: encrypted.encrypted,
            iv: encrypted.iv,
            checksum: crypto.createHash('sha256').update(encrypted.encrypted).digest('hex'),
            generatedAt: licenseData.generatedAt
        };

        // Save license file
        const appCode = applicationName.includes('FAR Sighted') ? 'FAR' :
                       applicationName.replace(/[^A-Za-z0-9]/g, '').substring(0, 3).toUpperCase();
        const fileName = `${appCode}_LICENSE_${companyInfo.companyName.replace(/[^A-Za-z0-9]/g, '_')}_${Date.now()}.json`;
        const filePath = path.join(__dirname, 'generated-licenses', fileName);
        
        // Create directory if it doesn't exist
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        fs.writeFileSync(filePath, JSON.stringify(licenseFile, null, 2));

        // Display results
        console.log('\n✅ LICENSE GENERATED SUCCESSFULLY!');
        console.log('==================================');
        console.log(`📄 License File: ${fileName}`);
        console.log(`🔑 License Key: ${licenseKey}`);
        console.log(`📅 Valid From: ${licenseDetails.validFrom}`);
        console.log(`📅 Valid Until: ${licenseDetails.validUpto}`);
        console.log(`👥 Max Users: ${licenseDetails.maxUsers}`);
        console.log(`🏢 Max Companies: ${licenseDetails.maxCompanies}`);
        console.log(`📁 File Location: ${filePath}`);

        // Create readable summary
        const summaryPath = path.join(__dirname, 'generated-licenses', `SUMMARY_${fileName.replace('.json', '.txt')}`);
        const summary = `
FAR SIGHTED LICENSE SUMMARY
===========================

Company: ${companyInfo.companyName}
PAN: ${companyInfo.pan}
CIN: ${companyInfo.cin}
Contact: ${companyInfo.contactPerson}
Email: ${companyInfo.email}
Mobile: ${companyInfo.mobile}

License Key: ${licenseKey}
Valid From: ${licenseDetails.validFrom}
Valid Until: ${licenseDetails.validUpto}
Max Users: ${licenseDetails.maxUsers}
Max Companies: ${licenseDetails.maxCompanies}
${requestId ? `Request ID: ${requestId}` : ''}

Generated: ${new Date().toLocaleString()}
File: ${fileName}

INSTRUCTIONS:
1. Send the ${fileName} file to the customer
2. Customer should place it in their FAR Sighted installation directory
3. Customer should activate the license through the application
4. Keep this summary for your records
`;

        fs.writeFileSync(summaryPath, summary);
        console.log(`📋 Summary: ${summaryPath}`);

    } catch (error) {
        console.error('❌ Error generating license:', error.message);
    }
}

// Validate license file
async function validateLicense() {
    console.log('🔍 FAR SIGHTED LICENSE VALIDATOR');
    console.log('================================\n');

    try {
        const filePath = await prompt('Enter license file path: ');
        
        if (!fs.existsSync(filePath)) {
            throw new Error('License file not found');
        }

        const licenseFile = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        // Validate structure
        if (!licenseFile.version || !licenseFile.key || !licenseFile.data || !licenseFile.iv) {
            throw new Error('Invalid license file structure');
        }

        // Validate checksum
        const calculatedChecksum = crypto.createHash('sha256').update(licenseFile.data).digest('hex');
        if (calculatedChecksum !== licenseFile.checksum) {
            throw new Error('License file corrupted - checksum mismatch');
        }

        // Decrypt and validate data
        const licenseData = decryptLicenseData(licenseFile.data, licenseFile.iv);
        
        console.log('✅ LICENSE VALIDATION SUCCESSFUL!');
        console.log('=================================');
        console.log(`🔑 License Key: ${licenseData.key}`);
        console.log(`🏢 Company: ${licenseData.company.companyName}`);
        console.log(`📅 Valid From: ${licenseData.license.validFrom}`);
        console.log(`📅 Valid Until: ${licenseData.license.validUpto}`);
        console.log(`👥 Max Users: ${licenseData.license.maxUsers}`);
        console.log(`🏢 Max Companies: ${licenseData.license.maxCompanies}`);
        console.log(`📅 Generated: ${new Date(licenseData.generatedAt).toLocaleString()}`);

        // Check expiry
        const expiryDate = new Date(licenseData.license.validUpto);
        const today = new Date();
        
        if (expiryDate < today) {
            console.log('⚠️  WARNING: License has expired!');
        } else {
            const daysLeft = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
            console.log(`✅ License valid for ${daysLeft} more days`);
        }

    } catch (error) {
        console.error('❌ License validation failed:', error.message);
    } finally {
        rl.close();
    }
}

// Show help
function showHelp() {
    console.log(`
🔐 UNIVERSAL LICENSE GENERATOR v1.0
===================================

USAGE:
  node license-generator.js [options]

OPTIONS:
  --generate       Generate a new license file (manual entry)
  --from-request   Generate license from request file
  --validate       Validate an existing license file
  --help           Show this help message

EXAMPLES:
  npm start                    # Interactive mode
  npm run generate            # Generate license manually
  npm run from-request        # Generate from request file
  npm run validate            # Validate license
  npm run gui                 # Start web-based GUI
  npm run help                # Show help

DESCRIPTION:
  This tool generates encrypted license files for various applications
  including FAR Sighted Asset Management System and other software.
  Each license is tied to specific company information and includes
  validity dates and feature permissions.

SUPPORTED APPLICATIONS:
  - FAR Sighted Asset Management System
  - Custom applications with PAN/CIN requirements
  - General business software licensing

COMPANY IDENTIFICATION:
  - PAN Number (required for Indian companies)
  - CIN Number (optional, for incorporated companies)
  - At least one of PAN or CIN must be provided

LICENSE FILE STRUCTURE:
  - Application identification
  - Encrypted company information
  - License validity dates
  - Feature permissions
  - Usage limits (users, companies)
  - Integrity checksums

SECURITY:
  - AES-256-CBC encryption
  - SHA-256 checksums
  - Unique license keys
  - Tamper detection
  - Request ID tracking
`);
}

// Main function
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help')) {
        showHelp();
        return;
    }
    
    if (args.includes('--generate')) {
        await generateLicense();
        return;
    }

    if (args.includes('--from-request')) {
        await generateFromRequestFile();
        return;
    }

    if (args.includes('--validate')) {
        await validateLicense();
        return;
    }
    
    // Interactive mode
    console.log('🔐 FAR SIGHTED LICENSE GENERATOR');
    console.log('================================\n');
    console.log('1. Generate License (Manual Entry)');
    console.log('2. Generate from Request File');
    console.log('3. Validate License');
    console.log('4. Help');
    console.log('5. Exit\n');

    const choice = await prompt('Select option (1-5): ');

    switch (choice) {
        case '1':
            await generateLicense();
            break;
        case '2':
            await generateFromRequestFile();
            break;
        case '3':
            await validateLicense();
            break;
        case '4':
            showHelp();
            rl.close();
            break;
        case '5':
            console.log('👋 Goodbye!');
            rl.close();
            break;
        default:
            console.log('❌ Invalid option');
            rl.close();
    }
}

// Export functions for GUI version
export { generateLicenseKey, encryptLicenseData, decryptLicenseData, LICENSE_CONFIG };

// Run the application only if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(error => {
        console.error('❌ Application error:', error);
        process.exit(1);
    });
}
