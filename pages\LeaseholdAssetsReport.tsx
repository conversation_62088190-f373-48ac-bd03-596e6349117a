/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, useEffect, useMemo } from 'react';
import { api } from '../lib/api';
import type { Asset } from '../lib/db-server';
import { DownloadIcon, FileTextIcon, CalendarIcon, FolderIcon } from '../Icons';
import { formatIndianNumber, exportToExcel } from '../lib/utils';

interface LeaseholdAssetsReportProps {
    companyId: string;
    companyName: string;
    showAlert: (title: string, message: string, type: 'success' | 'error' | 'info') => void;
}

interface LeaseholdAssetData extends Asset {
    leaseStartDate?: string;
    leaseEndDate?: string;
    remainingLeasePeriod?: number;
    leaseRenewalOptions?: string;
    monthlyLeaseAmount?: number;
    totalLeaseValue?: number;
    leaseEscalationRate?: number;
}

export const LeaseholdAssetsReport: React.FC<LeaseholdAssetsReportProps> = ({
    companyId,
    companyName,
    showAlert
}) => {
    const [loading, setLoading] = useState(false);
    const [leaseholdAssets, setLeaseholdAssets] = useState<LeaseholdAssetData[]>([]);
    const [selectedPeriod, setSelectedPeriod] = useState<string>('all');
    const [availableYears, setAvailableYears] = useState<string[]>([]);

    useEffect(() => {
        fetchLeaseholdAssets();
    }, [companyId]);

    const fetchLeaseholdAssets = async () => {
        setLoading(true);
        try {
            // Get all assets and filter for leasehold assets
            const allAssets = await api.getAssets(companyId);
            const leaseholdOnly = allAssets.filter(asset => asset.isLeasehold);
            
            // Get company data for financial years
            const companyData = await api.getCompanyData(companyId);
            if (companyData) {
                setAvailableYears(companyData.financialYears);
            }
            
            // Enhance leasehold assets with additional lease information
            const enhancedAssets = leaseholdOnly.map(asset => ({
                ...asset,
                leaseStartDate: asset.putToUseDate,
                leaseEndDate: calculateLeaseEndDate(asset.putToUseDate, asset.leasePeriod),
                remainingLeasePeriod: calculateRemainingLeasePeriod(asset.putToUseDate, asset.leasePeriod),
                totalLeaseValue: calculateTotalLeaseValue(asset.grossAmount, asset.leasePeriod),
                monthlyLeaseAmount: asset.leasePeriod > 0 ? asset.grossAmount / (asset.leasePeriod * 12) : 0
            }));
            
            setLeaseholdAssets(enhancedAssets);
            
            if (enhancedAssets.length === 0) {
                showAlert('No Data', 'No leasehold assets found for this company', 'info');
            }
        } catch (error) {
            console.error('Error fetching leasehold assets:', error);
            showAlert('Error', 'Failed to fetch leasehold assets data', 'error');
        } finally {
            setLoading(false);
        }
    };

    const calculateLeaseEndDate = (startDate: string | undefined, leasePeriod: number | undefined): string => {
        if (!startDate || !leasePeriod) return '';
        const start = new Date(startDate);
        const end = new Date(start);
        end.setFullYear(start.getFullYear() + leasePeriod);
        return end.toISOString().split('T')[0];
    };

    const calculateRemainingLeasePeriod = (startDate: string | undefined, leasePeriod: number | undefined): number => {
        if (!startDate || !leasePeriod) return 0;
        const start = new Date(startDate);
        const end = new Date(start);
        end.setFullYear(start.getFullYear() + leasePeriod);
        const now = new Date();
        
        if (now > end) return 0; // Lease expired
        
        const remainingMs = end.getTime() - now.getTime();
        const remainingYears = remainingMs / (1000 * 60 * 60 * 24 * 365.25);
        return Math.max(0, Math.round(remainingYears * 100) / 100);
    };

    const calculateTotalLeaseValue = (grossAmount: number | undefined, leasePeriod: number | undefined): number => {
        if (!grossAmount || !leasePeriod) return 0;
        // For leasehold assets, gross amount typically represents total lease value
        return grossAmount;
    };

    const filteredAssets = useMemo(() => {
        if (selectedPeriod === 'all') return leaseholdAssets;
        
        return leaseholdAssets.filter(asset => {
            const assetYear = asset.putToUseDate ? asset.putToUseDate.substring(0, 4) : '';
            return selectedPeriod.includes(assetYear);
        });
    }, [leaseholdAssets, selectedPeriod]);

    const summaryStats = useMemo(() => {
        const totalAssets = filteredAssets.length;
        const totalValue = filteredAssets.reduce((sum, asset) => sum + (asset.grossAmount || 0), 0);
        const activeLeases = filteredAssets.filter(asset => (asset.remainingLeasePeriod || 0) > 0).length;
        const expiredLeases = filteredAssets.filter(asset => (asset.remainingLeasePeriod || 0) === 0).length;
        const avgLeasePeriod = totalAssets > 0 
            ? filteredAssets.reduce((sum, asset) => sum + (asset.leasePeriod || 0), 0) / totalAssets 
            : 0;

        return {
            totalAssets,
            totalValue,
            activeLeases,
            expiredLeases,
            avgLeasePeriod
        };
    }, [filteredAssets]);

    const exportReport = () => {
        if (filteredAssets.length === 0) {
            showAlert('No Data', 'No leasehold assets available to export', 'info');
            return;
        }

        const exportData = filteredAssets.map(asset => ({
            'Asset ID': asset.recordId,
            'Asset Particulars': asset.assetParticulars,
            'Asset Group': asset.assetGroup,
            'Asset Sub-Group': asset.assetSubGroup,
            'Gross Amount': asset.grossAmount,
            'Put to Use Date': asset.putToUseDate,
            'Lease Period (Years)': asset.leasePeriod,
            'Lease Start Date': asset.leaseStartDate,
            'Lease End Date': asset.leaseEndDate,
            'Remaining Lease Period (Years)': asset.remainingLeasePeriod,
            'Monthly Lease Amount': Math.round(asset.monthlyLeaseAmount || 0),
            'Total Lease Value': asset.totalLeaseValue,
            'Depreciation Method': asset.depreciationMethod,
            'Life in Years': asset.lifeInYears,
            'Location': asset.location,
            'Vendor': asset.vendor,
            'Status': (asset.remainingLeasePeriod || 0) > 0 ? 'Active' : 'Expired'
        }));

        const filename = `Leasehold_Assets_Report_${companyName.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}`;
        exportToExcel(exportData, filename, `Leasehold Assets Report - ${companyName}`);
        
        showAlert('Success', `Leasehold assets report exported successfully as ${filename}.xlsx`, 'success');
    };

    if (loading) {
        return (
            <div className="loading-container">
                <div className="loading-spinner"></div>
                <p>Loading leasehold assets data...</p>
            </div>
        );
    }

    return (
        <div className="report-container">
            <div className="report-header">
                <div className="report-title">
                    <FolderIcon className="icon-info" />
                    <h2>Leasehold Assets Report</h2>
                </div>
                <div className="actions">
                    <select 
                        value={selectedPeriod} 
                        onChange={(e) => setSelectedPeriod(e.target.value)}
                        className="form-select"
                        title="Filter by financial year"
                    >
                        <option value="all">All Years</option>
                        {availableYears.map(year => (
                            <option key={year} value={year}>{year}</option>
                        ))}
                    </select>
                    <button 
                        type="button" 
                        className="btn btn-excel" 
                        onClick={exportReport}
                        disabled={loading || filteredAssets.length === 0}
                    >
                        <DownloadIcon /> Export
                    </button>
                </div>
            </div>

            {/* Summary Cards */}
            <div className="summary-cards">
                <div className="summary-card">
                    <div className="summary-card-header">
                        <FileTextIcon className="icon-info" />
                        <h3>Leasehold Assets Summary</h3>
                    </div>
                    <div className="summary-card-content">
                        <div className="summary-stat">
                            <span className="summary-label">Total Leasehold Assets:</span>
                            <span className="summary-value">{summaryStats.totalAssets}</span>
                        </div>
                        <div className="summary-stat">
                            <span className="summary-label">Total Value:</span>
                            <span className="summary-value">{formatIndianNumber(summaryStats.totalValue)}</span>
                        </div>
                        <div className="summary-stat">
                            <span className="summary-label">Active Leases:</span>
                            <span className="summary-value">{summaryStats.activeLeases}</span>
                        </div>
                        <div className="summary-stat">
                            <span className="summary-label">Expired Leases:</span>
                            <span className="summary-value">{summaryStats.expiredLeases}</span>
                        </div>
                        <div className="summary-stat">
                            <span className="summary-label">Average Lease Period:</span>
                            <span className="summary-value">{summaryStats.avgLeasePeriod.toFixed(1)} years</span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Leasehold Assets Table */}
            {filteredAssets.length > 0 ? (
                <div className="table-container">
                    <table className="data-table">
                        <thead>
                            <tr>
                                <th>Asset ID</th>
                                <th>Asset Particulars</th>
                                <th>Asset Group</th>
                                <th className="text-right">Gross Amount</th>
                                <th>Lease Period</th>
                                <th>Lease Start</th>
                                <th>Lease End</th>
                                <th className="text-right">Remaining Period</th>
                                <th className="text-right">Monthly Amount</th>
                                <th>Status</th>
                                <th>Location</th>
                            </tr>
                        </thead>
                        <tbody>
                            {filteredAssets.map(asset => (
                                <tr key={asset.recordId}>
                                    <td>{asset.recordId}</td>
                                    <td>{asset.assetParticulars}</td>
                                    <td>{asset.assetGroup}</td>
                                    <td className="text-right">{formatIndianNumber(asset.grossAmount || 0)}</td>
                                    <td>{asset.leasePeriod} years</td>
                                    <td>{asset.leaseStartDate}</td>
                                    <td>{asset.leaseEndDate}</td>
                                    <td className="text-right">
                                        <span className={`lease-status ${(asset.remainingLeasePeriod || 0) > 0 ? 'active' : 'expired'}`}>
                                            {asset.remainingLeasePeriod?.toFixed(1)} years
                                        </span>
                                    </td>
                                    <td className="text-right">{formatIndianNumber(Math.round(asset.monthlyLeaseAmount || 0))}</td>
                                    <td>
                                        <span className={`status-badge ${(asset.remainingLeasePeriod || 0) > 0 ? 'active' : 'expired'}`}>
                                            {(asset.remainingLeasePeriod || 0) > 0 ? 'Active' : 'Expired'}
                                        </span>
                                    </td>
                                    <td>{asset.location}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            ) : (
                <div className="no-data">
                    <FolderIcon className="icon-muted" />
                    <p>No leasehold assets found for the selected period.</p>
                </div>
            )}
        </div>
    );
};
