

/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo } from 'react';
import { api } from '../lib/api';
import type { Asset } from '../lib/db-server';
import { DataViewProps } from '../lib/types';
import { sortData, exportToExcel } from '../lib/utils';
import { DownloadIcon, SaveIcon, XIcon, EditIcon } from '../Icons';
import { Highlight } from './PlaceholderViews';

export function ExtraShiftDays({ companyId, companyName, year, financialYears, showAlert, showConfirmation, loggedInUser, unlockedYear }: DataViewProps) {
    const [assets, setAssets] = useState<Asset[]>([]);
    const [originalEligibleAssets, setOriginalEligibleAssets] = useState<Asset[]>([]);
    const [eligibleAssets, setEligibleAssets] = useState<Asset[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isDirty, setIsDirty] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>({ key: 'recordId', direction: 'asc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);
    const [filterText, setFilterText] = useState('');
    const [isEditMode, setIsEditMode] = useState(false);

    const secondShiftKey = `2nd Shift Days-${year}`;
    const thirdShiftKey = `3rd Shift Days-${year}`;

    const isLatestYear = year === financialYears[financialYears.length - 1];
    const isLocked = !isLatestYear && year !== unlockedYear;

    useEffect(() => {
        if (!companyId) {
            setLoading(false);
            setAssets([]);
            setEligibleAssets([]);
            setSelectedRowId(null);
            setSelectedColumnKey(null);
            setFilterText('');
            return;
        }

        const fetchData = async () => {
            setLoading(true);
            setError(null);
            setIsDirty(false);
            setSelectedRowId(null);
            setSelectedColumnKey(null);
            setFilterText('');
            try {
                const [allAssets, allRates] = await Promise.all([
                    api.getAssets(companyId),
                    api.getStatutoryRates(companyId)
                ]);
                
                const shiftEligibleMap = allRates.reduce((acc, rate) => {
                    if (rate.extraShiftDepreciation === 'Yes') {
                        acc[rate.assetSubGroup] = true;
                    }
                    return acc;
                }, {} as Record<string, boolean>);

                const filtered = allAssets.filter(asset => shiftEligibleMap[asset.assetSubGroup]);
                const deepClonedData = JSON.parse(JSON.stringify(filtered));
                setAssets(allAssets); // Keep all assets for saving later
                setOriginalEligibleAssets(deepClonedData);
                setEligibleAssets(deepClonedData); 
            } catch (err) {
                setError('Failed to load data for Extra Shift Days.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [companyId, year]);

    const sortedEligibleAssets = useMemo(() => {
        return sortData(eligibleAssets, sortConfig);
    }, [eligibleAssets, sortConfig]);

    const searchableFields: (keyof Asset)[] = ['recordId', 'assetParticulars'];

    const filteredEligibleAssets = useMemo(() => {
        if (!filterText) return sortedEligibleAssets;
        const searchTerm = filterText.trim().toLowerCase();
        return sortedEligibleAssets.filter(asset =>
            searchableFields.some(field =>
                String(asset[field] ?? '').toLowerCase().includes(searchTerm)
            )
        );
    }, [sortedEligibleAssets, filterText]);

    const requestSort = (key: string) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: string) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };

    const getHeaderClass = (key: string) => {
        const classes = ['sortable'];
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        if ([secondShiftKey, thirdShiftKey].includes(key)) classes.push('text-right');
        return classes.join(' ');
    };

    const getColumnClass = (key: string) => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if ([secondShiftKey, thirdShiftKey].includes(key)) classes.push('text-right');
        return classes.join(' ');
    };

    const handleShiftDayChange = (recordId: string, shiftType: '2nd' | '3rd', value: string) => {
        if (isLocked) return;
        const day = parseInt(value, 10);
        if (isNaN(day) && value !== '') return; 

        const key = `${shiftType} Shift Days-${year}`;
        
        setEligibleAssets(prev => prev.map(asset => 
            asset.recordId === recordId ? { ...asset, [key]: isNaN(day) ? 0 : day } : asset
        ));
        setIsDirty(true);
    };

    const handleSaveChanges = async () => {
        if (!companyId) return;
        setIsSaving(true);
        
        try {
            const latestFullAssetList = await api.getAssets(companyId);

            const updatedEligibleMap = eligibleAssets.reduce((acc, asset) => {
                acc[asset.recordId] = asset;
                return acc;
            }, {} as Record<string, Asset>);
    
            const updatedFullAssetList = latestFullAssetList.map(asset => 
                updatedEligibleMap[asset.recordId] || asset
            );
            
            if (loggedInUser) {
                const originalAssetsMap = new Map(originalEligibleAssets.map(a => [a.recordId, a]));
                const changedAssets = eligibleAssets.filter(ea => {
                    const oa = originalAssetsMap.get(ea.recordId);
                    if (!oa) return true; // Should not happen, but treat as changed if it is new
                    return (ea[secondShiftKey] ?? 0) !== (oa[secondShiftKey] ?? 0) || (ea[thirdShiftKey] ?? 0) !== (oa[thirdShiftKey] ?? 0);
                });

                if (changedAssets.length > 0) {
                     const changeDetails = changedAssets.map(ea => {
                        const oa = originalAssetsMap.get(ea.recordId)!;
                        const oldSecond = oa[secondShiftKey] ?? 0;
                        const newSecond = ea[secondShiftKey] ?? 0;
                        const oldThird = oa[thirdShiftKey] ?? 0;
                        const newThird = ea[thirdShiftKey] ?? 0;
                        
                        const changes = [];
                        if (oldSecond !== newSecond) {
                            changes.push(`2nd Shift Days: ${oldSecond} -> ${newSecond}`);
                        }
                        if (oldThird !== newThird) {
                            changes.push(`3rd Shift Days: ${oldThird} -> ${newThird}`);
                        }
                        return `Asset '${ea.assetParticulars}' (${ea.recordId}) -> ${changes.join(', ')}`;
                    }).join('. ');

                    await api.addAuditLog(companyId, {
                        userId: loggedInUser.id,
                        username: loggedInUser.username,
                        action: 'UPDATE_EXTRA_SHIFT_DAYS',
                        details: `Updated extra shift days for ${changedAssets.length} asset(s) for FY ${year}. Changes: ${changeDetails}`
                    });
                }
            }

            await api.updateAssets(companyId, updatedFullAssetList);
            const newOriginalEligible = eligibleAssets.filter(ea => updatedFullAssetList.some(fa => fa.recordId === ea.recordId));
            setOriginalEligibleAssets(JSON.parse(JSON.stringify(newOriginalEligible)));
            setAssets(updatedFullAssetList); 
            setIsDirty(false);
            showAlert("Success", "Shift day records saved successfully!", 'success');
        } catch (error) {
            console.error("Failed to save shift day records:", error);
            showAlert("Error", "Error saving shift day records. Please try again.", 'error');
        } finally {
            setIsSaving(false);
        }
    };

    const handleToggleEditMode = () => {
        if (isEditMode && isDirty) {
            showConfirmation(
                'Exit Edit Mode',
                'You have unsaved changes. Are you sure you want to exit edit mode? All changes will be lost.',
                () => {
                    setEligibleAssets(JSON.parse(JSON.stringify(originalEligibleAssets)));
                    setIsDirty(false);
                    setIsEditMode(false);
                }
            );
        } else {
            setIsEditMode(!isEditMode);
            if (!isEditMode) {
                // Entering edit mode - reset any unsaved changes
                setEligibleAssets(JSON.parse(JSON.stringify(originalEligibleAssets)));
                setIsDirty(false);
            }
        }
    };

    const handleCancelChanges = () => {
        showConfirmation(
            'Discard Changes',
            'Are you sure you want to discard all changes?',
            () => {
                setEligibleAssets(JSON.parse(JSON.stringify(originalEligibleAssets)));
                setIsDirty(false);
            }
        );
    };
    
    const handleExport = () => {
        if (!companyId || !companyName || !year) return;
        
        const dataToExport = filteredEligibleAssets.map(asset => ({
            "Record ID": asset.recordId,
            "Asset Particulars": asset.assetParticulars,
            "2nd Shift Days": asset[secondShiftKey] ?? 0,
            "3rd Shift Days": asset[thirdShiftKey] ?? 0,
        }));
        
        if (!exportToExcel({ data: dataToExport, companyName, year, reportName: 'Extra_Shift_Days' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    if (!companyId) return null;
    if (loading) return <div className="loading-indicator">Loading Extra Shift Days...</div>;
    if (error) return <div className="error-message">{error}</div>;

    return (
        <>
            <div className="view-header">
                <h2>Extra Shift Days ({year})</h2>
                <div className="actions">
                    <button type="button" className="btn btn-excel" onClick={handleExport} disabled={isSaving || loading}>
                        <DownloadIcon /> Export to Excel
                    </button>
                    {!isLocked && (
                        <button
                            type="button"
                            className={`btn ${isEditMode ? 'btn-secondary' : 'btn-primary'}`}
                            onClick={handleToggleEditMode}
                            disabled={isSaving}
                        >
                            <EditIcon /> {isEditMode ? 'Exit Edit Mode' : 'Edit Mode'}
                        </button>
                    )}
                    {isDirty && <button type="button" className="btn btn-warning" onClick={handleCancelChanges} disabled={isSaving || isLocked}><XIcon /> Cancel</button>}
                    {isDirty && <button type="button" className="btn btn-primary" onClick={handleSaveChanges} disabled={isSaving || isLocked}><SaveIcon /> {isSaving ? 'Saving...' : 'Save Changes'}</button>}
                </div>
            </div>
            <div className="filter-container">
                <div className="filter-input-wrapper">
                    <input
                        type="text"
                        placeholder="Filter by Record ID or Particulars..."
                        className="filter-input"
                        value={filterText}
                        onChange={e => setFilterText(e.target.value)}
                        disabled={isSaving || loading}
                        aria-label="Filter eligible assets"
                    />
                    <button
                        className={`filter-clear-btn ${!filterText ? 'hidden' : ''}`}
                        onClick={() => setFilterText('')}
                        aria-label="Clear filter"
                        title="Clear filter"
                        disabled={isSaving || loading}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </div>
                <div className="filter-info">
                    <span className="filter-info-icon">ⓘ</span>
                    <div className="filter-info-tooltip">
                        <strong>Filtering on:</strong>
                        <ul>
                            <li>Record ID</li>
                            <li>Asset Particulars</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div className="table-container">
                <table className="extra-shift-days-table">
                    <thead>
                        <tr>
                            <th className={getHeaderClass('recordId')} onClick={() => requestSort('recordId')}>Record ID{getSortIndicator('recordId')}</th>
                            <th className={getHeaderClass('assetParticulars')} onClick={() => requestSort('assetParticulars')}>Asset Particulars{getSortIndicator('assetParticulars')}</th>
                            <th className={getHeaderClass(secondShiftKey)} onClick={() => requestSort(secondShiftKey)}>2nd Shift Days{getSortIndicator(secondShiftKey)}</th>
                            <th className={getHeaderClass(thirdShiftKey)} onClick={() => requestSort(thirdShiftKey)}>3rd Shift Days{getSortIndicator(thirdShiftKey)}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredEligibleAssets.map(asset => (
                            <tr key={asset.recordId} onClick={() => setSelectedRowId(asset.recordId)} className={asset.recordId === selectedRowId ? 'selected-row' : ''}>
                                <td className={getColumnClass('recordId')}><Highlight text={asset.recordId} highlight={filterText} /></td>
                                <td className={getColumnClass('assetParticulars')}><Highlight text={asset.assetParticulars} highlight={filterText} /></td>
                                <td className={getColumnClass(secondShiftKey)}>
                                    <input
                                        type="number"
                                        className="table-input"
                                        value={asset[secondShiftKey] ?? 0}
                                        onChange={(e) => handleShiftDayChange(asset.recordId, '2nd', e.target.value)}
                                        disabled={!isEditMode || isSaving || isLocked}
                                        title={`2nd Shift Days for ${asset.assetParticulars}`}
                                        aria-label={`2nd Shift Days for ${asset.assetParticulars}`}
                                    />
                                </td>
                                <td className={getColumnClass(thirdShiftKey)}>
                                    <input
                                        type="number"
                                        className="table-input"
                                        value={asset[thirdShiftKey] ?? 0}
                                        onChange={(e) => handleShiftDayChange(asset.recordId, '3rd', e.target.value)}
                                        disabled={!isEditMode || isSaving || isLocked}
                                        title={`3rd Shift Days for ${asset.assetParticulars}`}
                                        aria-label={`3rd Shift Days for ${asset.assetParticulars}`}
                                    />
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </>
    );
}

