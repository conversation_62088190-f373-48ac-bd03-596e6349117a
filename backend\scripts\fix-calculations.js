import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const dbPath = join(__dirname, '../database/far_sighted.db');

console.log('🔧 Fixing Depreciation Calculations...\n');

const fixCalculations = async () => {
    const db = new sqlite3.Database(dbPath);

    // Helper functions
    const query = (sql, params = []) => {
        return new Promise((resolve, reject) => {
            db.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    };

    const runQuery = (sql, params = []) => {
        return new Promise((resolve, reject) => {
            db.run(sql, params, function(err) {
                if (err) reject(err);
                else resolve({ id: this.lastID, changes: this.changes });
            });
        });
    };

    try {
        console.log('📋 Step 1: Getting all assets...');
        
        // Get all assets with their details
        const assets = await query(`
            SELECT 
                id, record_id, asset_particulars, 
                gross_amount, wdv_of_adoption_date,
                put_to_use_date, depreciation_method, 
                life_in_years, asset_group, asset_sub_group
            FROM assets 
            ORDER BY record_id
        `);

        console.log(`   Found ${assets.length} assets to recalculate\n`);

        // Define correct depreciation rates per Schedule II
        const depreciationRates = {
            'Plant & Machinery': { rate: 0.15, method: 'WDV' }, // 15% for general P&M
            'Computer & Data Processing': { rate: 0.60, method: 'WDV' }, // 60% for computers
            'Computer Equipment': { rate: 0.60, method: 'WDV' }, // 60% for computers
            'Office Equipment': { rate: 0.10, method: 'WDV' }, // 10% for furniture
            'Furniture & Fixtures': { rate: 0.10, method: 'WDV' }, // 10% for furniture
            'Motor Vehicles': { rate: 0.15, method: 'WDV' }, // 15% for vehicles
            'Power Generation Equipment': { rate: 0.15, method: 'WDV' } // 15% for power equipment
        };

        // Financial years to calculate (chronological order)
        const financialYears = ['2022-2023', '2023-2024', '2024-2025'];

        console.log('🗑️  Step 2: Clearing existing incorrect yearly data...');
        await runQuery('DELETE FROM asset_yearly_data');
        console.log('   Cleared existing yearly data\n');

        console.log('💰 Step 3: Recalculating with correct depreciation rates...\n');

        for (const asset of assets) {
            console.log(`🏭 Processing: ${asset.record_id} - ${asset.asset_particulars}`);
            
            // Determine depreciation rate based on asset group
            let depRate = 0.095; // Default 9.5%
            let depMethod = 'WDV';
            
            // Find matching rate from Schedule II
            for (const [group, rateInfo] of Object.entries(depreciationRates)) {
                if (asset.asset_group?.includes(group) || 
                    asset.asset_sub_group?.includes(group) ||
                    asset.asset_particulars?.toLowerCase().includes(group.toLowerCase())) {
                    depRate = rateInfo.rate;
                    depMethod = rateInfo.method;
                    break;
                }
            }

            console.log(`   Asset Group: ${asset.asset_group}`);
            console.log(`   Purchase Value: ₹${(asset.gross_amount/100000).toFixed(2)}L`);
            console.log(`   Depreciation Rate: ${(depRate * 100).toFixed(1)}% (${depMethod})`);
            console.log(`   Put to Use: ${asset.put_to_use_date}\n`);

            // Starting WDV is the gross amount or WDV at adoption
            let currentWDV = asset.wdv_of_adoption_date || asset.gross_amount;
            
            // Parse put to use date to determine when depreciation starts
            const putToUseDate = new Date(asset.put_to_use_date || '2022-04-01');
            
            for (const year of financialYears) {
                const [startYear, endYear] = year.split('-');
                const yearStart = new Date(`${startYear}-04-01`);
                const yearEnd = new Date(`${endYear}-03-31`);
                
                // Check if asset was in use during this year
                if (putToUseDate > yearEnd) {
                    // Asset not yet in use - skip this year
                    continue;
                }
                
                // Calculate days in use during this financial year
                const useStartDate = putToUseDate > yearStart ? putToUseDate : yearStart;
                const useDays = Math.min(365, Math.ceil((yearEnd - useStartDate) / (1000 * 60 * 60 * 24)) + 1);
                
                // Calculate depreciation amount
                let depreciationAmount = 0;
                if (depMethod === 'WDV') {
                    // Written Down Value method - percentage of opening WDV
                    depreciationAmount = currentWDV * depRate * (useDays / 365);
                } else {
                    // Straight Line Method - percentage of original cost
                    depreciationAmount = asset.gross_amount * depRate * (useDays / 365);
                }
                
                const openingWDV = currentWDV;
                const closingWDV = currentWDV - depreciationAmount;
                
                console.log(`   📅 ${year}:`);
                console.log(`      Opening WDV: ₹${(openingWDV/100000).toFixed(2)}L`);
                console.log(`      Use Days: ${useDays}`);
                console.log(`      Depreciation: ₹${(depreciationAmount/100000).toFixed(2)}L`);
                console.log(`      Closing WDV: ₹${(closingWDV/100000).toFixed(2)}L`);
                
                // Insert yearly data
                await runQuery(`
                    INSERT INTO asset_yearly_data (
                        asset_id, year_range, opening_wdv, use_days, 
                        depreciation_amount, closing_wdv, second_shift_days, third_shift_days
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    asset.id, year, Math.round(openingWDV), useDays, 
                    Math.round(depreciationAmount), Math.round(closingWDV), 0, 0
                ]);
                
                // Update current WDV for next year (carry-forward)
                currentWDV = closingWDV;
            }
            console.log('   ✅ Completed\n---\n');
        }

        console.log('✅ All calculations fixed with proper Schedule II rates!');
        console.log('🔄 WDV carry-forward now works correctly year-over-year');
        console.log('📊 Each asset now has accurate depreciation calculations\n');

        // Summary of corrected rates applied
        console.log('📋 Depreciation Rates Applied (Schedule II Compliant):');
        console.log('   🖥️  Computers: 60% WDV');
        console.log('   🏭 Plant & Machinery: 15% WDV');
        console.log('   🪑 Furniture & Fixtures: 10% WDV');
        console.log('   🚛 Motor Vehicles: 15% WDV');
        console.log('   ⚡ Power Generation: 15% WDV\n');

    } catch (error) {
        console.error('❌ Error fixing calculations:', error);
    } finally {
        db.close();
    }
};

fixCalculations();