/**
 * Script to load comprehensive sample data for testing
 * This script will populate all companies with realistic test data
 */

import DatabaseManager from '../services/database-manager/DatabaseManager.js';

const sampleAssets = [
    // Building assets
    {
        recordId: 'BLDG001',
        assetParticulars: 'Corporate Office Building - Mumbai',
        bookEntryDate: '2022-04-01',
        putToUseDate: '2022-04-01',
        basicAmount: 50000000,
        dutiesTaxes: 5000000,
        grossAmount: 55000000,
        vendor: 'ABC Construction Ltd',
        invoiceNo: 'INV-2022-001',
        modelMake: 'RCC Frame Structure',
        location: 'Mumbai, Maharashtra',
        assetId: 'BLDG-MUM-001',
        remarks: 'Head office building',
        ledgerNameInBooks: 'Buildings',
        assetGroup: 'Buildings',
        assetSubGroup: 'Buildings (other than factory buildings) RCC Frame Structure',
        scheduleIIIClassification: 'Buildings - Freehold',
        depreciationMethod: 'WDV',
        lifeInYears: 60,
        salvagePercentage: 5,
        wdvOfAdoptionDate: 52000000,
        isLeasehold: false,
        disposalDate: null,
        disposalAmount: null,
        scrapIt: false
    },
    {
        recordId: 'BLDG002',
        assetParticulars: 'Factory Building - Pune',
        bookEntryDate: '2021-03-15',
        putToUseDate: '2021-06-01',
        basicAmount: 30000000,
        dutiesTaxes: 3000000,
        grossAmount: 33000000,
        vendor: 'XYZ Builders Pvt Ltd',
        invoiceNo: 'INV-2021-045',
        modelMake: 'Industrial Structure',
        location: 'Pune, Maharashtra',
        assetId: 'BLDG-PUN-001',
        remarks: 'Manufacturing facility',
        ledgerNameInBooks: 'Factory Buildings',
        assetGroup: 'Buildings',
        assetSubGroup: 'Factory building',
        scheduleIIIClassification: 'Buildings - Freehold',
        depreciationMethod: 'WDV',
        lifeInYears: 30,
        salvagePercentage: 5,
        wdvOfAdoptionDate: 31000000,
        isLeasehold: false,
        disposalDate: null,
        disposalAmount: null,
        scrapIt: false
    },
    // Plant and Machinery
    {
        recordId: 'PM001',
        assetParticulars: 'CNC Machining Center - Haas VF-2',
        bookEntryDate: '2022-08-15',
        putToUseDate: '2022-09-01',
        basicAmount: 2500000,
        dutiesTaxes: 450000,
        grossAmount: 2950000,
        vendor: 'Haas Automation India',
        invoiceNo: 'HAAS-2022-789',
        modelMake: 'Haas VF-2',
        location: 'Pune Factory',
        assetId: 'PM-CNC-001',
        remarks: 'High precision machining center',
        ledgerNameInBooks: 'Plant and Machinery',
        assetGroup: 'Plant and Machinery',
        assetSubGroup: 'General plant and machinery',
        scheduleIIIClassification: 'Plant and machinery - General',
        depreciationMethod: 'WDV',
        lifeInYears: 15,
        salvagePercentage: 10,
        wdvOfAdoptionDate: null,
        isLeasehold: false,
        disposalDate: null,
        disposalAmount: null,
        scrapIt: false
    },
    {
        recordId: 'PM002',
        assetParticulars: 'Continuous Process Plant - Chemical Reactor',
        bookEntryDate: '2020-12-01',
        putToUseDate: '2021-01-15',
        basicAmount: 15000000,
        dutiesTaxes: 2700000,
        grossAmount: 17700000,
        vendor: 'Process Equipment Ltd',
        invoiceNo: 'PEL-2020-456',
        modelMake: 'SS316L Reactor 5000L',
        location: 'Pune Factory',
        assetId: 'PM-REACT-001',
        remarks: 'Main production reactor',
        ledgerNameInBooks: 'Process Equipment',
        assetGroup: 'Plant and Machinery',
        assetSubGroup: 'Continuous process plant',
        scheduleIIIClassification: 'Plant and machinery - Continuous process',
        depreciationMethod: 'WDV',
        lifeInYears: 25,
        salvagePercentage: 5,
        wdvOfAdoptionDate: 16500000,
        isLeasehold: false,
        disposalDate: null,
        disposalAmount: null,
        scrapIt: false
    },
    // Computer Equipment
    {
        recordId: 'COMP001',
        assetParticulars: 'Dell Workstation - Precision 7000 Series',
        bookEntryDate: '2023-01-15',
        putToUseDate: '2023-01-20',
        basicAmount: 150000,
        dutiesTaxes: 27000,
        grossAmount: 177000,
        vendor: 'Dell Technologies India',
        invoiceNo: 'DELL-2023-123',
        modelMake: 'Dell Precision 7760',
        location: 'Mumbai Office',
        assetId: 'COMP-WS-001',
        remarks: 'CAD workstation for engineering',
        ledgerNameInBooks: 'Computer Equipment',
        assetGroup: 'Computer and data processing units',
        assetSubGroup: 'Computer including computer software',
        scheduleIIIClassification: 'Computer including computer software',
        depreciationMethod: 'WDV',
        lifeInYears: 3,
        salvagePercentage: 5,
        wdvOfAdoptionDate: null,
        isLeasehold: false,
        disposalDate: null,
        disposalAmount: null,
        scrapIt: false
    },
    // Vehicles
    {
        recordId: 'VEH001',
        assetParticulars: 'Toyota Innova Crysta - Company Vehicle',
        bookEntryDate: '2022-06-01',
        putToUseDate: '2022-06-05',
        basicAmount: 1800000,
        dutiesTaxes: 324000,
        grossAmount: 2124000,
        vendor: 'Toyota Kirloskar Motor',
        invoiceNo: 'TKM-2022-567',
        modelMake: 'Toyota Innova Crysta 2.4 VX',
        location: 'Mumbai Office',
        assetId: 'VEH-INN-001',
        remarks: 'Executive transport vehicle',
        ledgerNameInBooks: 'Motor Vehicles',
        assetGroup: 'Motor vehicles other than motor car',
        assetSubGroup: 'Motor vehicles other than motor car',
        scheduleIIIClassification: 'Motor vehicles other than motor car',
        depreciationMethod: 'WDV',
        lifeInYears: 8,
        salvagePercentage: 5,
        wdvOfAdoptionDate: null,
        isLeasehold: false,
        disposalDate: null,
        disposalAmount: null,
        scrapIt: false
    },
    // Furniture
    {
        recordId: 'FURN001',
        assetParticulars: 'Executive Office Furniture Set',
        bookEntryDate: '2022-04-10',
        putToUseDate: '2022-04-15',
        basicAmount: 250000,
        dutiesTaxes: 45000,
        grossAmount: 295000,
        vendor: 'Godrej Interio',
        invoiceNo: 'GI-2022-890',
        modelMake: 'Executive Series Premium',
        location: 'Mumbai Office',
        assetId: 'FURN-EXEC-001',
        remarks: 'CEO office furniture',
        ledgerNameInBooks: 'Furniture and Fixtures',
        assetGroup: 'Furniture and fittings',
        assetSubGroup: 'Furniture and fittings',
        scheduleIIIClassification: 'Furniture and fittings',
        depreciationMethod: 'WDV',
        lifeInYears: 10,
        salvagePercentage: 5,
        wdvOfAdoptionDate: null,
        isLeasehold: false,
        disposalDate: null,
        disposalAmount: null,
        scrapIt: false
    },
    // Disposed Asset (for testing disposal functionality)
    {
        recordId: 'COMP002',
        assetParticulars: 'Old Desktop Computer - HP EliteDesk',
        bookEntryDate: '2019-04-01',
        putToUseDate: '2019-04-05',
        basicAmount: 45000,
        dutiesTaxes: 8100,
        grossAmount: 53100,
        vendor: 'HP India Sales Pvt Ltd',
        invoiceNo: 'HP-2019-234',
        modelMake: 'HP EliteDesk 800 G4',
        location: 'Mumbai Office',
        assetId: 'COMP-HP-002',
        remarks: 'Disposed due to obsolescence',
        ledgerNameInBooks: 'Computer Equipment',
        assetGroup: 'Computer and data processing units',
        assetSubGroup: 'Computer including computer software',
        scheduleIIIClassification: 'Computer including computer software',
        depreciationMethod: 'WDV',
        lifeInYears: 3,
        salvagePercentage: 5,
        wdvOfAdoptionDate: 48000,
        isLeasehold: false,
        disposalDate: '2023-03-31',
        disposalAmount: 5000,
        scrapIt: false
    }
];

async function loadSampleData() {
    const dbManager = new DatabaseManager();
    await dbManager.init();
    
    console.log('🔄 Loading comprehensive sample data...');
    
    try {
        // Get all companies
        const companies = await dbManager.getAllMasterQuery('SELECT * FROM companies');
        console.log(`📊 Found ${companies.length} companies to populate with data`);
        
        for (const company of companies) {
            console.log(`\n📁 Loading data for: ${company.company_name}`);
            
            // Get company database service
            const companyDb = await dbManager.getCompanyDatabase(company.id);
            
            // Set different adoption dates for testing
            let adoptionDate = '2024-04-01';
            if (company.company_name.includes('Test Company')) {
                // For test companies, use 2022 adoption date for carry-forward testing
                adoptionDate = '2022-04-01';
                console.log(`  📅 Using adoption date: ${adoptionDate} (for carry-forward testing)`);
            }
            
            // Insert sample assets
            for (const asset of sampleAssets) {
                try {
                    // Adjust asset data based on adoption date
                    const adjustedAsset = { ...asset };
                    
                    // For companies with 2022 adoption date, adjust WDV values
                    if (adoptionDate === '2022-04-01' && asset.wdvOfAdoptionDate) {
                        adjustedAsset.wdvOfAdoptionDate = asset.wdvOfAdoptionDate * 0.85; // Simulate some depreciation
                    }
                    
                    await companyDb.run(`
                        INSERT OR REPLACE INTO assets (
                            record_id, asset_particulars, book_entry_date, put_to_use_date,
                            basic_amount, duties_taxes, gross_amount, vendor, invoice_no,
                            model_make, location, asset_id, remarks, ledger_name_in_books,
                            asset_group, asset_sub_group, schedule_iii_classification,
                            depreciation_method, life_in_years, salvage_percentage,
                            wdv_of_adoption_date, is_leasehold, disposal_date, disposal_amount, scrap_it
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        adjustedAsset.recordId, adjustedAsset.assetParticulars, adjustedAsset.bookEntryDate,
                        adjustedAsset.putToUseDate, adjustedAsset.basicAmount, adjustedAsset.dutiesTaxes,
                        adjustedAsset.grossAmount, adjustedAsset.vendor, adjustedAsset.invoiceNo,
                        adjustedAsset.modelMake, adjustedAsset.location, adjustedAsset.assetId,
                        adjustedAsset.remarks, adjustedAsset.ledgerNameInBooks, adjustedAsset.assetGroup,
                        adjustedAsset.assetSubGroup, adjustedAsset.scheduleIIIClassification,
                        adjustedAsset.depreciationMethod, adjustedAsset.lifeInYears, adjustedAsset.salvagePercentage,
                        adjustedAsset.wdvOfAdoptionDate, adjustedAsset.isLeasehold ? 'Yes' : 'No',
                        adjustedAsset.disposalDate, adjustedAsset.disposalAmount, adjustedAsset.scrapIt ? 'Yes' : 'No'
                    ]);
                    
                    console.log(`  ✅ Added asset: ${asset.assetParticulars}`);
                } catch (error) {
                    console.log(`  ❌ Failed to add asset ${asset.assetParticulars}: ${error.message}`);
                }
            }
            
            // Add some ledger names for testing
            const ledgerNames = [
                'Buildings', 'Factory Buildings', 'Plant and Machinery', 'Process Equipment',
                'Computer Equipment', 'Motor Vehicles', 'Furniture and Fixtures', 'Office Equipment'
            ];
            
            for (const ledgerName of ledgerNames) {
                try {
                    await companyDb.run(`
                        INSERT OR IGNORE INTO ledger_master (ledger_name) VALUES (?)
                    `, [ledgerName]);
                } catch (error) {
                    // Ignore if table doesn't exist or other errors
                }
            }
            
            console.log(`  📚 Added ${ledgerNames.length} ledger names`);
            console.log(`  ✅ Completed data loading for ${company.company_name}`);
        }
        
        console.log('\n🎉 Sample data loading completed successfully!');
        console.log('\n📋 Summary of loaded data:');
        console.log(`  • ${sampleAssets.length} sample assets per company`);
        console.log(`  • Mix of building, machinery, computer, vehicle, and furniture assets`);
        console.log(`  • Different adoption dates for testing carry-forward scenarios`);
        console.log(`  • One disposed asset for testing disposal functionality`);
        console.log(`  • Various depreciation methods and asset groups`);
        
    } catch (error) {
        console.error('❌ Error loading sample data:', error);
    }
}

// Run the script
loadSampleData().catch(console.error);
