@echo off
setlocal enabledelayedexpansion

echo =====================================
echo FAR SIGHTED - DATABASE MIGRATION
echo Professional Advisory Services - CA
echo Multi-Database Architecture Setup
echo =====================================
echo.

REM Navigate to project directory
cd /d "E:\Projects\FAR Sighted"

REM Verify we're in correct directory
if not exist "backend\" (
    echo ❌ Error: backend directory not found
    echo    Current directory: %cd%
    echo    Please ensure you're in the correct project directory
    pause
    exit /b 1
)

cd backend

if not exist "package.json" (
    echo ❌ Error: backend package.json not found
    pause
    exit /b 1
)

echo 🔄 FAR Sighted Database Migration Tool
echo.
echo 📊 Current Status Check...

REM Check migration status first
echo    🔍 Checking current migration status...
npm run status 2>nul
if %errorlevel% neq 0 (
    echo    ⚠️  Unable to check status (backend may not be running)
    echo    This is normal if backend is not started yet.
)

echo.
echo 🎯 Migration Options:
echo    1. Run Migration (recommended)
echo    2. Dry Run (preview only, no changes)
echo    3. Force Migration (override existing structure)
echo    4. Check Status Only
echo    5. Exit
echo.

choice /c 12345 /m "Select an option"
set "migration_choice=%errorlevel%"

if %migration_choice%==1 (
    echo.
    echo 🚀 Running Database Migration...
    echo.
    echo ⚠️  IMPORTANT: This will:
    echo    • Backup your current database
    echo    • Create separate databases for each company
    echo    • Migrate all existing data
    echo    • Enable multi-database architecture
    echo.
    choice /c YN /m "Proceed with migration (Y/N)?"
    if errorlevel 2 (
        echo    ❌ Migration cancelled by user
        goto :end
    )
    
    echo.
    echo 📦 Starting migration process...
    npm run migrate
    
    if %errorlevel% == 0 (
        echo.
        echo ✅ Migration completed successfully!
        echo.
        echo 📊 Checking new system status...
        npm run system-info 2>nul
    ) else (
        echo.
        echo ❌ Migration failed
        echo    Check the error messages above for details
    )
    
) else if %migration_choice%==2 (
    echo.
    echo 🔍 Running Migration Dry Run (Preview Mode)...
    echo    This will show what would be migrated without making changes.
    echo.
    npm run migrate:dry-run
    
) else if %migration_choice%==3 (
    echo.
    echo ⚠️  Force Migration Warning!
    echo    This will run migration even if the new structure already exists.
    echo    Use this only if you need to re-migrate or fix issues.
    echo.
    choice /c YN /m "Are you sure you want to force migration (Y/N)?"
    if errorlevel 2 (
        echo    ❌ Force migration cancelled
        goto :end
    )
    
    echo.
    echo 🔥 Running forced migration...
    npm run migrate:force
    
    if %errorlevel% == 0 (
        echo.
        echo ✅ Forced migration completed!
    ) else (
        echo.
        echo ❌ Forced migration failed
    )
    
) else if %migration_choice%==4 (
    echo.
    echo 📊 Checking Migration Status...
    echo.
    echo 🔍 Migration Status:
    npm run status
    echo.
    echo 🏥 System Health:
    npm run health
    echo.
    echo 📋 System Information:
    npm run system-info
    
) else (
    echo.
    echo 👋 Exiting migration tool
    goto :end
)

echo.
echo 📋 Migration Summary:
echo.
echo 🏗️  New Architecture Features:
echo    • Separate SQLite database per company
echo    • Master database for global settings and users
echo    • Enhanced data isolation and security
echo    • Company-specific backup and restore
echo    • Improved scalability and performance
echo.
echo 📁 Database Locations:
echo    • Master DB: backend/database/master.db
echo    • Company DBs: backend/database/companies/*/company.db
echo    • Backup: backend/database/far_sighted.db.migration-backup.*
echo.
echo 🎯 Next Steps:
echo    1. Start the application: start-far-app.bat
echo    2. Test login and company access
echo    3. Verify data integrity
echo    4. Run reports to confirm functionality
echo.

:end
echo 💡 Migration Tool Complete
echo    For additional help, check the documentation files:
echo    • MULTI_DATABASE_IMPLEMENTATION.md
echo    • MIGRATION_DEPLOYMENT_GUIDE.md
echo.
pause