/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useMemo, useState, FC } from 'react';
import { DataViewProps } from '../lib/types';
import { InfoIcon, AlertTriangleIcon, CheckCircleIcon } from '../Icons';

/**
 * A component that highlights occurrences of a substring within a text.
 * It's case-insensitive and safe for special regex characters.
 */
export const Highlight: FC<{ text: string | null | undefined; highlight: string }> = ({ text, highlight }) => {
    const textStr = String(text || '');
    const trimmedHighlight = highlight.trim();

    if (!trimmedHighlight || !textStr) {
        return <>{textStr}</>;
    }
    
    // Escape regex special characters in the highlight string
    const escapedHighlight = trimmedHighlight.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
    
    // Should not happen, but as a safeguard
    if (!escapedHighlight) {
        return <>{textStr}</>;
    }

    const regex = new RegExp(`(${escapedHighlight})`, 'gi');
    const parts = textStr.split(regex);
    const lowerCaseHighlight = trimmedHighlight.toLowerCase();

    return (
        <>
            {parts.map((part, i) =>
                // The matched parts from split will be case-variations of the highlight string.
                // A simple case-insensitive comparison is sufficient to identify them.
                // The original implementation's `regex.test()` was stateful and unreliable in a loop.
                part.toLowerCase() === lowerCaseHighlight ? (
                    <mark key={i}>{part}</mark>
                ) : (
                    <React.Fragment key={i}>{part}</React.Fragment>
                )
            )}
        </>
    );
};

export function ThemeSettings({ theme, onThemeChange }: DataViewProps) {
    if (!onThemeChange) return <h2>Theme Settings</h2>;

    return (
        <>
            <div className="view-header">
                <h2>Theme Settings</h2>
            </div>
            <div className="settings-container">
                <h3 className="settings-title">Select App Theme</h3>
                <p className="settings-description">Choose between a light or dark theme for the application. Your preference will be saved for your next visit.</p>
                <div className="theme-selector">
                    <button
                        className={`btn theme-btn ${theme === 'light' ? 'active' : ''}`}
                        onClick={() => onThemeChange('light')}
                        aria-pressed={theme === 'light'}
                    >
                        Light
                    </button>
                    <button
                        className={`btn theme-btn ${theme === 'dark' ? 'active' : ''}`}
                        onClick={() => onThemeChange('dark')}
                        aria-pressed={theme === 'dark'}
                    >
                        Dark
                    </button>
                </div>
            </div>
        </>
    );
}

// --- USER MANUAL & ABOUT COMPONENTS ---

const InfoBox: FC<{ type: 'info' | 'tip' | 'warning'; children: React.ReactNode; title: string; }> = ({ type, children, title }) => {
    const ICONS = {
        info: <InfoIcon size={20} className="icon-info"/>,
        tip: <CheckCircleIcon size={20} className="icon-success" />,
        warning: <AlertTriangleIcon size={20} className="icon-warning"/>,
    };
    const BORDER_COLORS = {
        info: 'var(--accent-primary)',
        tip: 'var(--accent-success)',
        warning: 'var(--accent-warning)',
    };
    const BG_COLORS = {
        info: 'rgba(59, 130, 246, 0.1)',
        tip: 'rgba(34, 197, 94, 0.1)',
        warning: 'rgba(249, 115, 22, 0.1)',
    };

    return (
        <div className="manual-infobox" style={{ 
            borderColor: BORDER_COLORS[type],
            backgroundColor: BG_COLORS[type] 
        }}>
            <div className="manual-infobox-header">
                {ICONS[type]}
                <h4>{title}</h4>
            </div>
            <div className="manual-infobox-content">
                {children}
            </div>
        </div>
    );
};

/**
 * Recursively extracts searchable text from a React node tree.
 * This is a synchronous operation that traverses children and specific props.
 */
const getReactNodeText = (node: React.ReactNode): string => {
    if (node == null || typeof node === 'boolean') {
        return '';
    }
    if (typeof node === 'string' || typeof node === 'number') {
        return String(node);
    }
    if (Array.isArray(node)) {
        // Join with spaces to ensure words from adjacent elements are separated
        return node.map(getReactNodeText).join(' ');
    }
    if (React.isValidElement(node)) {
        // Special handling for InfoBox to include its title in the search
        if (node.type === InfoBox) {
            const infoBoxProps = node.props as { title: string; children: React.ReactNode };
            return `${infoBoxProps.title} ${getReactNodeText(infoBoxProps.children)}`;
        }
        
        // General handling for other components by processing their children
        if (node.props && typeof node.props === 'object' && 'children' in node.props) {
            return getReactNodeText(node.props.children as React.ReactNode);
        }
    }
    return '';
};


const ManualSection: FC<{ title: string; children: React.ReactNode; defaultOpen?: boolean; filter: string }> = ({ title, children, defaultOpen = false, filter }) => {
    // Memoize the text extraction process. It runs only when the children change.
    const textContent = useMemo(() => {
        // Use a custom, synchronous text extractor that understands React nodes
        return getReactNodeText(children);
    }, [children]);

    const lowerCaseFilter = filter.toLowerCase();
    const isVisible = !filter || textContent.toLowerCase().includes(lowerCaseFilter) || title.toLowerCase().includes(lowerCaseFilter);

    if (!isVisible) {
        return null;
    }

    return (
        <div className="manual-section">
            <details open={defaultOpen || !!filter}>
                <summary><Highlight text={title} highlight={filter} /></summary>
                <div className="manual-content">
                    {children}
                </div>
            </details>
        </div>
    );
};

export function UserManual(props: DataViewProps) {
    const [filterText, setFilterText] = useState('');
    
    const sections = useMemo(() => [
        {
            title: 'Getting Started: The 5-Step Workflow',
            defaultOpen: true,
            content: <>
                <p>Welcome to FAR Sighted! This application is designed to make fixed asset management straightforward and compliant. Follow this typical workflow to get the most out of the system:</p>
                <ol className="manual-list">
                    <li><strong>Create a Company:</strong> Navigate to <code>Company Info</code> from the sidebar. Use the header buttons to <strong>Add Company</strong> and fill in the required details. This is your starting point.</li>
                    <li><strong>Review Asset Classification:</strong> Go to <code>Company Masters</code> → <code>Asset Classification</code>. These are the statutory categories for your assets as per the Companies Act. You can add your own non-statutory classes if needed by editing the 'Useful Life' for assets that are not statutory.</li>
                    <li><strong>Add Your Assets:</strong> Go to <code>Assets Data</code> → <code>Asset Records</code>. This is the core of your work. Here you can add assets one-by-one or perform a bulk import from a CSV file using the provided template.</li>
                    <li><strong>Manage Extra Shifts:</strong> If your company uses extra shifts for plant and machinery, visit the <code>Extra Shift Days</code> view to input the number of days for eligible assets each year.</li>
                    <li><strong>Finalize & Calculate:</strong> Once all data for a financial year is entered and verified, use the <strong>Create Next FY</strong> button in the header (visible on the <code>Company Info</code> page). This critical step calculates and finalizes all depreciation for the year and prepares the system for the next one.</li>
                </ol>
                <InfoBox type="warning" title="'Create Next FY' is Irreversible">
                    <p>The "Create Next Financial Year" process is a closing action. Once you create the next year, all data for the current year will be locked from editing. Please ensure all asset additions, disposals, and shift days are correctly entered before proceeding.</p>
                </InfoBox>
            </>
        },
        {
            title: 'Core Concepts Explained',
            content: <>
                <p>Understanding these core concepts is key to using the app effectively.</p>
                <h4>First Date of Adoption</h4>
                <p>This is the date from which your company started using this application. It's crucial for determining how to treat existing assets.</p>
                <ul className="manual-list">
                    <li>Assets purchased <strong>before</strong> this date are "historical assets." You must provide their <strong>WDV (Written Down Value) as of this date.</strong></li>
                    <li>Assets purchased <strong>on or after</strong> this date are "new assets." The system will calculate everything from their gross value.</li>
                </ul>

                <h4>Asset Classification: Statutory vs. Non-Statutory</h4>
                <p>In the <code>Asset Classification</code> screen, you'll see items marked as 'Statutory: Yes' or 'No'.</p>
                <ul className="manual-list">
                    <li><strong>Statutory ('Yes'):</strong> These are defined by the Companies Act. Their 'Useful Life' is fixed and cannot be changed.</li>
                    <li><strong>Non-Statutory ('No'):</strong> These are custom asset types you might need. You are free to define the 'Useful Life' for these assets.</li>
                </ul>

                <h4>Depreciation Methods: WDV vs. SLM</h4>
                <p>The application supports two methods of depreciation, which you select per asset:</p>
                <ul className="manual-list">
                    <li><strong>WDV (Written Down Value):</strong> Depreciation is calculated as a percentage of the asset's book value at the beginning of each year. This results in higher depreciation in earlier years.</li>
                    <li><strong>SLM (Straight Line Method):</strong> Depreciation is calculated evenly over the asset's useful life. The same amount is depreciated each year.</li>
                </ul>
            </>
        },
        {
            title: 'Guide to Asset Record Fields',
            content: <>
                <p>The <code>Asset Records</code> table is where you manage all your assets. Here's a breakdown of the most important fields:</p>
                
                <InfoBox type="tip" title="Date Validation">
                    <p>The application enforces date logic to prevent errors: <br/>
                    <code>Put to Use Date</code> must be on or after the <code>Book Entry Date</code>.<br/>
                    <code>Disposal Date</code> must be on or after the <code>Put to Use Date</code>.
                    </p>
                </InfoBox>

                <h4>Financial Details</h4>
                <ul className="manual-list">
                    <li><strong>Gross Amount:</strong> This is automatically calculated as <code>Basic Amount</code> + <code>Duties, Taxes, etc.</code> and cannot be edited directly.</li>
                    <li><strong>WDV of Adoption Date:</strong> This field is <strong>compulsory</strong> for any asset with a 'Put to use Date' that is before your company's 'First Date of Adoption'. For all other assets, it is disabled.</li>
                    <li><strong>Salvage %age:</strong> The estimated residual value of an asset at the end of its useful life, expressed as a percentage of its Gross Amount. This value is not depreciated. A default of 5% is suggested.</li>
                    <li><strong>Life in years:</strong> This is auto-filled when you select an 'Asset Sub-Group'. It is only editable if the chosen sub-group is not statutory.</li>
                </ul>
                
                <h4>Disposal Fields</h4>
                <p>The <code>Disposal Date</code> and <code>Disposal Amount</code> fields should only be filled in when an asset is sold, scrapped, or otherwise removed from service.</p>
                
                <InfoBox type="info" title="Interactive Calculation Cells">
                    <p>In the <code>Asset Calculations</code> view, you can double-click on cells like 'Depreciation Rate' or 'Depreciation for Year' to see a detailed modal explaining exactly how that value was calculated. This is very useful for auditing and understanding the results.</p>
                </InfoBox>
            </>
        },
        {
            title: 'Understanding the Reports',
            content: <>
                <p>The 'Reports' section aggregates your asset data into standard financial statements. All reports can be exported to Excel.</p>
                <ul className="manual-list">
                    <li><strong>Asset Group Report:</strong> A depreciation and WDV schedule grouped by the 'Asset Group' and 'Sub-Group' you defined.</li>
                    <li><strong>Schedule III:</strong> The primary fixed asset schedule as required by the Companies Act. Double-click any row to see the individual assets that make up that summary line.</li>
                    <li><strong>Ledger-wise:</strong> Groups all financial movements by the 'Ledger Name' assigned to the assets. Useful for reconciling with your book of accounts.</li>
                    <li><strong>Method-wise:</strong> Separates all asset values based on their depreciation method (WDV or SLM).</li>
                    <li><strong>Tangibility-wise:</strong> Groups assets into Tangible and Intangible categories.</li>
                </ul>
            </>
        },
    ], []);
    
    return (
        <>
            <div className="view-header">
                <h2>User Manual</h2>
            </div>
            <div className="filter-container" style={{ paddingBottom: '1rem' }}>
                <div className="filter-input-wrapper">
                    <input
                        type="text"
                        placeholder="Search manual..."
                        className="filter-input"
                        value={filterText}
                        onChange={e => setFilterText(e.target.value)}
                        aria-label="Search user manual"
                    />
                    <button
                        className={`filter-clear-btn ${!filterText ? 'hidden' : ''}`}
                        onClick={() => setFilterText('')}
                        aria-label="Clear filter"
                        title="Clear filter"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </div>
            </div>
            <div className="manual-container">
                {sections.map(section => (
                    <ManualSection 
                        key={section.title} 
                        title={section.title} 
                        defaultOpen={section.defaultOpen}
                        filter={filterText}
                    >
                        {section.content}
                    </ManualSection>
                ))}
            </div>
        </>
    );
}

export function About(props: DataViewProps) {
    return (
        <>
            <div className="view-header">
                <h2>About & Help</h2>
            </div>
            <div className="settings-container" style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
                <section style={{ textAlign: 'center' }}>
                    <img src="/FAR Logo 2.webp" alt="FAR Sighted Logo" style={{ width: '120px', height: 'auto', margin: '0 auto 1.5rem', display: 'block' }} />
                    <h3 className="settings-title">About FAR Sighted</h3>
                    <p className="settings-description" style={{ fontSize: '1.1rem', lineHeight: 1.6 }}>
                        <strong>FAR Sighted</strong> is a comprehensive system for managing fixed assets, depreciation, and reporting, designed to streamline accounting processes and ensure compliance.
                    </p>
                    <p>Version: 1.0.0</p>
                </section>
                <section>
                    <h3 className="settings-title">Getting Help</h3>
                    <p className="settings-description" style={{ fontSize: '1rem', lineHeight: 1.6 }}>
                        For detailed information on application features, data formats, and report calculations, please refer to the <strong>User Manual</strong> available in the sidebar.
                        <br /><br />
                        For technical issues or support requests, please contact our support team at: <a href="mailto:<EMAIL>" style={{color: 'var(--accent-primary)'}}><EMAIL></a>
                    </p>
                </section>
            </div>
        </>
    );
}

