/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useMemo, useState, FC } from 'react';
import { DataViewProps } from '../lib/types';
import { InfoIcon, AlertTriangleIcon, CheckCircleIcon } from '../Icons';

/**
 * A component that highlights occurrences of a substring within a text.
 * It's case-insensitive and safe for special regex characters.
 */
export const Highlight: FC<{ text: string | null | undefined; highlight: string }> = ({ text, highlight }) => {
    const textStr = String(text || '');
    const trimmedHighlight = highlight.trim();

    if (!trimmedHighlight || !textStr) {
        return <>{textStr}</>;
    }
    
    // Escape regex special characters in the highlight string
    const escapedHighlight = trimmedHighlight.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
    
    // Should not happen, but as a safeguard
    if (!escapedHighlight) {
        return <>{textStr}</>;
    }

    const regex = new RegExp(`(${escapedHighlight})`, 'gi');
    const parts = textStr.split(regex);
    const lowerCaseHighlight = trimmedHighlight.toLowerCase();

    return (
        <>
            {parts.map((part, i) =>
                // The matched parts from split will be case-variations of the highlight string.
                // A simple case-insensitive comparison is sufficient to identify them.
                // The original implementation's `regex.test()` was stateful and unreliable in a loop.
                part.toLowerCase() === lowerCaseHighlight ? (
                    <mark key={i}>{part}</mark>
                ) : (
                    <React.Fragment key={i}>{part}</React.Fragment>
                )
            )}
        </>
    );
};

export function ThemeSettings({ theme, onThemeChange }: DataViewProps) {
    if (!onThemeChange) return <h2>Theme Settings</h2>;

    return (
        <>
            <div className="view-header">
                <h2>Theme Settings</h2>
            </div>
            <div className="settings-container">
                <h3 className="settings-title">Select App Theme</h3>
                <p className="settings-description">Choose between a light or dark theme for the application. Your preference will be saved for your next visit.</p>
                <div className="theme-selector">
                    <button
                        className={`btn theme-btn ${theme === 'light' ? 'active' : ''}`}
                        onClick={() => onThemeChange('light')}
                        aria-pressed={theme === 'light'}
                    >
                        Light
                    </button>
                    <button
                        className={`btn theme-btn ${theme === 'dark' ? 'active' : ''}`}
                        onClick={() => onThemeChange('dark')}
                        aria-pressed={theme === 'dark'}
                    >
                        Dark
                    </button>
                </div>
            </div>
        </>
    );
}

// --- USER MANUAL & ABOUT COMPONENTS ---

const InfoBox: FC<{ type: 'info' | 'tip' | 'warning'; children: React.ReactNode; title: string; }> = ({ type, children, title }) => {
    const ICONS = {
        info: <InfoIcon size={20} className="icon-info"/>,
        tip: <CheckCircleIcon size={20} className="icon-success" />,
        warning: <AlertTriangleIcon size={20} className="icon-warning"/>,
    };
    const BORDER_COLORS = {
        info: 'var(--accent-primary)',
        tip: 'var(--accent-success)',
        warning: 'var(--accent-warning)',
    };
    const BG_COLORS = {
        info: 'rgba(59, 130, 246, 0.1)',
        tip: 'rgba(34, 197, 94, 0.1)',
        warning: 'rgba(249, 115, 22, 0.1)',
    };

    return (
        <div className="manual-infobox" style={{ 
            borderColor: BORDER_COLORS[type],
            backgroundColor: BG_COLORS[type] 
        }}>
            <div className="manual-infobox-header">
                {ICONS[type]}
                <h4>{title}</h4>
            </div>
            <div className="manual-infobox-content">
                {children}
            </div>
        </div>
    );
};

/**
 * Recursively extracts searchable text from a React node tree.
 * This is a synchronous operation that traverses children and specific props.
 */
const getReactNodeText = (node: React.ReactNode): string => {
    if (node == null || typeof node === 'boolean') {
        return '';
    }
    if (typeof node === 'string' || typeof node === 'number') {
        return String(node);
    }
    if (Array.isArray(node)) {
        // Join with spaces to ensure words from adjacent elements are separated
        return node.map(getReactNodeText).join(' ');
    }
    if (React.isValidElement(node)) {
        // Special handling for InfoBox to include its title in the search
        if (node.type === InfoBox) {
            const infoBoxProps = node.props as { title: string; children: React.ReactNode };
            return `${infoBoxProps.title} ${getReactNodeText(infoBoxProps.children)}`;
        }
        
        // General handling for other components by processing their children
        if (node.props && typeof node.props === 'object' && 'children' in node.props) {
            return getReactNodeText(node.props.children as React.ReactNode);
        }
    }
    return '';
};


const ManualSection: FC<{ title: string; children: React.ReactNode; defaultOpen?: boolean; filter: string }> = ({ title, children, defaultOpen = false, filter }) => {
    // Memoize the text extraction process. It runs only when the children change.
    const textContent = useMemo(() => {
        // Use a custom, synchronous text extractor that understands React nodes
        return getReactNodeText(children);
    }, [children]);

    const lowerCaseFilter = filter.toLowerCase();
    const isVisible = !filter || textContent.toLowerCase().includes(lowerCaseFilter) || title.toLowerCase().includes(lowerCaseFilter);

    if (!isVisible) {
        return null;
    }

    return (
        <div className="manual-section">
            <details open={defaultOpen || !!filter}>
                <summary><Highlight text={title} highlight={filter} /></summary>
                <div className="manual-content">
                    {children}
                </div>
            </details>
        </div>
    );
};

export function UserManual(props: DataViewProps) {
    const [filterText, setFilterText] = useState('');
    
    const sections = useMemo(() => [
        {
            title: 'Getting Started: The 5-Step Workflow',
            defaultOpen: true,
            content: <>
                <p>Welcome to FAR Sighted! This application is designed to make fixed asset management straightforward and compliant. Follow this typical workflow to get the most out of the system:</p>
                <ol className="manual-list">
                    <li><strong>Create a Company:</strong> Navigate to <code>Company Info</code> from the sidebar. Use the header buttons to <strong>Add Company</strong> and fill in the required details. This is your starting point.</li>
                    <li><strong>Review Asset Classification:</strong> Go to <code>Company Masters</code> → <code>Asset Classification</code>. These are the statutory categories for your assets as per the Companies Act. You can add your own non-statutory classes if needed by editing the 'Useful Life' for assets that are not statutory.</li>
                    <li><strong>Add Your Assets:</strong> Go to <code>Assets Data</code> → <code>Asset Records</code>. This is the core of your work. Here you can add assets one-by-one or perform a bulk import from a CSV file using the provided template.</li>
                    <li><strong>Manage Extra Shifts:</strong> If your company uses extra shifts for plant and machinery, visit the <code>Extra Shift Days</code> view to input the number of days for eligible assets each year.</li>
                    <li><strong>Finalize & Calculate:</strong> Once all data for a financial year is entered and verified, use the <strong>Create Next FY</strong> button in the header (visible on the <code>Company Info</code> page). This critical step calculates and finalizes all depreciation for the year and prepares the system for the next one.</li>
                </ol>
                <InfoBox type="warning" title="'Create Next FY' is Irreversible">
                    <p>The "Create Next Financial Year" process is a closing action. Once you create the next year, all data for the current year will be locked from editing. Please ensure all asset additions, disposals, and shift days are correctly entered before proceeding.</p>
                </InfoBox>
            </>
        },
        {
            title: 'Core Concepts Explained',
            content: <>
                <p>Understanding these core concepts is key to using the app effectively.</p>
                <h4>First Date of Adoption</h4>
                <p>This is the date from which your company started using this application. It's crucial for determining how to treat existing assets.</p>
                <ul className="manual-list">
                    <li>Assets purchased <strong>before</strong> this date are "historical assets." You must provide their <strong>WDV (Written Down Value) as of this date.</strong></li>
                    <li>Assets purchased <strong>on or after</strong> this date are "new assets." The system will calculate everything from their gross value.</li>
                </ul>

                <h4>Asset Classification: Statutory vs. Non-Statutory</h4>
                <p>In the <code>Asset Classification</code> screen, you'll see items marked as 'Statutory: Yes' or 'No'.</p>
                <ul className="manual-list">
                    <li><strong>Statutory ('Yes'):</strong> These are defined by the Companies Act. Their 'Useful Life' is fixed and cannot be changed.</li>
                    <li><strong>Non-Statutory ('No'):</strong> These are custom asset types you might need. You are free to define the 'Useful Life' for these assets.</li>
                </ul>

                <h4>Depreciation Methods: WDV vs. SLM</h4>
                <p>The application supports two methods of depreciation, which you select per asset:</p>
                <ul className="manual-list">
                    <li><strong>WDV (Written Down Value):</strong> Depreciation is calculated as a percentage of the asset's book value at the beginning of each year. This results in higher depreciation in earlier years.</li>
                    <li><strong>SLM (Straight Line Method):</strong> Depreciation is calculated evenly over the asset's useful life. The same amount is depreciated each year.</li>
                </ul>
            </>
        },
        {
            title: 'Guide to Asset Record Fields',
            content: <>
                <p>The <code>Asset Records</code> table is where you manage all your assets. Here's a breakdown of the most important fields:</p>
                
                <InfoBox type="tip" title="Date Validation">
                    <p>The application enforces date logic to prevent errors: <br/>
                    <code>Put to Use Date</code> must be on or after the <code>Book Entry Date</code>.<br/>
                    <code>Disposal Date</code> must be on or after the <code>Put to Use Date</code>.
                    </p>
                </InfoBox>

                <h4>Financial Details</h4>
                <ul className="manual-list">
                    <li><strong>Gross Amount:</strong> This is automatically calculated as <code>Basic Amount</code> + <code>Duties, Taxes, etc.</code> and cannot be edited directly.</li>
                    <li><strong>WDV of Adoption Date:</strong> This field is <strong>compulsory</strong> for any asset with a 'Put to use Date' that is before your company's 'First Date of Adoption'. For all other assets, it is disabled.</li>
                    <li><strong>Salvage %age:</strong> The estimated residual value of an asset at the end of its useful life, expressed as a percentage of its Gross Amount. This value is not depreciated. A default of 5% is suggested.</li>
                    <li><strong>Life in years:</strong> This is auto-filled when you select an 'Asset Sub-Group'. It is only editable if the chosen sub-group is not statutory.</li>
                </ul>
                
                <h4>Disposal Fields</h4>
                <p>The <code>Disposal Date</code> and <code>Disposal Amount</code> fields should only be filled in when an asset is sold, scrapped, or otherwise removed from service.</p>
                
                <InfoBox type="info" title="Interactive Calculation Cells">
                    <p>In the <code>Asset Calculations</code> view, you can double-click on cells like 'Depreciation Rate' or 'Depreciation for Year' to see a detailed modal explaining exactly how that value was calculated. This is very useful for auditing and understanding the results.</p>
                </InfoBox>
            </>
        },
        {
            title: 'Understanding the Reports',
            content: <>
                <p>The 'Reports' section aggregates your asset data into standard financial statements. All reports can be exported to Excel.</p>
                <ul className="manual-list">
                    <li><strong>Asset Group Report:</strong> A depreciation and WDV schedule grouped by the 'Asset Group' and 'Sub-Group' you defined.</li>
                    <li><strong>Schedule III:</strong> The primary fixed asset schedule as required by the Companies Act. Double-click any row to see the individual assets that make up that summary line.</li>
                    <li><strong>Ledger-wise:</strong> Groups all financial movements by the 'Ledger Name' assigned to the assets. Useful for reconciling with your book of accounts.</li>
                    <li><strong>Method-wise:</strong> Separates all asset values based on their depreciation method (WDV or SLM).</li>
                    <li><strong>Tangibility-wise:</strong> Groups assets into Tangible and Intangible categories.</li>
                </ul>
            </>
        },
        {
            title: 'Company Management',
            content: <>
                <p>Proper company setup is crucial for accurate asset management and compliance.</p>

                <h4>Creating a New Company</h4>
                <ol className="manual-list">
                    <li>Navigate to <code>Company Masters</code> → <code>Company Info</code></li>
                    <li>Click <strong>Add Company</strong> button in the header</li>
                    <li>Fill in all required fields:
                        <ul>
                            <li><strong>Company Name:</strong> Full legal name of the company</li>
                            <li><strong>CIN:</strong> Corporate Identification Number (21 characters)</li>
                            <li><strong>PAN:</strong> Permanent Account Number (10 characters)</li>
                            <li><strong>Financial Year Start:</strong> Usually April 1st for Indian companies</li>
                            <li><strong>First Date of Adoption:</strong> When you started using this system</li>
                        </ul>
                    </li>
                    <li>Click <strong>Save</strong> to create the company</li>
                </ol>

                <h4>Multi-Company Support</h4>
                <p>FAR Sighted supports multiple companies with complete data isolation:</p>
                <ul className="manual-list">
                    <li>Each company has its own separate database</li>
                    <li>Users can be assigned to specific companies</li>
                    <li>Switch between companies using the dropdown in the header</li>
                    <li>All data (assets, users, reports) are company-specific</li>
                </ul>

                <InfoBox type="warning" title="License Validity">
                    <p>License validity is tied to the company's financial year end, not calendar year. Ensure your license is valid for the entire financial year you're working with.</p>
                </InfoBox>
            </>
        },
        {
            title: 'Asset Data Management',
            content: <>
                <p>Comprehensive guide to managing your asset data effectively.</p>

                <h4>Adding Assets Manually</h4>
                <ol className="manual-list">
                    <li>Go to <code>Assets Data</code> → <code>Asset Records</code></li>
                    <li>Click <strong>Add Asset</strong> button</li>
                    <li>Fill in the required fields (marked with red asterisk)</li>
                    <li>Use the dropdown suggestions for consistent data entry</li>
                    <li>Click <strong>Save Changes</strong> when done</li>
                </ol>

                <h4>Bulk Import from Excel/CSV</h4>
                <ol className="manual-list">
                    <li>Download the template using <strong>Template</strong> button</li>
                    <li>Fill in your asset data in the template</li>
                    <li>Click <strong>Import File</strong> and select your file</li>
                    <li>Review the preview and fix any errors highlighted in red</li>
                    <li>Click <strong>Import Assets</strong> to complete the process</li>
                </ol>

                <h4>Edit Mode and Data Protection</h4>
                <p>Asset Records has built-in protection against accidental changes:</p>
                <ul className="manual-list">
                    <li>By default, records are in <strong>View Mode</strong> (read-only)</li>
                    <li>Click <strong>Edit Mode</strong> to enable editing</li>
                    <li>Changes are highlighted with colored borders</li>
                    <li>Use <strong>Save Changes</strong> to commit your edits</li>
                    <li>Click <strong>Exit Edit Mode</strong> to return to view mode</li>
                </ul>

                <h4>Asset Disposal and Scrapping</h4>
                <ol className="manual-list">
                    <li>Select the asset you want to dispose</li>
                    <li>Click the <strong>Dispose</strong> button in the Actions column</li>
                    <li>Enter the disposal date and amount</li>
                    <li>Check <strong>Mark as Scrap</strong> if the asset has no sale value</li>
                    <li>Disposed assets will show with strikethrough styling</li>
                </ol>
            </>
        },
        {
            title: 'Depreciation Calculations',
            content: <>
                <p>Understanding how FAR Sighted calculates depreciation for accurate financial reporting.</p>

                <h4>Depreciation Methods</h4>
                <p><strong>Written Down Value (WDV) Method:</strong></p>
                <ul className="manual-list">
                    <li>Rate = 1 - (Salvage Value / Gross Amount)^(1/Life in Years)</li>
                    <li>Annual Depreciation = Opening WDV × Rate × (Days Used / 365)</li>
                    <li>Higher depreciation in early years, lower in later years</li>
                </ul>

                <p><strong>Straight Line Method (SLM):</strong></p>
                <ul className="manual-list">
                    <li>Annual Depreciation = (Gross Amount - Salvage Value) / Life in Years</li>
                    <li>Same depreciation amount every year</li>
                    <li>Pro-rated based on days used in the year</li>
                </ul>

                <h4>First Year Depreciation</h4>
                <p>For assets purchased before the adoption date:</p>
                <ul className="manual-list">
                    <li>Depreciation is calculated on the <strong>WDV of Adoption Date</strong></li>
                    <li>Not on the original gross amount</li>
                    <li>This ensures accurate carry-forward from previous systems</li>
                </ul>

                <h4>Extra Shift Depreciation</h4>
                <p>For plant and machinery working extra shifts:</p>
                <ul className="manual-list">
                    <li>Go to <code>Assets Data</code> → <code>Extra Shift Days</code></li>
                    <li>Enter the number of 2nd and 3rd shift days for each asset</li>
                    <li>Extra depreciation = Normal Depreciation × (Extra Shift Days / 365)</li>
                    <li>Total depreciation = Normal + Extra Shift depreciation</li>
                </ul>

                <InfoBox type="tip" title="Interactive Calculations">
                    <p>In <code>Asset Calculations</code>, double-click on any depreciation rate or amount to see the detailed calculation breakdown. This helps with auditing and understanding the numbers.</p>
                </InfoBox>
            </>
        },
        {
            title: 'Reports and Compliance',
            content: <>
                <p>Generate professional reports for compliance and financial analysis.</p>

                <h4>Schedule III Report</h4>
                <p>The primary fixed asset schedule required by the Companies Act:</p>
                <ul className="manual-list">
                    <li>Shows opening balance, additions, disposals, and closing balance</li>
                    <li>Includes depreciation calculations and WDV</li>
                    <li>Double-click any row to see constituent assets</li>
                    <li>Export to CSV for further analysis</li>
                </ul>

                <h4>Asset Group Report</h4>
                <p>Detailed depreciation schedule grouped by asset categories:</p>
                <ul className="manual-list">
                    <li>Groups assets by Asset Group and Sub-Group</li>
                    <li>Shows year-wise depreciation and WDV</li>
                    <li>Useful for internal management reporting</li>
                    <li>Export to Excel with formatting</li>
                </ul>

                <h4>Specialized Reports</h4>
                <ul className="manual-list">
                    <li><strong>Ledger-wise:</strong> Groups by ledger names for book reconciliation</li>
                    <li><strong>Method-wise:</strong> Separates WDV and SLM assets</li>
                    <li><strong>Tangibility-wise:</strong> Tangible vs Intangible assets</li>
                    <li><strong>Scrap & End of Life:</strong> Assets reaching end of useful life</li>
                    <li><strong>Asset Additions:</strong> New assets added during the year</li>
                    <li><strong>Asset Deletions:</strong> Assets disposed during the year</li>
                </ul>

                <InfoBox type="info" title="Export Options">
                    <p>All reports can be exported to Excel with proper formatting. Schedule III also supports CSV export for data analysis tools.</p>
                </InfoBox>
            </>
        },
        {
            title: 'User Management and Security',
            content: <>
                <p>Manage users and control access to your asset data.</p>

                <h4>User Roles</h4>
                <ul className="manual-list">
                    <li><strong>Admin:</strong> Full access to all features including user management, year unlocking, and system settings</li>
                    <li><strong>Data Entry:</strong> Can add, edit, and delete asset records but cannot manage users or system settings</li>
                    <li><strong>Report Viewer:</strong> Read-only access to view reports and asset data</li>
                </ul>

                <h4>Adding New Users</h4>
                <ol className="manual-list">
                    <li>Navigate to <code>User Management</code> → <code>Add User</code> (Admin only)</li>
                    <li>Enter username, password, and select role</li>
                    <li>Assign the user to specific companies</li>
                    <li>User will receive login credentials to access the system</li>
                </ol>

                <h4>Company-Specific Users</h4>
                <p>Each company maintains its own user database:</p>
                <ul className="manual-list">
                    <li>Users are created per company, not globally</li>
                    <li>Same username can exist in different companies</li>
                    <li>Users must login separately when switching companies</li>
                    <li>Default admin user (admin/admin123) is created for each company</li>
                </ul>

                <h4>Password Management</h4>
                <ul className="manual-list">
                    <li>Default admin password must be changed on first login</li>
                    <li>Use strong passwords with mix of letters, numbers, and symbols</li>
                    <li>Password recovery available through username verification</li>
                    <li>Regular password updates recommended for security</li>
                </ul>
            </>
        },
        {
            title: 'System Administration',
            content: <>
                <p>Administrative functions for system maintenance and data protection.</p>

                <h4>Financial Year Management</h4>
                <p>Critical year-end processes:</p>
                <ol className="manual-list">
                    <li><strong>Create Next FY:</strong> Finalizes current year and creates next financial year</li>
                    <li><strong>Unlock Year:</strong> Admin can unlock previous years for corrections</li>
                    <li><strong>Year Locking:</strong> Previous years are automatically locked to prevent changes</li>
                </ol>

                <h4>Backup and Restore</h4>
                <p>Protect your data with regular backups:</p>
                <ul className="manual-list">
                    <li><strong>Manual Backup:</strong> Create immediate backup of all company data</li>
                    <li><strong>Automatic Backup:</strong> Scheduled weekly backups (Saturdays at noon)</li>
                    <li><strong>Backup Location:</strong> Configure custom backup directory</li>
                    <li><strong>Retention:</strong> System maintains 5 most recent backups</li>
                    <li><strong>Change Detection:</strong> Backups only created when data has changed</li>
                </ul>

                <h4>Audit Trail</h4>
                <p>Complete tracking of all system activities:</p>
                <ul className="manual-list">
                    <li>All user actions are logged with timestamps</li>
                    <li>Track asset additions, modifications, and deletions</li>
                    <li>User login/logout activities recorded</li>
                    <li>System changes and administrative actions logged</li>
                    <li>Export audit logs for compliance requirements</li>
                </ul>

                <h4>License Management</h4>
                <ul className="manual-list">
                    <li>License validity tied to company financial year</li>
                    <li>Generate license request files for renewal</li>
                    <li>Install new licenses through system settings</li>
                    <li>Company master becomes non-editable after license installation</li>
                </ul>
            </>
        },
        {
            title: 'Troubleshooting Common Issues',
            content: <>
                <p>Solutions to frequently encountered problems and error messages.</p>

                <h4>Data Entry Issues</h4>
                <p><strong>Problem:</strong> "Put to Use Date must be on or after Book Entry Date"</p>
                <ul className="manual-list">
                    <li><strong>Solution:</strong> Ensure the Put to Use Date is the same as or later than the Book Entry Date</li>
                    <li>This validation prevents logical errors in asset records</li>
                </ul>

                <p><strong>Problem:</strong> "WDV of Adoption Date is required"</p>
                <ul className="manual-list">
                    <li><strong>Solution:</strong> For assets purchased before the First Date of Adoption, you must provide the WDV as of that date</li>
                    <li>This field is mandatory for historical assets</li>
                </ul>

                <p><strong>Problem:</strong> Cannot edit Life in Years field</p>
                <ul className="manual-list">
                    <li><strong>Solution:</strong> This field is locked for statutory asset classifications</li>
                    <li>Only non-statutory asset types allow custom life values</li>
                </ul>

                <h4>Import/Export Issues</h4>
                <p><strong>Problem:</strong> Import file shows validation errors</p>
                <ul className="manual-list">
                    <li>Check that all required fields are filled</li>
                    <li>Ensure date formats are DD/MM/YYYY</li>
                    <li>Verify numeric fields contain only numbers</li>
                    <li>Use the provided template for correct column headers</li>
                </ul>

                <p><strong>Problem:</strong> Export to Excel not working</p>
                <ul className="manual-list">
                    <li>Ensure your browser allows file downloads</li>
                    <li>Check if popup blockers are preventing the download</li>
                    <li>Try using a different browser if issues persist</li>
                </ul>

                <h4>Performance Issues</h4>
                <p><strong>Problem:</strong> Application running slowly</p>
                <ul className="manual-list">
                    <li>Close unused browser tabs to free memory</li>
                    <li>Clear browser cache and cookies</li>
                    <li>Ensure adequate system resources (RAM, CPU)</li>
                    <li>Consider archiving old financial year data</li>
                </ul>

                <h4>Login and Access Issues</h4>
                <p><strong>Problem:</strong> Cannot login to the system</p>
                <ul className="manual-list">
                    <li>Verify username and password are correct</li>
                    <li>Check if Caps Lock is enabled</li>
                    <li>Use password recovery if available</li>
                    <li>Contact admin to reset your password</li>
                </ul>

                <p><strong>Problem:</strong> "Year is locked" message</p>
                <ul className="manual-list">
                    <li>Previous financial years are automatically locked</li>
                    <li>Contact an Admin user to unlock the year if corrections are needed</li>
                    <li>Use the Unlock Financial Year feature in Administration</li>
                </ul>
            </>
        },
        {
            title: 'Best Practices and Tips',
            content: <>
                <p>Recommendations for efficient and accurate asset management.</p>

                <h4>Data Entry Best Practices</h4>
                <ul className="manual-list">
                    <li><strong>Consistent Naming:</strong> Use standardized names for asset particulars, locations, and vendors</li>
                    <li><strong>Regular Backups:</strong> Create backups before major data entry sessions</li>
                    <li><strong>Batch Processing:</strong> Use bulk import for large numbers of assets</li>
                    <li><strong>Data Validation:</strong> Review all imported data before finalizing</li>
                    <li><strong>Documentation:</strong> Keep supporting documents for all asset entries</li>
                </ul>

                <h4>Year-End Procedures</h4>
                <ol className="manual-list">
                    <li>Complete all asset additions for the financial year</li>
                    <li>Enter all disposal transactions with proper dates and amounts</li>
                    <li>Update extra shift days for applicable assets</li>
                    <li>Review Asset Calculations report for accuracy</li>
                    <li>Generate and verify Schedule III report</li>
                    <li>Create comprehensive backup before year-end closing</li>
                    <li>Use "Create Next FY" only after thorough verification</li>
                </ol>

                <h4>Compliance Recommendations</h4>
                <ul className="manual-list">
                    <li><strong>Regular Reconciliation:</strong> Match asset records with physical verification</li>
                    <li><strong>Audit Trail:</strong> Maintain detailed logs of all changes</li>
                    <li><strong>Statutory Compliance:</strong> Follow Companies Act requirements for asset classification</li>
                    <li><strong>Professional Review:</strong> Have qualified accountants review reports before filing</li>
                </ul>

                <h4>Security Best Practices</h4>
                <ul className="manual-list">
                    <li><strong>User Access:</strong> Assign minimum required permissions to users</li>
                    <li><strong>Password Policy:</strong> Use strong passwords and change them regularly</li>
                    <li><strong>Data Protection:</strong> Store backups in secure, offsite locations</li>
                    <li><strong>Regular Updates:</strong> Keep the application updated with latest versions</li>
                    <li><strong>Access Monitoring:</strong> Review audit logs regularly for unauthorized access</li>
                </ul>

                <InfoBox type="success" title="Pro Tip">
                    <p>Use the column chooser feature in Asset Records and Asset Calculations to customize your view. Hide columns you don't need and resize others for optimal workflow. Your preferences are saved automatically!</p>
                </InfoBox>
            </>
        },
        {
            title: 'Keyboard Shortcuts and Efficiency Tips',
            content: <>
                <p>Speed up your workflow with these helpful shortcuts and features.</p>

                <h4>Navigation Shortcuts</h4>
                <ul className="manual-list">
                    <li><strong>Tab:</strong> Move between form fields</li>
                    <li><strong>Enter:</strong> Submit forms or confirm actions</li>
                    <li><strong>Escape:</strong> Close modals and cancel operations</li>
                    <li><strong>Double-click:</strong> Open calculation details in Asset Calculations</li>
                </ul>

                <h4>Table Features</h4>
                <ul className="manual-list">
                    <li><strong>Column Sorting:</strong> Click column headers to sort data</li>
                    <li><strong>Row Selection:</strong> Click rows to highlight them for better tracking</li>
                    <li><strong>Sticky Columns:</strong> First few columns remain visible when scrolling</li>
                    <li><strong>Column Chooser:</strong> Customize which columns are visible</li>
                    <li><strong>Filter Search:</strong> Use the search box to quickly find specific assets</li>
                </ul>

                <h4>Data Entry Efficiency</h4>
                <ul className="manual-list">
                    <li><strong>Dropdown Lists:</strong> Use autocomplete suggestions for consistent data</li>
                    <li><strong>Copy-Paste:</strong> Copy similar asset details and modify as needed</li>
                    <li><strong>Bulk Operations:</strong> Select multiple assets for disposal or other actions</li>
                    <li><strong>Template Usage:</strong> Always use the provided template for imports</li>
                </ul>

                <h4>Report Generation Tips</h4>
                <ul className="manual-list">
                    <li><strong>Export Early:</strong> Generate reports frequently during data entry</li>
                    <li><strong>Multiple Formats:</strong> Use Excel for analysis, CSV for data processing</li>
                    <li><strong>Print Preview:</strong> Check report formatting before printing</li>
                    <li><strong>Save Copies:</strong> Keep historical copies of year-end reports</li>
                </ul>

                <InfoBox type="info" title="Theme Customization">
                    <p>Switch between light and dark themes using the theme toggle in the header. Your preference is saved automatically and synced across all your devices when logged in.</p>
                </InfoBox>
            </>
        },
    ], []);
    
    return (
        <>
            <div className="view-header">
                <h2>User Manual</h2>
            </div>
            <div className="filter-container" style={{ paddingBottom: '1rem' }}>
                <div className="filter-input-wrapper">
                    <input
                        type="text"
                        placeholder="Search manual..."
                        className="filter-input"
                        value={filterText}
                        onChange={e => setFilterText(e.target.value)}
                        aria-label="Search user manual"
                    />
                    <button
                        className={`filter-clear-btn ${!filterText ? 'hidden' : ''}`}
                        onClick={() => setFilterText('')}
                        aria-label="Clear filter"
                        title="Clear filter"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </div>
            </div>
            <div className="manual-container">
                {sections.map(section => (
                    <ManualSection 
                        key={section.title} 
                        title={section.title} 
                        defaultOpen={section.defaultOpen}
                        filter={filterText}
                    >
                        {section.content}
                    </ManualSection>
                ))}
            </div>
        </>
    );
}

export function About(props: DataViewProps) {
    return (
        <>
            <div className="view-header">
                <h2>About & Help</h2>
            </div>
            <div className="settings-container" style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
                <section style={{ textAlign: 'center' }}>
                    <img src="/FAR Logo 2.webp" alt="FAR Sighted Logo" style={{ width: '120px', height: 'auto', margin: '0 auto 1.5rem', display: 'block' }} />
                    <h3 className="settings-title">About FAR Sighted</h3>
                    <p className="settings-description" style={{ fontSize: '1.1rem', lineHeight: 1.6 }}>
                        <strong>FAR Sighted</strong> is a comprehensive system for managing fixed assets, depreciation, and reporting, designed to streamline accounting processes and ensure compliance.
                    </p>
                    <p>Version: 1.0.0</p>
                </section>
                <section>
                    <h3 className="settings-title">Getting Help</h3>
                    <p className="settings-description" style={{ fontSize: '1rem', lineHeight: 1.6 }}>
                        For detailed information on application features, data formats, and report calculations, please refer to the <strong>User Manual</strong> available in the sidebar.
                        <br /><br />
                        For technical issues or support requests, please contact our support team at: <a href="mailto:<EMAIL>" style={{color: 'var(--accent-primary)'}}><EMAIL></a>
                    </p>
                </section>
            </div>
        </>
    );
}

