@echo off
setlocal enabledelayedexpansion

echo =====================================================
echo FAR SIGHTED - COMPLETE FIX FOR COMPANY DROPDOWN
echo Professional Advisory Services - CA
echo =====================================================
echo.

cd /d "E:\Projects\FAR Sighted"

echo 🎯 IDENTIFIED ISSUE: Database migration required
echo    The backend blocks API calls until migration is complete.
echo    This is why the company dropdown is empty.
echo.

echo 📋 Fix Steps:
echo    1. Stop any running processes
echo    2. Start backend server
echo    3. Run database migration
echo    4. Verify companies API works
echo    5. Start frontend
echo.

echo 🔧 Step 1: Stopping any running processes...
call kill-all-processes.bat >nul 2>&1
timeout /t 3 /nobreak >nul

echo.
echo 🚀 Step 2: Starting backend server...
cd backend
start "FAR Backend Fix" cmd /k "title FAR Backend Fix && echo Starting backend for migration... && npm start"
cd ..

echo.
echo ⏳ Step 3: Waiting for backend to initialize (15 seconds)...
timeout /t 15 /nobreak >nul

echo.
echo 🔍 Step 4: Testing backend status...
curl -s http://localhost:8090/api/health >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Backend is running and responding
) else (
    echo    ❌ Backend not responding. Wait longer and retry.
    echo    💡 Check the "FAR Backend Fix" window for errors
    pause
    exit /b 1
)

echo.
echo 🔄 Step 5: Checking migration status...
curl -s http://localhost:8090/api/migration-status | find "needsMigration" | find "true" >nul 2>&1
if %errorlevel% == 0 (
    echo    ⚠️  Migration is required (this is why dropdown is empty)
    echo.
    echo 🛠️  Step 6: Running database migration...
    cd backend
    npm run migrate
    if %errorlevel% == 0 (
        echo    ✅ Migration completed successfully!
    ) else (
        echo    ❌ Migration failed. Check error messages above.
        pause
        exit /b 1
    )
    cd ..
) else (
    echo    ✅ No migration needed or migration already complete
)

echo.
echo 🏢 Step 7: Testing companies API (THE CRITICAL TEST)...
echo    This should now return company data instead of migration error
echo.
curl -s http://localhost:8090/api/companies
echo.
curl -s -w "Response Code: %%{http_code}\n" -o nul http://localhost:8090/api/companies

if %errorlevel% == 0 (
    echo    ✅ Companies API is now working!
    echo    🎯 The dropdown should now populate with companies
) else (
    echo    ❌ Companies API still not working
    echo    💡 Check the response above for error details
)

echo.
echo 🖥️  Step 8: Starting frontend...
start "FAR Frontend Fix" cmd /k "title FAR Frontend Fix && echo Starting frontend... && npm run dev"

echo.
echo ⏳ Step 9: Waiting for frontend to start (10 seconds)...
timeout /t 10 /nobreak >nul

echo.
echo 🌐 Step 10: Opening application...
start http://localhost:9090

echo.
echo ✅ COMPLETE FIX APPLIED!
echo.
echo 📊 Summary:
echo    • Backend: http://localhost:8090 (should be running)
echo    • Frontend: http://localhost:9090 (should be running)
echo    • Companies API: http://localhost:8090/api/companies (should return data)
echo    • Database: Migrated to multi-database structure
echo.
echo 🎯 Expected Result:
echo    The company dropdown should now show your companies!
echo.
echo 🔍 If dropdown is still empty:
echo    1. Check browser console (F12) for JavaScript errors
echo    2. Verify both backend and frontend are running
echo    3. Test: curl http://localhost:8090/api/companies
echo.
echo 💡 Troubleshooting Commands:
echo    • Test API: test-companies-api.bat
echo    • Check status: check-status.bat
echo    • Migration: migrate-database.bat
echo.

pause
