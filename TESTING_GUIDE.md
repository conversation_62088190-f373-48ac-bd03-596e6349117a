# 🧪 FAR Sighted - Comprehensive Testing Guide

## 📋 Testing Overview

**Database Status:** ✅ Populated with realistic mock data  
**System Status:** ✅ Backend running on port 3001, Frontend on port 5176  
**Test Data:** 3 companies, 6 assets, 4 users with different roles  

---

## 🎯 Test Credentials

| **Role** | **Username** | **Password** | **Permissions** |
|----------|-------------|-------------|-----------------|
| **Admin** | `ca_admin` | `admin123` | Full system access |
| **Admin** | `senior_ca` | `senior123` | Full system access |
| **Data Entry** | `data_entry1` | `entry123` | Asset management, limited settings |
| **Report Viewer** | `report_viewer1` | `view123` | Read-only access |

---

## 🏢 Test Companies Available

### **1. Tech Innovations Pvt Ltd (c1001)**
- **Assets:** CNC Machine (₹29.5L), Dell Workstation (₹1.77L), Conference Table (₹1L)
- **Industry:** Technology/Manufacturing
- **Financial Years:** 2022-23, 2023-24, 2024-25

### **2. Maharashtra Manufacturing Ltd (c1002)**
- **Assets:** Press Machine (₹21.24L), Tata Vehicle (₹13.75L)
- **Industry:** Manufacturing
- **Financial Years:** 2022-23, 2023-24, 2024-25

### **3. Green Energy Solutions Pvt Ltd (c1003)**
- **Assets:** Solar Panel Array (₹41.3L)
- **Industry:** Renewable Energy
- **Financial Years:** 2023-24, 2024-25

---

## 🔗 API Testing (Backend - Port 3001)

### **Step 1: Health Check**
```bash
curl http://localhost:3001/api/health
```
**Expected:** Status 200 with system information

### **Step 2: User Authentication**
```bash
# Test Admin Login
curl -X POST http://localhost:3001/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"username": "ca_admin", "password": "admin123"}'

# Test Data Entry Login
curl -X POST http://localhost:3001/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"username": "data_entry1", "password": "entry123"}'
```
**Expected:** User object with role information

### **Step 3: Company Data Retrieval**
```bash
# Get all companies
curl http://localhost:3001/api/companies

# Get specific company with assets
curl http://localhost:3001/api/companies/c1001
```
**Expected:** Company list and detailed company data with assets

### **Step 4: Asset Management**
```bash
# Get assets for Tech Innovations
curl http://localhost:3001/api/assets/company/c1001
```
**Expected:** Array of 3 assets with yearly depreciation data

### **Step 5: Audit Trail**
```bash
# Get audit logs
curl http://localhost:3001/api/audit
```
**Expected:** Array of audit log entries

---

## 💻 Frontend Testing (Port 5176)

### **Test Suite 1: User Authentication & Roles**

#### **1.1 Admin User Testing**
1. **Login:** Use `ca_admin / admin123`
2. **Verify Access:**
   - ✅ Company management (add/edit companies)
   - ✅ User management (create/edit/delete users)
   - ✅ Asset management (full CRUD operations)
   - ✅ Settings & configuration access
   - ✅ All reports generation
   - ✅ Audit trail access

#### **1.2 Data Entry User Testing**
1. **Login:** Use `data_entry1 / entry123`
2. **Verify Access:**
   - ✅ Asset management (add/edit/delete assets)
   - ✅ Company data entry
   - ✅ Basic reports generation
   - ❌ User management (should be restricted)
   - ❌ System settings (should be restricted)

#### **1.3 Report Viewer Testing**
1. **Login:** Use `report_viewer1 / view123`
2. **Verify Access:**
   - ✅ View all reports
   - ✅ Export reports to Excel
   - ❌ Asset editing (should be read-only)
   - ❌ Company editing (should be read-only)
   - ❌ User management (should be restricted)

### **Test Suite 2: Company Management**

#### **2.1 View Companies**
1. Login as admin
2. Navigate to company selection
3. **Verify:** All 3 companies are listed
4. **Expected Companies:**
   - Tech Innovations Pvt Ltd
   - Maharashtra Manufacturing Ltd
   - Green Energy Solutions Pvt Ltd

#### **2.2 Company Details**
1. Select "Tech Innovations Pvt Ltd"
2. **Verify Company Info:**
   - Company Name: Tech Innovations Pvt Ltd
   - PAN: **********
   - CIN: U72200DL2018PTC334567
   - Financial Year: 2024-25
3. **Verify Assets:** Should show 3 assets

#### **2.3 Add New Company**
1. Click "Add Company"
2. Fill in details:
   - Company Name: Test Company Ltd
   - Financial Year Start: 2024-04-01
   - Financial Year End: 2025-03-31
   - First Date of Adoption: 2024-04-01
3. Save and verify company is created

### **Test Suite 3: Asset Management**

#### **3.1 View Assets**
1. Select "Tech Innovations Pvt Ltd"
2. Navigate to Asset Management
3. **Verify Assets Listed:**
   - P0001: CNC Machining Center - VMC 850
   - C0001: Dell Workstation - Precision 7760
   - F0001: Conference Table with Chairs Set

#### **3.2 Asset Details Verification**
1. Click on CNC Machine (P0001)
2. **Verify Details:**
   - Basic Amount: ₹25,00,000
   - Gross Amount: ₹29,50,000
   - Depreciation Method: WDV
   - Life: 15 years
   - Asset Group: Plant & Machinery

#### **3.3 Add New Asset**
1. Click "Add Asset"
2. Fill in details:
   - Asset Particulars: Test Laptop
   - Basic Amount: 75000
   - Asset Group: Computer Equipment
   - Depreciation Method: WDV
   - Life: 3 years
3. Save and verify asset is added

#### **3.4 Edit Asset**
1. Select existing asset
2. Modify basic amount
3. Save changes
4. Verify changes are reflected

### **Test Suite 4: Depreciation Calculations**

#### **4.1 Yearly Data Verification**
1. Select asset P0001 (CNC Machine)
2. **Verify Yearly Columns:**
   - Opening WDV 2022-23: ₹29,50,000
   - Depreciation 2022-23: ~₹2,80,250 (9.5%)
   - WDV 2022-23: ~₹26,69,750

#### **4.2 Method Testing**
1. Change depreciation method from WDV to SLM
2. Verify calculations update automatically
3. Revert to WDV and verify again

### **Test Suite 5: Reports Generation**

#### **5.1 Asset Group Report**
1. Navigate to Reports
2. Select "Asset Group Report"
3. Choose company and year
4. Generate report
5. **Verify:** Assets grouped by categories
6. Export to Excel and verify file download

#### **5.2 Asset Additions Report**
1. Generate "Asset Additions Report" for 2023-24
2. **Verify:** Shows assets added in that year
3. Check Dell Workstation appears (added Jan 2023)

#### **5.3 Method-wise Report**
1. Generate "Method-wise Report"
2. **Verify:** Assets separated by SLM and WDV methods
3. Check totals and calculations

### **Test Suite 6: Import/Export Testing**

#### **6.1 Export Functionality**
1. Navigate to Asset Management
2. Click "Export" button
3. **Verify:** Excel file downloads with all asset data
4. Open file and verify data integrity

#### **6.2 Template Download**
1. Click "Template" button
2. **Verify:** Excel template downloads
3. Check template has proper headers

#### **6.3 Import Testing**
1. Download template
2. Add 2-3 new assets in template
3. Import file back
4. **Verify:** New assets appear in system
5. Check auto-generated record IDs

### **Test Suite 7: Ledger Master**

#### **7.1 View Ledgers**
1. Navigate to Ledger Master
2. **Verify Existing Ledgers:**
   - Plant & Machinery
   - Computer Equipment
   - Furniture & Fixtures
   - Research Equipment
   - Testing Instruments

#### **7.2 Add New Ledger**
1. Click "Add Ledger"
2. Enter "Safety Equipment"
3. Save changes
4. Verify ledger appears in list

#### **7.3 Edit Ledger**
1. Select existing ledger
2. Rename it
3. Save changes
4. Verify rename is reflected in assets using that ledger

### **Test Suite 8: User Management (Admin Only)**

#### **8.1 View Users**
1. Login as admin (ca_admin)
2. Navigate to User Management
3. **Verify Users Listed:**
   - ca_admin (Admin)
   - data_entry1 (Data Entry)
   - report_viewer1 (Report Viewer)
   - senior_ca (Admin)

#### **8.2 Add New User**
1. Click "Add User"
2. Create new user:
   - Username: test_user
   - Role: Data Entry
   - Password: test123
3. Save and note recovery key
4. Verify user appears in list

#### **8.3 Password Reset Testing**
1. Select existing user
2. Click "Reset Password"
3. Verify new recovery key is generated
4. Test login with new credentials

### **Test Suite 9: System Settings**

#### **9.1 Application Settings**
1. Navigate to Settings
2. **Verify Current Settings:**
   - Export Path configuration
   - Idle timeout settings
3. Modify settings and save
4. Verify changes persist

#### **9.2 Backup Management**
1. Navigate to Backup section
2. Click "Create Backup"
3. **Verify:** Backup is created successfully
4. Check backup appears in backup history

### **Test Suite 10: Audit Trail**

#### **10.1 View Audit Logs**
1. Navigate to Audit Trail
2. **Verify Existing Logs:**
   - User login activities
   - Company creation
   - Asset additions
   - Report generations
3. Perform an action (add asset)
4. Verify new audit entry appears

## 🔍 Security Testing

### **11.1 Role-Based Access Control**
1. Login as "report_viewer1"
2. Try to access User Management
3. **Verify:** Access is denied or restricted
4. Try to edit assets
5. **Verify:** Only read access available

### **11.2 Session Management**
1. Login as admin
2. Close browser/clear cookies
3. Try to access protected pages
4. **Verify:** Redirected to login

### **11.3 Password Security**
1. Try login with wrong password
2. **Verify:** Login fails with appropriate message
3. Test password recovery functionality

## 📊 Performance Testing

### **12.1 Large Data Handling**
1. Import large Excel file (50+ assets)
2. **Verify:** Import completes successfully
3. Check system responsiveness with large dataset

### **12.2 Report Generation Speed**
1. Generate reports with full dataset
2. **Verify:** Reports generate within acceptable time
3. Test Excel export performance

## ✅ Test Completion Checklist

### **Frontend Functionality**
- [ ] User authentication (all roles)
- [ ] Company management (view/add/edit)
- [ ] Asset management (CRUD operations)
- [ ] Depreciation calculations
- [ ] Report generation (all 6 reports)
- [ ] Import/export functionality
- [ ] Ledger master management
- [ ] User management (admin only)
- [ ] System settings
- [ ] Audit trail viewing

### **Backend API**
- [ ] Health check endpoint
- [ ] User authentication endpoints
- [ ] Company management endpoints
- [ ] Asset management endpoints
- [ ] Settings endpoints
- [ ] Audit log endpoints
- [ ] Backup endpoints

### **Security & Compliance**
- [ ] Role-based access control
- [ ] Password security
- [ ] Input validation
- [ ] Error handling
- [ ] Audit logging

### **Data Integrity**
- [ ] Database relationships
- [ ] Calculation accuracy
- [ ] Data persistence
- [ ] Backup/restore functionality

---

## 🚨 Issues & Bug Reporting

### **How to Report Issues:**
1. **Document the Issue:**
   - What were you trying to do?
   - What actually happened?
   - What did you expect to happen?

2. **Provide Details:**
   - User role used for testing
   - Company/asset being tested
   - Browser and version
   - Any error messages

3. **Steps to Reproduce:**
   - List exact steps to recreate the issue
   - Include test data used

### **Common Test Scenarios to Verify:**

#### **High Priority Tests:**
1. ✅ All user roles can login successfully
2. ✅ Asset calculations are mathematically correct
3. ✅ Reports generate accurate data
4. ✅ Import/export maintains data integrity
5. ✅ User permissions are properly enforced

#### **Medium Priority Tests:**
1. ✅ System handles invalid inputs gracefully
2. ✅ Large datasets perform adequately
3. ✅ All forms validate inputs properly
4. ✅ Navigation works across all sections

#### **Nice-to-Have Tests:**
1. ✅ System works across different browsers
2. ✅ Mobile/tablet responsiveness
3. ✅ Keyboard navigation accessibility
4. ✅ Print functionality for reports

---

## 🎯 Success Criteria

**The system passes testing if:**
- ✅ All user roles function as designed
- ✅ Asset calculations are accurate per Schedule II
- ✅ All reports generate correctly
- ✅ Import/export works reliably
- ✅ Security measures prevent unauthorized access
- ✅ System handles errors gracefully
- ✅ Performance is acceptable for CA practice use

**🚀 Ready for Production Deployment when all tests pass!**
