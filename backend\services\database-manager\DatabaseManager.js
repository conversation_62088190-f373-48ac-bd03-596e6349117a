import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';
import fsSync from 'fs';
import path from 'path';
import CompanyDatabaseService from './CompanyDatabaseService.js';
import { getAllRates } from './StatutoryRatesData.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class DatabaseManager {
    constructor() {
        this.basePath = join(__dirname, '../../database/companies');
        this.masterDbPath = join(__dirname, '../../database/master.db');
        this.companyDatabases = new Map();
        this.masterDb = null;
        this.isInitialized = false;
        this.initializationError = null;
    }

    async init() {
        if (this.isInitialized) {
            return;
        }

        try {
            console.log('🔄 Starting DatabaseManager initialization...');
            await this.ensureDirectoryExists(join(__dirname, '../../database'));
            await this.ensureDirectoryExists(this.basePath);
            await this.initMasterDatabase();
            this.isInitialized = true;
            console.log('✅ DatabaseManager initialization complete');
        } catch (error) {
            this.initializationError = error;
            console.error('❌ DatabaseManager initialization failed:', error);
            throw error;
        }
    }

    async ensureDirectoryExists(path) {
        try {
            await fs.access(path);
        } catch {
            await fs.mkdir(path, { recursive: true });
        }
    }

    async checkIfTablesExist() {
        try {
            const result = await this.getMasterQuery(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='companies'"
            );
            return !!result;
        } catch (error) {
            return false;
        }
    }

    async initMasterDatabase() {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Master database connection timeout'));
            }, 10000);

            this.masterDb = new sqlite3.Database(this.masterDbPath, async (err) => {
                clearTimeout(timeout);
                if (err) {
                    reject(err);
                } else {
                    try {
                        await this.createMasterTables();
                        resolve();
                    } catch (tableError) {
                        reject(tableError);
                    }
                }
            });
        });
    }

    async createMasterTables() {
        const tablesExist = await this.checkIfTablesExist();
        if (tablesExist) {
            await this.createDefaultUser();
            return;
        }

        const createTables = [
            `CREATE TABLE IF NOT EXISTS companies (...)`,
            `CREATE TABLE IF NOT EXISTS users (...)`,
            `CREATE TABLE IF NOT EXISTS global_audit_logs (...)`,
            `CREATE TABLE IF NOT EXISTS user_preferences (...)`,
            `CREATE TABLE IF NOT EXISTS global_settings (...)`
        ];

        for (let i = 0; i < createTables.length; i++) {
            await this.runMasterQuery(createTables[i]);
        }

        await this.createDefaultUser();
        await this.runMigrations();
    }

    async runMigrations() {
        // Migration logic here
    }

    async createDefaultUser() {
        // Default user creation logic here
    }

    async createCompanyDefaultUser(companyId, companyName) {
        // Company default user creation logic here
    }

    async runMasterQuery(sql, params = []) {
        if (!this.masterDb) throw new Error('Master database not initialized');
        return new Promise((resolve, reject) => {
            this.masterDb.run(sql, params, function(err) {
                if (err) reject(err);
                else resolve({ id: this.lastID, changes: this.changes });
            });
        });
    }

    async getMasterQuery(sql, params = []) {
        if (!this.masterDb) throw new Error('Master database not initialized');
        return new Promise((resolve, reject) => {
            this.masterDb.get(sql, params, (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
    }

    async getAllMasterQuery(sql, params = []) {
        if (!this.masterDb) throw new Error('Master database not initialized');
        return new Promise((resolve, reject) => {
            this.masterDb.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    }

    async createCompany(companyData) {
        // Company creation logic here
    }

    async getCompanyDatabase(companyId) {
        if (this.companyDatabases.has(companyId)) {
            return this.companyDatabases.get(companyId);
        }
        const company = await this.getMasterQuery('SELECT * FROM companies WHERE id = ?', [companyId]);
        if (!company) throw new Error(`Company not found: ${companyId}`);
        const companyDbService = new CompanyDatabaseService(company.database_path);
        await companyDbService.initialize();
        this.companyDatabases.set(companyId, companyDbService);
        return companyDbService;
    }

    async getAllCompanies() {
        return await this.getAllMasterQuery('SELECT id, company_name as name FROM companies ORDER BY company_name');
    }

    async getCompanyInfo(companyId) {
        const company = await this.getMasterQuery('SELECT * FROM companies WHERE id = ?', [companyId]);
        if (!company) throw new Error(`Company not found: ${companyId}`);
        return { /* company data */ };
    }

    async updateCompanyInfo(companyId, companyData) {
        // Update company info logic here
    }

    async deleteCompany(companyId) {
        if (!this.isInitialized) {
            await this.init();
        }

        const companyInfo = await this.getMasterQuery(
            'SELECT * FROM companies WHERE id = ?',
            [companyId]
        );

        if (!companyInfo) {
            throw new Error('Company not found');
        }

        if (this.companyDatabases.has(companyId)) {
            const companyDbService = this.companyDatabases.get(companyId);
            await companyDbService.close();
            this.companyDatabases.delete(companyId);
            console.log(`[DatabaseManager] Closed and removed cached connection for company: ${companyId}`);
        }

        const companyDbPath = companyInfo.database_path;
        if (fsSync.existsSync(companyDbPath)) {
            fsSync.unlinkSync(companyDbPath);
            console.log(`[DatabaseManager] Deleted company database: ${companyDbPath}`);
        }

        const companyFolderPath = companyInfo.data_folder_path;
        if (fsSync.existsSync(companyFolderPath)) {
            fsSync.rmSync(companyFolderPath, { recursive: true, force: true });
            console.log(`[DatabaseManager] Deleted company folder: ${companyFolderPath}`);
        }

        await this.runMasterQuery(
            'DELETE FROM companies WHERE id = ?',
            [companyId]
        );

        console.log(`[DatabaseManager] Company ${companyId} deleted successfully`);
    }

    sanitizeFolderName(name) {
        return name.replace(/[<>:"/\\|?*]/g, '').replace(/\s+/g, '_').substring(0, 50);
    }

    async closeAll() {
        for (const [companyId, dbService] of this.companyDatabases) {
            await dbService.close();
        }
        this.companyDatabases.clear();
        if (this.masterDb) {
            return new Promise((resolve) => {
                this.masterDb.close((err) => {
                    resolve();
                });
            });
        }
    }

    async getUsers() {
        return await this.getAllMasterQuery('SELECT id, username, role, created_at FROM users ORDER BY username');
    }

    async createUser(userData) {
        // Create user logic here
    }

    async updateUser(userId, userData) {
        // Update user logic here
    }

    async authenticateUser(username, password) {
        // Authenticate user logic here
    }

    getDefaultStatutoryRates() {
        return getAllRates();
    }
}

export default DatabaseManager;