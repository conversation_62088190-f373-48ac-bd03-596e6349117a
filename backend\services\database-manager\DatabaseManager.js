import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';
import fsSync from 'fs';
import path from 'path';
import CompanyDatabaseService from './CompanyDatabaseService.js';
import { getAllRates } from './StatutoryRatesData.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class DatabaseManager {
    constructor() {
        this.basePath = join(__dirname, '../../database/companies');
        this.masterDbPath = join(__dirname, '../../database/master.db');
        this.companyDatabases = new Map();
        this.masterDb = null;
        this.isInitialized = false;
        this.initializationError = null;
    }

    async init() {
        if (this.isInitialized) {
            return;
        }

        try {
            console.log('🔄 Starting DatabaseManager initialization...');
            await this.ensureDirectoryExists(join(__dirname, '../../database'));
            await this.ensureDirectoryExists(this.basePath);
            await this.initMasterDatabase();
            this.isInitialized = true;
            console.log('✅ DatabaseManager initialization complete');
        } catch (error) {
            this.initializationError = error;
            console.error('❌ DatabaseManager initialization failed:', error);
            throw error;
        }
    }

    async ensureDirectoryExists(path) {
        try {
            await fs.access(path);
        } catch {
            await fs.mkdir(path, { recursive: true });
        }
    }

    async checkIfTablesExist() {
        try {
            const result = await this.getMasterQuery(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='companies'"
            );
            return !!result;
        } catch (error) {
            return false;
        }
    }

    async initMasterDatabase() {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Master database connection timeout'));
            }, 10000);

            this.masterDb = new sqlite3.Database(this.masterDbPath, async (err) => {
                clearTimeout(timeout);
                if (err) {
                    reject(err);
                } else {
                    try {
                        await this.createMasterTables();
                        resolve();
                    } catch (tableError) {
                        reject(tableError);
                    }
                }
            });
        });
    }

    async createMasterTables() {
        const tablesExist = await this.checkIfTablesExist();
        if (tablesExist) {
            await this.createDefaultUser();
            return;
        }

        const createTables = [
            `CREATE TABLE IF NOT EXISTS companies (...)`,
            `CREATE TABLE IF NOT EXISTS users (...)`,
            `CREATE TABLE IF NOT EXISTS global_audit_logs (...)`,
            `CREATE TABLE IF NOT EXISTS user_preferences (...)`,
            `CREATE TABLE IF NOT EXISTS global_settings (...)`
        ];

        for (let i = 0; i < createTables.length; i++) {
            await this.runMasterQuery(createTables[i]);
        }

        await this.createDefaultUser();
        await this.runMigrations();
    }

    async runMigrations() {
        // Migration logic here
    }

    async createDefaultUser() {
        // Default user creation logic here
    }

    async createCompanyDefaultUser(companyId, companyName) {
        const username = 'admin'; // Each company can have its own 'admin' user

        // Get company database service
        const companyDbService = this.companyDatabases.get(companyId);
        const existingUser = await companyDbService.get(
            'SELECT id FROM users WHERE username = ?',
            [username]
        );

        if (!existingUser) {
            console.log(`👤 Creating default admin user for company: ${companyName}`);

            // Generate proper bcrypt hash for 'admin123'
            const bcrypt = await import('bcrypt');
            const hashedPassword = await bcrypt.hash('admin123', 10);

            // Generate recovery key
            const recoveryKey = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
            const recoveryKeyHash = await bcrypt.hash(recoveryKey, 10);

            const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            await companyDbService.run(
                `INSERT INTO users (id, username, password_hash, role, recovery_key_hash, has_saved_recovery_key, is_active, must_change_password)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [userId, username, hashedPassword, 'Admin', recoveryKeyHash, 0, 1, 1]
            );

            console.log(`✅ Default admin user created for ${companyName}`);
            console.log(`   Username: admin, Password: admin123`);
            console.log(`   User will be prompted to change password on first login`);

            return { userId, recoveryKey };
        } else {
            console.log(`👤 Default admin user already exists for company: ${companyName}`);
            return null;
        }
    }

    async runMasterQuery(sql, params = []) {
        if (!this.masterDb) throw new Error('Master database not initialized');
        return new Promise((resolve, reject) => {
            this.masterDb.run(sql, params, function(err) {
                if (err) reject(err);
                else resolve({ id: this.lastID, changes: this.changes });
            });
        });
    }

    async getMasterQuery(sql, params = []) {
        if (!this.masterDb) throw new Error('Master database not initialized');
        return new Promise((resolve, reject) => {
            this.masterDb.get(sql, params, (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
    }

    async getAllMasterQuery(sql, params = []) {
        if (!this.masterDb) throw new Error('Master database not initialized');
        return new Promise((resolve, reject) => {
            this.masterDb.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    }

    async createCompany(companyData) {
        if (!this.isInitialized) {
            await this.init();
        }

        // Generate company ID
        const companyId = `c${Date.now()}`;

        // Create user-friendly folder name with uniqueness check
        const baseFolderName = this.sanitizeFolderName(companyData.companyName);
        const uniqueFolderName = await this.generateUniqueFolderName(baseFolderName);

        // Create paths
        const dataFolderPath = path.join(this.dataDir, 'companies', `${companyId}-${uniqueFolderName}`);
        const databasePath = path.join(dataFolderPath, 'company.db');

        // Ensure directory exists
        await fs.mkdir(dataFolderPath, { recursive: true });

        // Insert company record in master database
        await this.runMasterQuery(
            `INSERT INTO companies (
                id, company_name, pan, cin, date_of_incorporation,
                financial_year_start, financial_year_end, first_date_of_adoption,
                data_folder_path, database_path, address_line1, address_line2,
                city, pin, email, mobile, contact_person, license_valid_upto,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                companyId, companyData.companyName, companyData.pan, companyData.cin,
                companyData.dateOfIncorporation, companyData.financialYearStart,
                companyData.financialYearEnd, companyData.firstDateOfAdoption,
                dataFolderPath, databasePath, companyData.addressLine1, companyData.addressLine2,
                companyData.city, companyData.pin, companyData.email, companyData.mobile,
                companyData.contactPerson, companyData.licenseValidUpto,
                new Date().toISOString(), new Date().toISOString()
            ]
        );

        // Create company database
        const companyDbService = new CompanyDatabaseService(databasePath);
        await companyDbService.init();

        // Add default financial year
        const startYear = new Date(companyData.financialYearStart).getFullYear();
        const endYear = new Date(companyData.financialYearEnd).getFullYear();
        const yearRange = `${startYear}-${endYear}`;

        await companyDbService.run(
            'INSERT INTO financial_years (year_range, is_locked) VALUES (?, ?)',
            [yearRange, false]
        );

        // Create default admin user for this company
        await this.createCompanyDefaultUser(companyId, companyData.companyName);

        // Cache the company database service
        this.companyDatabases.set(companyId, companyDbService);

        console.log(`✅ Company ${companyData.companyName} created successfully`);
        console.log(`📁 Data folder: ${dataFolderPath}`);
        console.log(`💾 Database: ${databasePath}`);

        return {
            id: companyId,
            name: companyData.companyName,
            dataFolderPath,
            databasePath
        };
    }

    async getCompanyDatabase(companyId) {
        if (this.companyDatabases.has(companyId)) {
            return this.companyDatabases.get(companyId);
        }
        const company = await this.getMasterQuery('SELECT * FROM companies WHERE id = ?', [companyId]);
        if (!company) throw new Error(`Company not found: ${companyId}`);
        const companyDbService = new CompanyDatabaseService(company.database_path);
        await companyDbService.initialize();
        this.companyDatabases.set(companyId, companyDbService);
        return companyDbService;
    }

    async getAllCompanies() {
        return await this.getAllMasterQuery('SELECT id, company_name as name FROM companies ORDER BY company_name');
    }

    async getCompanyInfo(companyId) {
        const company = await this.getMasterQuery('SELECT * FROM companies WHERE id = ?', [companyId]);
        if (!company) throw new Error(`Company not found: ${companyId}`);

        // Convert database column names to frontend format
        return {
            id: company.id,
            companyName: company.company_name,
            pan: company.pan,
            cin: company.cin,
            dateOfIncorporation: company.date_of_incorporation,
            financialYearStart: company.financial_year_start,
            financialYearEnd: company.financial_year_end,
            firstDateOfAdoption: company.first_date_of_adoption,
            dataFolderPath: company.data_folder_path,
            addressLine1: company.address_line1,
            addressLine2: company.address_line2,
            city: company.city,
            pin: company.pin,
            email: company.email,
            mobile: company.mobile,
            contactPerson: company.contact_person,
            licenseValidUpto: company.license_valid_upto,
            databasePath: company.database_path
        };
    }

    async updateCompanyInfo(companyId, companyData) {
        // Update company info logic here
    }

    async deleteCompany(companyId) {
        if (!this.isInitialized) {
            await this.init();
        }

        const companyInfo = await this.getMasterQuery(
            'SELECT * FROM companies WHERE id = ?',
            [companyId]
        );

        if (!companyInfo) {
            throw new Error('Company not found');
        }

        if (this.companyDatabases.has(companyId)) {
            const companyDbService = this.companyDatabases.get(companyId);
            await companyDbService.close();
            this.companyDatabases.delete(companyId);
            console.log(`[DatabaseManager] Closed and removed cached connection for company: ${companyId}`);
        }

        const companyDbPath = companyInfo.database_path;
        if (fsSync.existsSync(companyDbPath)) {
            fsSync.unlinkSync(companyDbPath);
            console.log(`[DatabaseManager] Deleted company database: ${companyDbPath}`);
        }

        const companyFolderPath = companyInfo.data_folder_path;
        if (fsSync.existsSync(companyFolderPath)) {
            fsSync.rmSync(companyFolderPath, { recursive: true, force: true });
            console.log(`[DatabaseManager] Deleted company folder: ${companyFolderPath}`);
        }

        await this.runMasterQuery(
            'DELETE FROM companies WHERE id = ?',
            [companyId]
        );

        console.log(`[DatabaseManager] Company ${companyId} deleted successfully`);
    }

    sanitizeFolderName(name) {
        return name.replace(/[<>:"/\\|?*]/g, '').replace(/\s+/g, '_').substring(0, 50);
    }

    /**
     * Generate unique folder name by checking existing folders
     */
    async generateUniqueFolderName(baseName) {
        if (!this.isInitialized) {
            await this.init();
        }

        // Check if base name is already used
        const existingCompanies = await this.getAllMasterQuery(
            'SELECT data_folder_path FROM companies'
        );

        const existingFolderNames = existingCompanies.map(company => {
            const folderPath = company.data_folder_path;
            const folderName = path.basename(folderPath);
            // Extract the part after the company ID (e.g., "c123456-CompanyName" -> "CompanyName")
            const parts = folderName.split('-');
            return parts.length > 1 ? parts.slice(1).join('-') : folderName;
        });

        let uniqueName = baseName;
        let counter = 1;

        // Keep incrementing until we find a unique name
        while (existingFolderNames.includes(uniqueName)) {
            uniqueName = `${baseName}_${counter}`;
            counter++;
        }

        return uniqueName;
    }

    async closeAll() {
        for (const [companyId, dbService] of this.companyDatabases) {
            await dbService.close();
        }
        this.companyDatabases.clear();
        if (this.masterDb) {
            return new Promise((resolve) => {
                this.masterDb.close((err) => {
                    resolve();
                });
            });
        }
    }

    async getUsers() {
        return await this.getAllMasterQuery('SELECT id, username, role, created_at FROM users ORDER BY username');
    }

    async createUser(userData) {
        // Create user logic here
    }

    async updateUser(userId, userData) {
        // Update user logic here
    }

    async authenticateUser(username, password) {
        // Authenticate user logic here
    }

    getDefaultStatutoryRates() {
        return getAllRates();
    }
}

export default DatabaseManager;