import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';
import CompanyDatabaseService from './CompanyDatabaseService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * DatabaseManager - Manages multiple company databases
 * Each company gets its own SQLite database in a separate folder
 */
class DatabaseManager {
    constructor() {
        this.basePath = join(__dirname, '../../database/companies');
        this.masterDbPath = join(__dirname, '../../database/master.db');
        this.companyDatabases = new Map(); // Cache for active connections
        this.masterDb = null;
        this.init();
    }

    async init() {
        // Ensure base directories exist
        await this.ensureDirectoryExists(join(__dirname, '../../database'));
        await this.ensureDirectoryExists(this.basePath);
        
        // Initialize master database
        await this.initMasterDatabase();
    }

    async ensureDirectoryExists(path) {
        try {
            await fs.access(path);
        } catch {
            await fs.mkdir(path, { recursive: true });
        }
    }

    /**
     * Initialize master database for company registry and users
     */
    async initMasterDatabase() {
        return new Promise((resolve, reject) => {
            this.masterDb = new sqlite3.Database(this.masterDbPath, (err) => {
                if (err) {
                    console.error('❌ Error connecting to master database:', err.message);
                    reject(err);
                } else {
                    console.log('✅ Connected to master database');
                    this.createMasterTables().then(resolve).catch(reject);
                }
            });
        });
    }

    /**
     * Create master database tables
     */
    async createMasterTables() {
        const createTables = [
            // Companies registry
            `CREATE TABLE IF NOT EXISTS companies (
                id TEXT PRIMARY KEY,
                company_name TEXT NOT NULL,
                pan TEXT,
                cin TEXT,
                date_of_incorporation TEXT,
                financial_year_start TEXT NOT NULL,
                financial_year_end TEXT NOT NULL,
                first_date_of_adoption TEXT NOT NULL,
                data_folder_path TEXT NOT NULL,
                address_line1 TEXT,
                address_line2 TEXT,
                city TEXT,
                pin TEXT,
                email TEXT,
                mobile TEXT,
                contact_person TEXT,
                license_valid_upto TEXT,
                database_path TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Global users table
            `CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL CHECK (role IN ('Admin', 'Data Entry', 'Report Viewer')),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Global audit trail
            `CREATE TABLE IF NOT EXISTS global_audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                user_id TEXT,
                username TEXT,
                company_id TEXT,
                action TEXT NOT NULL,
                details TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )`,
            
            // Global settings
            `CREATE TABLE IF NOT EXISTS global_settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (const sql of createTables) {
            await this.runMasterQuery(sql);
        }

        // Create default admin user if not exists
        await this.createDefaultUser();
    }

    /**
     * Create default admin user
     */
    async createDefaultUser() {
        const existingUser = await this.getMasterQuery(
            'SELECT id FROM users WHERE username = ?', 
            ['admin']
        );

        if (!existingUser) {
            // Using bcrypt hash for 'admin123'
            const hashedPassword = '$2b$10$rGK4VJkQJkZQJG4VJkQJkOK4VJkQJkZQJG4VJkQJkZQJG4VJkQJkQu';
            await this.runMasterQuery(
                `INSERT INTO users (id, username, password, role) 
                 VALUES (?, ?, ?, ?)`,
                ['admin-001', 'admin', hashedPassword, 'Admin']
            );
            console.log('✅ Default admin user created');
        }
    }

    /**
     * Run query on master database
     */
    runMasterQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.masterDb.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    /**
     * Get single row from master database
     */
    getMasterQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.masterDb.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * Get multiple rows from master database
     */
    getAllMasterQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.masterDb.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    /**
     * Create a new company with its own database
     */
    async createCompany(companyData) {
        const companyId = `c${Date.now()}`;
        const companyFolderName = this.sanitizeFolderName(companyData.companyName);
        const companyFolderPath = join(this.basePath, `${companyId}-${companyFolderName}`);
        const databasePath = join(companyFolderPath, 'company.db');

        try {
            // Create company folder
            await this.ensureDirectoryExists(companyFolderPath);

            // Create company database
            const companyDbService = new CompanyDatabaseService(databasePath);
            await companyDbService.initialize();

            // Register company in master database
            await this.runMasterQuery(
                `INSERT INTO companies (
                    id, company_name, pan, cin, date_of_incorporation,
                    financial_year_start, financial_year_end, first_date_of_adoption,
                    data_folder_path, address_line1, address_line2, city, pin,
                    email, mobile, contact_person, license_valid_upto, database_path
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    companyId, companyData.companyName, companyData.pan, companyData.cin,
                    companyData.dateOfIncorporation, companyData.financialYearStart,
                    companyData.financialYearEnd, companyData.firstDateOfAdoption,
                    companyFolderPath, companyData.addressLine1, companyData.addressLine2,
                    companyData.city, companyData.pin, companyData.email, companyData.mobile,
                    companyData.contactPerson, companyData.licenseValidUpto, databasePath
                ]
            );

            // Store company service in cache
            this.companyDatabases.set(companyId, companyDbService);

            console.log(`✅ Company created: ${companyData.companyName} (${companyId})`);
            return { id: companyId, name: companyData.companyName };

        } catch (error) {
            console.error('❌ Error creating company:', error);
            throw error;
        }
    }

    /**
     * Get company database service
     */
    async getCompanyDatabase(companyId) {
        // Return cached connection if available
        if (this.companyDatabases.has(companyId)) {
            return this.companyDatabases.get(companyId);
        }

        // Get company info from master database
        const company = await this.getMasterQuery(
            'SELECT * FROM companies WHERE id = ?',
            [companyId]
        );

        if (!company) {
            throw new Error(`Company not found: ${companyId}`);
        }

        // Create new connection
        const companyDbService = new CompanyDatabaseService(company.database_path);
        await companyDbService.initialize();
        
        // Cache the connection
        this.companyDatabases.set(companyId, companyDbService);

        return companyDbService;
    }

    /**
     * Get all companies
     */
    async getAllCompanies() {
        return await this.getAllMasterQuery(
            'SELECT id, company_name as name FROM companies ORDER BY company_name'
        );
    }

    /**
     * Get company info
     */
    async getCompanyInfo(companyId) {
        const company = await this.getMasterQuery(
            'SELECT * FROM companies WHERE id = ?',
            [companyId]
        );

        if (!company) {
            throw new Error(`Company not found: ${companyId}`);
        }

        return {
            id: company.id,
            companyName: company.company_name,
            pan: company.pan,
            cin: company.cin,
            dateOfIncorporation: company.date_of_incorporation,
            financialYearStart: company.financial_year_start,
            financialYearEnd: company.financial_year_end,
            firstDateOfAdoption: company.first_date_of_adoption,
            dataFolderPath: company.data_folder_path,
            addressLine1: company.address_line1,
            addressLine2: company.address_line2,
            city: company.city,
            pin: company.pin,
            email: company.email,
            mobile: company.mobile,
            contactPerson: company.contact_person,
            licenseValidUpto: company.license_valid_upto
        };
    }

    /**
     * Update company info
     */
    async updateCompanyInfo(companyId, companyData) {
        await this.runMasterQuery(
            `UPDATE companies SET 
                company_name = ?, pan = ?, cin = ?, date_of_incorporation = ?,
                financial_year_start = ?, financial_year_end = ?, first_date_of_adoption = ?,
                address_line1 = ?, address_line2 = ?, city = ?, pin = ?,
                email = ?, mobile = ?, contact_person = ?, license_valid_upto = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?`,
            [
                companyData.companyName, companyData.pan, companyData.cin,
                companyData.dateOfIncorporation, companyData.financialYearStart,
                companyData.financialYearEnd, companyData.firstDateOfAdoption,
                companyData.addressLine1, companyData.addressLine2,
                companyData.city, companyData.pin, companyData.email, companyData.mobile,
                companyData.contactPerson, companyData.licenseValidUpto, companyId
            ]
        );
    }

    /**
     * Sanitize folder name for file system
     */
    sanitizeFolderName(name) {
        return name
            .replace(/[<>:"/\\|?*]/g, '') // Remove invalid characters
            .replace(/\s+/g, '_') // Replace spaces with underscores
            .substring(0, 50); // Limit length
    }

    /**
     * Close all database connections
     */
    async closeAll() {
        // Close company databases
        for (const [companyId, dbService] of this.companyDatabases) {
            await dbService.close();
        }
        this.companyDatabases.clear();

        // Close master database
        if (this.masterDb) {
            return new Promise((resolve) => {
                this.masterDb.close((err) => {
                    if (err) {
                        console.error('Error closing master database:', err);
                    } else {
                        console.log('🔌 Master database connection closed');
                    }
                    resolve();
                });
            });
        }
    }

    /**
     * Get master database users
     */
    async getUsers() {
        return await this.getAllMasterQuery(
            'SELECT id, username, role, created_at FROM users ORDER BY username'
        );
    }

    /**
     * Create new user
     */
    async createUser(userData) {
        const userId = `u${Date.now()}`;
        await this.runMasterQuery(
            'INSERT INTO users (id, username, password, role) VALUES (?, ?, ?, ?)',
            [userId, userData.username, userData.password, userData.role]
        );
        return userId;
    }

    /**
     * Update user
     */
    async updateUser(userId, userData) {
        await this.runMasterQuery(
            'UPDATE users SET username = ?, role = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [userData.username, userData.role, userId]
        );
    }

    /**
     * Authenticate user
     */
    async authenticateUser(username, password) {
        return await this.getMasterQuery(
            'SELECT * FROM users WHERE username = ? AND password = ?',
            [username, password]
        );
    }
}

export default DatabaseManager;