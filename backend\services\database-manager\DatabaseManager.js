import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';
import CompanyDatabaseService from './CompanyDatabaseService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * DatabaseManager - Manages multiple company databases
 * Each company gets its own SQLite database in a separate folder
 */
class DatabaseManager {
    constructor() {
        this.basePath = join(__dirname, '../../database/companies');
        this.masterDbPath = join(__dirname, '../../database/master.db');
        this.companyDatabases = new Map(); // Cache for active connections
        this.masterDb = null;
        this.isInitialized = false;
        this.initializationError = null;
    }

    async init() {
        if (this.isInitialized) {
            return;
        }

        try {
            console.log('🔄 Starting DatabaseManager initialization...');
            
            // Ensure base directories exist
            console.log('📁 Creating database directories...');
            await this.ensureDirectoryExists(join(__dirname, '../../database'));
            await this.ensureDirectoryExists(this.basePath);
            
            // Initialize master database
            console.log('🗄️ Initializing master database...');
            await this.initMasterDatabase();
            
            this.isInitialized = true;
            console.log('✅ DatabaseManager initialization complete');
        } catch (error) {
            this.initializationError = error;
            console.error('❌ DatabaseManager initialization failed:', error);
            throw error;
        }
    }

    async ensureDirectoryExists(path) {
        try {
            await fs.access(path);
            console.log(`📁 Directory exists: ${path}`);
        } catch {
            console.log(`📁 Creating directory: ${path}`);
            await fs.mkdir(path, { recursive: true });
        }
    }

    /**
     * Initialize master database for company registry and users
     */
    async initMasterDatabase() {
        console.log(`🔗 Connecting to master database: ${this.masterDbPath}`);
        
        return new Promise((resolve, reject) => {
            // Set a timeout to prevent hanging
            const timeout = setTimeout(() => {
                reject(new Error('Master database connection timeout'));
            }, 10000); // 10 second timeout

            this.masterDb = new sqlite3.Database(this.masterDbPath, async (err) => {
                clearTimeout(timeout);
                
                if (err) {
                    console.error('❌ Error connecting to master database:', err.message);
                    reject(err);
                } else {
                    console.log('✅ Connected to master database');
                    try {
                        await this.createMasterTables();
                        resolve();
                    } catch (tableError) {
                        console.error('❌ Error creating master tables:', tableError);
                        reject(tableError);
                    }
                }
            });
        });
    }

    /**
     * Create master database tables
     */
    async createMasterTables() {
        console.log('📋 Creating master database tables...');
        
        const createTables = [
            // Companies registry
            `CREATE TABLE IF NOT EXISTS companies (
                id TEXT PRIMARY KEY,
                company_name TEXT NOT NULL,
                pan TEXT,
                cin TEXT,
                date_of_incorporation TEXT,
                financial_year_start TEXT NOT NULL,
                financial_year_end TEXT NOT NULL,
                first_date_of_adoption TEXT NOT NULL,
                data_folder_path TEXT NOT NULL,
                address_line1 TEXT,
                address_line2 TEXT,
                city TEXT,
                pin TEXT,
                email TEXT,
                mobile TEXT,
                contact_person TEXT,
                license_valid_upto TEXT,
                database_path TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Global users table
            `CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL CHECK (role IN ('Admin', 'Data Entry', 'Report Viewer')),
                recovery_key_hash TEXT,
                has_saved_recovery_key BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                must_change_password BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Global audit trail
            `CREATE TABLE IF NOT EXISTS global_audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                user_id TEXT,
                username TEXT,
                company_id TEXT,
                action TEXT NOT NULL,
                details TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )`,
            
            // Global settings
            `CREATE TABLE IF NOT EXISTS global_settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (let i = 0; i < createTables.length; i++) {
            console.log(`📋 Creating table ${i + 1}/${createTables.length}...`);
            await this.runMasterQuery(createTables[i]);
        }

        // Create default admin user if not exists
        console.log('👤 Checking for default admin user...');
        await this.createDefaultUser();
        
        console.log('✅ Master database tables created successfully');
    }

    /**
     * Create default admin user
     */
    async createDefaultUser() {
        const existingUser = await this.getMasterQuery(
            'SELECT id FROM users WHERE username = ?',
            ['admin']
        );

        if (!existingUser) {
            console.log('👤 Creating default admin user...');
            // Generate proper bcrypt hash for 'admin123'
            const bcrypt = await import('bcrypt');
            const hashedPassword = await bcrypt.hash('admin123', 10);

            // Generate recovery key
            const recoveryKey = `rk_admin_${Math.random().toString(36).substring(2, 15)}`;
            const recoveryKeyHash = await bcrypt.hash(recoveryKey, 10);

            await this.runMasterQuery(
                `INSERT INTO users (id, username, password_hash, role, recovery_key_hash, has_saved_recovery_key, is_active, must_change_password)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                ['admin-001', 'admin', hashedPassword, 'Admin', recoveryKeyHash, false, true, true]
            );
            console.log('✅ Default admin user created with username: admin, password: admin123');
            console.log('⚠️  User will be prompted to change password on first login');
        } else {
            console.log('👤 Default admin user already exists');
        }
    }

    /**
     * Create default admin user for a company
     */
    async createCompanyDefaultUser(companyId, companyName) {
        const username = 'admin';
        const existingUser = await this.getMasterQuery(
            'SELECT id FROM users WHERE username = ? AND id LIKE ?',
            [username, `${companyId}-%`]
        );

        if (!existingUser) {
            console.log(`👤 Creating default admin user for company: ${companyName}`);

            // Generate proper bcrypt hash for 'admin123'
            const bcrypt = await import('bcrypt');
            const hashedPassword = await bcrypt.hash('admin123', 10);

            // Generate recovery key
            const recoveryKey = `rk_${companyId}_admin_${Math.random().toString(36).substring(2, 15)}`;
            const recoveryKeyHash = await bcrypt.hash(recoveryKey, 10);

            const userId = `${companyId}-admin-001`;

            await this.runMasterQuery(
                `INSERT INTO users (id, username, password_hash, role, recovery_key_hash, has_saved_recovery_key, is_active, must_change_password)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [userId, username, hashedPassword, 'Admin', recoveryKeyHash, false, true, true]
            );

            console.log(`✅ Default admin user created for ${companyName}`);
            console.log(`   Username: admin, Password: admin123`);
            console.log(`   User will be prompted to change password on first login`);

            return { userId, recoveryKey };
        } else {
            console.log(`👤 Default admin user already exists for company: ${companyName}`);
            return null;
        }
    }

    /**
     * Run query on master database
     */
    async runMasterQuery(sql, params = []) {
        if (!this.masterDb) {
            throw new Error('Master database not initialized');
        }
        
        return new Promise((resolve, reject) => {
            // Add timeout to prevent hanging
            const timeout = setTimeout(() => {
                reject(new Error('Query timeout'));
            }, 5000);

            this.masterDb.run(sql, params, function(err) {
                clearTimeout(timeout);
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    /**
     * Get single row from master database
     */
    async getMasterQuery(sql, params = []) {
        if (!this.masterDb) {
            throw new Error('Master database not initialized');
        }
        
        return new Promise((resolve, reject) => {
            // Add timeout to prevent hanging
            const timeout = setTimeout(() => {
                reject(new Error('Query timeout'));
            }, 5000);

            this.masterDb.get(sql, params, (err, row) => {
                clearTimeout(timeout);
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * Get multiple rows from master database
     */
    async getAllMasterQuery(sql, params = []) {
        if (!this.masterDb) {
            throw new Error('Master database not initialized');
        }
        
        return new Promise((resolve, reject) => {
            // Add timeout to prevent hanging
            const timeout = setTimeout(() => {
                reject(new Error('Query timeout'));
            }, 5000);

            this.masterDb.all(sql, params, (err, rows) => {
                clearTimeout(timeout);
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    /**
     * Create a new company with its own database
     */
    async createCompany(companyData) {
        if (!this.isInitialized) {
            await this.init();
        }
        
        const companyId = `c${Date.now()}`;
        const companyFolderName = this.sanitizeFolderName(companyData.companyName);
        const companyFolderPath = join(this.basePath, `${companyId}-${companyFolderName}`);
        const databasePath = join(companyFolderPath, 'company.db');

        try {
            // Create company folder
            await this.ensureDirectoryExists(companyFolderPath);

            // Create company database
            const companyDbService = new CompanyDatabaseService(databasePath);
            await companyDbService.initialize();

            // Insert initial financial year into company database
            const fyStart = new Date(companyData.financialYearStart);
            const fyEnd = new Date(companyData.financialYearEnd);
            const initialFinancialYear = `${fyStart.getFullYear()}-${fyEnd.getFullYear()}`;

            await companyDbService.run(
                'INSERT INTO financial_years (year_range) VALUES (?)',
                [initialFinancialYear]
            );

            // Insert company info into company database
            await companyDbService.run(
                `INSERT INTO company_info (
                    id, company_name, pan, cin, date_of_incorporation,
                    financial_year_start, financial_year_end, first_date_of_adoption,
                    address_line1, address_line2, city, pin,
                    email, mobile, contact_person, license_valid_upto,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    companyId, companyData.companyName, companyData.pan, companyData.cin,
                    companyData.dateOfIncorporation, companyData.financialYearStart,
                    companyData.financialYearEnd, companyData.firstDateOfAdoption,
                    companyData.addressLine1, companyData.addressLine2,
                    companyData.city, companyData.pin, companyData.email, companyData.mobile,
                    companyData.contactPerson, companyData.licenseValidUpto,
                    new Date().toISOString(), new Date().toISOString()
                ]
            );

            // Insert default statutory rates
            const defaultStatutoryRates = this.getDefaultStatutoryRates();
            for (const rate of defaultStatutoryRates) {
                await companyDbService.run(
                    `INSERT INTO statutory_rates (
                        is_statutory, tangibility, asset_group, asset_sub_group,
                        extra_shift_depreciation, useful_life_years, schedule_ii_classification
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
                    [
                        rate.isStatutory, rate.tangibility, rate.assetGroup,
                        rate.assetSubGroup, rate.extraShiftDepreciation,
                        rate.usefulLifeYears, rate.scheduleIIClassification
                    ]
                );
            }

            // Create default admin user for this company
            await this.createCompanyDefaultUser(companyId, companyData.companyName);

            // Register company in master database
            await this.runMasterQuery(
                `INSERT INTO companies (
                    id, company_name, pan, cin, date_of_incorporation,
                    financial_year_start, financial_year_end, first_date_of_adoption,
                    data_folder_path, address_line1, address_line2, city, pin,
                    email, mobile, contact_person, license_valid_upto, database_path,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    companyId, companyData.companyName, companyData.pan, companyData.cin,
                    companyData.dateOfIncorporation, companyData.financialYearStart,
                    companyData.financialYearEnd, companyData.firstDateOfAdoption,
                    companyFolderPath, companyData.addressLine1, companyData.addressLine2,
                    companyData.city, companyData.pin, companyData.email, companyData.mobile,
                    companyData.contactPerson, companyData.licenseValidUpto, databasePath,
                    new Date().toISOString(), new Date().toISOString()
                ]
            );

            // Store company service in cache
            this.companyDatabases.set(companyId, companyDbService);

            console.log(`✅ Company created: ${companyData.companyName} (${companyId})`);
            return { id: companyId, name: companyData.companyName };

        } catch (error) {
            console.error('❌ Error creating company:', error);
            throw error;
        }
    }

    /**
     * Get company database service
     */
    async getCompanyDatabase(companyId) {
        if (!this.isInitialized) {
            await this.init();
        }
        
        // Return cached connection if available
        if (this.companyDatabases.has(companyId)) {
            return this.companyDatabases.get(companyId);
        }

        // Get company info from master database
        const company = await this.getMasterQuery(
            'SELECT * FROM companies WHERE id = ?',
            [companyId]
        );

        if (!company) {
            throw new Error(`Company not found: ${companyId}`);
        }

        // Create new connection
        const companyDbService = new CompanyDatabaseService(company.database_path);
        await companyDbService.initialize();
        
        // Cache the connection
        this.companyDatabases.set(companyId, companyDbService);

        return companyDbService;
    }

    /**
     * Get all companies
     */
    async getAllCompanies() {
        if (!this.isInitialized) {
            await this.init();
        }
        
        return await this.getAllMasterQuery(
            'SELECT id, company_name as name FROM companies ORDER BY company_name'
        );
    }

    /**
     * Get company info
     */
    async getCompanyInfo(companyId) {
        if (!this.isInitialized) {
            await this.init();
        }
        
        const company = await this.getMasterQuery(
            'SELECT * FROM companies WHERE id = ?',
            [companyId]
        );

        if (!company) {
            throw new Error(`Company not found: ${companyId}`);
        }

        return {
            id: company.id,
            companyName: company.company_name,
            pan: company.pan,
            cin: company.cin,
            dateOfIncorporation: company.date_of_incorporation,
            financialYearStart: company.financial_year_start,
            financialYearEnd: company.financial_year_end,
            firstDateOfAdoption: company.first_date_of_adoption,
            dataFolderPath: company.data_folder_path,
            addressLine1: company.address_line1,
            addressLine2: company.address_line2,
            city: company.city,
            pin: company.pin,
            email: company.email,
            mobile: company.mobile,
            contactPerson: company.contact_person,
            licenseValidUpto: company.license_valid_upto
        };
    }

    /**
     * Update company info
     */
    async updateCompanyInfo(companyId, companyData) {
        if (!this.isInitialized) {
            await this.init();
        }
        
        await this.runMasterQuery(
            `UPDATE companies SET 
                company_name = ?, pan = ?, cin = ?, date_of_incorporation = ?,
                financial_year_start = ?, financial_year_end = ?, first_date_of_adoption = ?,
                address_line1 = ?, address_line2 = ?, city = ?, pin = ?,
                email = ?, mobile = ?, contact_person = ?, license_valid_upto = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?`,
            [
                companyData.companyName, companyData.pan, companyData.cin,
                companyData.dateOfIncorporation, companyData.financialYearStart,
                companyData.financialYearEnd, companyData.firstDateOfAdoption,
                companyData.addressLine1, companyData.addressLine2,
                companyData.city, companyData.pin, companyData.email, companyData.mobile,
                companyData.contactPerson, companyData.licenseValidUpto, companyId
            ]
        );
    }

    /**
     * Sanitize folder name for file system
     */
    sanitizeFolderName(name) {
        return name
            .replace(/[<>:"/\\|?*]/g, '') // Remove invalid characters
            .replace(/\s+/g, '_') // Replace spaces with underscores
            .substring(0, 50); // Limit length
    }

    /**
     * Close all database connections
     */
    async closeAll() {
        console.log('🔌 Closing all database connections...');
        
        // Close company databases
        for (const [companyId, dbService] of this.companyDatabases) {
            try {
                await dbService.close();
                console.log(`🔌 Closed company database: ${companyId}`);
            } catch (error) {
                console.error(`Error closing company database ${companyId}:`, error);
            }
        }
        this.companyDatabases.clear();

        // Close master database
        if (this.masterDb) {
            return new Promise((resolve) => {
                this.masterDb.close((err) => {
                    if (err) {
                        console.error('Error closing master database:', err);
                    } else {
                        console.log('🔌 Master database connection closed');
                    }
                    resolve();
                });
            });
        }
    }

    /**
     * Get master database users
     */
    async getUsers() {
        if (!this.isInitialized) {
            await this.init();
        }
        
        return await this.getAllMasterQuery(
            'SELECT id, username, role, created_at FROM users ORDER BY username'
        );
    }

    /**
     * Create new user
     */
    async createUser(userData) {
        if (!this.isInitialized) {
            await this.init();
        }
        
        const userId = `u${Date.now()}`;
        await this.runMasterQuery(
            'INSERT INTO users (id, username, password, role) VALUES (?, ?, ?, ?)',
            [userId, userData.username, userData.password, userData.role]
        );
        return userId;
    }

    /**
     * Update user
     */
    async updateUser(userId, userData) {
        if (!this.isInitialized) {
            await this.init();
        }
        
        await this.runMasterQuery(
            'UPDATE users SET username = ?, role = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [userData.username, userData.role, userId]
        );
    }

    /**
     * Authenticate user
     */
    async authenticateUser(username, password) {
        if (!this.isInitialized) {
            await this.init();
        }

        return await this.getMasterQuery(
            'SELECT * FROM users WHERE username = ? AND password = ?',
            [username, password]
        );
    }

    /**
     * Get default statutory rates for new companies
     */
    getDefaultStatutoryRates() {
        return [
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Buildings', assetSubGroup: 'Buildings (other than factory buildings) RCC Frame Structure', extraShiftDepreciation: 'No', usefulLifeYears: '60', scheduleIIClassification: 'Buildings - Freehold' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Buildings', assetSubGroup: 'Buildings (other than factory buildings) other than RCC Frame Structure', extraShiftDepreciation: 'No', usefulLifeYears: '30', scheduleIIClassification: 'Buildings - Freehold' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Buildings', assetSubGroup: 'Factory building', extraShiftDepreciation: 'No', usefulLifeYears: '30', scheduleIIClassification: 'Buildings - Freehold' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Buildings', assetSubGroup: 'Fences, wells, tube wells', extraShiftDepreciation: 'No', usefulLifeYears: '5', scheduleIIClassification: 'Buildings - Freehold' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Buildings', assetSubGroup: 'Others (including temporary structure, etc.)', extraShiftDepreciation: 'No', usefulLifeYears: '3', scheduleIIClassification: 'Buildings - Freehold' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Bridges, culverts, bunders, etc.', assetSubGroup: 'Bridges, culverts, bunders, etc.', extraShiftDepreciation: 'No', usefulLifeYears: '30', scheduleIIClassification: 'Buildings - Freehold' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Plant and Machinery', assetSubGroup: 'General plant and machinery', extraShiftDepreciation: 'Yes', usefulLifeYears: '15', scheduleIIClassification: 'Plant and machinery - General' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Plant and Machinery', assetSubGroup: 'Continuous process plant', extraShiftDepreciation: 'Yes', usefulLifeYears: '25', scheduleIIClassification: 'Plant and machinery - Continuous process' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Plant and Machinery', assetSubGroup: 'Dies, jigs, patterns, etc.', extraShiftDepreciation: 'No', usefulLifeYears: '8', scheduleIIClassification: 'Plant and machinery - Dies, jigs, patterns' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Furniture and fittings', assetSubGroup: 'Furniture and fittings', extraShiftDepreciation: 'No', usefulLifeYears: '10', scheduleIIClassification: 'Furniture and fittings' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Office equipment', assetSubGroup: 'Office equipment', extraShiftDepreciation: 'No', usefulLifeYears: '5', scheduleIIClassification: 'Office equipment' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Computer and data processing units', assetSubGroup: 'Computer including computer software', extraShiftDepreciation: 'No', usefulLifeYears: '3', scheduleIIClassification: 'Computer including computer software' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Computer and data processing units', assetSubGroup: 'Computer software', extraShiftDepreciation: 'No', usefulLifeYears: '3', scheduleIIClassification: 'Computer including computer software' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Motor car', assetSubGroup: 'Motor car', extraShiftDepreciation: 'No', usefulLifeYears: '8', scheduleIIClassification: 'Motor car' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Motor vehicles other than motor car', assetSubGroup: 'Motor vehicles other than motor car', extraShiftDepreciation: 'No', usefulLifeYears: '8', scheduleIIClassification: 'Motor vehicles other than motor car' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Ships', assetSubGroup: 'Ships', extraShiftDepreciation: 'No', usefulLifeYears: '20', scheduleIIClassification: 'Ships' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Aircraft', assetSubGroup: 'Aircraft', extraShiftDepreciation: 'No', usefulLifeYears: '20', scheduleIIClassification: 'Aircraft' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Railway sidings', assetSubGroup: 'Railway sidings', extraShiftDepreciation: 'No', usefulLifeYears: '15', scheduleIIClassification: 'Railway sidings' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Laboratory equipment', assetSubGroup: 'Laboratory equipment', extraShiftDepreciation: 'No', usefulLifeYears: '10', scheduleIIClassification: 'Laboratory equipment' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Medical equipment', assetSubGroup: 'Medical equipment', extraShiftDepreciation: 'No', usefulLifeYears: '13', scheduleIIClassification: 'Medical equipment' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Pollution control equipment', assetSubGroup: 'Pollution control equipment', extraShiftDepreciation: 'No', usefulLifeYears: '15', scheduleIIClassification: 'Pollution control equipment' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Fire fighting equipment', assetSubGroup: 'Fire fighting equipment', extraShiftDepreciation: 'No', usefulLifeYears: '15', scheduleIIClassification: 'Fire fighting equipment' },
            { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Electrical installations and equipments', assetSubGroup: 'Electrical installations and equipments', extraShiftDepreciation: 'No', usefulLifeYears: '10', scheduleIIClassification: 'Electrical installations and equipments' },
            { isStatutory: 'Yes', tangibility: 'Intangible', assetGroup: 'Patents', assetSubGroup: 'Patents', extraShiftDepreciation: 'No', usefulLifeYears: '8', scheduleIIClassification: 'Patents' },
            { isStatutory: 'Yes', tangibility: 'Intangible', assetGroup: 'Copyrights', assetSubGroup: 'Copyrights', extraShiftDepreciation: 'No', usefulLifeYears: '6', scheduleIIClassification: 'Copyrights' },
            { isStatutory: 'Yes', tangibility: 'Intangible', assetGroup: 'Trademarks', assetSubGroup: 'Trademarks', extraShiftDepreciation: 'No', usefulLifeYears: '5', scheduleIIClassification: 'Trademarks' },
            { isStatutory: 'Yes', tangibility: 'Intangible', assetGroup: 'Licenses and franchise', assetSubGroup: 'Licenses and franchise', extraShiftDepreciation: 'No', usefulLifeYears: '10', scheduleIIClassification: 'Licenses and franchise' }
        ];
    }
}

export default DatabaseManager;