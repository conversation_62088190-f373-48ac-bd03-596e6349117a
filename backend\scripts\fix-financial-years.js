#!/usr/bin/env node

/**
 * Fix Financial Years for Existing Companies
 * This script adds missing financial years and company_info to existing company databases
 */

import dbService from '../services/database-new.js';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function fixFinancialYears() {
    console.log('🔧 FIXING FINANCIAL YEARS FOR EXISTING COMPANIES');
    console.log('================================================\n');

    try {
        // Initialize database service
        await dbService.ensureInitialized();

        // Get all companies
        const companies = await dbService.getAllCompanies();
        console.log(`📊 Found ${companies.length} companies to fix\n`);

        for (const company of companies) {
            console.log(`🏢 Processing company: ${company.name} (${company.id})`);
            
            try {
                // Get company info from master database
                const companyInfo = await dbService.getCompanyInfo(company.id);
                console.log(`   📋 Company info loaded`);

                // Get company database service
                const companyDbService = await dbService.databaseManager.getCompanyDatabase(company.id);

                // Check if company_info exists in company database
                const existingCompanyInfo = await companyDbService.get(
                    'SELECT * FROM company_info WHERE id = ?', [company.id]);

                if (!existingCompanyInfo) {
                    console.log(`   ➕ Adding company_info to company database`);
                    await companyDbService.run(
                        `INSERT INTO company_info (
                            id, company_name, pan, cin, date_of_incorporation,
                            financial_year_start, financial_year_end, first_date_of_adoption,
                            address_line1, address_line2, city, pin,
                            email, mobile, contact_person, license_valid_upto,
                            created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                            company.id, companyInfo.companyName, companyInfo.pan, companyInfo.cin,
                            companyInfo.dateOfIncorporation, companyInfo.financialYearStart,
                            companyInfo.financialYearEnd, companyInfo.firstDateOfAdoption,
                            companyInfo.addressLine1, companyInfo.addressLine2,
                            companyInfo.city, companyInfo.pin, companyInfo.email, companyInfo.mobile,
                            companyInfo.contactPerson, companyInfo.licenseValidUpto,
                            new Date().toISOString(), new Date().toISOString()
                        ]
                    );
                } else {
                    console.log(`   ✅ Company info already exists in company database`);
                }

                // Check if financial years exist
                const existingYears = await companyDbService.all(
                    'SELECT year_range FROM financial_years ORDER BY year_range');

                if (existingYears.length === 0) {
                    console.log(`   ➕ Adding initial financial year`);

                    // Calculate initial financial year from company info
                    const fyStart = new Date(companyInfo.financialYearStart);
                    const fyEnd = new Date(companyInfo.financialYearEnd);
                    const initialFinancialYear = `${fyStart.getFullYear()}-${fyEnd.getFullYear()}`;

                    await companyDbService.run(
                        'INSERT INTO financial_years (year_range) VALUES (?)',
                        [initialFinancialYear]
                    );

                    console.log(`   ✅ Added financial year: ${initialFinancialYear}`);
                } else {
                    console.log(`   ✅ Financial years already exist: ${existingYears.map(y => y.year_range).join(', ')}`);
                }

                console.log(`   ✅ Company ${company.name} processed successfully\n`);

            } catch (error) {
                console.error(`   ❌ Error processing company ${company.name}:`, error.message);
                console.log('');
            }
        }

        console.log('🎉 FINANCIAL YEARS FIX COMPLETED');
        console.log('================================\n');

        // Test one company to verify the fix
        if (companies.length > 0) {
            console.log('🧪 TESTING FIX');
            console.log('==============');
            
            const testCompany = companies[0];
            console.log(`Testing company: ${testCompany.name} (${testCompany.id})`);
            
            try {
                const companyData = await dbService.getCompanyWithData(testCompany.id);
                console.log(`✅ Financial years found: ${companyData.financialYears.length}`);
                console.log(`   Years: ${companyData.financialYears.join(', ')}`);
                console.log(`✅ Company info present: ${companyData.info ? 'Yes' : 'No'}`);
                console.log(`✅ Assets count: ${companyData.assets.length}`);
                console.log(`✅ Statutory rates count: ${companyData.statutoryRates.length}`);
            } catch (error) {
                console.error(`❌ Test failed:`, error.message);
            }
        }

    } catch (error) {
        console.error('❌ Fatal error:', error);
        process.exit(1);
    }
}

// Run the fix
fixFinancialYears().then(() => {
    console.log('\n✅ Script completed successfully');
    process.exit(0);
}).catch(error => {
    console.error('\n❌ Script failed:', error);
    process.exit(1);
});
