import express from 'express';
import dbService from '../services/database.js';

const router = express.Router();

// Get user preferences
router.get('/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        
        const preferences = await dbService.getDatabaseManager().getMasterQuery(
            `SELECT 
                theme,
                language,
                timezone,
                date_format,
                number_format,
                updated_at
            FROM user_preferences WHERE user_id = ?`,
            [userId]
        );
        
        if (!preferences) {
            // Return default preferences if none exist
            return res.json({
                theme: 'dark',
                language: 'en',
                timezone: 'UTC',
                dateFormat: 'DD/MM/YYYY',
                numberFormat: 'en-IN'
            });
        }
        
        res.json({
            theme: preferences.theme,
            language: preferences.language,
            timezone: preferences.timezone,
            dateFormat: preferences.date_format,
            numberFormat: preferences.number_format,
            updatedAt: preferences.updated_at
        });
    } catch (error) {
        console.error('Error fetching user preferences:', error);
        res.status(500).json({ error: 'Failed to fetch user preferences' });
    }
});

// Update user preferences
router.put('/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const { theme, language, timezone, dateFormat, numberFormat } = req.body;
        
        // Check if user exists
        const user = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT id FROM users WHERE id = ?',
            [userId]
        );
        
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        // Insert or update preferences
        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT OR REPLACE INTO user_preferences (
                user_id, theme, language, timezone, date_format, number_format, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
            [userId, theme, language, timezone, dateFormat, numberFormat]
        );
        
        // Add audit log
        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [userId, req.user?.username || 'system', 'UPDATE_USER_PREFERENCES', 
             `Updated preferences: theme=${theme}, language=${language}, timezone=${timezone}`]
        );
        
        res.json({ message: 'User preferences updated successfully' });
    } catch (error) {
        console.error('Error updating user preferences:', error);
        res.status(500).json({ error: 'Failed to update user preferences' });
    }
});

// Update only theme preference (lightweight endpoint)
router.patch('/:userId/theme', async (req, res) => {
    try {
        const { userId } = req.params;
        const { theme } = req.body;
        
        if (!theme || !['light', 'dark'].includes(theme)) {
            return res.status(400).json({ error: 'Valid theme (light/dark) is required' });
        }
        
        // Check if user exists
        const user = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT id FROM users WHERE id = ?',
            [userId]
        );
        
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        // Update only theme preference
        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT OR REPLACE INTO user_preferences (
                user_id, 
                theme, 
                language, 
                timezone, 
                date_format, 
                number_format, 
                updated_at
            ) VALUES (
                ?, 
                ?, 
                COALESCE((SELECT language FROM user_preferences WHERE user_id = ?), 'en'),
                COALESCE((SELECT timezone FROM user_preferences WHERE user_id = ?), 'UTC'),
                COALESCE((SELECT date_format FROM user_preferences WHERE user_id = ?), 'DD/MM/YYYY'),
                COALESCE((SELECT number_format FROM user_preferences WHERE user_id = ?), 'en-IN'),
                CURRENT_TIMESTAMP
            )`,
            [userId, theme, userId, userId, userId, userId]
        );
        
        res.json({ message: 'Theme preference updated successfully' });
    } catch (error) {
        console.error('Error updating theme preference:', error);
        res.status(500).json({ error: 'Failed to update theme preference' });
    }
});

export default router;
