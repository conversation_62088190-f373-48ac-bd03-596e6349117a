@echo off
setlocal enabledelayedexpansion

echo =====================================
echo FAR SIGHTED APPLICATION RESTART v2.0
echo Professional Advisory Services - CA
echo Multi-Database Architecture
echo =====================================
echo.

echo 🔄 Stopping all running instances...
echo.

REM Kill Node.js processes
echo 📛 Killing Node.js backend processes...
taskkill /f /im node.exe 2>nul
if %errorlevel% == 0 (
    echo    ✅ Node.js processes terminated
) else (
    echo    ℹ️  No Node.js processes found
)

REM Kill npm processes
echo 📛 Killing npm processes...
taskkill /f /im npm.cmd 2>nul
taskkill /f /im npm 2>nul
if %errorlevel% == 0 (
    echo    ✅ npm processes terminated
) else (
    echo    ℹ️  No npm processes found
)

REM Kill nodemon processes
echo 📛 Killing nodemon processes...
taskkill /f /im nodemon 2>nul
if %errorlevel% == 0 (
    echo    ✅ nodemon processes terminated
) else (
    echo    ℹ️  No nodemon processes found
)

REM Kill processes on specific ports
echo 📛 Killing processes on application ports...

REM Frontend port 9090
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":9090" ^| find "LISTENING"') do (
    echo    Killing process on port 9090 (PID: %%a)
    taskkill /f /PID %%a 2>nul
)

REM Backend port 8090
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":8090" ^| find "LISTENING"') do (
    echo    Killing process on port 8090 (PID: %%a)
    taskkill /f /PID %%a 2>nul
)

REM Alternative common ports
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":5173" ^| find "LISTENING"') do (
    echo    Killing process on port 5173 (PID: %%a)
    taskkill /f /PID %%a 2>nul
)

for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":8080" ^| find "LISTENING"') do (
    echo    Killing process on port 8080 (PID: %%a)
    taskkill /f /PID %%a 2>nul
)

echo.
echo ⏳ Waiting 3 seconds for processes to fully terminate...
timeout /t 3 /nobreak >nul

echo.
echo 🚀 Starting FAR Sighted Application (Multi-Database)...
echo.

REM Navigate to project directory
cd /d "E:\Projects\FAR Sighted"

REM Verify we're in correct directory
if not exist "package.json" (
    echo ❌ Error: package.json not found in current directory
    echo    Current directory: %cd%
    echo    Please ensure you're in the correct project directory
    pause
    exit /b 1
)

REM Check if backend directory exists
if not exist "backend\" (
    echo ❌ Error: backend directory not found
    echo    Current directory: %cd%
    pause
    exit /b 1
)

REM Start backend first
echo 📡 Starting backend server (Multi-Database)...
cd backend

if exist "package.json" (
    echo    ✅ Backend directory found
    echo    📊 Checking migration status...
    
    REM Check if migration is needed
    timeout /t 2 /nobreak >nul
    
    echo    🚀 Starting backend server...
    start "FAR Backend v2.0" cmd /k "echo FAR Sighted Backend v2.0 - Multi-Database && echo. && npm start"
    echo    ✅ Backend server starting on port 8090...
    
    REM Wait for backend to initialize
    echo    ⏳ Waiting for backend to initialize...
    timeout /t 5 /nobreak >nul
    
) else (
    echo    ❌ No backend package.json found
    pause
    exit /b 1
)

REM Go back to root and start frontend
cd ..
echo.
echo 🖥️  Starting frontend application...

REM Install frontend dependencies if needed
if not exist "node_modules\" (
    echo    📦 Installing frontend dependencies...
    call npm install --silent
)

echo    🎨 Starting Vite development server on port 9090...
start "FAR Frontend v2.0" cmd /k "echo FAR Sighted Frontend v2.0 && echo. && npm run dev"

echo.
echo ✅ FAR Sighted Application v2.0 Restart Complete!
echo.
echo 📊 Application Status:
echo    • Backend:  http://localhost:8090/api (Multi-Database)
echo    • Frontend: http://localhost:9090 (Vite Development Server)
echo    • Health:   http://localhost:8090/api/health
echo    • Status:   http://localhost:8090/api/migration-status
echo    • System:   http://localhost:8090/api/system-info
echo.
echo 🏗️  Database Architecture:
echo    • Structure: Multi-Database (Separate DB per company)
echo    • Master DB: backend/database/master.db
echo    • Companies: backend/database/companies/*/company.db
echo.

REM Wait a moment for servers to start
timeout /t 8 /nobreak >nul

REM Check if backend is responding
echo 🔍 Checking backend health...
curl -s http://localhost:3001/api/health >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Backend is responding
) else (
    echo    ⚠️  Backend may still be starting...
)

echo.
echo 🌐 Attempting to open application in browser...
start http://localhost:9090

echo.
echo 🎯 Quick Commands:
echo    • Backend Health: curl http://localhost:3001/api/health
echo    • Migration Status: curl http://localhost:3001/api/migration-status  
echo    • System Info: curl http://localhost:3001/api/system-info
echo    • Run Migration: cd backend && npm run migrate
echo.
echo 💡 Migration Info:
echo    If this is first run with new structure, migration may be required.
echo    Check migration status at: http://localhost:3001/api/migration-status
echo    Run migration with: cd backend && npm run migrate
echo.
echo 🔧 Development Features:
echo    • Separate database per company
echo    • Enhanced data isolation
echo    • Company-specific backups
echo    • Improved scalability
echo    • Granular audit trails
echo.
echo ✨ FAR Sighted v2.0 is now running with Multi-Database Architecture!
echo    Press any key to close this window...

pause >nul