@echo off
echo =====================================
echo FAR SIGHTED APPLICATION RESTART
echo Professional Advisory Services - CA
echo =====================================
echo.

echo 🔄 Stopping all running instances...
echo.

REM Kill Node.js processes
echo 📛 Killing Node.js backend processes...
taskkill /f /im node.exe 2>nul
if %errorlevel% == 0 (
    echo    ✅ Node.js processes terminated
) else (
    echo    ℹ️  No Node.js processes found
)

REM Kill npm processes
echo 📛 Killing npm processes...
taskkill /f /im npm.cmd 2>nul
if %errorlevel% == 0 (
    echo    ✅ npm processes terminated
) else (
    echo    ℹ️  No npm processes found
)

REM Kill any processes using common ports
echo 📛 Killing processes on common ports...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3000" ^| find "LISTENING"') do (
    echo    Killing process on port 3000 (PID: %%a)
    taskkill /f /PID %%a 2>nul
)

for /f "tokens=5" %%a in ('netstat -aon ^| find ":5000" ^| find "LISTENING"') do (
    echo    Killing process on port 5000 (PID: %%a)
    taskkill /f /PID %%a 2>nul
)

for /f "tokens=5" %%a in ('netstat -aon ^| find ":8080" ^| find "LISTENING"') do (
    echo    Killing process on port 8080 (PID: %%a)
    taskkill /f /PID %%a 2>nul
)

echo.
echo ⏳ Waiting 3 seconds for processes to fully terminate...
timeout /t 3 /nobreak >nul

echo.
echo 🚀 Starting FAR Sighted Application...
echo.

REM Navigate to project directory
cd /d "E:\Projects\FAR Sighted"

REM Check if package.json exists
if not exist "package.json" (
    echo ❌ Error: package.json not found in current directory
    echo    Current directory: %cd%
    echo    Please ensure you're in the correct project directory
    pause
    exit /b 1
)

REM Start backend first
echo 📡 Starting backend server...
cd backend
if exist "package.json" (
    echo    Backend directory found, starting server...
    start "FAR Backend" cmd /k "npm start"
    echo    ✅ Backend server starting in separate window...
) else (
    echo    ⚠️  No backend package.json found, skipping backend startup
)

REM Go back to root and start frontend
cd ..
echo.
echo 🖥️  Starting frontend application...
echo    Installing dependencies if needed...
call npm install --silent

echo    Starting development server...
start "FAR Frontend" cmd /k "npm run dev"

echo.
echo ✅ FAR Sighted Application Restart Complete!
echo.
echo 📊 Application Status:
echo    • Backend: http://localhost:5000 (if backend exists)
echo    • Frontend: http://localhost:3000 (typical Vite/React port)
echo    • Database: SQLite (backend/database/far_sighted.db)
echo.
echo 🔗 Application should open automatically in your browser
echo    If not, manually navigate to: http://localhost:3000
echo.
echo 💡 To test the Opening WDV fix:
echo    1. Login to Tech Innovations Pvt Ltd
echo    2. Go to Reports → Schedule III
echo    3. Select FY 2024-2025
echo    4. Verify Opening WDV shows calculated values (not gross amounts)
echo.
echo 📋 Enhanced test data includes:
echo    • HIST001: Historical asset
echo    • MULTI001: Multi-year asset  
echo    • FULLY001: Fully depreciated asset
echo    • NEW001: Current year addition
echo    • And 6 more test scenarios
echo.

REM Wait a moment then try to open browser
timeout /t 5 /nobreak >nul
echo 🌐 Attempting to open application in browser...
start http://localhost:3000

echo.
echo ✨ FAR Sighted is now running with corrected Opening WDV calculations!
echo    Press any key to close this window...
pause >nul
