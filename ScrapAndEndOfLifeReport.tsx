/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo, FC } from 'react';
import { api } from './lib/api';
import { DataViewProps, ScrapReportRow } from './lib/types';
import { sortData, exportToExcel } from './lib/utils';
import { DownloadIcon } from './Icons';
import { Highlight } from './pages/PlaceholderViews';

export const ScrapAndEndOfLifeReport: FC<DataViewProps> = ({ companyId, companyName, year, showAlert }) => {
    const [allProcessedAssets, setAllProcessedAssets] = useState<ScrapReportRow[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [filterOption, setFilterOption] = useState<'all' | 'scrappable' | 'endOfLife'>('all');
    const [sortConfig, setSortConfig] = useState<{ key: keyof ScrapReportRow; direction: 'asc' | 'desc' } | null>({ key: 'recordId', direction: 'asc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);
    const [filterText, setFilterText] = useState('');

    useEffect(() => {
        const fetchData = async () => {
            if (!companyId || !year) {
                setAllProcessedAssets([]);
                setLoading(false);
                return;
            }
            setLoading(true);
            setError(null);
            setSelectedRowId(null);
            setSelectedColumnKey(null);
            setFilterText('');

            try {
                const assets = await api.getAssets(companyId);
                const [, endYearStr] = year.split('-');
                const fyEnd = new Date(`${endYearStr}-03-31T23:59:59.999Z`);

                const processedData = assets.map(asset => {
                    const putToUseDate = new Date(asset.putToUseDate);
                    let endOfLifeDate: Date | null = null;
                    if (asset.lifeInYears > 0) {
                        endOfLifeDate = new Date(putToUseDate.getTime());
                        endOfLifeDate.setFullYear(endOfLifeDate.getFullYear() + asset.lifeInYears);
                    }

                    // Skip disposed assets - they are handled in Asset Deletions Report
                    const isDisposed = !!asset.disposalDate;
                    if (isDisposed) return null;

                    const isScrappable = asset.scrapIt === true;
                    const isEndOfLife = endOfLifeDate ? endOfLifeDate <= fyEnd : false;

                    const statuses = [];
                    if (isScrappable) statuses.push('Scrappable');
                    if (isEndOfLife) statuses.push('End of Life');

                    const uniqueStatuses = [...new Set(statuses)];
                    const status = uniqueStatuses.join(', ');
                    
                    if (status) {
                         return {
                            recordId: asset.recordId,
                            assetParticulars: asset.assetParticulars,
                            assetGroup: asset.assetGroup,
                            assetSubGroup: asset.assetSubGroup,
                            putToUseDate: asset.putToUseDate,
                            lifeInYears: asset.lifeInYears,
                            endOfLifeDate: endOfLifeDate ? endOfLifeDate.toISOString().split('T')[0] : 'N/A',
                            status,
                        };
                    }
                    return null;
                }).filter((asset): asset is ScrapReportRow => asset !== null);

                setAllProcessedAssets(processedData);

            } catch (err) {
                setError('Failed to fetch and process asset data.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [companyId, year]);

    const filteredByStatusData = useMemo(() => {
        if (filterOption === 'all') {
            return allProcessedAssets;
        }
        const keyword = {
            scrappable: 'Scrappable',
            endOfLife: 'End of Life',
        }[filterOption];
    
        return allProcessedAssets.filter(a => a.status.includes(keyword));
    }, [allProcessedAssets, filterOption]);
    
    const filteredByTextData = useMemo(() => {
        if (!filterText) return filteredByStatusData;
        const searchTerm = filterText.trim().toLowerCase();
        const searchableFields: (keyof ScrapReportRow)[] = ['recordId', 'assetParticulars', 'assetGroup', 'assetSubGroup', 'status'];
        return filteredByStatusData.filter(row =>
            searchableFields.some(field =>
                String(row[field] ?? '').toLowerCase().includes(searchTerm)
            )
        );
    }, [filteredByStatusData, filterText]);
    
    const sortedData = useMemo(() => {
        return sortData(filteredByTextData, sortConfig);
    }, [filteredByTextData, sortConfig]);

    const requestSort = (key: keyof ScrapReportRow) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: keyof ScrapReportRow) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };
    
    const getHeaderClass = (key: keyof ScrapReportRow) => {
        const classes = ['sortable'];
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        return classes.join(' ');
    };
    
    const getColumnClass = (key: keyof ScrapReportRow) => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if (['assetParticulars', 'assetGroup', 'assetSubGroup'].includes(key)) classes.push('td-wrap');
        return classes.join(' ');
    };

    const handleExport = () => {
        if (!companyId || !companyName || !year) return;
        
        const dataToExport = sortedData.map(row => ({
            "Record ID": row.recordId,
            "Asset Particulars": row.assetParticulars,
            "Asset Group": row.assetGroup,
            "Asset Sub-Group": row.assetSubGroup,
            "Put to Use Date": row.putToUseDate,
            "Life (Yrs)": row.lifeInYears,
            "End of Life Date": row.endOfLifeDate,
            "Status": row.status,
        }));
        
        if (!exportToExcel({ data: dataToExport, companyName, year, reportName: `Scrap_EOL_Report_${filterOption}` })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };
    
    if (loading) return <div className="loading-indicator">Generating Report...</div>;
    if (error) return <div className="error-message">{error}</div>;
    if (!companyId) return null;

    return (
        <>
            <div className="view-header">
                <h2>Scrap, Disposal & End of Life Report</h2>
                <div className="actions">
                    <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export to Excel</button>
                </div>
            </div>
            <div className="filter-container">
                <div className="report-filter-group">
                    <label>Show:</label>
                    <div className="radio-group">
                        <input type="radio" id="filterAll" name="statusFilter" value="all" checked={filterOption === 'all'} onChange={() => setFilterOption('all')} />
                        <label htmlFor="filterAll">All</label>

                        <input type="radio" id="filterScrappable" name="statusFilter" value="scrappable" checked={filterOption === 'scrappable'} onChange={() => setFilterOption('scrappable')} />
                        <label htmlFor="filterScrappable">Scrappable</label>

                        <input type="radio" id="filterEndOfLife" name="statusFilter" value="endOfLife" checked={filterOption === 'endOfLife'} onChange={() => setFilterOption('endOfLife')} />
                        <label htmlFor="filterEndOfLife">End of Life</label>
                    </div>
                </div>
                 <div className="filter-input-wrapper">
                    <input
                        type="text"
                        placeholder="Filter results..."
                        className="filter-input"
                        value={filterText}
                        onChange={e => setFilterText(e.target.value)}
                        aria-label="Filter scrap and end of life assets"
                    />
                    <button
                        className={`filter-clear-btn ${!filterText ? 'hidden' : ''}`}
                        onClick={() => setFilterText('')}
                        aria-label="Clear filter"
                        title="Clear filter"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </div>
            </div>
            <div className="table-container">
                <table>
                    <thead>
                        <tr>
                            <th className={getHeaderClass('recordId')} onClick={() => requestSort('recordId')}>Record ID{getSortIndicator('recordId')}</th>
                            <th className={getHeaderClass('assetParticulars')} onClick={() => requestSort('assetParticulars')}>Asset Particulars{getSortIndicator('assetParticulars')}</th>
                            <th className={getHeaderClass('assetGroup')} onClick={() => requestSort('assetGroup')}>Asset Group{getSortIndicator('assetGroup')}</th>
                            <th className={getHeaderClass('assetSubGroup')} onClick={() => requestSort('assetSubGroup')}>Asset Sub-Group{getSortIndicator('assetSubGroup')}</th>
                            <th className={getHeaderClass('putToUseDate')} onClick={() => requestSort('putToUseDate')}>Put to Use Date{getSortIndicator('putToUseDate')}</th>
                            <th className={getHeaderClass('lifeInYears')} onClick={() => requestSort('lifeInYears')}>Life (Yrs){getSortIndicator('lifeInYears')}</th>
                            <th className={getHeaderClass('endOfLifeDate')} onClick={() => requestSort('endOfLifeDate')}>End of Life Date{getSortIndicator('endOfLifeDate')}</th>
                            <th className={getHeaderClass('status')} onClick={() => requestSort('status')}>Status{getSortIndicator('status')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {sortedData.length > 0 ? sortedData.map(row => (
                            <tr key={row.recordId} onClick={() => setSelectedRowId(row.recordId)} className={row.recordId === selectedRowId ? 'selected-row' : ''}>
                                <td className={getColumnClass('recordId')}><Highlight text={row.recordId} highlight={filterText} /></td>
                                <td className={getColumnClass('assetParticulars')}><Highlight text={row.assetParticulars} highlight={filterText} /></td>
                                <td className={getColumnClass('assetGroup')}><Highlight text={row.assetGroup} highlight={filterText} /></td>
                                <td className={getColumnClass('assetSubGroup')}><Highlight text={row.assetSubGroup} highlight={filterText} /></td>
                                <td className={getColumnClass('putToUseDate')}>{new Date(row.putToUseDate).toLocaleDateString()}</td>
                                <td className={`${getColumnClass('lifeInYears')} text-right`}>{row.lifeInYears}</td>
                                <td className={getColumnClass('endOfLifeDate')}>{row.endOfLifeDate !== 'N/A' ? new Date(row.endOfLifeDate).toLocaleDateString() : 'N/A'}</td>
                                <td className={getColumnClass('status')}><Highlight text={row.status} highlight={filterText} /></td>
                            </tr>
                        )) : (
                           <tr><td colSpan={8} style={{textAlign: 'center', padding: '2rem'}}>No assets match the current filter criteria.</td></tr>
                        )}
                    </tbody>
                </table>
            </div>
        </>
    );
};
