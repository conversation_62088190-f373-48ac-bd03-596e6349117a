/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect } from 'react';
import type { StatutoryRate } from '../lib/db-server';
import { SaveIcon, XIcon } from '../Icons';

interface AssetClassificationModalProps {
    isOpen: boolean;
    isEdit: boolean;
    rate: StatutoryRate | null;
    onSave: (rate: Omit<StatutoryRate, 'isStatutory'>) => void;
    onCancel: () => void;
}

export function AssetClassificationModal({ isOpen, isEdit, rate, onSave, onCancel }: AssetClassificationModalProps) {
    const [formData, setFormData] = useState({
        tangibility: 'Tangible',
        assetGroup: '',
        assetSubGroup: '',
        extraShiftDepreciation: 'No',
        usefulLifeYears: '',
        scheduleIIClassification: ''
    });

    const [errors, setErrors] = useState<Record<string, string>>({});

    useEffect(() => {
        if (isEdit && rate) {
            setFormData({
                tangibility: rate.tangibility,
                assetGroup: rate.assetGroup,
                assetSubGroup: rate.assetSubGroup,
                extraShiftDepreciation: rate.extraShiftDepreciation,
                usefulLifeYears: rate.usefulLifeYears,
                scheduleIIClassification: rate.scheduleIIClassification
            });
        } else {
            setFormData({
                tangibility: 'Tangible',
                assetGroup: '',
                assetSubGroup: '',
                extraShiftDepreciation: 'No',
                usefulLifeYears: '',
                scheduleIIClassification: ''
            });
        }
        setErrors({});
    }, [isEdit, rate, isOpen]);

    const validateForm = () => {
        const newErrors: Record<string, string> = {};

        if (!formData.assetGroup.trim()) {
            newErrors.assetGroup = 'Asset Group is required';
        }
        if (!formData.assetSubGroup.trim()) {
            newErrors.assetSubGroup = 'Asset Sub-Group is required';
        }
        if (!formData.usefulLifeYears.trim()) {
            newErrors.usefulLifeYears = 'Useful Life Years is required';
        } else {
            const years = parseInt(formData.usefulLifeYears);
            if (isNaN(years) || years <= 0 || years > 100) {
                newErrors.usefulLifeYears = 'Useful Life Years must be a number between 1 and 100';
            }
        }
        if (!formData.scheduleIIClassification.trim()) {
            newErrors.scheduleIIClassification = 'Schedule III Classification is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (validateForm()) {
            onSave(formData);
        }
    };

    const handleInputChange = (field: string, value: string) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
    };

    if (!isOpen) return null;

    return (
        <div className="modal-overlay">
            <div className="modal-content large-modal">
                <div className="modal-header">
                    <h3>{isEdit ? 'Edit Asset Classification' : 'Add New Asset Classification'}</h3>
                    <button type="button" className="modal-close" onClick={onCancel}>
                        <XIcon />
                    </button>
                </div>

                <form onSubmit={handleSubmit} className="modal-body">
                    <div className="form-grid">
                        <div className="form-group">
                            <label htmlFor="tangibility">
                                Tangibility <span className="required-asterisk">*</span>
                            </label>
                            <select
                                id="tangibility"
                                value={formData.tangibility}
                                onChange={(e) => handleInputChange('tangibility', e.target.value)}
                                className={errors.tangibility ? 'error' : ''}
                            >
                                <option value="Tangible">Tangible</option>
                                <option value="Intangible">Intangible</option>
                            </select>
                            {errors.tangibility && <span className="error-message">{errors.tangibility}</span>}
                        </div>

                        <div className="form-group">
                            <label htmlFor="assetGroup">
                                Asset Group <span className="required-asterisk">*</span>
                            </label>
                            <input
                                type="text"
                                id="assetGroup"
                                value={formData.assetGroup}
                                onChange={(e) => handleInputChange('assetGroup', e.target.value)}
                                className={errors.assetGroup ? 'error' : ''}
                                placeholder="Enter asset group"
                            />
                            {errors.assetGroup && <span className="error-message">{errors.assetGroup}</span>}
                        </div>

                        <div className="form-group">
                            <label htmlFor="assetSubGroup">
                                Asset Sub-Group <span className="required-asterisk">*</span>
                            </label>
                            <input
                                type="text"
                                id="assetSubGroup"
                                value={formData.assetSubGroup}
                                onChange={(e) => handleInputChange('assetSubGroup', e.target.value)}
                                className={errors.assetSubGroup ? 'error' : ''}
                                placeholder="Enter asset sub-group"
                            />
                            {errors.assetSubGroup && <span className="error-message">{errors.assetSubGroup}</span>}
                        </div>

                        <div className="form-group">
                            <label htmlFor="usefulLifeYears">
                                Useful Life Years <span className="required-asterisk">*</span>
                            </label>
                            <input
                                type="number"
                                id="usefulLifeYears"
                                value={formData.usefulLifeYears}
                                onChange={(e) => handleInputChange('usefulLifeYears', e.target.value)}
                                className={errors.usefulLifeYears ? 'error' : ''}
                                placeholder="Enter useful life in years"
                                min="1"
                                max="100"
                            />
                            {errors.usefulLifeYears && <span className="error-message">{errors.usefulLifeYears}</span>}
                        </div>

                        <div className="form-group">
                            <label htmlFor="extraShiftDepreciation">
                                Extra Shift Depreciation <span className="required-asterisk">*</span>
                            </label>
                            <select
                                id="extraShiftDepreciation"
                                value={formData.extraShiftDepreciation}
                                onChange={(e) => handleInputChange('extraShiftDepreciation', e.target.value)}
                                className={errors.extraShiftDepreciation ? 'error' : ''}
                            >
                                <option value="No">No</option>
                                <option value="Yes">Yes</option>
                            </select>
                            {errors.extraShiftDepreciation && <span className="error-message">{errors.extraShiftDepreciation}</span>}
                        </div>

                        <div className="form-group">
                            <label htmlFor="scheduleIIClassification">
                                Schedule III Classification <span className="required-asterisk">*</span>
                            </label>
                            <input
                                type="text"
                                id="scheduleIIClassification"
                                value={formData.scheduleIIClassification}
                                onChange={(e) => handleInputChange('scheduleIIClassification', e.target.value)}
                                className={errors.scheduleIIClassification ? 'error' : ''}
                                placeholder="Enter Schedule III classification"
                            />
                            {errors.scheduleIIClassification && <span className="error-message">{errors.scheduleIIClassification}</span>}
                        </div>
                    </div>

                    <div className="modal-actions">
                        <button type="button" className="btn btn-secondary" onClick={onCancel}>
                            <XIcon /> Cancel
                        </button>
                        <button type="submit" className="btn btn-primary">
                            <SaveIcon /> {isEdit ? 'Update' : 'Add'} Classification
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}
