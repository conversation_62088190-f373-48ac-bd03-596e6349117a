/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo } from 'react';
import type { StatutoryRate } from '../lib/db-server';
import { SaveIcon, XIcon } from '../Icons';

interface AssetClassificationModalProps {
    isOpen: boolean;
    isEdit: boolean;
    rate: StatutoryRate | null;
    existingRates: StatutoryRate[];
    onSave: (rate: Omit<StatutoryRate, 'isStatutory'>) => void;
    onCancel: () => void;
    companyId: string | null;
}

export function AssetClassificationModal({ isOpen, isEdit, rate, existingRates, onSave, onCancel, companyId }: AssetClassificationModalProps) {
    const [formData, setFormData] = useState({
        tangibility: 'Tangible',
        assetGroup: '',
        assetSubGroup: '',
        extraShiftDepreciation: 'No',
        usefulLifeYears: '',
        scheduleIIClassification: '',
        isLeasehold: false,
        leasePeriod: ''
    });

    const [errors, setErrors] = useState<Record<string, string>>({});
    const [filteredScheduleIIIOptions, setFilteredScheduleIIIOptions] = useState<string[]>([]);

    // Get unique Schedule III classifications from existing rates, re-calculating when existingRates changes
    const scheduleIIIOptions = useMemo(() => {
        return [...new Set(existingRates.map(rate => rate.scheduleIIClassification))].sort();
    }, [existingRates]);

    useEffect(() => {
        if (isEdit && rate) {
            setFormData({
                tangibility: rate.tangibility,
                assetGroup: rate.assetGroup,
                assetSubGroup: rate.assetSubGroup,
                extraShiftDepreciation: rate.extraShiftDepreciation,
                usefulLifeYears: rate.usefulLifeYears,
                scheduleIIClassification: rate.scheduleIIClassification,
                isLeasehold: false, // Default for existing rates
                leasePeriod: ''
            });
        } else {
            setFormData({
                tangibility: 'Tangible',
                assetGroup: '',
                assetSubGroup: '',
                extraShiftDepreciation: 'No',
                usefulLifeYears: '',
                scheduleIIClassification: '',
                isLeasehold: false,
                leasePeriod: ''
            });
        }
        setErrors({});
        setFilteredScheduleIIIOptions(scheduleIIIOptions);
    }, [isEdit, rate, isOpen, scheduleIIIOptions]);

    const validateForm = () => {
        const newErrors: Record<string, string> = {};

        if (!formData.assetGroup.trim()) {
            newErrors.assetGroup = 'Asset Group is required';
        }
        if (!formData.assetSubGroup.trim()) {
            newErrors.assetSubGroup = 'Asset Sub-Group is required';
        }
        if (!formData.isLeasehold && !formData.usefulLifeYears.trim()) {
            newErrors.usefulLifeYears = 'Useful Life Years is required';
        } else if (!formData.isLeasehold) {
            const years = parseInt(formData.usefulLifeYears);
            if (isNaN(years) || years <= 0 || years > 100) {
                newErrors.usefulLifeYears = 'Useful Life Years must be a number between 1 and 100';
            }
        }
        if (!formData.scheduleIIClassification.trim()) {
            newErrors.scheduleIIClassification = 'Schedule III Classification is required';
        }

        // Validate lease period if leasehold is selected
        if (formData.isLeasehold) {
            if (!formData.leasePeriod.trim()) {
                newErrors.leasePeriod = 'Lease Period is required for leasehold assets';
            } else {
                const period = parseInt(formData.leasePeriod);
                if (isNaN(period) || period <= 0 || period > 99) {
                    newErrors.leasePeriod = 'Lease Period must be a number between 1 and 99 years';
                }
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (validateForm()) {
            onSave(formData);
        }
    };

    const handleInputChange = (field: string, value: string | boolean) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }

        if (field === 'scheduleIIClassification' && typeof value === 'string') {
            const filtered = scheduleIIIOptions.filter(option =>
                option.toLowerCase().includes(value.toLowerCase())
            );
            setFilteredScheduleIIIOptions(filtered);
        }

        // If leasehold is checked, disable and clear useful life
        if (field === 'isLeasehold') {
            if (value) {
                setFormData(prev => ({ ...prev, usefulLifeYears: '' }));
                if (errors.usefulLifeYears) {
                    setErrors(prev => ({ ...prev, usefulLifeYears: '' }));
                }
            } else {
                // Clear lease period if leasehold is unchecked
                setFormData(prev => ({ ...prev, leasePeriod: '' }));
                if (errors.leasePeriod) {
                    setErrors(prev => ({ ...prev, leasePeriod: '' }));
                }
            }
        }
    };

    if (!isOpen) return null;

    return (
        <div className="modal-overlay">
            <div className="modal-content large-modal">
                <div className="modal-header">
                    <h3>{isEdit ? 'Edit Asset Classification' : 'Add New Asset Classification'}</h3>
                    <button type="button" className="modal-close" onClick={onCancel}>
                        <XIcon />
                    </button>
                </div>

                <form onSubmit={handleSubmit} className="modal-body">
                    <div className="form-grid">
                        <div className="form-group">
                            <label htmlFor="tangibility">
                                Tangibility <span className="required-asterisk">*</span>
                            </label>
                            <select
                                id="tangibility"
                                value={formData.tangibility}
                                onChange={(e) => handleInputChange('tangibility', e.target.value)}
                                className={errors.tangibility ? 'error' : ''}
                            >
                                <option value="Tangible">Tangible</option>
                                <option value="Intangible">Intangible</option>
                            </select>
                            {errors.tangibility && <span className="error-message">{errors.tangibility}</span>}
                        </div>

                        <div className="form-group">
                            <label htmlFor="assetGroup">
                                Asset Group <span className="required-asterisk">*</span>
                            </label>
                            <input
                                type="text"
                                id="assetGroup"
                                value={formData.assetGroup}
                                onChange={(e) => handleInputChange('assetGroup', e.target.value)}
                                className={errors.assetGroup ? 'error' : ''}
                                placeholder="Enter asset group"
                            />
                            {errors.assetGroup && <span className="error-message">{errors.assetGroup}</span>}
                        </div>

                        <div className="form-group">
                            <label htmlFor="assetSubGroup">
                                Asset Sub-Group <span className="required-asterisk">*</span>
                            </label>
                            <input
                                type="text"
                                id="assetSubGroup"
                                value={formData.assetSubGroup}
                                onChange={(e) => handleInputChange('assetSubGroup', e.target.value)}
                                className={errors.assetSubGroup ? 'error' : ''}
                                placeholder="Enter asset sub-group"
                            />
                            {errors.assetSubGroup && <span className="error-message">{errors.assetSubGroup}</span>}
                        </div>

                        <div className="form-group">
                            <label htmlFor="usefulLifeYears">
                                Useful Life Years <span className="required-asterisk">*</span>
                            </label>
                            <input
                                    type="number"
                                    id="usefulLifeYears"
                                    value={formData.usefulLifeYears}
                                    onChange={(e) => handleInputChange('usefulLifeYears', e.target.value)}
                                    className={errors.usefulLifeYears ? 'error' : ''}
                                    placeholder="Enter useful life in years"
                                    min="1"
                                    max="100"
                                    disabled={formData.isLeasehold}
                                />
                            {errors.usefulLifeYears && <span className="error-message">{errors.usefulLifeYears}</span>}
                        </div>

                        <div className="form-group">
                            <label htmlFor="scheduleIIClassification">
                                Schedule III Classification <span className="required-asterisk">*</span>
                            </label>
                            <input
                                type="text"
                                id="scheduleIIClassification"
                                value={formData.scheduleIIClassification}
                                onChange={(e) => handleInputChange('scheduleIIClassification', e.target.value)}
                                className={errors.scheduleIIClassification ? 'error' : ''}
                                list="scheduleIIIOptions"
                                placeholder="Select or type a classification"
                            />
                            <datalist id="scheduleIIIOptions">
                                {filteredScheduleIIIOptions.map(option => (
                                    <option key={option} value={option} />
                                ))}
                            </datalist>
                            {errors.scheduleIIClassification && <span className="error-message">{errors.scheduleIIClassification}</span>}
                        </div>

                        <div className="form-group">
                            <label htmlFor="isLeasehold">
                                Is Leasehold Asset
                            </label>
                            <div className="checkbox-wrapper">
                                <input
                                    type="checkbox"
                                    id="isLeasehold"
                                    checked={formData.isLeasehold}
                                    onChange={(e) => handleInputChange('isLeasehold', e.target.checked)}
                                />
                                <label htmlFor="isLeasehold">This asset type can be leasehold</label>
                            </div>
                        </div>

                        {formData.isLeasehold && (
                            <div className="form-group">
                                <label htmlFor="leasePeriod">
                                    Default Lease Period (Years) <span className="required-asterisk">*</span>
                                </label>
                                <input
                                    type="number"
                                    id="leasePeriod"
                                    value={formData.leasePeriod}
                                    onChange={(e) => handleInputChange('leasePeriod', e.target.value)}
                                    className={errors.leasePeriod ? 'error' : ''}
                                    placeholder="Enter lease period in years"
                                    min="1"
                                    max="99"
                                />
                                {errors.leasePeriod && <span className="error-message">{errors.leasePeriod}</span>}
                            </div>
                        )}
                    </div>

                    <div className="modal-actions">
                        <button type="button" className="btn btn-secondary" onClick={onCancel}>
                            <XIcon /> Cancel
                        </button>
                        <button type="submit" className="btn btn-primary">
                            <SaveIcon /> {isEdit ? 'Update' : 'Add'} Classification
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}
