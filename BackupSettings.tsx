/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { api } from './lib/api';
import type { BackupLogEntry } from './lib/db-server';
import { DataViewProps } from './lib/types';

export function BackupSettings({ showAlert, showConfirmation, loggedInUser }: DataViewProps) {
    const [logs, setLogs] = useState<BackupLogEntry[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const fetchLogs = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const logData = await api.getBackupLogs();
            setLogs(logData);
        } catch (err) {
            setError("Failed to fetch backup history.");
            console.error(err);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchLogs();
    }, [fetchLogs]);

    const handleCreateBackup = async () => {
        try {
            const dataStr = await api.exportDatabase();
            const blob = new Blob([dataStr], { type: "application/json" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            link.download = `far_system_backup_${timestamp}.json`;
            link.href = url;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            
            await api.addBackupLog({ action: 'Backup', initiatedBy: 'Manual', details: 'Success' });
            if (loggedInUser) {
                await api.addAuditLog({ userId: loggedInUser.id, username: loggedInUser.username, action: 'MANUAL_BACKUP', details: 'Created manual system backup.' });
            }
            fetchLogs(); // Refresh logs
        } catch (err) {
            showAlert("Backup Failed", "Failed to create backup.", 'error');
            console.error(err);
            await api.addBackupLog({ action: 'Backup', initiatedBy: 'Manual', details: `Failed: ${err instanceof Error ? err.message : 'Unknown error'}` });
            fetchLogs(); // Refresh logs
        }
    };
    
    const handleRestoreClick = () => {
        fileInputRef.current?.click();
    };

    const handleFileSelected = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = async (e) => {
            const content = e.target?.result as string;
            
            showConfirmation(
                'Confirm Restore',
                'WARNING: This will overwrite ALL current data. An automatic backup of the current data will be taken before proceeding. This action cannot be undone.',
                async () => {
                    try {
                        await api.createAutoBackup();
                        await api.importDatabase(content);
                        await api.addBackupLog({ action: 'Restore', initiatedBy: 'Manual', details: `Success from file: ${file.name}` });
                        if(loggedInUser) {
                            await api.addAuditLog({ userId: loggedInUser.id, username: loggedInUser.username, action: 'MANUAL_RESTORE', details: `Restored system from manual backup file: ${file.name}.` });
                        }
                        showAlert("Success", "Restore successful. The application will now reload.", 'success');
                        setTimeout(() => window.location.reload(), 2000);
                    } catch(err) {
                        showAlert("Restore Failed", `Restore failed: ${err instanceof Error ? err.message : 'Unknown error'}`, 'error');
                        console.error(err);
                         await api.addBackupLog({ action: 'Restore', initiatedBy: 'Manual', details: `Failed: ${err instanceof Error ? err.message : 'Unknown error'}`});
                        fetchLogs();
                    }
                }
            );
        };
        reader.readAsText(file);
        event.target.value = ''; // Reset input
    };

    return (
        <>
            <div className="view-header">
                <h2>Backup & Restore</h2>
            </div>
            <div className="settings-container" style={{ display: 'flex', flexDirection: 'column', gap: '2.5rem' }}>
                
                {/* Manual Backup */}
                <section>
                    <h3 className="settings-title">Manual Backup</h3>
                    <p className="settings-description">Create and download a JSON file containing all companies and users. Store this file in a safe place.</p>
                    <button className="btn btn-primary" onClick={handleCreateBackup}>Create Backup</button>
                </section>
                
                {/* Manual Restore */}
                <section>
                    <h3 className="settings-title">Restore from Backup</h3>
                    <p className="settings-description">Restore data from a backup file. <strong style={{color: 'var(--accent-danger)'}}>WARNING: This will overwrite ALL current data.</strong></p>
                    <input type="file" ref={fileInputRef} onChange={handleFileSelected} style={{ display: 'none' }} accept="application/json,.json" />
                    <button className="btn btn-warning" onClick={handleRestoreClick}>Restore from File</button>
                </section>

                {/* Backup History */}
                <section>
                    <h3 className="settings-title">Operation History</h3>
                    <p className="settings-description">A log of recent backup and restore operations.</p>
                    <div className="table-container" style={{maxHeight: '400px'}}>
                        <table>
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Action</th>
                                    <th>Initiated By</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                {loading ? (
                                    <tr><td colSpan={4} className="loading-indicator">Loading history...</td></tr>
                                ) : error ? (
                                    <tr><td colSpan={4} className="error-message">{error}</td></tr>
                                ) : logs.length === 0 ? (
                                     <tr><td colSpan={4} style={{textAlign: 'center', padding: '2rem'}}>No backup operations recorded.</td></tr>
                                ) : (
                                    logs.map(log => (
                                        <tr key={log.id}>
                                            <td>{new Date(log.timestamp).toLocaleString()}</td>
                                            <td>{log.action}</td>
                                            <td>{log.initiatedBy}</td>
                                            <td className="td-wrap">{log.details}</td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                </section>
            </div>
        </>
    );
}
