/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, useEffect } from 'react';
import { DataViewProps } from './lib/types';
import { DatabaseIcon, DownloadIcon, SettingsIcon, AlertTriangleIcon, CheckCircleIcon, InfoIcon } from './Icons';

interface BackupSettings {
    backupPath: string;
    isScheduled: boolean;
    maxBackups: number;
}

interface BackupStatus {
    isSet: boolean;
    backupPath: string | null;
    message: string;
}

export function BackupSettings(props: DataViewProps) {
    const [settings, setSettings] = useState<BackupSettings | null>(null);
    const [status, setStatus] = useState<BackupStatus | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [newBackupPath, setNewBackupPath] = useState('');
    const [isCreatingBackup, setIsCreatingBackup] = useState(false);
    const [notification, setNotification] = useState<{ type: 'success' | 'error' | 'info'; message: string } | null>(null);

    // Fetch backup settings and status
    const fetchBackupData = async () => {
        try {
            setLoading(true);
            setError(null);

            // Fetch both settings and status
            const [settingsResponse, statusResponse] = await Promise.all([
                fetch('/api/backup/settings'),
                fetch('/api/backup/path-check')
            ]);

            if (settingsResponse.ok && statusResponse.ok) {
                const settingsData = await settingsResponse.json();
                const statusData = await statusResponse.json();
                
                setSettings(settingsData);
                setStatus(statusData);
                setNewBackupPath(settingsData.backupPath || '');
            } else {
                throw new Error('Failed to fetch backup data');
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load backup settings');
        } finally {
            setLoading(false);
        }
    };

    // Set backup path
    const handleSetBackupPath = async () => {
        if (!newBackupPath.trim()) {
            showNotification('error', 'Please enter a valid backup path');
            return;
        }

        try {
            const response = await fetch('/api/backup/settings/path', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ backupPath: newBackupPath.trim() }),
            });

            if (response.ok) {
                const result = await response.json();
                showNotification('success', result.message);
                await fetchBackupData(); // Refresh data
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to set backup path');
            }
        } catch (err) {
            showNotification('error', err instanceof Error ? err.message : 'Failed to set backup path');
        }
    };

    // Create manual backup
    const handleCreateBackup = async () => {
        if (!status?.isSet) {
            showNotification('error', 'Please set backup path first');
            return;
        }

        try {
            setIsCreatingBackup(true);
            const response = await fetch('/api/backup/create', {
                method: 'POST',
            });

            if (response.ok) {
                const result = await response.json();
                if (result.skipped) {
                    showNotification('info', result.message);
                } else {
                    showNotification('success', `Backup created successfully: ${result.fileName}`);
                }
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to create backup');
            }
        } catch (err) {
            showNotification('error', err instanceof Error ? err.message : 'Failed to create backup');
        } finally {
            setIsCreatingBackup(false);
        }
    };

    // Show notification helper
    const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
        setNotification({ type, message });
        setTimeout(() => setNotification(null), 5000);
    };

    // Load data on component mount
    useEffect(() => {
        fetchBackupData();
    }, []);

    if (loading) {
        return (
            <div className="view-header">
                <h2>Backup & Restore Settings</h2>
                <div>Loading backup settings...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="view-header">
                <h2>Backup & Restore Settings</h2>
                <div className="error-message" style={{ color: 'var(--accent-danger)', padding: '1rem' }}>
                    <AlertTriangleIcon size={20} style={{ marginRight: '0.5rem' }} />
                    {error}
                </div>
            </div>
        );
    }

    return (
        <>
            <div className="view-header">
                <h2>Backup & Restore Settings</h2>
                <p style={{ marginTop: '0.5rem', color: 'var(--text-secondary)' }}>
                    Configure automatic backups and create manual backups of your database
                </p>
            </div>

            {/* Notification */}
            {notification && (
                <div 
                    className={`notification ${notification.type}`}
                    style={{
                        padding: '1rem',
                        marginBottom: '1.5rem',
                        borderRadius: '6px',
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: notification.type === 'success' ? 'rgba(34, 197, 94, 0.1)' : 
                                       notification.type === 'error' ? 'rgba(239, 68, 68, 0.1)' : 
                                       'rgba(59, 130, 246, 0.1)',
                        border: `1px solid ${notification.type === 'success' ? 'var(--accent-success)' : 
                                            notification.type === 'error' ? 'var(--accent-danger)' : 
                                            'var(--accent-primary)'}`
                    }}
                >
                    {notification.type === 'success' ? <CheckCircleIcon size={20} /> : 
                     notification.type === 'error' ? <AlertTriangleIcon size={20} /> : 
                     <InfoIcon size={20} />}
                    <span style={{ marginLeft: '0.5rem' }}>{notification.message}</span>
                </div>
            )}

            <div className="settings-container">
                {/* Current Status */}
                <section className="settings-section">
                    <h3 className="settings-title">Current Backup Status</h3>
                    <div style={{ 
                        padding: '1rem', 
                        backgroundColor: 'var(--surface-secondary)', 
                        borderRadius: '6px',
                        marginBottom: '1.5rem'
                    }}>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                            <DatabaseIcon size={20} style={{ marginRight: '0.5rem' }} />
                            <strong>Status: </strong>
                            <span style={{ 
                                marginLeft: '0.5rem',
                                color: status?.isSet ? 'var(--accent-success)' : 'var(--accent-warning)'
                            }}>
                                {status?.isSet ? 'Configured' : 'Not Configured'}
                            </span>
                        </div>
                        {status?.backupPath && (
                            <div style={{ fontSize: '0.9rem', color: 'var(--text-secondary)' }}>
                                <strong>Current Path:</strong> {status.backupPath}
                            </div>
                        )}
                        <div style={{ fontSize: '0.9rem', color: 'var(--text-secondary)', marginTop: '0.5rem' }}>
                            <strong>Scheduled Backups:</strong> {settings?.isScheduled ? 'Enabled' : 'Disabled'}
                        </div>
                        <div style={{ fontSize: '0.9rem', color: 'var(--text-secondary)' }}>
                            <strong>Max Backups:</strong> {settings?.maxBackups || 'Not set'}
                        </div>
                    </div>
                </section>

                {/* Configure Backup Path */}
                <section className="settings-section">
                    <h3 className="settings-title">Configure Backup Location</h3>
                    <p className="settings-description">
                        Set the directory where backup files will be stored. The system will automatically 
                        create scheduled backups and you can create manual backups as needed.
                    </p>
                    <div style={{ display: 'flex', gap: '1rem', alignItems: 'flex-end' }}>
                        <div style={{ flex: 1 }}>
                            <label htmlFor="backup-path" style={{ 
                                display: 'block', 
                                marginBottom: '0.5rem',
                                fontWeight: '500'
                            }}>
                                Backup Directory Path
                            </label>
                            <input
                                id="backup-path"
                                type="text"
                                value={newBackupPath}
                                onChange={(e) => setNewBackupPath(e.target.value)}
                                placeholder="e.g., C:\Backups\FAR_Sighted or /home/<USER>/backups"
                                style={{
                                    width: '100%',
                                    padding: '0.75rem',
                                    borderRadius: '6px',
                                    border: '1px solid var(--border-primary)',
                                    backgroundColor: 'var(--surface-primary)'
                                }}
                            />
                        </div>
                        <button
                            onClick={handleSetBackupPath}
                            className="btn btn-primary"
                            style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}
                        >
                            <SettingsIcon size={16} />
                            Set Path
                        </button>
                    </div>
                </section>

                {/* Manual Backup */}
                <section className="settings-section">
                    <h3 className="settings-title">Manual Backup</h3>
                    <p className="settings-description">
                        Create an immediate backup of your database. This is useful before making 
                        significant changes or as an additional safety measure.
                    </p>
                    <button
                        onClick={handleCreateBackup}
                        disabled={!status?.isSet || isCreatingBackup}
                        className="btn btn-secondary"
                        style={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            gap: '0.5rem',
                            opacity: (!status?.isSet || isCreatingBackup) ? 0.6 : 1
                        }}
                    >
                        <DownloadIcon size={16} />
                        {isCreatingBackup ? 'Creating Backup...' : 'Create Backup Now'}
                    </button>
                    {!status?.isSet && (
                        <p style={{ 
                            fontSize: '0.9rem', 
                            color: 'var(--accent-warning)', 
                            marginTop: '0.5rem' 
                        }}>
                            Please configure backup path first
                        </p>
                    )}
                </section>

                {/* Information */}
                <section className="settings-section">
                    <h3 className="settings-title">Important Information</h3>
                    <div style={{ 
                        padding: '1rem', 
                        backgroundColor: 'rgba(59, 130, 246, 0.1)', 
                        borderRadius: '6px',
                        border: '1px solid var(--accent-primary)'
                    }}>
                        <ul style={{ margin: 0, paddingLeft: '1.2rem' }}>
                            <li>Backups are created with timestamps for easy identification</li>
                            <li>The system maintains a maximum number of backup files to save disk space</li>
                            <li>Scheduled backups run automatically when the application is running</li>
                            <li>Backup files include the complete database with all your asset data</li>
                            <li>Store backups in a secure location and consider off-site storage for critical data</li>
                        </ul>
                    </div>
                </section>
            </div>
        </>
    );
}