/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

// Debug script to troubleshoot user creation issues
import dbService from './services/database-new.js';

async function debugUserCreation() {
    console.log('🔍 FAR Sighted User Creation Diagnostics');
    console.log('========================================\n');

    try {
        // Step 1: Check database initialization
        console.log('1️⃣ Checking database initialization...');
        await dbService.ensureInitialized();
        console.log('✅ Database service initialized\n');

        // Step 2: Check migration status
        console.log('2️⃣ Checking migration status...');
        const migrationStatus = await dbService.getMigrationStatus();
        console.log('Migration Status:', migrationStatus);
        
        const isReady = await dbService.isSystemReady();
        console.log('System Ready:', isReady);
        
        if (!isReady) {
            console.log('❌ System requires migration!');
            console.log('   Please run: npm run migrate');
            console.log('   Or: node backend/scripts/migrate-database.js\n');
            return;
        }
        console.log('✅ System is ready\n');

        // Step 3: List companies
        console.log('3️⃣ Listing companies...');
        const companies = await dbService.getAllCompanies();
        console.log(`Found ${companies.length} companies:`);
        companies.forEach(company => {
            console.log(`   - ${company.id}: ${company.name}`);
        });
        
        if (companies.length === 0) {
            console.log('❌ No companies found! You need to create a company first.');
            console.log('   Use the frontend to create a company or run migration.\n');
            return;
        }
        console.log('');

        // Step 4: Test company database access for each company
        console.log('4️⃣ Testing company database access...');
        const dbManager = dbService.getDatabaseManager();
        
        for (const company of companies) {
            try {
                console.log(`   Testing company: ${company.name} (${company.id})`);
                const companyDb = await dbManager.getCompanyDatabase(company.id);
                
                if (!companyDb) {
                    console.log(`   ❌ Failed to get database for company ${company.id}`);
                    continue;
                }
                
                // Check if users table exists
                const tables = await companyDb.all(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='users'"
                );
                
                if (tables.length === 0) {
                    console.log(`   ❌ Users table does not exist for company ${company.id}`);
                    continue;
                }
                
                // Check existing users
                const users = await companyDb.all('SELECT id, username, role FROM users');
                console.log(`   ✅ Company database ready. Users: ${users.length}`);
                
                if (users.length > 0) {
                    users.forEach(user => {
                        console.log(`      - ${user.username} (${user.role})`);
                    });
                }
                
            } catch (error) {
                console.log(`   ❌ Error accessing company database:`, error.message);
            }
        }
        console.log('');

        // Step 5: Test user creation for first company
        if (companies.length > 0) {
            console.log('5️⃣ Testing user creation process...');
            const testCompany = companies[0];
            console.log(`   Using test company: ${testCompany.name} (${testCompany.id})`);
            
            try {
                const companyDb = await dbManager.getCompanyDatabase(testCompany.id);
                
                // Test data
                const testUsername = `test_user_${Date.now()}`;
                const testPassword = 'testpass123';
                const testRole = 'Data Entry';
                
                console.log(`   Creating test user: ${testUsername}`);
                
                // Check if username exists (should not)
                const existingUser = await companyDb.get(
                    'SELECT id FROM users WHERE username = ?',
                    [testUsername]
                );
                
                if (existingUser) {
                    console.log(`   ❌ Test username already exists (unexpected)`);
                    return;
                }
                
                // Try to create user (dry run - we'll delete it after)
                const bcrypt = await import('bcrypt');
                const hashedPassword = await bcrypt.hash(testPassword, 10);
                const recoveryKey = `rk_${testCompany.id}_${testUsername}_${Math.random().toString(36).substring(2, 15)}`;
                const recoveryKeyHash = await bcrypt.hash(recoveryKey, 10);
                const userId = `${testCompany.id}-${testUsername}-${Date.now()}`;
                
                await companyDb.run(
                    `INSERT INTO users (id, username, password_hash, role, recovery_key_hash, has_saved_recovery_key, is_active, must_change_password)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                    [userId, testUsername, hashedPassword, testRole, recoveryKeyHash, 0, 1, 1]
                );
                
                console.log(`   ✅ Test user created successfully`);
                
                // Clean up test user
                await companyDb.run('DELETE FROM users WHERE id = ?', [userId]);
                console.log(`   🧹 Test user cleaned up`);
                
            } catch (error) {
                console.log(`   ❌ User creation test failed:`, error.message);
                console.log(`   Error details:`, error.stack);
            }
        }

        console.log('\n🎉 Diagnostics completed!');
        console.log('\nIf all checks passed, try creating a user again.');
        console.log('If issues persist, check the backend logs for detailed error messages.');

    } catch (error) {
        console.error('❌ Diagnostic failed:', error);
        console.error('Error details:', error.stack);
    } finally {
        // Close database connections
        try {
            await dbService.close();
        } catch (error) {
            console.warn('Warning: Failed to close database connections:', error.message);
        }
        process.exit(0);
    }
}

// Run diagnostics
debugUserCreation().catch(error => {
    console.error('❌ Failed to run diagnostics:', error);
    process.exit(1);
});
