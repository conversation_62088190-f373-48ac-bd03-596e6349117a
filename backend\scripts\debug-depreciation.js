/**
 * COMPREHENSIVE DEPRECIATION DEBUGGING SCRIPT
 * Professional Advisory Services - Chartered Accountant
 * 
 * This script debugs exactly why depreciation is showing as 0
 * and traces through each step of the calculation process.
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const dbPath = join(__dirname, '../database/far_sighted.db');

console.log('🔍 COMPREHENSIVE DEPRECIATION DEBUGGING');
console.log('=====================================');

const debugDepreciation = async () => {
    const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
            console.error('❌ Error opening database:', err.message);
            process.exit(1);
        }
    });

    const runQuery = (sql, params = []) => {
        return new Promise((resolve, reject) => {
            db.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    };

    // Helper function to calculate inclusive date difference
    const inclusiveDateDiffInDays = (d1, d2) => {
        if (!d1 || !d2) return 0;
        const utc1 = Date.UTC(d1.getUTCFullYear(), d1.getUTCMonth(), d1.getUTCDate());
        const utc2 = Date.UTC(d2.getUTCFullYear(), d2.getUTCMonth(), d2.getUTCDate());
        const msPerDay = 1000 * 60 * 60 * 24;
        return Math.floor((utc2 - utc1) / msPerDay) + 1;
    };

    try {
        console.log('📊 Step 1: Checking available data...\n');

        // Get company info
        const companies = await runQuery('SELECT * FROM companies WHERE id = ?', ['c1001']);
        if (companies.length === 0) {
            console.log('❌ No company found with ID c1001');
            return;
        }
        
        const company = companies[0];
        console.log(`✅ Company: ${company.company_name}`);
        console.log(`📅 First adoption date: ${company.first_date_of_adoption}`);
        
        // Get financial years
        const financialYears = await runQuery('SELECT * FROM financial_years WHERE company_id = ? ORDER BY year_range', ['c1001']);
        console.log(`📅 Financial years: ${financialYears.map(fy => fy.year_range).join(', ')}`);

        // Get assets for 2022-2023
        const assets = await runQuery(`
            SELECT * FROM assets 
            WHERE company_id = 'c1001' 
            ORDER BY record_id
        `);

        console.log(`\n📊 Step 2: Found ${assets.length} assets to analyze\n`);

        if (assets.length === 0) {
            console.log('❌ No assets found. Running enhanced mock data script...');
            return;
        }

        const testYear = '2022-2023';
        const [startYearStr, endYearStr] = testYear.split('-');
        const fyStart = new Date(`${startYearStr}-04-01T00:00:00.000Z`);
        const fyEnd = new Date(`${endYearStr}-03-31T23:59:59.999Z`);
        const firstAdoptionDate = new Date(company.first_date_of_adoption);

        console.log(`🧪 Testing year: ${testYear}`);
        console.log(`📅 FY Start: ${fyStart.toDateString()}`);
        console.log(`📅 FY End: ${fyEnd.toDateString()}\n`);

        let totalIssues = 0;

        for (const asset of assets) {
            console.log(`🔍 DEBUGGING: ${asset.record_id} - ${asset.asset_particulars}`);
            console.log('═'.repeat(80));

            // Step 1: Basic asset info
            const putToUseDate = new Date(asset.put_to_use_date);
            const grossAmount = asset.gross_amount || 0;
            const lifeInYears = asset.life_in_years || 0;
            const salvagePercentage = asset.salvage_percentage || 0;
            const depreciationMethod = asset.depreciation_method;

            console.log(`📋 Basic Info:`);
            console.log(`   Gross Amount: ₹${grossAmount.toLocaleString()}`);
            console.log(`   Put to Use Date: ${asset.put_to_use_date} (${putToUseDate.toDateString()})`);
            console.log(`   Life in Years: ${lifeInYears}`);
            console.log(`   Salvage Percentage: ${salvagePercentage}%`);
            console.log(`   Depreciation Method: ${depreciationMethod}`);

            // Step 2: Calculate salvage value
            const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));
            console.log(`   Salvage Value: ₹${salvageValue.toLocaleString()}`);

            // Step 3: Check if asset is active in the year
            const isActiveInFY = putToUseDate <= fyEnd;
            console.log(`\n📅 Year Activity Check:`);
            console.log(`   Put to use before FY end? ${isActiveInFY}`);
            
            if (!isActiveInFY) {
                console.log(`⏭️ SKIP: Asset not active in ${testYear}\n`);
                continue;
            }

            // Step 4: Calculate opening figures
            const openingGross = (putToUseDate < fyStart) ? grossAmount : 0;
            const additionsGross = (putToUseDate >= fyStart && putToUseDate <= fyEnd) ? grossAmount : 0;
            const isCurrentYearAddition = additionsGross > 0;
            
            console.log(`\n📊 Schedule III Classification:`);
            console.log(`   Opening Gross: ₹${openingGross.toLocaleString()}`);
            console.log(`   Additions Gross: ₹${additionsGross.toLocaleString()}`);
            console.log(`   Is Current Year Addition: ${isCurrentYearAddition}`);

            // Step 5: Calculate use days
            const startOfUseInFY = putToUseDate > fyStart ? putToUseDate : fyStart;
            const endOfUseInFY = fyEnd; // No disposal in this test
            let useDaysInFY = 0;
            
            if (endOfUseInFY >= startOfUseInFY && putToUseDate <= endOfUseInFY) {
                useDaysInFY = inclusiveDateDiffInDays(startOfUseInFY, endOfUseInFY);
            }

            console.log(`\n⏰ Use Days Calculation:`);
            console.log(`   Start of use in FY: ${startOfUseInFY.toDateString()}`);
            console.log(`   End of use in FY: ${endOfUseInFY.toDateString()}`);
            console.log(`   Use days in FY: ${useDaysInFY}`);

            // Step 6: Determine depreciation base
            const depreciationBase = isCurrentYearAddition ? grossAmount : openingGross;
            console.log(`\n💰 Depreciation Base Determination:`);
            console.log(`   Depreciation base: ₹${depreciationBase.toLocaleString()}`);
            console.log(`   Logic: ${isCurrentYearAddition ? 'Current year addition → use gross amount' : 'Existing asset → use opening gross'}`);

            // Step 7: Check depreciation conditions
            console.log(`\n🔍 Depreciation Condition Checks:`);
            const condition1 = lifeInYears > 0;
            const condition2 = depreciationBase > salvageValue;
            const condition3 = useDaysInFY > 0;
            
            console.log(`   Life > 0: ${condition1} (${lifeInYears})`);
            console.log(`   Base > Salvage: ${condition2} (₹${depreciationBase.toLocaleString()} > ₹${salvageValue.toLocaleString()})`);
            console.log(`   Use days > 0: ${condition3} (${useDaysInFY})`);
            
            const canCalculateDepreciation = condition1 && condition2 && condition3;
            console.log(`   ✅ Can calculate depreciation: ${canCalculateDepreciation}`);

            if (!canCalculateDepreciation) {
                console.log(`❌ ISSUE: Cannot calculate depreciation due to failed conditions`);
                totalIssues++;
                
                if (!condition1) console.log(`   🔧 FIX NEEDED: Set valid life in years (currently ${lifeInYears})`);
                if (!condition2) console.log(`   🔧 FIX NEEDED: Base amount too low or salvage too high`);
                if (!condition3) console.log(`   🔧 FIX NEEDED: No use days calculated`);
                
                console.log('');
                continue;
            }

            // Step 8: Calculate depreciation
            let depreciationForYear = 0;
            const depreciableAmount = grossAmount - salvageValue;

            console.log(`\n🧮 Depreciation Calculation (${depreciationMethod} method):`);

            if (depreciationMethod === 'SLM') {
                const yearlyDepreciation = depreciableAmount / lifeInYears;
                depreciationForYear = (yearlyDepreciation / 365.25) * useDaysInFY;
                
                console.log(`   Depreciable amount: ₹${depreciableAmount.toLocaleString()}`);
                console.log(`   Yearly depreciation: ₹${yearlyDepreciation.toLocaleString()}`);
                console.log(`   Days factor: ${useDaysInFY}/365.25 = ${(useDaysInFY/365.25).toFixed(4)}`);
                console.log(`   Depreciation for year: ₹${Math.round(depreciationForYear).toLocaleString()}`);
            } else { // WDV
                if (grossAmount > 0 && salvageValue < grossAmount) {
                    const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
                    const yearlyDepreciation = depreciationBase * rate;
                    depreciationForYear = (yearlyDepreciation / 365.25) * useDaysInFY;
                    
                    console.log(`   Rate calculation: 1 - (${salvageValue}/${grossAmount})^(1/${lifeInYears}) = ${(rate * 100).toFixed(2)}%`);
                    console.log(`   Yearly depreciation: ₹${depreciationBase.toLocaleString()} × ${(rate * 100).toFixed(2)}% = ₹${yearlyDepreciation.toLocaleString()}`);
                    console.log(`   Days factor: ${useDaysInFY}/365.25 = ${(useDaysInFY/365.25).toFixed(4)}`);
                    console.log(`   Depreciation for year: ₹${Math.round(depreciationForYear).toLocaleString()}`);
                } else {
                    console.log(`   ❌ Cannot calculate WDV: Invalid gross/salvage ratio`);
                }
            }

            const finalDepreciation = Math.round(depreciationForYear);
            console.log(`\n🎯 Final Result:`);
            console.log(`   Depreciation for ${testYear}: ₹${finalDepreciation.toLocaleString()}`);
            
            if (finalDepreciation === 0) {
                console.log(`❌ ISSUE: Depreciation calculated as zero despite passing conditions`);
                totalIssues++;
            } else {
                console.log(`✅ SUCCESS: Depreciation calculated correctly`);
            }

            console.log('');
        }

        // Summary
        console.log('🎯 DEBUGGING SUMMARY');
        console.log('═'.repeat(50));
        console.log(`📊 Total assets analyzed: ${assets.length}`);
        console.log(`❌ Issues found: ${totalIssues}`);
        
        if (totalIssues === 0) {
            console.log('✅ All calculations working correctly in isolation');
            console.log('🔍 Issue might be in data retrieval or frontend display');
        } else {
            console.log('❌ Issues found in depreciation calculation logic');
        }

        console.log('\n💡 NEXT STEPS:');
        console.log('1. Fix any condition failures identified above');
        console.log('2. Verify database has correct asset data');
        console.log('3. Check if frontend is calling the right calculation function');
        console.log('4. Test Schedule III report generation');

        console.log('\n🔧 IMMEDIATE FIXES NEEDED:');
        if (totalIssues > 0) {
            console.log('- Review asset life in years values');
            console.log('- Check salvage percentage settings');
            console.log('- Verify put to use dates are valid');
            console.log('- Ensure depreciation method is set correctly');
        }

    } catch (error) {
        console.error('❌ Debugging error:', error);
    } finally {
        db.close();
    }
};

debugDepreciation();