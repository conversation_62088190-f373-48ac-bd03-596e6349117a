import fetch from 'node-fetch';

console.log('🔍 Checking API Response for Opening WDV Issues...\n');

async function checkAPIResponse() {
    try {
        const response = await fetch('http://localhost:3001/api/assets/company/c1001');
        const assets = await response.json();
        
        console.log(`📊 Found ${assets.length} assets for Tech Innovations\n`);
        
        // Check CNC Machine (P0001) specifically
        const cncMachine = assets.find(a => a.recordId === 'P0001');
        
        if (cncMachine) {
            console.log('🏭 CNC Machining Center (P0001) Analysis:');
            console.log(`   Purchase Value (Gross): ₹${(cncMachine.grossAmount/100000).toFixed(2)}L`);
            console.log(`   WDV at Adoption: ₹${(cncMachine.wdvOfAdoptionDate/100000).toFixed(2)}L\n`);
            
            // Check yearly data columns
            const yearlyColumns = Object.keys(cncMachine).filter(key => 
                key.includes('Opening-WDV') || key.includes('WDV-') || key.includes('Depreciation-')
            );
            
            console.log('📅 Yearly Data in API Response:');
            yearlyColumns.sort().forEach(col => {
                const value = cncMachine[col];
                if (value !== undefined && value !== null) {
                    console.log(`   ${col}: ₹${(value/100000).toFixed(2)}L`);
                }
            });
            
            // Specifically check the opening WDV values
            console.log('\n🔍 Opening WDV Analysis:');
            console.log(`   Opening-WDV-2022-2023: ₹${((cncMachine['Opening-WDV-2022-2023'] || 0)/100000).toFixed(2)}L`);
            console.log(`   Opening-WDV-2023-2024: ₹${((cncMachine['Opening-WDV-2023-2024'] || 0)/100000).toFixed(2)}L`);
            console.log(`   Opening-WDV-2024-2025: ₹${((cncMachine['Opening-WDV-2024-2025'] || 0)/100000).toFixed(2)}L`);
            
            // Check if opening WDV matches gross amount (the problem)
            const opening2022 = cncMachine['Opening-WDV-2022-2023'] || 0;
            const opening2023 = cncMachine['Opening-WDV-2023-2024'] || 0;
            const opening2024 = cncMachine['Opening-WDV-2024-2025'] || 0;
            
            console.log('\n⚠️  Problem Analysis:');
            if (Math.abs(opening2022 - cncMachine.grossAmount) < 1000) {
                console.log('   ❌ Opening-WDV-2022-2023 = Gross Amount (CORRECT for first year)');
            }
            if (Math.abs(opening2023 - cncMachine.grossAmount) < 1000) {
                console.log('   ❌ Opening-WDV-2023-2024 = Gross Amount (WRONG - should be previous year closing)');
            }
            if (Math.abs(opening2024 - cncMachine.grossAmount) < 1000) {
                console.log('   ❌ Opening-WDV-2024-2025 = Gross Amount (WRONG - should be previous year closing)');
            }
            
            // Check closing WDV values to verify carry-forward
            console.log('\n🔄 Closing WDV for Carry-Forward Check:');
            console.log(`   WDV-2022-2023: ₹${((cncMachine['WDV-2022-2023'] || 0)/100000).toFixed(2)}L`);
            console.log(`   WDV-2023-2024: ₹${((cncMachine['WDV-2023-2024'] || 0)/100000).toFixed(2)}L`);
            console.log(`   WDV-2024-2025: ₹${((cncMachine['WDV-2024-2025'] || 0)/100000).toFixed(2)}L`);
        }
        
        // Check Dell Workstation too
        const dellWorkstation = assets.find(a => a.recordId === 'C0001');
        if (dellWorkstation) {
            console.log('\n🖥️  Dell Workstation (C0001) Quick Check:');
            console.log(`   Gross Amount: ₹${(dellWorkstation.grossAmount/100000).toFixed(2)}L`);
            console.log(`   Opening-WDV-2023-2024: ₹${((dellWorkstation['Opening-WDV-2023-2024'] || 0)/100000).toFixed(2)}L`);
            
            if (Math.abs((dellWorkstation['Opening-WDV-2023-2024'] || 0) - dellWorkstation.grossAmount) < 1000) {
                console.log('   ❌ PROBLEM: Opening WDV = Gross Amount (should be depreciated value)');
            } else {
                console.log('   ✅ Opening WDV appears to be depreciated value');
            }
        }
        
    } catch (error) {
        console.error('❌ Error checking API response:', error);
    }
}

checkAPIResponse();