import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import dbService from '../services/database-new.js';

const router = express.Router();

// Middleware to check if migration is needed
router.use(async (req, res, next) => {
    try {
        const isReady = await dbService.isSystemReady();
        if (!isReady) {
            return res.status(503).json({
                error: 'System migration required',
                message: 'Database migration is needed. Please contact administrator.',
                migrationEndpoint: '/api/admin/migrate'
            });
        }
        next();
    } catch (error) {
        console.error('Error checking system status:', error);
        res.status(500).json({ error: 'System check failed' });
    }
});

// Get all companies
router.get('/', async (req, res) => {
    try {
        const companies = await dbService.getAllCompanies();
        res.json(companies);
    } catch (error) {
        console.error('Error fetching companies:', error);
        res.status(500).json({ error: 'Failed to fetch companies' });
    }
});

// Get company by ID with complete data
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const companyData = await dbService.getCompanyWithData(id);
        
        if (!companyData) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        res.json(companyData);
    } catch (error) {
        console.error('Error fetching company data:', error);
        res.status(500).json({ error: 'Failed to fetch company data' });
    }
});

// Add new company
router.post('/', async (req, res) => {
    try {
        const companyInfo = req.body;
        
        // Validate required fields
        if (!companyInfo.companyName || !companyInfo.financialYearStart || 
            !companyInfo.financialYearEnd || !companyInfo.firstDateOfAdoption) {
            return res.status(400).json({ 
                error: 'Missing required fields: companyName, financialYearStart, financialYearEnd, firstDateOfAdoption' 
            });
        }
        
        // Create company with its own database
        const newCompany = await dbService.createCompany(companyInfo);
        
        // Set context to new company and add initial data
        await dbService.setCompanyContext(newCompany.id);
        
        // Add default financial year
        const startYear = new Date(companyInfo.financialYearStart).getFullYear();
        const endYear = new Date(companyInfo.financialYearEnd).getFullYear();
        const yearRange = `${startYear}-${endYear}`;
        
        await dbService.run(
            'INSERT INTO financial_years (year_range) VALUES (?)',
            [yearRange]
        );
        
        // Add audit log
        await dbService.addAuditLog({
            userId: req.user?.id || 'system',
            username: req.user?.username || 'system',
            action: 'CREATE_COMPANY',
            details: `Created new company: ${companyInfo.companyName}`,
            tableName: 'companies',
            recordId: newCompany.id,
            newValues: JSON.stringify(companyInfo)
        });
        
        res.status(201).json(newCompany);
    } catch (error) {
        console.error('Error creating company:', error);
        res.status(500).json({ error: 'Failed to create company' });
    }
});

// Update company
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const companyInfo = req.body;
        
        // Check if company exists
        const existingCompany = await dbService.getCompanyInfo(id);
        if (!existingCompany) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // Update company info in master database
        await dbService.updateCompanyInfo(id, companyInfo);
        
        // Set context and add audit log
        await dbService.setCompanyContext(id);
        await dbService.addAuditLog({
            userId: req.user?.id || 'system',
            username: req.user?.username || 'system',
            action: 'UPDATE_COMPANY',
            details: `Updated company information`,
            tableName: 'companies',
            recordId: id,
            oldValues: JSON.stringify(existingCompany),
            newValues: JSON.stringify(companyInfo)
        });
        
        res.json({ message: 'Company updated successfully' });
    } catch (error) {
        console.error('Error updating company:', error);
        res.status(500).json({ error: 'Failed to update company' });
    }
});

// Get company info only
router.get('/:id/info', async (req, res) => {
    try {
        const { id } = req.params;
        
        const companyInfo = await dbService.getCompanyInfo(id);
        if (!companyInfo) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        res.json(companyInfo);
    } catch (error) {
        console.error('Error fetching company info:', error);
        res.status(500).json({ error: 'Failed to fetch company info' });
    }
});

// Update statutory rates for a company
router.put('/:id/statutory-rates', async (req, res) => {
    try {
        const { id } = req.params;
        const { rates } = req.body;
        
        if (!Array.isArray(rates)) {
            return res.status(400).json({ error: 'Rates must be an array' });
        }
        
        // Check if company exists
        const company = await dbService.getCompanyInfo(id);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // Set company context
        await dbService.setCompanyContext(id);
        
        await dbService.beginTransaction();
        
        try {
            // Delete existing statutory rates
            await dbService.run('DELETE FROM statutory_rates');
            
            // Insert new rates
            for (const rate of rates) {
                await dbService.run(
                    `INSERT INTO statutory_rates (
                        is_statutory, tangibility, asset_group, asset_sub_group,
                        extra_shift_depreciation, useful_life_years, schedule_ii_classification
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
                    [
                        rate.isStatutory, rate.tangibility, rate.assetGroup, 
                        rate.assetSubGroup, rate.extraShiftDepreciation, 
                        rate.usefulLifeYears, rate.scheduleIIClassification
                    ]
                );
            }
            
            await dbService.commit();
            
            // Add audit log
            await dbService.addAuditLog({
                userId: req.user?.id || 'system',
                username: req.user?.username || 'system',
                action: 'UPDATE_STATUTORY_RATES',
                details: `Updated statutory rates (${rates.length} rates)`,
                tableName: 'statutory_rates',
                recordId: id
            });
            
            res.json({ message: 'Statutory rates updated successfully' });
        } catch (error) {
            await dbService.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error updating statutory rates:', error);
        res.status(500).json({ error: 'Failed to update statutory rates' });
    }
});

// Update extra ledgers for a company
router.put('/:id/extra-ledgers', async (req, res) => {
    try {
        const { id } = req.params;
        const { ledgers } = req.body;
        
        if (!Array.isArray(ledgers)) {
            return res.status(400).json({ error: 'Ledgers must be an array' });
        }
        
        // Check if company exists
        const company = await dbService.getCompanyInfo(id);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // Set company context
        await dbService.setCompanyContext(id);
        
        await dbService.beginTransaction();
        
        try {
            // Delete existing extra ledgers
            await dbService.run('DELETE FROM extra_ledgers');
            
            // Insert new ledgers
            for (const ledger of ledgers) {
                await dbService.run(
                    'INSERT INTO extra_ledgers (ledger_name) VALUES (?)',
                    [ledger]
                );
            }
            
            await dbService.commit();
            
            // Add audit log
            await dbService.addAuditLog({
                userId: req.user?.id || 'system',
                username: req.user?.username || 'system',
                action: 'UPDATE_EXTRA_LEDGERS',
                details: `Updated extra ledgers (${ledgers.length} ledgers)`,
                tableName: 'extra_ledgers',
                recordId: id
            });
            
            res.json({ message: 'Extra ledgers updated successfully' });
        } catch (error) {
            await dbService.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error updating extra ledgers:', error);
        res.status(500).json({ error: 'Failed to update extra ledgers' });
    }
});

// Create next financial year
router.post('/:id/financial-years', async (req, res) => {
    try {
        const { id } = req.params;
        const { currentYear } = req.body;
        
        // Check if company exists
        const company = await dbService.getCompanyInfo(id);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // Set company context
        await dbService.setCompanyContext(id);
        
        // Parse current year and create next year
        const [startYear, endYear] = currentYear.split('-').map(Number);
        const nextYearRange = `${startYear + 1}-${endYear + 1}`;
        
        // Check if next year already exists
        const existingYear = await dbService.get(
            'SELECT id FROM financial_years WHERE year_range = ?',
            [nextYearRange]
        );
        
        if (existingYear) {
            return res.status(409).json({ error: 'Financial year already exists' });
        }
        
        await dbService.run(
            'INSERT INTO financial_years (year_range) VALUES (?)',
            [nextYearRange]
        );
        
        // Add audit log
        await dbService.addAuditLog({
            userId: req.user?.id || 'system',
            username: req.user?.username || 'system',
            action: 'CREATE_FINANCIAL_YEAR',
            details: `Created financial year: ${nextYearRange}`,
            tableName: 'financial_years',
            recordId: nextYearRange
        });
        
        res.json({ yearRange: nextYearRange });
    } catch (error) {
        console.error('Error creating financial year:', error);
        res.status(500).json({ error: 'Failed to create financial year' });
    }
});

// Activate license for a company
router.post('/:id/activate-license', async (req, res) => {
    try {
        const { id } = req.params;
        const { licenseData } = req.body;
        
        // Check if company exists
        const company = await dbService.getCompanyInfo(id);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // Update license in master database
        await dbService.updateCompanyInfo(id, {
            ...company,
            licenseValidUpto: licenseData.validUpto
        });
        
        // Set company context and add to license history
        await dbService.setCompanyContext(id);
        await dbService.run(
            `INSERT INTO license_history (id, license_key, valid_from, valid_upto, activated_at)
             VALUES (?, ?, ?, ?, ?)`,
            [
                uuidv4(), licenseData.key, licenseData.validFrom, 
                licenseData.validUpto, new Date().toISOString()
            ]
        );
        
        // Add audit log
        await dbService.addAuditLog({
            userId: req.user?.id || 'system',
            username: req.user?.username || 'system',
            action: 'ACTIVATE_LICENSE',
            details: `Activated license valid until: ${licenseData.validUpto}`,
            tableName: 'license_history',
            recordId: licenseData.key
        });
        
        // Return updated company info
        const updatedCompany = await dbService.getCompanyInfo(id);
        res.json(updatedCompany);
    } catch (error) {
        console.error('Error activating license:', error);
        res.status(500).json({ error: 'Failed to activate license' });
    }
});

// Company backup endpoint
router.post('/:id/backup', async (req, res) => {
    try {
        const { id } = req.params;
        
        // Check if company exists
        const company = await dbService.getCompanyInfo(id);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // Set company context and create backup
        await dbService.setCompanyContext(id);
        const backupData = await dbService.createCompanyBackup();
        
        // Include company info in backup
        backupData.company_info = company;
        
        res.json({
            message: 'Backup created successfully',
            data: backupData,
            timestamp: backupData.backup_timestamp
        });
    } catch (error) {
        console.error('Error creating backup:', error);
        res.status(500).json({ error: 'Failed to create backup' });
    }
});

// Company restore endpoint
router.post('/:id/restore', async (req, res) => {
    try {
        const { id } = req.params;
        const { backupData } = req.body;
        
        if (!backupData) {
            return res.status(400).json({ error: 'Backup data is required' });
        }
        
        // Check if company exists
        const company = await dbService.getCompanyInfo(id);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // Set company context and restore
        await dbService.setCompanyContext(id);
        await dbService.restoreCompanyFromBackup(backupData);
        
        res.json({ message: 'Company data restored successfully' });
    } catch (error) {
        console.error('Error restoring company data:', error);
        res.status(500).json({ error: 'Failed to restore company data' });
    }
});

export default router;