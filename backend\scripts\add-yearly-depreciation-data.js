/**
 * Add Previous Years Depreciation Data for Schedule III Testing
 * Creates yearly depreciation records for comprehensive testing
 */

import DatabaseManager from '../services/database-manager/DatabaseManager.js';

// Function to calculate depreciation for a given year
function calculateDepreciation(asset, year, openingWDV) {
    const [startYearStr, endYearStr] = year.split('-');
    const fyStart = new Date(`20${startYearStr}-04-01T00:00:00.000Z`);
    const fyEnd = new Date(`20${endYearStr}-03-31T23:59:59.999Z`);
    
    const putToUseDate = new Date(asset.putToUseDate);
    const disposalDate = asset.disposalDate ? new Date(asset.disposalDate) : null;
    
    // Calculate use days in this financial year
    const startOfUseInFY = putToUseDate > fyStart ? putToUseDate : fyStart;
    const endOfUseInFY = disposalDate && disposalDate < fyEnd ? disposalDate : fyEnd;
    
    let useDays = 0;
    if (startOfUseInFY <= endOfUseInFY) {
        useDays = Math.ceil((endOfUseInFY - startOfUseInFY) / (1000 * 60 * 60 * 24)) + 1;
    }
    
    if (useDays <= 0) return { depreciation: 0, useDays: 0, closingWDV: openingWDV };
    
    const salvageValue = Math.round(asset.grossAmount * (asset.salvagePercentage / 100));
    let depreciation = 0;
    
    if (asset.depreciationMethod === 'SLM') {
        const depreciableAmount = asset.grossAmount - salvageValue;
        const yearlyDepreciation = depreciableAmount / asset.lifeInYears;
        depreciation = Math.round((yearlyDepreciation / 365.25) * useDays);
    } else { // WDV
        if (openingWDV > salvageValue) {
            const rate = 1 - Math.pow((salvageValue / asset.grossAmount), (1 / asset.lifeInYears));
            const yearlyDepreciation = openingWDV * rate;
            depreciation = Math.round((yearlyDepreciation / 365.25) * useDays);
        }
    }
    
    const closingWDV = Math.max(openingWDV - depreciation, salvageValue);
    
    return { depreciation, useDays, closingWDV };
}

// Function to add yearly depreciation data
async function addYearlyDepreciationData(companyId, companyName) {
    console.log(`\n📊 Adding yearly depreciation data to: ${companyName} (${companyId})`);
    
    try {
        const dbManager = new DatabaseManager();
        const companyDb = await dbManager.getCompanyDatabase(companyId);
        
        // Get all assets for this company
        const assets = await companyDb.all('SELECT * FROM assets');
        console.log(`  📝 Found ${assets.length} assets to process`);
        
        // Financial years to process
        const years = ['2022-23', '2023-24', '2024-25'];
        
        for (const year of years) {
            console.log(`  📅 Processing year: ${year}`);
            
            for (const asset of assets) {
                // Skip if asset was purchased after this year
                const assetYear = new Date(asset.putToUseDate).getFullYear();
                const [, endYearStr] = year.split('-');
                const yearEnd = parseInt(`20${endYearStr}`);
                
                if (assetYear > yearEnd) {
                    continue; // Asset not yet purchased in this year
                }
                
                // Calculate opening WDV for this year
                let openingWDV;
                if (year === '2022-23') {
                    // First year - use adoption WDV or gross amount
                    openingWDV = asset.wdvOfAdoptionDate || asset.grossAmount;
                } else {
                    // Get closing WDV from previous year
                    const prevYear = years[years.indexOf(year) - 1];
                    const prevData = await companyDb.get(
                        'SELECT closing_wdv FROM asset_yearly_data WHERE asset_id = ? AND year_range = ?',
                        [asset.id, prevYear]
                    );
                    openingWDV = prevData ? prevData.closing_wdv : (asset.wdvOfAdoptionDate || asset.grossAmount);
                }
                
                // Calculate depreciation for this year
                const { depreciation, useDays, closingWDV } = calculateDepreciation(asset, year, openingWDV);
                
                // Calculate disposal values if asset was disposed in this year
                let disposalWDV = null;
                let disposalAmount = null;
                let gainLossOnDisposal = null;
                
                if (asset.disposalDate) {
                    const disposalYear = new Date(asset.disposalDate).getFullYear();
                    
                    if (disposalYear === yearEnd) {
                        disposalWDV = closingWDV;
                        disposalAmount = asset.disposalAmount;
                        gainLossOnDisposal = disposalAmount - disposalWDV;
                    }
                }
                
                // Insert or update yearly data
                await companyDb.run(`
                    INSERT OR REPLACE INTO asset_yearly_data (
                        asset_id, year_range, opening_wdv, use_days, normal_depreciation,
                        total_depreciation, closing_wdv, disposal_wdv, gain_loss_on_disposal
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    asset.id, year, openingWDV, useDays, depreciation,
                    depreciation, closingWDV, disposalWDV, gainLossOnDisposal
                ]);
            }
        }
        
        console.log(`  ✅ Added yearly depreciation data for ${assets.length} assets across ${years.length} years`);
        
    } catch (error) {
        console.error(`  ❌ Error adding yearly data: ${error.message}`);
        throw error;
    }
}

// Main execution
async function main() {
    console.log('🚀 Adding Previous Years Depreciation Data for Schedule III Testing');
    console.log('===================================================================\n');
    
    try {
        const dbManager = new DatabaseManager();
        
        // Get all companies
        const companies = await dbManager.getAllCompanies();
        
        if (!companies || companies.length === 0) {
            console.error('❌ No companies found');
            process.exit(1);
        }
        
        console.log(`📁 Found ${companies.length} company(ies)`);
        
        // Add yearly data to first 3 companies
        const targetCompanies = companies.slice(0, 3);
        
        for (const company of targetCompanies) {
            try {
                await addYearlyDepreciationData(company.id, company.name);
            } catch (error) {
                console.error(`❌ Failed to add yearly data to ${company.name}: ${error.message}`);
                console.log('   Continuing with next company...\n');
            }
        }
        
        console.log('\n🎉 Yearly depreciation data addition completed successfully!');
        console.log('\n📋 Summary:');
        console.log('   • Added depreciation data for 3 financial years (2022-23, 2023-24, 2024-25)');
        console.log('   • Calculated opening and closing WDV for each year');
        console.log('   • Included disposal calculations where applicable');
        console.log('   • Ready for Schedule III carry-forward testing');
        console.log('\n📊 You can now test:');
        console.log('   • Schedule III report with proper opening balances');
        console.log('   • Year-over-year carry-forward calculations');
        console.log('   • Asset disposal impact on reports');
        console.log('   • Multi-year depreciation accuracy');
        
    } catch (error) {
        console.error('❌ Script failed:', error.message);
        process.exit(1);
    }
}

// Run the script
main();
