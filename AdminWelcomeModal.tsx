/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { FC } from 'react';
import { InfoIcon } from './Icons';

interface AdminWelcomeModalProps {
    isOpen: boolean;
    onConfirm: () => void;
}

export const AdminWelcomeModal: FC<AdminWelcomeModalProps> = ({ isOpen, onConfirm }) => {
    if (!isOpen) return null;

    return (
        <div className="modal-overlay">
            <div className="modal-content" style={{ maxWidth: '550px' }} onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <div className="modal-header-content">
                        <InfoIcon className="icon-info" />
                        <h2>Important: Secure Your System</h2>
                    </div>
                </div>
                <div style={{ padding: '1.5rem 0', fontSize: '1.1rem', lineHeight: '1.6' }}>
                    <p>Welcome, Administrator!</p>
                    <br />
                    <p>For security and to prevent being locked out, it is <strong>highly recommended</strong> to create a second administrator account.</p>
                    <br />
                    <p>If you forget your password, the second administrator can help you recover your account.</p>
                     <br />
                    <p>You can add new users by navigating to <strong>Users → Add User</strong> in the sidebar.</p>
                </div>
                <div className="modal-actions">
                    <button type="button" className="btn btn-primary" onClick={onConfirm}>OK, I Understand</button>
                </div>
            </div>
        </div>
    );
};
