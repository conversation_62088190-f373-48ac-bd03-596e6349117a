/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo } from 'react';
import { api } from '../lib/api';
import type { CompanyData } from '../lib/db-server';
import { DataViewProps, ScheduleIIIRow, ScheduleIIIDetailRow } from '../lib/types';
import { formatIndianNumber, calculateYearlyMetrics, sortData, exportToExcel, exportToCSV } from '../lib/utils';
import { ScheduleIIIDetailModal } from './ScheduleIIIDetailModal';
import { DownloadIcon } from '../Icons';

export function ScheduleIII({ companyId, companyName, year, showAlert }: DataViewProps) {
    const [reportData, setReportData] = useState<ScheduleIIIRow[]>([]);
    const [totals, setTotals] = useState<ScheduleIIIRow | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [sortConfig, setSortConfig] = useState<{ key: keyof ScheduleIIIRow; direction: 'asc' | 'desc' } | null>({ key: 'assetType', direction: 'asc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);

    const [companyData, setCompanyData] = useState<CompanyData | null>(null);
    const [drillDownAssetType, setDrillDownAssetType] = useState<string | null>(null);
    const [drillDownData, setDrillDownData] = useState<ScheduleIIIDetailRow[]>([]);
    
    useEffect(() => {
        const fetchCompanyData = async () => {
             if (!companyId) {
                setCompanyData(null);
                setReportData([]);
                setTotals(null);
                setLoading(false);
                setSelectedRowId(null);
                setSelectedColumnKey(null);
                return;
            }
            setLoading(true);
            setSelectedRowId(null);
            setSelectedColumnKey(null);
            try {
                const data = await api.getCompanyData(companyId);
                setCompanyData(data);
            } catch (err) {
                 setError('Failed to fetch company data.');
                 console.error(err);
                 setCompanyData(null);
            } finally {
                setLoading(false);
            }
        };
        fetchCompanyData();
    }, [companyId]);

    useEffect(() => {
        if (!companyData || companyData.assets.length === 0) {
            setReportData([]);
            setTotals(null);
            return;
        }
        calculateReport(companyData, year);
    }, [companyData, year]);
    
    const calculateReport = (companyData: CompanyData, currentYear: string) => {
        setLoading(true);
        setError(null);

        const [startYearStr, endYearStr] = currentYear.split('-');
        const fyStart = new Date(`${startYearStr}-04-01T00:00:00.000Z`);
        const fyEnd = new Date(`${endYearStr}-03-31T23:59:59.999Z`);

        const reportMap: Record<string, ScheduleIIIRow> = {};

        for (const asset of companyData.assets) {
            if (!asset.scheduleIIIClassification || !asset.putToUseDate) continue;
            
            const putToUseDate = new Date(asset.putToUseDate);
            const disposalDate = asset.disposalDate ? new Date(asset.disposalDate) : null;
            
            const wasActiveInFY = putToUseDate <= fyEnd && (!disposalDate || disposalDate >= fyStart);
            if (!wasActiveInFY) continue;

            const assetType = asset.scheduleIIIClassification;
            if (!reportMap[assetType]) {
                reportMap[assetType] = { assetType, openingGross: 0, additionsGross: 0, deletionsGross: 0, closingGross: 0, openingDepreciation: 0, additionsDepreciation: 0, deletionsDepreciation: 0, closingDepreciation: 0, openingNetBlock: 0, closingNetBlock: 0, assetCount: 0 };
            }
            
            const metrics = calculateYearlyMetrics(asset, currentYear, companyData);
            reportMap[assetType].openingGross += metrics.openingGross;
            reportMap[assetType].additionsGross += metrics.additionsGross;
            reportMap[assetType].deletionsGross += metrics.deletionsGross;
            reportMap[assetType].openingDepreciation += metrics.openingDepreciation;
            reportMap[assetType].additionsDepreciation += metrics.additionsDepreciation;
            reportMap[assetType].deletionsDepreciation += metrics.deletionsDepreciation;
            reportMap[assetType].assetCount++;
        }

        const finalReportData: ScheduleIIIRow[] = Object.values(reportMap).map(row => {
            row.closingGross = row.openingGross + row.additionsGross - row.deletionsGross;
            row.closingDepreciation = row.openingDepreciation + row.additionsDepreciation - row.deletionsDepreciation;
            row.openingNetBlock = row.openingGross - row.openingDepreciation;
            row.closingNetBlock = row.closingGross - row.closingDepreciation;
            return row;
        });
        
        const totalRow: ScheduleIIIRow = { assetType: 'Total', openingGross: 0, additionsGross: 0, deletionsGross: 0, closingGross: 0, openingDepreciation: 0, additionsDepreciation: 0, deletionsDepreciation: 0, closingDepreciation: 0, openingNetBlock: 0, closingNetBlock: 0, assetCount: 0 };
        finalReportData.forEach(row => {
            (Object.keys(row) as Array<keyof ScheduleIIIRow>).forEach(key => {
                if (key !== 'assetType') {
                    totalRow[key] += row[key] as number;
                }
            });
        });
        
        setReportData(finalReportData);
        setTotals(totalRow);
        setLoading(false);
    };

    const handleRowDoubleClick = (assetType: string) => {
        if (!assetType || assetType === 'Total' || !companyData) return;
        
        const filtered = companyData.assets.filter(a => a.scheduleIIIClassification === assetType);
        
        const details = filtered.map((asset): ScheduleIIIDetailRow => {
            const metrics = calculateYearlyMetrics(asset, year, companyData);

            return {
                recordId: asset.recordId,
                assetParticulars: asset.assetParticulars,
                assetType: assetType,
                openingGross: metrics.openingGross,
                additionsGross: metrics.additionsGross,
                deletionsGross: metrics.deletionsGross,
                closingGross: metrics.closingGross,
                openingDepreciation: metrics.openingDepreciation,
                additionsDepreciation: metrics.additionsDepreciation,
                deletionsDepreciation: metrics.deletionsDepreciation,
                closingDepreciation: metrics.closingDepreciation,
                openingNetBlock: metrics.openingNetBlock,
                closingNetBlock: metrics.closingNetBlock,
                assetCount: 1, // Each detail row is a single asset
            };
        });
        
        setDrillDownData(details);
        setDrillDownAssetType(assetType);
    };


    const sortedReportData = useMemo(() => {
        return sortData(reportData, sortConfig);
    }, [reportData, sortConfig]);

    const requestSort = (key: keyof ScheduleIIIRow) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: keyof ScheduleIIIRow) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };
    
    const getExportData = () => {
        const dataToExport = sortedReportData.map(row => ({
            "Asset Type": row.assetType,
            "No. of Assets": row.assetCount,
            "Gross Block - Opening": row.openingGross,
            "Gross Block - Additions": row.additionsGross,
            "Gross Block - Deletions": row.deletionsGross,
            "Gross Block - Closing": row.closingGross,
            "Depreciation - Opening": row.openingDepreciation,
            "Depreciation - For the Year": row.additionsDepreciation,
            "Depreciation - On Disposals": row.deletionsDepreciation,
            "Depreciation - Closing": row.closingDepreciation,
            "Net Block - Opening": row.openingNetBlock,
            "Net Block - Closing": row.closingNetBlock,
        }));

        if (totals) {
            dataToExport.push({
                "Asset Type": totals.assetType,
                "No. of Assets": totals.assetCount,
                "Gross Block - Opening": totals.openingGross,
                "Gross Block - Additions": totals.additionsGross,
                "Gross Block - Deletions": totals.deletionsGross,
                "Gross Block - Closing": totals.closingGross,
                "Depreciation - Opening": totals.openingDepreciation,
                "Depreciation - For the Year": totals.additionsDepreciation,
                "Depreciation - On Disposals": totals.deletionsDepreciation,
                "Depreciation - Closing": totals.closingDepreciation,
                "Net Block - Opening": totals.openingNetBlock,
                "Net Block - Closing": totals.closingNetBlock,
            });
        }
        return dataToExport;
    }

    const handleExport = () => {
        if (!companyId || !companyName || !year) return;
        const dataToExport = getExportData();
        if (!exportToExcel({ data: dataToExport, companyName, year, reportName: 'Schedule_III' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    const handleExportCSV = () => {
        if (!companyId || !companyName || !year) return;
        
        const dataToExport = getExportData();
        const headers = Object.keys(dataToExport[0] || {});

        if (!exportToCSV({ data: dataToExport, headers: headers, companyName, year, reportName: 'Schedule_III' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    if (loading) return <div className="loading-indicator">Generating Schedule III Report...</div>;
    if (error) return <div className="error-message">{error}</div>;
    if (!companyId) return null;
    if (reportData.length === 0) return <div className="company-info-container"><p>No asset data available to generate the report for the selected year.</p></div>;

    const getHeaderClass = (key: keyof ScheduleIIIRow) => {
        const classes = ['sortable'];
        if (key !== 'assetType') classes.push('text-right');
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        return classes.join(' ');
    };

    const getColumnClass = (key: keyof ScheduleIIIRow) => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if (key !== 'assetType') classes.push('text-right');
        return classes.join(' ');
    };

    return (
        <div className="report-view">
            <div className="view-header">
                <h2>Schedule III Report for FY {year}</h2>
                <div className="actions">
                    <button className="btn btn-csv" onClick={handleExportCSV}><DownloadIcon /> Export to CSV</button>
                    <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export to Excel</button>
                </div>
            </div>
            <div className="table-container">
                <table>
                    <thead>
                        <tr>
                            <th className={getHeaderClass('assetType')} onClick={() => requestSort('assetType')}>Asset Type{getSortIndicator('assetType')}</th>
                            <th className={getHeaderClass('assetCount')} onClick={() => requestSort('assetCount')}>No. of Assets{getSortIndicator('assetCount')}</th>
                            <th className={getHeaderClass('openingGross')} onClick={() => requestSort('openingGross')}>Gross Block - Opening{getSortIndicator('openingGross')}</th>
                            <th className={getHeaderClass('additionsGross')} onClick={() => requestSort('additionsGross')}>Gross Block - Additions{getSortIndicator('additionsGross')}</th>
                            <th className={getHeaderClass('deletionsGross')} onClick={() => requestSort('deletionsGross')}>Gross Block - Deletions{getSortIndicator('deletionsGross')}</th>
                            <th className={getHeaderClass('closingGross')} onClick={() => requestSort('closingGross')}>Gross Block - Closing{getSortIndicator('closingGross')}</th>
                            <th className={getHeaderClass('openingDepreciation')} onClick={() => requestSort('openingDepreciation')}>Depreciation - Opening{getSortIndicator('openingDepreciation')}</th>
                            <th className={getHeaderClass('additionsDepreciation')} onClick={() => requestSort('additionsDepreciation')}>Depreciation - For the Year{getSortIndicator('additionsDepreciation')}</th>
                            <th className={getHeaderClass('deletionsDepreciation')} onClick={() => requestSort('deletionsDepreciation')}>Depreciation - On Disposals{getSortIndicator('deletionsDepreciation')}</th>
                            <th className={getHeaderClass('closingDepreciation')} onClick={() => requestSort('closingDepreciation')}>Depreciation - Closing{getSortIndicator('closingDepreciation')}</th>
                            <th className={getHeaderClass('openingNetBlock')} onClick={() => requestSort('openingNetBlock')}>Net Block - Opening{getSortIndicator('openingNetBlock')}</th>
                            <th className={getHeaderClass('closingNetBlock')} onClick={() => requestSort('closingNetBlock')}>Net Block - Closing{getSortIndicator('closingNetBlock')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {sortedReportData.map((row) => (
                            <tr key={row.assetType} onDoubleClick={() => handleRowDoubleClick(row.assetType)} onClick={() => setSelectedRowId(row.assetType)} className={`clickable-row ${row.assetType === selectedRowId ? 'selected-row' : ''}`}>
                                <td className={getColumnClass('assetType')}>{row.assetType}</td>
                                <td className={getColumnClass('assetCount')}>{formatIndianNumber(row.assetCount)}</td>
                                <td className={getColumnClass('openingGross')}>{formatIndianNumber(row.openingGross)}</td>
                                <td className={getColumnClass('additionsGross')}>{formatIndianNumber(row.additionsGross)}</td>
                                <td className={getColumnClass('deletionsGross')}>{formatIndianNumber(row.deletionsGross)}</td>
                                <td className={getColumnClass('closingGross')}>{formatIndianNumber(row.closingGross)}</td>
                                <td className={getColumnClass('openingDepreciation')}>{formatIndianNumber(row.openingDepreciation)}</td>
                                <td className={getColumnClass('additionsDepreciation')}>{formatIndianNumber(row.additionsDepreciation)}</td>
                                <td className={getColumnClass('deletionsDepreciation')}>{formatIndianNumber(row.deletionsDepreciation)}</td>
                                <td className={getColumnClass('closingDepreciation')}>{formatIndianNumber(row.closingDepreciation)}</td>
                                <td className={getColumnClass('openingNetBlock')}>{formatIndianNumber(row.openingNetBlock)}</td>
                                <td className={getColumnClass('closingNetBlock')}>{formatIndianNumber(row.closingNetBlock)}</td>
                            </tr>
                        ))}
                    </tbody>
                    {totals && (
                        <tfoot>
                            <tr>
                                <td className={getColumnClass('assetType')}>{totals.assetType}</td>
                                <td className={getColumnClass('assetCount')}>{formatIndianNumber(totals.assetCount)}</td>
                                <td className={getColumnClass('openingGross')}>{formatIndianNumber(totals.openingGross)}</td>
                                <td className={getColumnClass('additionsGross')}>{formatIndianNumber(totals.additionsGross)}</td>
                                <td className={getColumnClass('deletionsGross')}>{formatIndianNumber(totals.deletionsGross)}</td>
                                <td className={getColumnClass('closingGross')}>{formatIndianNumber(totals.closingGross)}</td>
                                <td className={getColumnClass('openingDepreciation')}>{formatIndianNumber(totals.openingDepreciation)}</td>
                                <td className={getColumnClass('additionsDepreciation')}>{formatIndianNumber(totals.additionsDepreciation)}</td>
                                <td className={getColumnClass('deletionsDepreciation')}>{formatIndianNumber(totals.deletionsDepreciation)}</td>
                                <td className={getColumnClass('closingDepreciation')}>{formatIndianNumber(totals.closingDepreciation)}</td>
                                <td className={getColumnClass('openingNetBlock')}>{formatIndianNumber(totals.openingNetBlock)}</td>
                                <td className={getColumnClass('closingNetBlock')}>{formatIndianNumber(totals.closingNetBlock)}</td>
                            </tr>
                        </tfoot>
                    )}
                </table>
            </div>
            <ScheduleIIIDetailModal
                isOpen={!!drillDownAssetType}
                onClose={() => setDrillDownAssetType(null)}
                assetType={drillDownAssetType}
                data={drillDownData}
                year={year}
                companyId={companyId}
                companyName={companyName}
                showAlert={showAlert}
            />
        </div>
    );
}

