# FAR Sighted v2.0 - Professional Asset Management System
## Multi-Database Architecture for Enhanced Security and Scalability

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/your-repo/far-sighted)
[![Architecture](https://img.shields.io/badge/architecture-Multi--Database-green.svg)](docs/MULTI_DATABASE_IMPLEMENTATION.md)
[![License](https://img.shields.io/badge/license-Apache%202.0-orange.svg)](LICENSE)

### 🎯 **Overview**

FAR Sighted is a professional Fixed Asset Register (FAR) management system designed for Chartered Accountants and accounting firms. Version 2.0 introduces a revolutionary **Multi-Database Architecture** that provides complete data isolation between companies, enhanced security, and professional-grade compliance capabilities.

---

## 🏗️ **Architecture Highlights**

### **Multi-Database Structure**
```
📁 Database Architecture
├── 🗄️ master.db                    # Global users & company registry
├── 📂 companies/                   # Isolated company databases
│   ├── 🏢 c123-ABC_Ltd/company.db   # Company A's isolated data
│   ├── 🏢 c456-XYZ_Corp/company.db  # Company B's isolated data
│   └── 🏢 ...                       # Additional companies
└── 📋 far_sighted.db               # Original (auto-backed up)
```

### **Key Benefits**
- ✅ **Complete Data Isolation** - No cross-company data access possible
- ✅ **Enhanced Security** - Company-specific audit trails and access control
- ✅ **Professional Compliance** - Separate client data management for CA practices
- ✅ **Horizontal Scalability** - Each company database scales independently
- ✅ **Granular Backup/Restore** - Company-specific disaster recovery

---

## 🚀 **Quick Start**

### **Prerequisites**
- **Node.js** v16.0.0 or higher
- **npm** (latest version)
- **Windows** (for batch scripts)
- **Available Ports**: 9090 (frontend), 3001 (backend)

### **1. Quick Installation**
```batch
# Clone the repository
git clone <repository-url>
cd far-sighted

# Complete system setup (recommended)
update-system.bat
```

### **2. Manual Installation**
```batch
# Install dependencies
npm install
cd backend && npm install && cd ..

# Run database migration (if upgrading from v1.x)
migrate-database.bat

# Start the application
restart-far-app.bat
```

### **3. Access the Application**
- **Frontend**: http://localhost:9090
- **Backend API**: http://localhost:3001/api
- **Health Check**: http://localhost:3001/api/health

---

## 📋 **Management Scripts**

### **Daily Operations**
| Script | Purpose | Usage |
|--------|---------|-------|
| `restart-far-app.bat` | **Complete restart** | Daily development work |
| `start-far-app.bat` | **Clean startup** | When no processes running |
| `kill-all-processes.bat` | **Stop all services** | Clean shutdown |
| `check-status.bat` | **System health check** | Troubleshooting |

### **Setup & Migration**
| Script | Purpose | Usage |
|--------|---------|-------|
| `update-system.bat` | **Complete system update** | First-time setup |
| `migrate-database.bat` | **Database migration** | Converting from v1.x |

### **Quick Commands**
```batch
# Daily use
restart-far-app.bat

# Check everything is working
check-status.bat

# Clean shutdown
kill-all-processes.bat
```

---

## 🏢 **Professional Features**

### **Chartered Accountant Practice Management**
- **Client Data Isolation**: Complete separation between client companies
- **Professional Audit Trails**: Detailed logging per company engagement
- **Compliance Ready**: Meets professional accounting standards
- **Backup Granularity**: Individual client data backup and restore

### **Fixed Asset Management**
- **Schedule II Compliance**: Statutory depreciation rates per Companies Act
- **Multiple Depreciation Methods**: WDV, SLM with automatic calculations
- **Financial Year Management**: Multi-year depreciation tracking
- **Asset Classification**: Comprehensive grouping and sub-grouping

### **Reporting & Analytics**
- **Schedule III Reports**: Companies Act compliant reporting
- **Asset Group Analysis**: Comprehensive asset categorization
- **Depreciation Calculations**: Automated with audit trail
- **Export Capabilities**: Excel export for all reports

---

## 🔧 **Technical Specifications**

### **Frontend Stack**
- **Framework**: React 18.2.0 with TypeScript
- **Build Tool**: Vite 6.2.0 (Development Server)
- **Styling**: TailwindCSS with professional themes
- **Port**: 9090 (configurable)

### **Backend Stack**
- **Runtime**: Node.js with Express.js framework
- **Database**: SQLite3 with Multi-Database architecture
- **Authentication**: bcrypt with session management
- **API**: RESTful with comprehensive error handling
- **Port**: 3001 (configurable)

### **Database Design**
- **Master Database**: Users, companies registry, global settings
- **Company Databases**: Assets, financial years, audit logs, company-specific data
- **Migration System**: Automated conversion from single to multi-database
- **Backup System**: Individual and system-wide backup capabilities

---

## 📊 **System Requirements**

### **Minimum Requirements**
- **OS**: Windows 10 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 1GB available space
- **Network**: LAN access support for multi-PC usage

### **Development Requirements**
- **Node.js**: v16.0.0 or higher
- **npm**: Latest version
- **Git**: For version control
- **Code Editor**: VS Code recommended

---

## 🛡️ **Security Features**

### **Data Protection**
- **Isolation**: Complete database separation per company
- **Access Control**: Role-based user management (Admin, Data Entry, Report Viewer)
- **Audit Trails**: Comprehensive logging of all user actions
- **Session Management**: Secure authentication with timeout

### **Professional Compliance**
- **CA Standards**: Meets Chartered Accountant practice requirements
- **Audit Trail**: Complete transaction history per company
- **Data Integrity**: Foreign key constraints and validation
- **Backup Security**: Encrypted backup capabilities

---

## 📚 **Documentation**

### **Implementation Guides**
- [`MULTI_DATABASE_IMPLEMENTATION.md`](MULTI_DATABASE_IMPLEMENTATION.md) - Technical implementation details
- [`MIGRATION_DEPLOYMENT_GUIDE.md`](MIGRATION_DEPLOYMENT_GUIDE.md) - Complete deployment guide
- [`BATCH_SCRIPTS_GUIDE.md`](BATCH_SCRIPTS_GUIDE.md) - Management scripts documentation

### **Status Reports**
- [`IMPLEMENTATION_STATUS.md`](IMPLEMENTATION_STATUS.md) - Current system status
- [`API_REFERENCE.md`](API_REFERENCE.md) - Complete API documentation

---

## 🔄 **Migration from Version 1.x**

### **Automatic Migration**
The system includes an automated migration tool that safely converts from the old single-database structure to the new multi-database architecture:

```batch
# Check if migration is needed
check-status.bat

# Run interactive migration
migrate-database.bat

# Verify migration success
check-status.bat
```

### **Migration Safety**
- ✅ **Original database backed up** before any changes
- ✅ **Atomic process** - all-or-nothing migration
- ✅ **Data integrity verification** during migration
- ✅ **Rollback capability** if needed

---

## 🚨 **Troubleshooting**

### **Common Issues**

**Port Conflicts**
```batch
kill-all-processes.bat
restart-far-app.bat
```

**Migration Required**
```batch
migrate-database.bat
```

**Backend Not Responding**
```batch
check-status.bat
# Check console windows for error messages
```

**Frontend Won't Load**
```batch
# Verify Node.js installation
node --version
npm --version

# Clean restart
restart-far-app.bat
```

### **Support Resources**
- Check console windows for detailed error logs
- Review `check-status.bat` output for system state
- Consult documentation files for detailed guidance
- Monitor health endpoint: http://localhost:3001/api/health

---

## 🏆 **Professional Benefits**

### **For Accounting Practices**
- **Client Trust**: Demonstrated data security and isolation
- **Compliance**: Simplified regulatory compliance per client
- **Efficiency**: Faster operations and improved workflow
- **Scalability**: Support unlimited client company growth

### **For Auditors**
- **Data Integrity**: Complete audit trails per engagement
- **Independence**: Separate database per client
- **Reporting**: Professional-grade reports and analytics
- **Security**: Enhanced data protection and access control

---

## 📝 **License**

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

---

## 🤝 **Support**

### **Professional Support**
For professional support and implementation assistance:
- **Email**: [Contact Information]
- **Documentation**: Review included markdown files
- **System Status**: Use `check-status.bat` for diagnostics

### **Development Support**
- **Issues**: Report via project repository
- **Documentation**: Comprehensive guides included
- **Health Monitoring**: Built-in health check endpoints

---

## 🎯 **Version History**

### **v2.0.0 - Multi-Database Architecture (Current)**
- ✅ Complete database separation per company
- ✅ Enhanced security and compliance features
- ✅ Professional-grade audit trails
- ✅ Automated migration system
- ✅ Comprehensive management tools

### **v1.x - Single Database (Legacy)**
- Basic asset management functionality
- Single shared database for all companies
- Limited security and isolation

---

**🌟 FAR Sighted v2.0 - Professional Asset Management with Enterprise-Grade Multi-Database Architecture**

*Developed by Professional Chartered Accountant Technology Services*  
*Meeting the highest standards for accounting practice management*