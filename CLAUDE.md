# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

FAR Sighted is a professional Fixed Asset Register (FAR) management system designed for Chartered Accountants and accounting firms. Version 2.0 features a Multi-Database Architecture that provides complete data isolation between companies, enhanced security, and professional-grade compliance capabilities.

## Development Commands

### Frontend (React + TypeScript + Vite)
- **Development**: `npm run dev` (runs on port 9090)
- **Build**: `npm run build`
- **Preview**: `npm run preview`

### Backend (Node.js + Express + SQLite)
- **Development**: `npm run backend` or `cd backend && npm run dev` (runs on port 8090)
- **Production**: `npm run backend:start` or `cd backend && npm start`
- **Database Init**: `npm run backend:init` or `cd backend && npm run init-db`

### Full Stack Development
- **Development**: `npm run dev:full` (runs both frontend and backend concurrently)
- **Production**: `npm run start:full`

### Management Scripts (Windows Batch Files)
- **Complete restart**: `restart-far-app.bat`
- **Clean startup**: `start-far-app.bat`
- **System health check**: `check-status.bat`
- **Stop all services**: `kill-all-processes.bat`
- **Complete system update**: `update-system.bat`
- **Database migration**: `migrate-database.bat`

## Architecture Overview

### Multi-Database Structure
```
📁 Database Architecture
├── 🗄️ backend/database/master.db           # Global users & company registry
├── 📂 backend/database/companies/          # Isolated company databases
│   ├── 🏢 c123-ABC_Ltd/company.db         # Company A's isolated data
│   ├── 🏢 c456-XYZ_Corp/company.db        # Company B's isolated data
│   └── 🏢 ...                             # Additional companies
└── 📋 backend/database/far_sighted.db     # Original (auto-backed up)
```

### Key Services
- **DatabaseManager** (`backend/services/database-manager/DatabaseManager.js`): Master database orchestration
- **CompanyDatabaseService** (`backend/services/database-manager/CompanyDatabaseService.js`): Individual company operations
- **Multi-Database Service** (`backend/services/database-new.js`): Multi-company database management
- **Backup Service** (`backend/services/backup-service.js`): Automated backup system

### Frontend Architecture
- **React 18.2.0** with TypeScript
- **Vite 6.2.0** for development and building
- **Main Entry**: `pages/index.tsx`
- **API Layer**: `lib/api.ts`
- **Types**: `lib/types.ts` and `lib/db-server.ts`
- **Components**: Organized in root directory (e.g., `AppHeader.tsx`, `Navigation.tsx`)
- **Pages**: In `pages/` directory (e.g., `AssetRecords.tsx`, `CompanyInfo.tsx`)

### Backend Architecture
- **Express.js** server on port 8090
- **Route Structure**: `backend/routes/` with company-specific routing
  - `companies-new.js`: Multi-database company management
  - `assets-new.js`: Multi-database asset operations
  - `users-new.js`: Multi-database user management
- **Database Migration**: `backend/scripts/migrate-database.js`

## Port Configuration
- **Frontend Development**: 9090 (Vite dev server)
- **Backend API**: 8090 (Express server)
- **Frontend Production Preview**: 9090 (via Vite preview)

## Testing and Verification
- **API Health Check**: `http://localhost:8090/api/health`
- **System Status**: `backend/scripts/check-*.js` files
- **Migration Status**: `npm run status` in backend directory

## Development Workflow
1. Use `restart-far-app.bat` for daily development work
2. Check system status with `check-status.bat` when troubleshooting
3. Frontend runs on port 9090, backend on port 8090
4. Database changes should use the multi-database services in `backend/services/database-manager/`
5. Always test migrations with dry-run before applying: `npm run migrate:dry-run` in backend

## Key Features
- **Complete Data Isolation**: Separate database per company
- **Professional Compliance**: CA practice standards compliance
- **Asset Management**: Schedule II/III compliance, multiple depreciation methods
- **User Management**: Role-based access (Admin, Data Entry, Report Viewer)
- **Backup System**: Individual and system-wide backup capabilities
- **Audit Trails**: Comprehensive logging per company

## Important Notes
- This is a Windows-focused application with batch script management
- The system uses SQLite3 with a sophisticated multi-database architecture
- Company data is completely isolated - no cross-company access possible
- Migration from v1.x single-database to v2.0 multi-database is automated
- All user authentication and session management is handled by the backend