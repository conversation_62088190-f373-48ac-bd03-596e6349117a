import express from 'express';
import bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import dbService from '../services/database.js';

const router = express.Router();

// Get all users
router.get('/', async (req, res) => {
    try {
        const users = await dbService.all(
            `SELECT 
                id, 
                username, 
                role, 
                has_saved_recovery_key as hasSaved<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                created_at as createdAt
            FROM users ORDER BY username`
        );
        res.json(users);
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ error: 'Failed to fetch users' });
    }
});

// Get user by ID
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const user = await dbService.get(
            `SELECT 
                id, 
                username, 
                role, 
                has_saved_recovery_key as hasSaved<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                created_at as createdAt
            FROM users WHERE id = ?`, [id]
        );
        
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        res.json(user);
    } catch (error) {
        console.error('Error fetching user:', error);
        res.status(500).json({ error: 'Failed to fetch user' });
    }
});

// User login
router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        
        if (!username || !password) {
            return res.status(400).json({ error: 'Username and password required' });
        }
        
        const user = await dbService.get(
            'SELECT * FROM users WHERE username = ?', [username.toLowerCase()]
        );
        
        if (!user) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        const isValid = await bcrypt.compare(password, user.password_hash);
        if (!isValid) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        // Check if this is the only admin (for admin welcome modal)
        const adminCount = await dbService.get(
            'SELECT COUNT(*) as count FROM users WHERE role = ?', ['Admin']
        );
        
        const showAdminWelcome = user.role === 'Admin' && adminCount.count < 2;
        
        // Remove sensitive data before sending
        const { password_hash, recovery_key_hash, ...safeUser } = user;
        const userResponse = {
            id: safeUser.id,
            username: safeUser.username,
            role: safeUser.role,
            hasSavedRecoveryKey: safeUser.has_saved_recovery_key
        };
        
        res.json({ 
            user: userResponse, 
            showAdminWelcome 
        });
    } catch (error) {
        console.error('Error during login:', error);
        res.status(500).json({ error: 'Login failed' });
    }
});

// Add new user
router.post('/', async (req, res) => {
    try {
        const { username, password, role } = req.body;
        
        if (!username || !password || !role) {
            return res.status(400).json({ error: 'Username, password, and role required' });
        }
        
        // Check if username already exists
        const existingUser = await dbService.get(
            'SELECT id FROM users WHERE username = ?', [username.toLowerCase()]
        );
        
        if (existingUser) {
            return res.status(409).json({ error: 'Username already exists' });
        }
        
        // Hash password and generate recovery key
        const passwordHash = await bcrypt.hash(password, 10);
        const recoveryKey = `rk_${Math.random().toString(36).substring(2, 15)}`;
        const recoveryKeyHash = await bcrypt.hash(recoveryKey, 10);
        
        const userId = uuidv4();
        
        await dbService.run(
            `INSERT INTO users (id, username, password_hash, role, recovery_key_hash, has_saved_recovery_key)
             VALUES (?, ?, ?, ?, ?, ?)`,
            [userId, username.toLowerCase(), passwordHash, role, recoveryKeyHash, false]
        );
        
        const newUser = {
            id: userId,
            username: username.toLowerCase(),
            role: role,
            hasSavedRecoveryKey: false
        };
        
        res.status(201).json({
            user: newUser,
            recoveryKey: recoveryKey
        });
    } catch (error) {
        console.error('Error creating user:', error);
        res.status(500).json({ error: 'Failed to create user' });
    }
});

// Update user
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { password, role } = req.body;
        
        const user = await dbService.get('SELECT * FROM users WHERE id = ?', [id]);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        let recoveryKey = null;
        
        if (password) {
            // Update password and generate new recovery key
            const passwordHash = await bcrypt.hash(password, 10);
            recoveryKey = `rk_${Math.random().toString(36).substring(2, 15)}`;
            const recoveryKeyHash = await bcrypt.hash(recoveryKey, 10);
            
            await dbService.run(
                `UPDATE users SET 
                    password_hash = ?, 
                    recovery_key_hash = ?, 
                    has_saved_recovery_key = ?,
                    role = COALESCE(?, role),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?`,
                [passwordHash, recoveryKeyHash, false, role, id]
            );
        } else if (role) {
            // Update only role
            await dbService.run(
                'UPDATE users SET role = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [role, id]
            );
        }
        
        res.json({ recoveryKey });
    } catch (error) {
        console.error('Error updating user:', error);
        res.status(500).json({ error: 'Failed to update user' });
    }
});

// Verify admin password
router.post('/:id/verify-password', async (req, res) => {
    try {
        const { id } = req.params;
        const { password } = req.body;
        
        if (!password) {
            return res.status(400).json({ error: 'Password required' });
        }
        
        const user = await dbService.get('SELECT password_hash FROM users WHERE id = ?', [id]);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        const isValid = await bcrypt.compare(password, user.password_hash);
        res.json(isValid);
    } catch (error) {
        console.error('Error verifying password:', error);
        res.status(500).json({ error: 'Failed to verify password' });
    }
});

// Password recovery
router.post('/:username/recover', async (req, res) => {
    try {
        const { username } = req.params;
        const { recoveryKey, newPassword } = req.body;
        
        if (!recoveryKey || !newPassword) {
            return res.status(400).json({ error: 'Recovery key and new password required' });
        }
        
        const user = await dbService.get(
            'SELECT * FROM users WHERE username = ?', [username.toLowerCase()]
        );
        
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        // Verify recovery key
        const isValidRecovery = await bcrypt.compare(recoveryKey, user.recovery_key_hash);
        if (!isValidRecovery) {
            return res.status(401).json({ error: 'Invalid recovery key' });
        }
        
        // Update password and generate new recovery key
        const newPasswordHash = await bcrypt.hash(newPassword, 10);
        const newRecoveryKey = `rk_${Math.random().toString(36).substring(2, 15)}`;
        const newRecoveryKeyHash = await bcrypt.hash(newRecoveryKey, 10);
        
        await dbService.run(
            `UPDATE users SET 
                password_hash = ?, 
                recovery_key_hash = ?, 
                has_saved_recovery_key = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?`,
            [newPasswordHash, newRecoveryKeyHash, false, user.id]
        );
        
        res.json({ recoveryKey: newRecoveryKey });
    } catch (error) {
        console.error('Error during password recovery:', error);
        res.status(500).json({ error: 'Password recovery failed' });
    }
});

// Confirm recovery key saved
router.post('/:id/confirm-recovery-key', async (req, res) => {
    try {
        const { id } = req.params;
        
        const user = await dbService.get('SELECT id FROM users WHERE id = ?', [id]);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        await dbService.run(
            'UPDATE users SET has_saved_recovery_key = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [true, id]
        );
        
        res.json({ message: 'Recovery key confirmation updated' });
    } catch (error) {
        console.error('Error confirming recovery key:', error);
        res.status(500).json({ error: 'Failed to confirm recovery key' });
    }
});

// Delete user
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const user = await dbService.get('SELECT * FROM users WHERE id = ?', [id]);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        // Check if this is the last admin
        if (user.role === 'Admin') {
            const adminCount = await dbService.get(
                'SELECT COUNT(*) as count FROM users WHERE role = ?', ['Admin']
            );
            
            if (adminCount.count <= 1) {
                return res.status(400).json({ 
                    error: 'Cannot delete the last admin user' 
                });
            }
        }
        
        await dbService.run('DELETE FROM users WHERE id = ?', [id]);
        res.json({ message: 'User deleted successfully' });
    } catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({ error: 'Failed to delete user' });
    }
});

export default router;
