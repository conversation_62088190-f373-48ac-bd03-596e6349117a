/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useCallback } from 'react';
import { api } from './lib/api';
import type { User } from './lib/db-server';
import { DataViewProps } from './lib/types';
import { SaveIcon, XIcon } from './Icons';

const ROLES: User['role'][] = ['Admin', 'Data Entry', 'Report Viewer'];

export function AddUser({ editingUserId, companyId, onUserFormSuccess, loggedInUser, showAlert }: DataViewProps) {
    const [formData, setFormData] = useState({
        username: '',
        password: '',
        confirmPassword: '',
        role: ROLES[1] // Default to 'Data Entry'
    });
    const [loading, setLoading] = useState(true);
    const [isSaving, setIsSaving] = useState(false);
    const [error, setError] = useState('');
    const [originalUsername, setOriginalUsername] = useState('');

    const mode = editingUserId ? 'edit' : 'add';

    const fetchUser = useCallback(async () => {
        if (!editingUserId) {
            setLoading(false);
            return;
        }
        setLoading(true);
        try {
            const user = await api.getUser(editingUserId);
            if (user) {
                setFormData({
                    username: user.username,
                    password: '',
                    confirmPassword: '',
                    role: user.role
                });
                setOriginalUsername(user.username);
            } else {
                setError('User not found.');
            }
        } catch (err) {
            setError('Failed to fetch user data.');
        } finally {
            setLoading(false);
        }
    }, [editingUserId]);

    useEffect(() => {
        // Reset form when editingUserId changes (e.g., navigating from edit to add)
        setError('');
        setIsSaving(false);
        if (editingUserId) {
            fetchUser();
        } else {
            setFormData({ username: '', password: '', confirmPassword: '', role: ROLES[1] });
            setOriginalUsername('');
            setLoading(false);
        }
    }, [editingUserId, fetchUser]);
    
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');

        if (formData.password !== formData.confirmPassword) {
            setError("Passwords do not match.");
            return;
        }
        if (mode === 'add' && !formData.password) {
            setError("Password is required for a new user.");
            return;
        }

        setIsSaving(true);
        try {
            let result;
            if (mode === 'add') {
                if (!companyId) {
                    throw new Error('Company ID is required to create users');
                }
                result = await api.addUser({
                    username: formData.username,
                    password: formData.password,
                    role: formData.role as User['role'],
                    companyId: companyId,
                });
            } else {
                const updateData: Partial<User> = { role: formData.role as User['role'] };
                if (formData.password) {
                    updateData.password = formData.password;
                }
                result = await api.updateUser(editingUserId!, updateData);
            }

            if (loggedInUser) {
                const action = mode === 'add' ? 'CREATE_USER' : 'UPDATE_USER';
                let details = `${mode === 'add' ? 'Created' : 'Updated'} user "${formData.username}".`;
                if(mode === 'edit' && formData.password) details += " Password changed.";
                await api.addAuditLog({ userId: loggedInUser.id, username: loggedInUser.username, action, details });
            }
            
            const newUserId = mode === 'add' ? result.user.id : editingUserId!;
            const recoveryKey = mode === 'add' ? result.recoveryKey : result.recoveryKey;
            
            showAlert("Success", `User ${mode === 'add' ? 'created' : 'updated'} successfully.`, 'success');

            if (onUserFormSuccess) onUserFormSuccess(recoveryKey, newUserId, formData.username);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An unknown error occurred.');
            setIsSaving(false);
        }
    };
    
    const handleCancel = () => {
        if(onUserFormSuccess) onUserFormSuccess(null, '', '');
    }

    if (loading) return <div className="loading-indicator">Loading...</div>;

    return (
        <>
            <div className="view-header">
                <h2>{mode === 'add' ? 'Add New User' : `Edit User: ${originalUsername}`}</h2>
            </div>
            <div className="settings-container">
                <form onSubmit={handleSubmit}>
                    <div className="form-grid">
                        <div className="form-group">
                            <label htmlFor="username">Username<span className="required-asterisk">*</span></label>
                            <input type="text" id="username" name="username" value={formData.username} onChange={handleChange} required disabled={mode === 'edit'}/>
                        </div>

                        <div className="form-group">
                            <label htmlFor="role">Role<span className="required-asterisk">*</span></label>
                            <select id="role" name="role" value={formData.role} onChange={handleChange} className="table-select">
                                {ROLES.map(role => <option key={role} value={role}>{role}</option>)}
                            </select>
                        </div>
                        
                        <div className="form-group">
                            <label htmlFor="password">Password{mode === 'add' && <span className="required-asterisk">*</span>}</label>
                            <input type="password" id="password" name="password" value={formData.password} onChange={handleChange} placeholder={mode === 'edit' ? 'Leave blank to keep current' : ''} />
                        </div>

                        <div className="form-group">
                            <label htmlFor="confirmPassword">Confirm Password{mode === 'add' && <span className="required-asterisk">*</span>}</label>
                            <input type="password" id="confirmPassword" name="confirmPassword" value={formData.confirmPassword} onChange={handleChange} />
                        </div>
                    </div>

                    {error && <p style={{ color: 'var(--accent-danger)', marginTop: '1rem' }}>{error}</p>}
                    
                    <div className="modal-actions">
                        <button type="button" className="btn btn-warning" onClick={handleCancel} disabled={isSaving}><XIcon /> Cancel</button>
                        <button type="submit" className="btn btn-primary" disabled={isSaving}>
                            <SaveIcon /> {isSaving ? 'Saving...' : (mode === 'add' ? 'Create User' : 'Save Changes')}
                        </button>
                    </div>
                </form>
            </div>
        </>
    );
}
