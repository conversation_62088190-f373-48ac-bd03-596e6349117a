/**
 * <PERSON>ript to fix user datatype issues in company databases
 * This script will update boolean fields to use proper integer values
 */

import DatabaseManager from '../services/database-manager/DatabaseManager.js';

async function fixUserDatatypes() {
    const dbManager = new DatabaseManager();
    await dbManager.init();
    
    console.log('🔄 Fixing user datatype issues...');
    
    try {
        // Get all companies
        const companies = await dbManager.getAllMasterQuery('SELECT * FROM companies');
        console.log(`📊 Found ${companies.length} companies to fix`);
        
        for (const company of companies) {
            console.log(`\n📁 Fixing users for: ${company.company_name}`);
            
            try {
                // Get company database service
                const companyDb = await dbManager.getCompanyDatabase(company.id);
                
                // Check if users table exists
                const tableExists = await companyDb.get(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='users'"
                );
                
                if (!tableExists) {
                    console.log(`  ⚠️  No users table found, skipping...`);
                    continue;
                }
                
                // Get all users
                const users = await companyDb.all('SELECT * FROM users');
                console.log(`  👥 Found ${users.length} users`);
                
                if (users.length === 0) {
                    console.log(`  ⚠️  No users found, skipping...`);
                    continue;
                }
                
                // Update each user to fix boolean fields
                for (const user of users) {
                    try {
                        await companyDb.run(`
                            UPDATE users SET 
                                has_saved_recovery_key = ?,
                                is_active = ?,
                                must_change_password = ?
                            WHERE id = ?
                        `, [
                            user.has_saved_recovery_key ? 1 : 0,
                            user.is_active ? 1 : 0,
                            user.must_change_password ? 1 : 0,
                            user.id
                        ]);
                        
                        console.log(`    ✅ Fixed user: ${user.username}`);
                    } catch (error) {
                        console.log(`    ❌ Failed to fix user ${user.username}: ${error.message}`);
                    }
                }
                
                console.log(`  ✅ Completed fixing users for ${company.company_name}`);
                
            } catch (error) {
                console.log(`  ❌ Error processing company ${company.company_name}: ${error.message}`);
            }
        }
        
        console.log('\n🎉 User datatype fixing completed!');
        
    } catch (error) {
        console.error('❌ Error fixing user datatypes:', error);
    }
}

// Run the script
fixUserDatatypes().catch(console.error);
