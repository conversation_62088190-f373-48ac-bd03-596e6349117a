/**
 * DATABASE CONTENT CHECKER
 * Quick check of what's actually in the database
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const dbPath = join(__dirname, '../database/far_sighted.db');

console.log('📊 DATABASE CONTENT CHECK');
console.log('========================');

const checkDatabase = async () => {
    const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
            console.error('❌ Error opening database:', err.message);
            process.exit(1);
        }
    });

    const runQuery = (sql, params = []) => {
        return new Promise((resolve, reject) => {
            db.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    };

    try {
        // Check companies
        const companies = await runQuery('SELECT * FROM companies WHERE id = ?', ['c1001']);
        console.log(`\n🏢 Company: ${companies.length > 0 ? companies[0].company_name : 'NOT FOUND'}`);

        // Check assets
        const assets = await runQuery(`
            SELECT record_id, asset_particulars, gross_amount, put_to_use_date, 
                   life_in_years, salvage_percentage, depreciation_method
            FROM assets 
            WHERE company_id = 'c1001' 
            ORDER BY put_to_use_date
        `);

        console.log(`\n🏭 Assets (${assets.length} found):`);
        assets.forEach(asset => {
            console.log(`   ${asset.record_id}: ₹${asset.gross_amount?.toLocaleString()} (${asset.put_to_use_date}, ${asset.life_in_years}Y, ${asset.depreciation_method})`);
        });

        // Check if any have zero or null life
        const zeroLifeAssets = assets.filter(a => !a.life_in_years || a.life_in_years <= 0);
        if (zeroLifeAssets.length > 0) {
            console.log(`\n❌ ISSUE: ${zeroLifeAssets.length} assets with zero/null life:`);
            zeroLifeAssets.forEach(asset => {
                console.log(`   ${asset.record_id}: life = ${asset.life_in_years}`);
            });
        }

        // Quick fix for zero life assets
        if (zeroLifeAssets.length > 0) {
            console.log(`\n🔧 FIXING: Setting default life for assets with zero life...`);
            for (const asset of zeroLifeAssets) {
                let defaultLife = 10; // Default
                if (asset.asset_particulars?.toLowerCase().includes('computer')) defaultLife = 3;
                if (asset.asset_particulars?.toLowerCase().includes('furniture')) defaultLife = 10;
                if (asset.asset_particulars?.toLowerCase().includes('machinery')) defaultLife = 15;
                
                await runQuery(
                    'UPDATE assets SET life_in_years = ? WHERE record_id = ? AND company_id = ?',
                    [defaultLife, asset.record_id, 'c1001']
                );
                console.log(`   ✅ Fixed ${asset.record_id}: life = ${defaultLife} years`);
            }
        }

        // Check financial years
        const fyears = await runQuery('SELECT * FROM financial_years WHERE company_id = ? ORDER BY year_range', ['c1001']);
        console.log(`\n📅 Financial Years (${fyears.length} found):`);
        fyears.forEach(fy => {
            console.log(`   ${fy.year_range} (Locked: ${fy.is_locked})`);
        });

    } catch (error) {
        console.error('❌ Database check error:', error);
    } finally {
        db.close();
    }
};

checkDatabase();