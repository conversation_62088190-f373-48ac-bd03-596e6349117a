#!/usr/bin/env node

/**
 * Check Asset Yearly Data Storage
 * Verifies if first year depreciation data is properly stored in asset_yearly_data table
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database path
const dbPath = join(__dirname, '../database/far_sighted.db');

// Helper function to run queries
const runQuery = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath);
        db.all(sql, params, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
        db.close();
    });
};

async function checkYearlyData() {
    console.log('🔍 CHECKING ASSET YEARLY DATA STORAGE');
    console.log('=====================================\n');

    try {
        // Check if asset_yearly_data table exists
        const tables = await runQuery(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='asset_yearly_data'
        `);

        if (tables.length === 0) {
            console.log('❌ asset_yearly_data table does not exist!');
            return;
        }

        console.log('✅ asset_yearly_data table exists\n');

        // Get all yearly data
        const yearlyData = await runQuery(`
            SELECT ayd.*, a.record_id, a.asset_particulars, a.gross_amount
            FROM asset_yearly_data ayd
            JOIN assets a ON ayd.asset_id = a.id
            WHERE a.company_id = 'c1001'
            ORDER BY a.record_id, ayd.year_range
        `);

        console.log(`📊 Found ${yearlyData.length} yearly data records\n`);

        if (yearlyData.length === 0) {
            console.log('❌ NO YEARLY DATA FOUND!');
            console.log('This explains why first year depreciation is not displayed.\n');
            
            // Check if assets exist
            const assets = await runQuery(`
                SELECT record_id, asset_particulars, gross_amount, put_to_use_date
                FROM assets 
                WHERE company_id = 'c1001'
                ORDER BY record_id
            `);
            
            console.log(`📋 Assets found: ${assets.length}`);
            assets.forEach(asset => {
                console.log(`   ${asset.record_id}: ₹${asset.gross_amount.toLocaleString()} (${asset.put_to_use_date})`);
            });
            
            console.log('\n💡 SOLUTION REQUIRED:');
            console.log('1. Run "Create Next FY" process to generate yearly data');
            console.log('2. Or populate yearly data manually');
            console.log('3. Check if financial year creation process is working');
            
            return;
        }

        // Group by asset
        const assetGroups = {};
        yearlyData.forEach(row => {
            if (!assetGroups[row.record_id]) {
                assetGroups[row.record_id] = [];
            }
            assetGroups[row.record_id].push(row);
        });

        // Display yearly data for each asset
        Object.keys(assetGroups).forEach(recordId => {
            const assetData = assetGroups[recordId];
            const firstRow = assetData[0];
            
            console.log(`🏭 ASSET: ${recordId} - ${firstRow.asset_particulars}`);
            console.log(`   Gross Amount: ₹${firstRow.gross_amount.toLocaleString()}`);
            console.log('   ──────────────────────────────────────────────────');
            
            assetData.forEach(row => {
                console.log(`   📅 ${row.year_range}:`);
                console.log(`      Opening WDV: ₹${row.opening_wdv.toLocaleString()}`);
                console.log(`      Use Days: ${row.use_days}`);
                console.log(`      Depreciation: ₹${row.depreciation_amount.toLocaleString()}`);
                console.log(`      Closing WDV: ₹${row.closing_wdv.toLocaleString()}`);
                console.log(`      Extra Shifts: 2nd=${row.second_shift_days}, 3rd=${row.third_shift_days}`);
                console.log('');
            });
        });

        // Check specifically for first year (2022-2023)
        const firstYearData = yearlyData.filter(row => row.year_range === '2022-2023');
        
        console.log(`🎯 FIRST YEAR (2022-2023) DATA: ${firstYearData.length} records`);
        
        if (firstYearData.length === 0) {
            console.log('❌ NO FIRST YEAR DATA FOUND!');
            console.log('This is the root cause of the display issue.\n');
        } else {
            console.log('✅ First year data exists');
            firstYearData.forEach(row => {
                console.log(`   ${row.record_id}: Depreciation ₹${row.depreciation_amount.toLocaleString()}`);
            });
        }

        // Check financial years
        console.log('\n📅 FINANCIAL YEARS:');
        const financialYears = await runQuery(`
            SELECT year_range, is_locked 
            FROM financial_years 
            WHERE company_id = 'c1001'
            ORDER BY year_range
        `);
        
        financialYears.forEach(fy => {
            const lockStatus = fy.is_locked ? 'Locked' : 'Unlocked';
            const hasData = yearlyData.some(row => row.year_range === fy.year_range);
            const dataStatus = hasData ? '✅ Has Data' : '❌ No Data';
            console.log(`   ${fy.year_range}: ${lockStatus} - ${dataStatus}`);
        });

    } catch (error) {
        console.error('❌ Error checking yearly data:', error);
    }
}

// Run the check
checkYearlyData().then(() => {
    console.log('\n🏁 Yearly data check complete');
}).catch(error => {
    console.error('❌ Script failed:', error);
});
