@echo off
setlocal enabledelayedexpansion

echo =====================================
echo FAR SIGHTED - SYSTEM STATUS CHECK
echo Professional Advisory Services - CA
echo =====================================
echo.

REM Navigate to project directory
cd /d "E:\Projects\FAR Sighted"

echo 🔍 Checking FAR Sighted System Status...
echo.

echo 📁 File Structure Check:
if exist "package.json" (
    echo    ✅ Frontend package.json found
) else (
    echo    ❌ Frontend package.json missing
)

if exist "backend\package.json" (
    echo    ✅ Backend package.json found
) else (
    echo    ❌ Backend package.json missing
)

if exist "backend\services\database-new.js" (
    echo    ✅ Multi-database service found
) else (
    echo    ❌ Multi-database service missing
)

if exist "backend\routes\companies-new.js" (
    echo    ✅ Updated route files found
) else (
    echo    ❌ Updated route files missing
)

if exist "backend\scripts\migrate-database.js" (
    echo    ✅ Migration script found
) else (
    echo    ❌ Migration script missing
)

echo.
echo 📊 Database Structure Check:
if exist "backend\database\far_sighted.db" (
    echo    ✅ Original database found
) else (
    echo    ❌ Original database not found
)

if exist "backend\database\master.db" (
    echo    ✅ Master database found (Multi-DB structure)
) else (
    echo    ⚠️  Master database not found (may need migration)
)

if exist "backend\database\companies\" (
    echo    ✅ Company databases directory found
    for /d %%i in ("backend\database\companies\*") do (
        echo       • Company folder: %%~nxi
    )
) else (
    echo    ⚠️  Company databases directory not found (may need migration)
)

echo.
echo 🌐 Service Status Check:
echo    🔍 Checking if services are running...

REM Check frontend port 9090
netstat -an | find ":9090" | find "LISTENING" >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Frontend service running on port 9090
) else (
    echo    ❌ Frontend service not running (port 9090 free)
)

REM Check backend port 3001
netstat -an | find ":3001" | find "LISTENING" >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Backend service running on port 3001
) else (
    echo    ❌ Backend service not running (port 3001 free)
)

echo.
echo 🏥 Backend Health Check:
if exist "backend\package.json" (
    echo    🔍 Attempting backend health check...
    curl -s http://localhost:3001/api/health >nul 2>&1
    if %errorlevel% == 0 (
        echo    ✅ Backend is responding correctly
        
        echo    📊 Getting system information...
        curl -s http://localhost:3001/api/system-info 2>nul | node -p "try { const data = JSON.parse(require('fs').readFileSync(0)); console.log('       Version: ' + data.version); console.log('       Structure: ' + data.databaseStructure); console.log('       Companies: ' + data.companies.total); } catch(e) { 'Unable to parse system info'; }" 2>nul
        
        echo    🔄 Getting migration status...
        curl -s http://localhost:3001/api/migration-status 2>nul | node -p "try { const data = JSON.parse(require('fs').readFileSync(0)); console.log('       System Ready: ' + (data.systemReady ? 'Yes' : 'No')); console.log('       Migration Needed: ' + (data.needsMigration ? 'Yes' : 'No')); } catch(e) { 'Unable to parse migration status'; }" 2>nul
        
    ) else (
        echo    ❌ Backend health check failed (service may be down)
    )
) else (
    echo    ❌ Cannot check backend health (package.json missing)
)

echo.
echo 🎯 Available Management Scripts:
if exist "restart-far-app.bat" (
    echo    ✅ restart-far-app.bat
) else (
    echo    ❌ restart-far-app.bat missing
)

if exist "start-far-app.bat" (
    echo    ✅ start-far-app.bat
) else (
    echo    ❌ start-far-app.bat missing
)

if exist "kill-all-processes.bat" (
    echo    ✅ kill-all-processes.bat
) else (
    echo    ❌ kill-all-processes.bat missing
)

if exist "migrate-database.bat" (
    echo    ✅ migrate-database.bat
) else (
    echo    ❌ migrate-database.bat missing
)

if exist "update-system.bat" (
    echo    ✅ update-system.bat
) else (
    echo    ❌ update-system.bat missing
)

echo.
echo 📚 Documentation Files:
if exist "MULTI_DATABASE_IMPLEMENTATION.md" (
    echo    ✅ MULTI_DATABASE_IMPLEMENTATION.md
) else (
    echo    ❌ MULTI_DATABASE_IMPLEMENTATION.md missing
)

if exist "MIGRATION_DEPLOYMENT_GUIDE.md" (
    echo    ✅ MIGRATION_DEPLOYMENT_GUIDE.md
) else (
    echo    ❌ MIGRATION_DEPLOYMENT_GUIDE.md missing
)

if exist "IMPLEMENTATION_STATUS.md" (
    echo    ✅ IMPLEMENTATION_STATUS.md
) else (
    echo    ❌ IMPLEMENTATION_STATUS.md missing
)

echo.
echo 📋 System Status Summary:
echo.

REM Determine overall system status
set "system_ready=false"
set "migration_needed=false"
set "services_running=false"

REM Check if services are running
netstat -an | find ":3001" | find "LISTENING" >nul 2>&1
if %errorlevel% == 0 (
    netstat -an | find ":9090" | find "LISTENING" >nul 2>&1
    if %errorlevel% == 0 (
        set "services_running=true"
    )
)

REM Check if multi-db structure exists
if exist "backend\database\master.db" (
    if exist "backend\database\companies\" (
        set "system_ready=true"
    )
)

REM Check if migration is needed
if exist "backend\database\far_sighted.db" (
    if not exist "backend\database\master.db" (
        set "migration_needed=true"
    )
)

if "%services_running%"=="true" (
    echo    🟢 Status: RUNNING
    echo       Both frontend and backend services are active
) else (
    echo    🟡 Status: STOPPED
    echo       Services are not currently running
)

if "%system_ready%"=="true" (
    echo    🟢 Architecture: MULTI-DATABASE
    echo       System is using the new multi-database structure
) else (
    if "%migration_needed%"=="true" (
        echo    🟡 Architecture: MIGRATION REQUIRED
        echo       Old database found, migration needed for multi-database structure
    ) else (
        echo    🟠 Architecture: UNKNOWN
        echo       Unable to determine current database structure
    )
)

echo.
echo 💡 Recommended Actions:
if "%services_running%"=="false" (
    echo    🚀 Start services: run restart-far-app.bat or start-far-app.bat
)

if "%migration_needed%"=="true" (
    echo    🔄 Run migration: migrate-database.bat
)

if "%system_ready%"=="true" (
    if "%services_running%"=="true" (
        echo    ✅ System is ready! Access at: http://localhost:9090
    ) else (
        echo    🚀 Start the application: start-far-app.bat
    )
) else (
    echo    🛠️  Complete setup: update-system.bat
)

echo.
echo 🔗 Quick Links:
echo    • Frontend: http://localhost:9090
echo    • Backend API: http://localhost:3001/api
echo    • Health Check: http://localhost:3001/api/health
echo    • Migration Status: http://localhost:3001/api/migration-status
echo    • System Info: http://localhost:3001/api/system-info
echo.

pause