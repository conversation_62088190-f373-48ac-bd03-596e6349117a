import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import dbService from '../services/database-new.js';

const router = express.Router();

// Get audit logs
router.get('/', async (req, res) => {
    try {
        const { limit = 100, offset = 0 } = req.query;

        await dbService.ensureInitialized();
        const dbManager = dbService.getDatabaseManager();

        const auditLogs = await dbManager.getCompanyQuery(
            `SELECT
                id,
                timestamp,
                user_id as userId,
                username,
                action,
                details,
                created_at as createdAt
            FROM audit_logs
            ORDER BY timestamp DESC, created_at DESC
            LIMIT ? OFFSET ?`,
            [parseInt(limit), parseInt(offset)]
        );

        res.json(auditLogs);
    } catch (error) {
        console.error('Error fetching audit logs:', error);
        res.status(500).json({ error: 'Failed to fetch audit logs' });
    }
});

// Add audit log entry
router.post('/', async (req, res) => {
    try {
        const { userId, username, action, details } = req.body;

        if (!userId || !username || !action) {
            return res.status(400).json({
                error: 'userId, username, and action are required'
            });
        }

        await dbService.ensureInitialized();
        const dbManager = dbService.getDatabaseManager();

        const logId = uuidv4();
        const timestamp = new Date().toISOString();

        await dbManager.runCompanyQuery(
            `INSERT INTO audit_logs (id, timestamp, user_id, username, action, details)
             VALUES (?, ?, ?, ?, ?, ?)`,
            [logId, timestamp, userId, username, action, details || '']
        );

        res.status(201).json({
            message: 'Audit log entry created',
            id: logId
        });
    } catch (error) {
        console.error('Error creating audit log:', error);
        res.status(500).json({ error: 'Failed to create audit log' });
    }
});

// Get audit log statistics
router.get('/stats', async (req, res) => {
    try {
        const stats = await dbService.all(
            `SELECT 
                action,
                COUNT(*) as count,
                DATE(created_at) as date
            FROM audit_logs 
            WHERE created_at >= date('now', '-30 days')
            GROUP BY action, DATE(created_at)
            ORDER BY date DESC, count DESC`
        );
        
        res.json(stats);
    } catch (error) {
        console.error('Error fetching audit log stats:', error);
        res.status(500).json({ error: 'Failed to fetch audit log statistics' });
    }
});

export default router;
