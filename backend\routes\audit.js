import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import dbService from '../services/database-new.js';

const router = express.Router();

// Get audit logs for a specific company
router.get('/:companyId', async (req, res) => {
    try {
        const { companyId } = req.params;
        const { limit = 100, offset = 0 } = req.query;

        await dbService.ensureInitialized();
        await dbService.setCompanyContext(companyId);

        const auditLogs = await dbService.all(
            `SELECT
                id,
                timestamp,
                user_id as userId,
                username,
                action,
                details,
                table_name as tableName,
                record_id as recordId
            FROM audit_logs
            ORDER BY timestamp DESC
            LIMIT ? OFFSET ?`,
            [parseInt(limit), parseInt(offset)]
        );

        res.json(auditLogs);
    } catch (error) {
        console.error('Error fetching audit logs:', error);
        res.status(500).json({ error: 'Failed to fetch audit logs' });
    }
});

// Add audit log entry for a specific company
router.post('/:companyId', async (req, res) => {
    try {
        const { companyId } = req.params;
        const { userId, username, action, details, tableName, recordId } = req.body;

        if (!userId || !username || !action) {
            return res.status(400).json({
                error: 'userId, username, and action are required'
            });
        }

        await dbService.ensureInitialized();
        await dbService.setCompanyContext(companyId);

        await dbService.addAuditLog({
            userId,
            username,
            action,
            details: details || '',
            tableName: tableName || '',
            recordId: recordId || ''
        });

        res.status(201).json({
            message: 'Audit log entry created'
        });
    } catch (error) {
        console.error('Error creating audit log:', error);
        res.status(500).json({ error: 'Failed to create audit log' });
    }
});

// Get audit log statistics
router.get('/stats', async (req, res) => {
    try {
        const stats = await dbService.all(
            `SELECT 
                action,
                COUNT(*) as count,
                DATE(created_at) as date
            FROM audit_logs 
            WHERE created_at >= date('now', '-30 days')
            GROUP BY action, DATE(created_at)
            ORDER BY date DESC, count DESC`
        );
        
        res.json(stats);
    } catch (error) {
        console.error('Error fetching audit log stats:', error);
        res.status(500).json({ error: 'Failed to fetch audit log statistics' });
    }
});

export default router;
