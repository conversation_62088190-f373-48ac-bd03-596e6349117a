import sqlite3 from 'sqlite3';
import bcrypt from 'bcrypt';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database file path
const dbPath = join(__dirname, '../database/far_sighted.db');

// Create database directory if it doesn't exist
import { mkdirSync, existsSync } from 'fs';
const dbDir = join(__dirname, '../database');
if (!existsSync(dbDir)) {
    mkdirSync(dbDir, { recursive: true });
}

console.log('🗄️  Initializing FAR Sighted SQLite Database...');
console.log('📁 Database location:', dbPath);

const initializeDatabase = async () => {
    const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
            console.error('❌ Error opening database:', err.message);
            process.exit(1);
        }
        console.log('✅ Connected to SQLite database');
    });

// SQL Schema Definition
const schema = `
-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Companies Table
CREATE TABLE IF NOT EXISTS companies (
    id TEXT PRIMARY KEY,
    company_name TEXT NOT NULL,
    pan TEXT,
    cin TEXT,
    date_of_incorporation TEXT,
    financial_year_start TEXT NOT NULL,
    financial_year_end TEXT NOT NULL,
    first_date_of_adoption TEXT NOT NULL,
    data_folder_path TEXT,
    address_line1 TEXT,
    address_line2 TEXT,
    city TEXT,
    pin TEXT,
    email TEXT,
    mobile TEXT,
    contact_person TEXT,
    license_valid_upto TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Users Table
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT CHECK(role IN ('Admin', 'Data Entry', 'Report Viewer')) NOT NULL,
    recovery_key_hash TEXT,
    has_saved_recovery_key BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Financial Years Table
CREATE TABLE IF NOT EXISTS financial_years (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id TEXT NOT NULL,
    year_range TEXT NOT NULL, -- e.g., '2023-2024'
    is_locked BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    UNIQUE(company_id, year_range)
);

-- Assets Table
CREATE TABLE IF NOT EXISTS assets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id TEXT NOT NULL,
    record_id TEXT NOT NULL,
    asset_particulars TEXT NOT NULL,
    book_entry_date TEXT,
    put_to_use_date TEXT,
    basic_amount REAL DEFAULT 0,
    duties_taxes REAL DEFAULT 0,
    gross_amount REAL DEFAULT 0,
    vendor TEXT,
    invoice_no TEXT,
    model_make TEXT,
    location TEXT,
    asset_id TEXT,
    remarks TEXT,
    ledger_name_in_books TEXT,
    asset_group TEXT,
    asset_sub_group TEXT,
    schedule_iii_classification TEXT,
    disposal_date TEXT NULL,
    disposal_amount REAL NULL,
    salvage_percentage REAL DEFAULT 5,
    wdv_of_adoption_date REAL NULL,
    is_leasehold BOOLEAN DEFAULT FALSE,
    depreciation_method TEXT CHECK(depreciation_method IN ('SLM', 'WDV')) DEFAULT 'WDV',
    life_in_years REAL DEFAULT 0,
    lease_period REAL NULL,
    scrap_it BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    UNIQUE(company_id, record_id)
);

-- Asset Yearly Data Table (for depreciation calculations by year)
CREATE TABLE IF NOT EXISTS asset_yearly_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    asset_id INTEGER NOT NULL,
    year_range TEXT NOT NULL,
    opening_wdv REAL DEFAULT 0,
    use_days INTEGER DEFAULT 0,
    depreciation_amount REAL DEFAULT 0,
    closing_wdv REAL DEFAULT 0,
    second_shift_days INTEGER DEFAULT 0,
    third_shift_days INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    UNIQUE(asset_id, year_range)
);

-- Statutory Rates Table
CREATE TABLE IF NOT EXISTS statutory_rates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id TEXT NOT NULL,
    is_statutory TEXT,
    tangibility TEXT,
    asset_group TEXT,
    asset_sub_group TEXT,
    extra_shift_depreciation TEXT,
    useful_life_years TEXT,
    schedule_ii_classification TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);

-- Extra Ledgers Table
CREATE TABLE IF NOT EXISTS extra_ledgers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id TEXT NOT NULL,
    ledger_name TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    UNIQUE(company_id, ledger_name)
);

-- License History Table
CREATE TABLE IF NOT EXISTS license_history (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL,
    license_key TEXT NOT NULL,
    valid_from TEXT NOT NULL,
    valid_upto TEXT NOT NULL,
    activated_at TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);

-- Backup Log Table
CREATE TABLE IF NOT EXISTS backup_logs (
    id TEXT PRIMARY KEY,
    timestamp TEXT NOT NULL,
    action TEXT CHECK(action IN ('Backup', 'Restore')) NOT NULL,
    initiated_by TEXT NOT NULL,
    details TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Audit Log Table
CREATE TABLE IF NOT EXISTS audit_logs (
    id TEXT PRIMARY KEY,
    timestamp TEXT NOT NULL,
    user_id TEXT NOT NULL,
    username TEXT NOT NULL,
    action TEXT NOT NULL,
    details TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- App Settings Table
CREATE TABLE IF NOT EXISTS app_settings (
    id INTEGER PRIMARY KEY CHECK(id = 1), -- Ensure only one row
    default_export_path TEXT DEFAULT '',
    idle_timeout_minutes INTEGER DEFAULT 60,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Insert default settings if not exists
INSERT OR IGNORE INTO app_settings (id, default_export_path, idle_timeout_minutes) 
VALUES (1, '', 60);

-- Create Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assets_company_id ON assets(company_id);
CREATE INDEX IF NOT EXISTS idx_assets_record_id ON assets(record_id);
CREATE INDEX IF NOT EXISTS idx_asset_yearly_data_asset_id ON asset_yearly_data(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_yearly_data_year ON asset_yearly_data(year_range);
CREATE INDEX IF NOT EXISTS idx_financial_years_company_id ON financial_years(company_id);
CREATE INDEX IF NOT EXISTS idx_statutory_rates_company_id ON statutory_rates(company_id);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
`;

    // Execute schema
    db.exec(schema, async (err) => {
        if (err) {
            console.error('❌ Error creating database schema:', err.message);
            process.exit(1);
        }
        
        console.log('✅ Database schema created successfully');
        
        // Insert default admin user if no users exist
        const defaultUserQuery = `
            INSERT OR IGNORE INTO users (id, username, password_hash, role, recovery_key_hash, has_saved_recovery_key)
            VALUES (?, ?, ?, ?, ?, ?)
        `;
        
        try {
            // Default admin credentials (password: 'admin123' - should be changed in production)
            const defaultPasswordHash = await bcrypt.hash('admin123', 10);
            const defaultRecoveryHash = await bcrypt.hash('recovery_admin_key', 10);
            
            db.run(defaultUserQuery, [
                'u1',
                'admin',
                defaultPasswordHash,
                'Admin',
                defaultRecoveryHash,
                true
            ], function(err) {
                if (err) {
                    console.error('❌ Error creating default admin user:', err.message);
                } else if (this.changes > 0) {
                    console.log('✅ Default admin user created (username: admin, password: admin123)');
                    console.log('⚠️  IMPORTANT: Change the default password after first login!');
                } else {
                    console.log('ℹ️  Admin user already exists');
                }
                
                // Close database connection
                db.close((err) => {
                    if (err) {
                        console.error('❌ Error closing database:', err.message);
                    } else {
                        console.log('✅ Database initialization completed successfully!');
                        console.log('🚀 Database ready for use');
                    }
                });
            });
        } catch (error) {
            console.error('❌ Error hashing passwords:', error);
            db.close();
        }
    });
};

initializeDatabase();
