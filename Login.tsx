/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, FC } from 'react';
import { api } from './lib/api';
import type { User } from './db-server';

interface LoginProps {
    onLoginSuccess: (loginResponse: { user: User, showAdminWelcome: boolean }) => void;
    onForgotPassword: () => void;
    companyName?: string; // Optional company name
}

export const Login: FC<LoginProps> = ({ onLoginSuccess, onForgotPassword, companyName }) => {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);

        try {
            const loginResponse = await api.loginUser(username, password);
            if (loginResponse) {
                onLoginSuccess(loginResponse);
            } else {
                setError('Invalid username or password.');
            }
        } catch (err) {
            setError('An error occurred during login. Please try again.');
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="login-container">
            <div className="login-box">
                <img src="/FAR Logo 2.webp" alt="FAR Sighted Logo" className="login-logo" />
                <p className="login-subtitle">Fixed Asset Register Management System</p>
                {companyName && <p className="login-company-name">Company: <strong>{companyName}</strong></p>}
                <form onSubmit={handleSubmit} className="login-form">
                    <div className="form-group">
                        <label htmlFor="username">Username<span className="required-asterisk">*</span></label>
                        <input
                            type="text"
                            id="username"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            required
                            autoFocus
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="password">Password<span className="required-asterisk">*</span></label>
                        <input
                            type="password"
                            id="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                        />
                    </div>
                    {error && <p className="login-error">{error}</p>}
                    <button type="submit" className="btn btn-primary" style={{ width: '100%', marginTop: '1rem' }} disabled={isLoading}>
                        {isLoading ? 'Logging in...' : 'Login'}
                    </button>
                </form>
                <div className="login-footer">
                    <a href="#" onClick={onForgotPassword} className="forgot-password-link">
                        Forgot Password?
                    </a>
                </div>
            </div>
        </div>
    );
};
