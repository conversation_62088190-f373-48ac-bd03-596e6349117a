/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React from 'react';

export function ChevronIcon({ isOpen }: { isOpen: boolean }) {
    return (
        <svg className={`chevron ${isOpen ? 'open' : ''}`} width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="m9 18 6-6-6-6" /></svg>
    );
}

interface NavLinkProps {
    activeView: string;
    viewName: string;
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
}

export function NavLink({ activeView, viewName, label, onClick, icon }: NavLinkProps) {
    return (
        <a className={`nav-link ${activeView === viewName ? 'active' : ''}`} onClick={onClick}>
            {icon && <span className="icon">{icon}</span>}
            <span>{label}</span>
        </a>
    );
}

export function NavGroup({ title, isOpen, onToggle, children }: { title: string; isOpen: boolean; onToggle: () => void; children: React.ReactNode; }) {
    return (
        <div className="nav-group">
            <button className="nav-group-header" onClick={onToggle}><span>{title}</span><ChevronIcon isOpen={isOpen} /></button>
            {isOpen && <div className="nav-group-content">{children}</div>}
        </div>
    );
}
