# FAR Sighted - SQLite Backend Integration

## Overview
The FAR Sighted application has been successfully migrated from localStorage-based data persistence to a professional SQLite database with a REST API backend.

## Architecture

### Backend (Port 3001)
- **Express.js** REST API server
- **SQLite** database for data persistence
- **bcrypt** for password hashing
- **CORS** enabled for frontend communication
- **Morgan** for request logging
- **Helmet** for security headers

### Frontend (Port 5173)
- **React + TypeScript** application
- **Vite** build system
- Updated API client to communicate with backend

## Database Schema

### Core Tables
- `companies` - Company information and settings
- `users` - User accounts and authentication
- `assets` - Fixed asset records
- `financial_years` - Financial year tracking
- `asset_yearly_data` - Year-wise depreciation calculations
- `statutory_rates` - Statutory depreciation rates
- `extra_ledgers` - Additional ledger accounts
- `license_history` - License activation history
- `audit_logs` - System audit trail
- `backup_logs` - Backup operation logs
- `app_settings` - Application configuration

## API Endpoints

### Authentication
- `POST /api/users/login` - User authentication
- `GET /api/users` - List all users
- `POST /api/users` - Create new user
- `PUT /api/users/:id` - Update user

### Companies
- `GET /api/companies` - List all companies
- `GET /api/companies/:id` - Get company with full data
- `POST /api/companies` - Create new company
- `PUT /api/companies/:id` - Update company

### Assets
- `GET /api/assets/company/:companyId` - Get company assets
- `PUT /api/assets/company/:companyId` - Update company assets

### System
- `GET /api/settings` - Get application settings
- `PUT /api/settings` - Update application settings
- `GET /api/audit` - Get audit logs
- `POST /api/audit` - Add audit log entry
- `GET /api/backup/logs` - Get backup logs
- `POST /api/backup/auto` - Create automatic backup
- `GET /api/health` - Health check endpoint

## Getting Started

### 1. Initialize Database
```bash
cd backend
npm run init-db
```

### 2. Start Backend Server
```bash
cd backend
npm start
# or for development with auto-reload
npm run dev
```

### 3. Start Frontend (in separate terminal)
```bash
npm run dev
```

### 4. Start Both Together
```bash
npm run dev:full
```

## Default Credentials
- **Username**: admin
- **Password**: admin123
- **Role**: Admin

⚠️ **IMPORTANT**: Change the default password after first login!

## Environment Variables

### Backend (.env)
```
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:5173
```

## Database Location
- **Development**: `backend/database/far_sighted.db`
- **Production**: Configure path in database service

## Migration Benefits

### ✅ Enhanced Security
- Password hashing with bcrypt
- SQL injection protection
- CORS security
- Secure session management

### ✅ Better Performance
- Indexed database queries
- Transaction support
- Optimized data retrieval

### ✅ Scalability
- Multi-user support
- Concurrent access handling
- Professional database management

### ✅ Data Integrity
- Foreign key constraints
- Data validation
- Backup and restore capabilities

### ✅ Professional Features
- Comprehensive audit logging
- Automated backup system
- RESTful API design
- Error handling and logging

## Backup and Recovery
- Automatic backup creation
- Full database export/import
- Audit trail maintenance
- Recovery key system for users

## Development Tools
- Health check endpoint for monitoring
- Comprehensive error logging
- Development vs production configurations
- Hot reload for backend development

## Next Steps
1. Configure production database settings
2. Set up SSL/HTTPS for production
3. Implement user session management
4. Add data validation middleware
5. Set up automated testing
6. Configure deployment pipeline

The system is now ready for professional use with enterprise-grade data persistence and security features.
