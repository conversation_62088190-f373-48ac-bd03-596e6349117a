/**
 * Fix Missing Asset Fields Script
 * 
 * This script fixes existing assets that have null or empty values for critical fields:
 * - asset_group
 * - asset_sub_group  
 * - depreciation_method
 * - put_to_use_date
 * 
 * It provides default values and allows manual assignment based on asset particulars.
 */

const path = require('path');
const fs = require('fs');
const sqlite3 = require('sqlite3').verbose();

// Default mappings based on common asset types
const ASSET_MAPPINGS = {
    'computer': {
        assetGroup: 'Computer & Data Processing',
        assetSubGroup: 'Computers',
        scheduleIIIClassification: 'Computer including computer software',
        lifeInYears: 3
    },
    'laptop': {
        assetGroup: 'Computer & Data Processing', 
        assetSubGroup: 'Computers',
        scheduleIIIClassification: 'Computer including computer software',
        lifeInYears: 3
    },
    'furniture': {
        assetGroup: 'Furniture & Fixtures',
        assetSubGroup: 'Office Furniture',
        scheduleIIIClassification: 'Furniture and fixtures',
        lifeInYears: 10
    },
    'chair': {
        assetGroup: 'Furniture & Fixtures',
        assetSubGroup: 'Office Furniture', 
        scheduleIIIClassification: 'Furniture and fixtures',
        lifeInYears: 10
    },
    'table': {
        assetGroup: 'Furniture & Fixtures',
        assetSubGroup: 'Office Furniture',
        scheduleIIIClassification: 'Furniture and fixtures', 
        lifeInYears: 10
    },
    'machinery': {
        assetGroup: 'Plant & Machinery',
        assetSubGroup: 'General Machinery',
        scheduleIIIClassification: 'Plant and machinery',
        lifeInYears: 15
    },
    'vehicle': {
        assetGroup: 'Motor Vehicles',
        assetSubGroup: 'Motor Cars',
        scheduleIIIClassification: 'Motor vehicles',
        lifeInYears: 8
    },
    'car': {
        assetGroup: 'Motor Vehicles',
        assetSubGroup: 'Motor Cars',
        scheduleIIIClassification: 'Motor vehicles',
        lifeInYears: 8
    },
    'building': {
        assetGroup: 'Buildings',
        assetSubGroup: 'Factory Buildings',
        scheduleIIIClassification: 'Buildings',
        lifeInYears: 60
    }
};

// Default values for unknown assets
const DEFAULT_ASSET = {
    assetGroup: 'Plant & Machinery',
    assetSubGroup: 'General Machinery', 
    scheduleIIIClassification: 'Plant and machinery',
    lifeInYears: 10
};

function getAssetMapping(assetParticulars) {
    const particulars = assetParticulars.toLowerCase();
    
    for (const [keyword, mapping] of Object.entries(ASSET_MAPPINGS)) {
        if (particulars.includes(keyword)) {
            return mapping;
        }
    }
    
    return DEFAULT_ASSET;
}

async function fixMissingAssetFields() {
    console.log('🔧 FAR Sighted - Fix Missing Asset Fields');
    console.log('==========================================\n');

    const companiesDir = path.join(__dirname, '../database/companies');
    
    if (!fs.existsSync(companiesDir)) {
        console.log('❌ Companies directory not found:', companiesDir);
        return;
    }

    const companyFolders = fs.readdirSync(companiesDir).filter(folder => 
        fs.statSync(path.join(companiesDir, folder)).isDirectory()
    );

    console.log(`📁 Found ${companyFolders.length} company folders\n`);

    let totalAssetsFixed = 0;
    let totalCompaniesProcessed = 0;

    for (const companyFolder of companyFolders) {
        const companyDbPath = path.join(companiesDir, companyFolder, 'company.db');
        
        if (!fs.existsSync(companyDbPath)) {
            console.log(`⚠️  Skipping ${companyFolder} - no company.db found`);
            continue;
        }

        console.log(`🏢 Processing: ${companyFolder}`);
        
        try {
            const db = new sqlite3.Database(companyDbPath);

            // Get assets with missing fields
            const assetsWithMissingFields = await new Promise((resolve, reject) => {
                db.all(`
                    SELECT id, record_id, asset_particulars, book_entry_date, put_to_use_date,
                           asset_group, asset_sub_group, depreciation_method, life_in_years,
                           schedule_iii_classification
                    FROM assets
                    WHERE asset_group IS NULL OR asset_group = ''
                       OR asset_sub_group IS NULL OR asset_sub_group = ''
                       OR depreciation_method IS NULL OR depreciation_method = ''
                       OR put_to_use_date IS NULL OR put_to_use_date = ''
                `, (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                });
            });

            if (assetsWithMissingFields.length === 0) {
                console.log(`   ✅ No assets with missing fields found`);
                db.close();
                continue;
            }

            console.log(`   📊 Found ${assetsWithMissingFields.length} assets with missing fields`);

            let assetsFixed = 0;

            for (const asset of assetsWithMissingFields) {
                const mapping = getAssetMapping(asset.asset_particulars);

                // Fix missing fields
                const assetGroup = asset.asset_group || mapping.assetGroup;
                const assetSubGroup = asset.asset_sub_group || mapping.assetSubGroup;
                const depreciationMethod = asset.depreciation_method || 'WDV';
                const scheduleIIIClassification = asset.schedule_iii_classification || mapping.scheduleIIIClassification;
                const lifeInYears = asset.life_in_years || mapping.lifeInYears;

                // Fix put_to_use_date - use book_entry_date if available, otherwise use a default
                let putToUseDate = asset.put_to_use_date;
                if (!putToUseDate || putToUseDate === '') {
                    putToUseDate = asset.book_entry_date || '2024-04-01'; // Default to start of current FY
                }

                await new Promise((resolve, reject) => {
                    db.run(`
                        UPDATE assets
                        SET asset_group = ?,
                            asset_sub_group = ?,
                            depreciation_method = ?,
                            put_to_use_date = ?,
                            schedule_iii_classification = ?,
                            life_in_years = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    `, [
                        assetGroup,
                        assetSubGroup,
                        depreciationMethod,
                        putToUseDate,
                        scheduleIIIClassification,
                        lifeInYears,
                        asset.id
                    ], (err) => {
                        if (err) reject(err);
                        else resolve();
                    });
                });

                console.log(`   ✅ Fixed: ${asset.record_id} - ${asset.asset_particulars.substring(0, 30)}...`);
                console.log(`      Group: ${assetGroup} | Sub-Group: ${assetSubGroup}`);
                console.log(`      Method: ${depreciationMethod} | Put to Use: ${putToUseDate}`);

                assetsFixed++;
            }

            await new Promise((resolve) => {
                db.close(resolve);
            });
            
            console.log(`   🎉 Fixed ${assetsFixed} assets in ${companyFolder}\n`);
            totalAssetsFixed += assetsFixed;
            totalCompaniesProcessed++;

        } catch (error) {
            console.error(`   ❌ Error processing ${companyFolder}:`, error.message);
        }
    }

    console.log('📈 SUMMARY');
    console.log('==========');
    console.log(`Companies Processed: ${totalCompaniesProcessed}`);
    console.log(`Total Assets Fixed: ${totalAssetsFixed}`);
    console.log('\n✅ Asset field fixing completed!');
}

// Run the script
if (require.main === module) {
    fixMissingAssetFields().catch(console.error);
}

module.exports = { fixMissingAssetFields };
