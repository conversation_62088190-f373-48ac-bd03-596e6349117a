/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo, FC } from 'react';
import { api } from './lib/api';
import { DataViewProps } from './lib/types';
import { sortData, exportToExcel, formatIndianNumber } from './lib/utils';
import { DownloadIcon } from './Icons';
import { Highlight } from './pages/PlaceholderViews';

interface AdditionsReportRow {
    recordId: string;
    assetParticulars: string;
    assetGroup: string;
    assetSubGroup: string;
    ledgerNameInBooks: string;
    putToUseDate: string;
    grossAmount: number;
}

export const AssetAdditionsReport: FC<DataViewProps> = ({ companyId, companyName, year, showAlert }) => {
    const [reportData, setReportData] = useState<AdditionsReportRow[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [sortConfig, setSortConfig] = useState<{ key: keyof AdditionsReportRow; direction: 'asc' | 'desc' } | null>({ key: 'putToUseDate', direction: 'asc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);
    const [filterText, setFilterText] = useState('');

    useEffect(() => {
        const fetchData = async () => {
            if (!companyId || !year) {
                setReportData([]);
                setLoading(false);
                return;
            }
            setLoading(true);
            setError(null);
            setSelectedRowId(null);
            setSelectedColumnKey(null);
            setFilterText('');

            try {
                const assets = await api.getAssets(companyId);
                const [startYearStr, endYearStr] = year.split('-');
                const fyStart = new Date(`${startYearStr}-04-01T00:00:00.000Z`);
                const fyEnd = new Date(`${endYearStr}-03-31T23:59:59.999Z`);

                const additions = assets.filter(asset => {
                    const putToUseDate = new Date(asset.putToUseDate);
                    return putToUseDate >= fyStart && putToUseDate <= fyEnd;
                }).map(asset => ({
                    recordId: asset.recordId,
                    assetParticulars: asset.assetParticulars,
                    assetGroup: asset.assetGroup,
                    assetSubGroup: asset.assetSubGroup,
                    ledgerNameInBooks: asset.ledgerNameInBooks,
                    putToUseDate: asset.putToUseDate,
                    grossAmount: asset.grossAmount,
                }));

                setReportData(additions);
            } catch (err) {
                setError('Failed to fetch and process asset data for additions report.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [companyId, year]);

    const searchableFields: (keyof AdditionsReportRow)[] = ['recordId', 'assetParticulars', 'assetGroup', 'assetSubGroup', 'ledgerNameInBooks'];

    const sortedData = useMemo(() => {
        return sortData(reportData, sortConfig);
    }, [reportData, sortConfig]);
    
    const filteredData = useMemo(() => {
        if (!filterText) return sortedData;
        const searchTerm = filterText.trim().toLowerCase();
        return sortedData.filter(row =>
            searchableFields.some(field =>
                String(row[field] ?? '').toLowerCase().includes(searchTerm)
            )
        );
    }, [sortedData, filterText]);

    const requestSort = (key: keyof AdditionsReportRow) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: keyof AdditionsReportRow) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };
    
    const getHeaderClass = (key: keyof AdditionsReportRow) => {
        const classes = ['sortable'];
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        if (key === 'grossAmount') classes.push('text-right');
        return classes.join(' ');
    };

    const getColumnClass = (key: keyof AdditionsReportRow) => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if (['assetParticulars', 'assetGroup', 'assetSubGroup', 'ledgerNameInBooks'].includes(key)) classes.push('td-wrap');
        if (key === 'grossAmount') classes.push('text-right');
        return classes.join(' ');
    };

    const handleExport = () => {
        if (!companyId || !companyName || !year) return;
        
        const dataToExport = filteredData.map(row => ({
            "Record ID": row.recordId,
            "Asset Particulars": row.assetParticulars,
            "Asset Group": row.assetGroup,
            "Asset Sub-Group": row.assetSubGroup,
            "Ledger Name": row.ledgerNameInBooks,
            "Put to Use Date": new Date(row.putToUseDate).toLocaleDateString(),
            "Gross Amount": row.grossAmount,
        }));
        
        if (!exportToExcel({ data: dataToExport, companyName, year, reportName: 'Asset_Additions' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    if (loading) return <div className="loading-indicator">Generating Report...</div>;
    if (error) return <div className="error-message">{error}</div>;
    if (!companyId) return null;

    return (
        <div className="report-view">
            <div className="view-header">
                <h2>Asset Additions Report ({year})</h2>
                <div className="actions">
                    <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export to Excel</button>
                </div>
            </div>
            <div className="filter-container">
                <div className="filter-input-wrapper">
                    <input
                        type="text"
                        placeholder="Filter by ID, Particulars, Group, Ledger..."
                        className="filter-input"
                        value={filterText}
                        onChange={e => setFilterText(e.target.value)}
                        aria-label="Filter asset additions"
                    />
                    <button
                        className={`filter-clear-btn ${!filterText ? 'hidden' : ''}`}
                        onClick={() => setFilterText('')}
                        aria-label="Clear filter"
                        title="Clear filter"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </div>
            </div>
            <div className="table-container">
                {filteredData.length > 0 ? (
                    <table>
                        <thead>
                            <tr>
                                <th className={getHeaderClass('recordId')} onClick={() => requestSort('recordId')}>Record ID{getSortIndicator('recordId')}</th>
                                <th className={getHeaderClass('assetParticulars')} onClick={() => requestSort('assetParticulars')}>Asset Particulars{getSortIndicator('assetParticulars')}</th>
                                <th className={getHeaderClass('assetGroup')} onClick={() => requestSort('assetGroup')}>Asset Group{getSortIndicator('assetGroup')}</th>
                                <th className={getHeaderClass('assetSubGroup')} onClick={() => requestSort('assetSubGroup')}>Asset Sub-Group{getSortIndicator('assetSubGroup')}</th>
                                <th className={getHeaderClass('ledgerNameInBooks')} onClick={() => requestSort('ledgerNameInBooks')}>Ledger Name{getSortIndicator('ledgerNameInBooks')}</th>
                                <th className={getHeaderClass('putToUseDate')} onClick={() => requestSort('putToUseDate')}>Put to Use Date{getSortIndicator('putToUseDate')}</th>
                                <th className={`${getHeaderClass('grossAmount')} text-right`} onClick={() => requestSort('grossAmount')}>Gross Amount{getSortIndicator('grossAmount')}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {filteredData.map(row => (
                                <tr key={row.recordId} onClick={() => setSelectedRowId(row.recordId)} className={row.recordId === selectedRowId ? 'selected-row' : ''}>
                                    <td className={getColumnClass('recordId')}><Highlight text={row.recordId} highlight={filterText} /></td>
                                    <td className={getColumnClass('assetParticulars')}><Highlight text={row.assetParticulars} highlight={filterText} /></td>
                                    <td className={getColumnClass('assetGroup')}><Highlight text={row.assetGroup} highlight={filterText} /></td>
                                    <td className={getColumnClass('assetSubGroup')}><Highlight text={row.assetSubGroup} highlight={filterText} /></td>
                                    <td className={getColumnClass('ledgerNameInBooks')}><Highlight text={row.ledgerNameInBooks} highlight={filterText} /></td>
                                    <td className={getColumnClass('putToUseDate')}>{new Date(row.putToUseDate).toLocaleDateString()}</td>
                                    <td className={getColumnClass('grossAmount')}>{formatIndianNumber(row.grossAmount)}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                ) : (
                    <div className="company-info-container"><p>No asset additions found for the selected year.</p></div>
                )}
            </div>
        </div>
    );
};
