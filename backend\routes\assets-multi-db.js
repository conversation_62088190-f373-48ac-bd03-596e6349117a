/**
 * UPDATED ASSET ROUTES
 * Professional Advisory Services - Chartered Accountant
 * 
 * Updated to use multi-database architecture with separate databases
 * for each company in different folders.
 */

import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import multiDbService from '../services/multi-database.js';

const router = express.Router();

// Get all assets for a company
router.get('/:companyId', async (req, res) => {
    try {
        const { companyId } = req.params;
        
        const { db } = await multiDbService.getCompanyDatabase(companyId);
        
        const assets = await multiDbService.companyAll(db, `
            SELECT 
                id as assetDbId,
                record_id as recordId,
                asset_particulars as assetParticulars,
                book_entry_date as bookEntryDate,
                put_to_use_date as putToUseDate,
                basic_amount as basicAmount,
                duties_taxes as dutiesTaxes,
                gross_amount as grossAmount,
                vendor,
                invoice_no as invoiceNo,
                model_make as modelMake,
                location,
                asset_id as assetId,
                remarks,
                ledger_name_in_books as ledgerNameInBooks,
                asset_group as assetGroup,
                asset_sub_group as assetSubGroup,
                schedule_iii_classification as scheduleIIIClassification,
                disposal_date as disposalDate,
                disposal_amount as disposalAmount,
                salvage_percentage as salvagePercentage,
                wdv_of_adoption_date as wdvOfAdoptionDate,
                is_leasehold as isLeasehold,
                depreciation_method as depreciationMethod,
                life_in_years as lifeInYears,
                lease_period as leasePeriod,
                scrap_it as scrapIt
            FROM assets ORDER BY record_id
        `);
        
        res.json(assets);
    } catch (error) {
        console.error('Error fetching assets:', error);
        if (error.message.includes('not found')) {
            res.status(404).json({ error: 'Company not found' });
        } else {
            res.status(500).json({ error: 'Failed to fetch assets' });
        }
    }
});

// Add new asset
router.post('/:companyId', async (req, res) => {
    try {
        const { companyId } = req.params;
        const asset = req.body;
        
        // Validate required fields
        if (!asset.assetParticulars || !asset.putToUseDate || !asset.grossAmount) {
            return res.status(400).json({ 
                error: 'Missing required fields: assetParticulars, putToUseDate, grossAmount' 
            });
        }
        
        const { db } = await multiDbService.getCompanyDatabase(companyId);
        
        // Generate record ID if not provided
        if (!asset.recordId) {
            const assetCount = await multiDbService.companyGet(db, 'SELECT COUNT(*) as count FROM assets');
            asset.recordId = `A${String(assetCount.count + 1).padStart(4, '0')}`;
        }
        
        const result = await multiDbService.companyRun(db,
            `INSERT INTO assets (
                record_id, asset_particulars, book_entry_date, put_to_use_date,
                basic_amount, duties_taxes, gross_amount, vendor, invoice_no, model_make,
                location, asset_id, remarks, ledger_name_in_books, asset_group,
                asset_sub_group, schedule_iii_classification, disposal_date, disposal_amount,
                salvage_percentage, wdv_of_adoption_date, is_leasehold, depreciation_method,
                life_in_years, lease_period, scrap_it
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                asset.recordId, asset.assetParticulars, asset.bookEntryDate, asset.putToUseDate,
                asset.basicAmount, asset.dutiesTaxes, asset.grossAmount, asset.vendor, asset.invoiceNo,
                asset.modelMake, asset.location, asset.assetId, asset.remarks, asset.ledgerNameInBooks,
                asset.assetGroup, asset.assetSubGroup, asset.scheduleIIIClassification,
                asset.disposalDate, asset.disposalAmount, asset.salvagePercentage, asset.wdvOfAdoptionDate,
                asset.isLeasehold, asset.depreciationMethod, asset.lifeInYears, asset.leasePeriod, asset.scrapIt
            ]
        );
        
        res.status(201).json({
            id: result.id,
            recordId: asset.recordId,
            message: 'Asset created successfully'
        });
    } catch (error) {
        console.error('Error creating asset:', error);
        if (error.message.includes('not found')) {
            res.status(404).json({ error: 'Company not found' });
        } else if (error.message.includes('UNIQUE constraint failed')) {
            res.status(409).json({ error: 'Asset with this record ID already exists' });
        } else {
            res.status(500).json({ error: 'Failed to create asset' });
        }
    }
});

// Update asset
router.put('/:companyId/:recordId', async (req, res) => {
    try {
        const { companyId, recordId } = req.params;
        const asset = req.body;
        
        const { db } = await multiDbService.getCompanyDatabase(companyId);
        
        // Check if asset exists
        const existingAsset = await multiDbService.companyGet(db, 'SELECT id FROM assets WHERE record_id = ?', [recordId]);
        if (!existingAsset) {
            return res.status(404).json({ error: 'Asset not found' });
        }
        
        await multiDbService.companyRun(db,
            `UPDATE assets SET 
                asset_particulars = ?, book_entry_date = ?, put_to_use_date = ?,
                basic_amount = ?, duties_taxes = ?, gross_amount = ?, vendor = ?, invoice_no = ?,
                model_make = ?, location = ?, asset_id = ?, remarks = ?, ledger_name_in_books = ?,
                asset_group = ?, asset_sub_group = ?, schedule_iii_classification = ?,
                disposal_date = ?, disposal_amount = ?, salvage_percentage = ?, wdv_of_adoption_date = ?,
                is_leasehold = ?, depreciation_method = ?, life_in_years = ?, lease_period = ?,
                scrap_it = ?, updated_at = CURRENT_TIMESTAMP
            WHERE record_id = ?`,
            [
                asset.assetParticulars, asset.bookEntryDate, asset.putToUseDate,
                asset.basicAmount, asset.dutiesTaxes, asset.grossAmount, asset.vendor, asset.invoiceNo,
                asset.modelMake, asset.location, asset.assetId, asset.remarks, asset.ledgerNameInBooks,
                asset.assetGroup, asset.assetSubGroup, asset.scheduleIIIClassification,
                asset.disposalDate, asset.disposalAmount, asset.salvagePercentage, asset.wdvOfAdoptionDate,
                asset.isLeasehold, asset.depreciationMethod, asset.lifeInYears, asset.leasePeriod,
                asset.scrapIt, recordId
            ]
        );
        
        res.json({ message: 'Asset updated successfully' });
    } catch (error) {
        console.error('Error updating asset:', error);
        if (error.message.includes('not found')) {
            res.status(404).json({ error: 'Company not found' });
        } else {
            res.status(500).json({ error: 'Failed to update asset' });
        }
    }
});

// Delete asset
router.delete('/:companyId/:recordId', async (req, res) => {
    try {
        const { companyId, recordId } = req.params;
        
        const { db } = await multiDbService.getCompanyDatabase(companyId);
        
        // Check if asset exists
        const existingAsset = await multiDbService.companyGet(db, 'SELECT id FROM assets WHERE record_id = ?', [recordId]);
        if (!existingAsset) {
            return res.status(404).json({ error: 'Asset not found' });
        }
        
        // Delete asset (cascade will handle yearly data)
        await multiDbService.companyRun(db, 'DELETE FROM assets WHERE record_id = ?', [recordId]);
        
        res.json({ message: 'Asset deleted successfully' });
    } catch (error) {
        console.error('Error deleting asset:', error);
        if (error.message.includes('not found')) {
            res.status(404).json({ error: 'Company not found' });
        } else {
            res.status(500).json({ error: 'Failed to delete asset' });
        }
    }
});

// Bulk import assets
router.post('/:companyId/import', async (req, res) => {
    try {
        const { companyId } = req.params;
        const { assets } = req.body;
        
        if (!Array.isArray(assets) || assets.length === 0) {
            return res.status(400).json({ error: 'Assets array is required and cannot be empty' });
        }
        
        const { db } = await multiDbService.getCompanyDatabase(companyId);
        
        await multiDbService.beginCompanyTransaction(companyId);
        
        try {
            let successCount = 0;
            const errors = [];
            
            for (let i = 0; i < assets.length; i++) {
                const asset = assets[i];
                
                try {
                    // Generate record ID if not provided
                    if (!asset.recordId) {
                        const assetCount = await multiDbService.companyGet(db, 'SELECT COUNT(*) as count FROM assets');
                        asset.recordId = `I${String(assetCount.count + i + 1).padStart(4, '0')}`;
                    }
                    
                    await multiDbService.companyRun(db,
                        `INSERT INTO assets (
                            record_id, asset_particulars, book_entry_date, put_to_use_date,
                            basic_amount, duties_taxes, gross_amount, vendor, invoice_no, model_make,
                            location, asset_id, remarks, ledger_name_in_books, asset_group,
                            asset_sub_group, schedule_iii_classification, disposal_date, disposal_amount,
                            salvage_percentage, wdv_of_adoption_date, is_leasehold, depreciation_method,
                            life_in_years, lease_period, scrap_it
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                            asset.recordId, asset.assetParticulars, asset.bookEntryDate, asset.putToUseDate,
                            asset.basicAmount, asset.dutiesTaxes, asset.grossAmount, asset.vendor, asset.invoiceNo,
                            asset.modelMake, asset.location, asset.assetId, asset.remarks, asset.ledgerNameInBooks,
                            asset.assetGroup, asset.assetSubGroup, asset.scheduleIIIClassification,
                            asset.disposalDate, asset.disposalAmount, asset.salvagePercentage, asset.wdvOfAdoptionDate,
                            asset.isLeasehold, asset.depreciationMethod, asset.lifeInYears, asset.leasePeriod, asset.scrapIt
                        ]
                    );
                    
                    successCount++;
                } catch (assetError) {
                    errors.push({
                        index: i,
                        recordId: asset.recordId,
                        error: assetError.message
                    });
                }
            }
            
            await multiDbService.commitCompanyTransaction(companyId);
            
            res.json({
                message: 'Asset import completed',
                totalAssets: assets.length,
                successCount: successCount,
                errorCount: errors.length,
                errors: errors
            });
        } catch (error) {
            await multiDbService.rollbackCompanyTransaction(companyId);
            throw error;
        }
    } catch (error) {
        console.error('Error importing assets:', error);
        if (error.message.includes('not found')) {
            res.status(404).json({ error: 'Company not found' });
        } else {
            res.status(500).json({ error: 'Failed to import assets' });
        }
    }
});

// Get asset yearly data
router.get('/:companyId/:recordId/yearly-data', async (req, res) => {
    try {
        const { companyId, recordId } = req.params;
        
        const { db } = await multiDbService.getCompanyDatabase(companyId);
        
        // Get asset ID
        const asset = await multiDbService.companyGet(db, 'SELECT id FROM assets WHERE record_id = ?', [recordId]);
        if (!asset) {
            return res.status(404).json({ error: 'Asset not found' });
        }
        
        const yearlyData = await multiDbService.companyAll(db,
            `SELECT year_range, opening_wdv, use_days, depreciation_amount, closing_wdv,
                    second_shift_days, third_shift_days
             FROM asset_yearly_data 
             WHERE asset_id = ? 
             ORDER BY year_range`,
            [asset.id]
        );
        
        res.json(yearlyData);
    } catch (error) {
        console.error('Error fetching asset yearly data:', error);
        if (error.message.includes('not found')) {
            res.status(404).json({ error: 'Company not found' });
        } else {
            res.status(500).json({ error: 'Failed to fetch yearly data' });
        }
    }
});

// Update asset yearly data
router.put('/:companyId/:recordId/yearly-data/:year', async (req, res) => {
    try {
        const { companyId, recordId, year } = req.params;
        const yearlyData = req.body;
        
        const { db } = await multiDbService.getCompanyDatabase(companyId);
        
        // Get asset ID
        const asset = await multiDbService.companyGet(db, 'SELECT id FROM assets WHERE record_id = ?', [recordId]);
        if (!asset) {
            return res.status(404).json({ error: 'Asset not found' });
        }
        
        // Upsert yearly data
        await multiDbService.companyRun(db,
            `INSERT OR REPLACE INTO asset_yearly_data (
                asset_id, year_range, opening_wdv, use_days, depreciation_amount,
                closing_wdv, second_shift_days, third_shift_days
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                asset.id, year, yearlyData.openingWdv, yearlyData.useDays,
                yearlyData.depreciationAmount, yearlyData.closingWdv,
                yearlyData.secondShiftDays, yearlyData.thirdShiftDays
            ]
        );
        
        res.json({ message: 'Yearly data updated successfully' });
    } catch (error) {
        console.error('Error updating asset yearly data:', error);
        if (error.message.includes('not found')) {
            res.status(404).json({ error: 'Company not found' });
        } else {
            res.status(500).json({ error: 'Failed to update yearly data' });
        }
    }
});

export default router;