# Asset Calculations Sticky Columns Implementation

## Overview
Added sticky column functionality to Asset Calculations table to match the functionality already present in Asset Records. This improves navigation and usability when scrolling through wide tables with many columns.

## Problem Addressed
Asset Calculations table lacked sticky columns, making it difficult to identify which asset a row belonged to when scrolling horizontally through the many calculation columns.

## Solution Implemented

### 1. CSS Styling for Sticky Columns
**File**: `styling/index.css`

Added comprehensive sticky column styles specifically for Asset Calculations:

```css
/* --- Sticky Column Styles for AssetCalculations --- */
.asset-calculations-table-container .sticky-col {
    position: -webkit-sticky; /* For Safari */
    position: sticky;
    background-color: var(--background-primary);
}

.asset-calculations-table-container th.sticky-col {
    z-index: 25; /* Higher than sticky headers (15) and regular headers (10) */
    background-color: var(--background-tertiary);
}

.asset-calculations-table-container td.sticky-col {
    z-index: 5;
    background-color: var(--background-primary); /* Ensure solid background */
}

.asset-calculations-table-container .sticky-col-1 {
    left: 0;
}

.asset-calculations-table-container .sticky-col-2 {
    left: 120px; /* Width of col-1 (Record ID) */
}

.asset-calculations-table-container .sticky-col-3 {
    left: 370px; /* 120px (col-1) + 250px (col-2) */
}

/* Ensure hover color doesn't override sticky background */
.asset-calculations-table-container tbody tr:hover .sticky-col {
    background-color: var(--background-hover) !important;
}

/* Ensure sticky header background is correct */
.asset-calculations-table-container tbody tr:hover th.sticky-col,
.asset-calculations-table-container th.sticky-col {
    background-color: var(--background-tertiary) !important;
}

/* Special styling for Record ID column */
.asset-calculations-table-container .td-record-id {
    font-weight: 500;
    color: var(--text-secondary);
    background-color: var(--background-tertiary);
}

.asset-calculations-table-container tbody tr:hover td.td-record-id.sticky-col {
    background-color: var(--background-tertiary) !important;
}
```

### 2. Component Updates
**File**: `AssetCalculations.tsx`

#### Table Container Class (Line 672)
```typescript
// Before
<div className="table-container">

// After
<div className="table-container asset-calculations-table-container">
```

#### Enhanced Header Class Function (Lines 587-605)
```typescript
const getHeaderClass = (key: keyof CalculationRow) => {
    const classes = ['sortable'];
    if (!['recordId', 'assetParticulars', 'depreciationMethod', 'endOfLifeDate'].includes(key)) {
        classes.push('text-right');
    }
    if (sortConfig?.key === key) classes.push('sortable-active');
    if (selectedColumnKey === key) classes.push('selected-col');
    
    // Add sticky column classes for first few columns
    if (key === 'recordId') {
        classes.push('sticky-col', 'sticky-col-1');
    } else if (key === 'assetParticulars') {
        classes.push('sticky-col', 'sticky-col-2');
    } else if (key === 'depreciationMethod') {
        classes.push('sticky-col', 'sticky-col-3');
    }
    
    return classes.join(' ');
};
```

#### Enhanced Column Class Function (Lines 607-624)
```typescript
const getColumnClass = (key: keyof CalculationRow) => {
    const classes = [];
    if (selectedColumnKey === key) classes.push('selected-col');
    if (!['recordId', 'assetParticulars', 'depreciationMethod', 'endOfLifeDate'].includes(key)) {
        classes.push('text-right');
    }
    
    // Add sticky column classes for first few columns
    if (key === 'recordId') {
        classes.push('sticky-col', 'sticky-col-1', 'td-record-id');
    } else if (key === 'assetParticulars') {
        classes.push('sticky-col', 'sticky-col-2');
    } else if (key === 'depreciationMethod') {
        classes.push('sticky-col', 'sticky-col-3');
    }
    
    return classes.join(' ');
};
```

## Sticky Columns Configuration

### Column Layout
1. **Record ID** (`sticky-col-1`): 
   - Position: `left: 0`
   - Width: ~120px
   - Special styling: Bold, secondary color, tertiary background

2. **Asset Particulars** (`sticky-col-2`):
   - Position: `left: 120px`
   - Width: ~250px
   - Contains asset description for easy identification

3. **Depreciation Method** (`sticky-col-3`):
   - Position: `left: 370px`
   - Width: ~120px
   - Shows WDV/SLM method for context

### Z-Index Management
- **Sticky Headers**: `z-index: 25`
- **Sticky Cells**: `z-index: 5`
- **Regular Headers**: `z-index: 10`
- **Regular Cells**: `z-index: 1`

## Visual Design

### Background Colors
- **Headers**: `var(--background-tertiary)` (darker)
- **Cells**: `var(--background-primary)` (standard)
- **Record ID**: `var(--background-tertiary)` (matches header)
- **Hover**: `var(--background-hover)` (interactive feedback)

### Browser Compatibility
- Uses `-webkit-sticky` for Safari support
- Standard `position: sticky` for modern browsers
- Fallback background colors for older browsers

## User Experience Benefits

### 1. Improved Navigation
- ✅ Always visible asset identification when scrolling horizontally
- ✅ Context preservation across wide calculation tables
- ✅ Easier data verification and cross-referencing

### 2. Consistent Interface
- ✅ Matches Asset Records sticky column behavior
- ✅ Uniform experience across all table views
- ✅ Familiar interaction patterns

### 3. Enhanced Usability
- ✅ Reduced cognitive load when reviewing calculations
- ✅ Faster asset identification in large datasets
- ✅ Better workflow for financial analysis

## Technical Considerations

### 1. Performance
- Minimal performance impact
- CSS-only implementation
- No JavaScript scroll listeners required

### 2. Responsive Design
- Works on various screen sizes
- Maintains functionality on tablets
- Graceful degradation on mobile

### 3. Accessibility
- Maintains keyboard navigation
- Screen reader compatibility
- Focus management preserved

## Testing Scenarios

### 1. Horizontal Scrolling
1. Open Asset Calculations with many assets
2. Scroll horizontally to view calculation columns
3. **Expected**: Record ID, Asset Particulars, and Depreciation Method remain visible

### 2. Column Selection
1. Click on various column headers
2. **Expected**: Selected column highlighting works with sticky columns
3. **Expected**: Sticky columns maintain proper background colors

### 3. Row Hover Effects
1. Hover over different rows
2. **Expected**: Hover effects work correctly on sticky columns
3. **Expected**: Background colors change appropriately

### 4. Sorting Functionality
1. Sort by different columns including sticky ones
2. **Expected**: Sorting works normally
3. **Expected**: Sticky columns maintain position during sort

## Comparison with Asset Records

### Similarities
- Same sticky column approach
- Consistent CSS class naming
- Similar z-index management
- Matching visual design

### Differences
- **Column Widths**: Adjusted for Asset Calculations content
- **Column Selection**: Different columns chosen as sticky
- **Container Class**: Separate namespace for styling isolation

## Future Enhancements

### 1. User Customization
- Allow users to choose which columns are sticky
- Configurable column widths
- Persistent user preferences

### 2. Advanced Features
- Resizable sticky columns
- Drag-and-drop column reordering
- Column visibility toggles

### 3. Mobile Optimization
- Touch-friendly sticky column interactions
- Responsive column hiding
- Swipe gesture support

---
**Completed**: 2025-07-10
**Status**: ✅ Complete
**Impact**: Improved navigation and usability for Asset Calculations table with consistent sticky column behavior
