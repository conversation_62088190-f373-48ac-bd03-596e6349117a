import React, { useState, useEffect } from 'react';
import { SettingsIcon, EyeIcon, EyeOffIcon, ChevronLeftIcon, ChevronRightIcon } from '../Icons';

interface ColumnConfig {
    key: string;
    label: string;
    visible: boolean;
    width: number;
    minWidth?: number;
    maxWidth?: number;
    resizable?: boolean;
    sticky?: boolean;
}

interface ColumnManagerProps {
    tableId: string;
    columns: ColumnConfig[];
    onColumnsChange: (columns: ColumnConfig[]) => void;
    className?: string;
}

export function ColumnManager({ tableId, columns, onColumnsChange, className = '' }: ColumnManagerProps) {
    const [showColumnChooser, setShowColumnChooser] = useState(false);
    const [localColumns, setLocalColumns] = useState<ColumnConfig[]>(columns);

    // Load saved column configuration on mount
    useEffect(() => {
        const savedConfig = localStorage.getItem(`columnConfig_${tableId}`);
        if (savedConfig) {
            try {
                const parsed = JSON.parse(savedConfig);
                const mergedColumns = columns.map(col => {
                    const saved = parsed.find((p: ColumnConfig) => p.key === col.key);
                    return saved ? { ...col, ...saved } : col;
                });
                setLocalColumns(mergedColumns);
                onColumnsChange(mergedColumns);
            } catch (error) {
                console.error('Error loading column configuration:', error);
            }
        }
    }, [tableId, columns, onColumnsChange]);

    // Save column configuration to localStorage
    const saveColumnConfig = (newColumns: ColumnConfig[]) => {
        try {
            localStorage.setItem(`columnConfig_${tableId}`, JSON.stringify(newColumns));
        } catch (error) {
            console.error('Error saving column configuration:', error);
        }
    };

    // Toggle column visibility
    const toggleColumnVisibility = (columnKey: string) => {
        const newColumns = localColumns.map(col =>
            col.key === columnKey ? { ...col, visible: !col.visible } : col
        );
        setLocalColumns(newColumns);
        onColumnsChange(newColumns);
        saveColumnConfig(newColumns);
    };

    // Update column width
    const updateColumnWidth = (columnKey: string, width: number) => {
        const newColumns = localColumns.map(col =>
            col.key === columnKey ? { ...col, width: Math.max(width, col.minWidth || 50) } : col
        );
        setLocalColumns(newColumns);
        onColumnsChange(newColumns);
        saveColumnConfig(newColumns);
    };

    // Reset to default configuration
    const resetToDefaults = () => {
        setLocalColumns(columns);
        onColumnsChange(columns);
        localStorage.removeItem(`columnConfig_${tableId}`);
        setShowColumnChooser(false);
    };

    // Show/hide all columns
    const toggleAllColumns = (visible: boolean) => {
        const newColumns = localColumns.map(col => ({ ...col, visible }));
        setLocalColumns(newColumns);
        onColumnsChange(newColumns);
        saveColumnConfig(newColumns);
    };

    const visibleCount = localColumns.filter(col => col.visible).length;
    const totalCount = localColumns.length;

    return (
        <div className={`column-manager ${className}`}>
            {/* Column Manager Button */}
            <button
                type="button"
                className="btn btn-secondary column-manager-toggle"
                onClick={() => setShowColumnChooser(!showColumnChooser)}
                title="Manage Columns"
            >
                <SettingsIcon />
                Columns ({visibleCount}/{totalCount})
            </button>

            {/* Column Chooser Panel */}
            {showColumnChooser && (
                <div className="column-chooser-panel">
                    <div className="column-chooser-header">
                        <h4>Manage Columns</h4>
                        <button
                            type="button"
                            className="btn btn-ghost btn-sm"
                            onClick={() => setShowColumnChooser(false)}
                        >
                            ×
                        </button>
                    </div>

                    <div className="column-chooser-actions">
                        <button
                            type="button"
                            className="btn btn-sm btn-secondary"
                            onClick={() => toggleAllColumns(true)}
                        >
                            Show All
                        </button>
                        <button
                            type="button"
                            className="btn btn-sm btn-secondary"
                            onClick={() => toggleAllColumns(false)}
                        >
                            Hide All
                        </button>
                        <button
                            type="button"
                            className="btn btn-sm btn-warning"
                            onClick={resetToDefaults}
                        >
                            Reset
                        </button>
                    </div>

                    <div className="column-list">
                        {localColumns.map((column) => (
                            <div key={column.key} className="column-item">
                                <div className="column-visibility">
                                    <button
                                        type="button"
                                        className={`btn btn-ghost btn-sm ${column.visible ? 'active' : ''}`}
                                        onClick={() => toggleColumnVisibility(column.key)}
                                        title={column.visible ? 'Hide column' : 'Show column'}
                                    >
                                        {column.visible ? <EyeIcon /> : <EyeOffIcon />}
                                    </button>
                                    <span className={`column-label ${!column.visible ? 'disabled' : ''}`}>
                                        {column.label}
                                        {column.sticky && <span className="sticky-indicator">📌</span>}
                                    </span>
                                </div>

                                {column.resizable !== false && (
                                    <div className="column-width-controls">
                                        <button
                                            type="button"
                                            className="btn btn-ghost btn-xs"
                                            onClick={() => updateColumnWidth(column.key, column.width - 10)}
                                            disabled={column.width <= (column.minWidth || 50)}
                                            title="Decrease width"
                                        >
                                            <ChevronLeftIcon />
                                        </button>
                                        <span className="width-display">{column.width}px</span>
                                        <button
                                            type="button"
                                            className="btn btn-ghost btn-xs"
                                            onClick={() => updateColumnWidth(column.key, column.width + 10)}
                                            disabled={column.maxWidth ? column.width >= column.maxWidth : false}
                                            title="Increase width"
                                        >
                                            <ChevronRightIcon />
                                        </button>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>

                    <div className="column-chooser-footer">
                        <small>
                            Settings are saved automatically and persist across sessions.
                        </small>
                    </div>
                </div>
            )}
        </div>
    );
}

// Hook for using column management in components
export function useColumnManager(tableId: string, defaultColumns: ColumnConfig[]) {
    const [columns, setColumns] = useState<ColumnConfig[]>(defaultColumns);

    // Generate CSS styles for column widths
    const getColumnStyles = () => {
        const styles: { [key: string]: React.CSSProperties } = {};
        columns.forEach(col => {
            if (col.visible) {
                styles[col.key] = {
                    width: `${col.width}px`,
                    minWidth: `${col.minWidth || 50}px`,
                    maxWidth: col.maxWidth ? `${col.maxWidth}px` : undefined
                };
            }
        });
        return styles;
    };

    // Get visible columns only
    const getVisibleColumns = () => columns.filter(col => col.visible);

    // Check if column is visible
    const isColumnVisible = (columnKey: string) => {
        const column = columns.find(col => col.key === columnKey);
        return column ? column.visible : false;
    };

    // Get column width
    const getColumnWidth = (columnKey: string) => {
        const column = columns.find(col => col.key === columnKey);
        return column ? column.width : 150;
    };

    return {
        columns,
        setColumns,
        getColumnStyles,
        getVisibleColumns,
        isColumnVisible,
        getColumnWidth
    };
}
