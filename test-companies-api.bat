@echo off
echo ===========================================
echo FAR SIGHTED - COMPANIES API TEST
echo Professional Advisory Services - CA
echo ===========================================
echo.

cd /d "E:\Projects\FAR Sighted"

echo 🔍 Testing Companies API Endpoint...
echo.

echo 📡 Step 1: Check if backend is running on port 8090
netstat -an | find ":8090" | find "LISTENING" >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Backend is running on port 8090
) else (
    echo    ❌ Backend is NOT running on port 8090
    echo    💡 Start backend: cd backend && npm start
    goto :end
)

echo.
echo 🏥 Step 2: Backend Health Check
curl -s -w "Response Code: %%{http_code}\n" http://localhost:8090/api/health
if %errorlevel% == 0 (
    echo    ✅ Health check passed
) else (
    echo    ❌ Health check failed
    goto :end
)

echo.
echo 🔄 Step 3: Migration Status Check
curl -s http://localhost:8090/api/migration-status
echo.
if %errorlevel% == 0 (
    echo    ✅ Migration status check passed
) else (
    echo    ❌ Migration status check failed
)

echo.
echo 🏢 Step 4: Companies API Test (THE MAIN ISSUE)
echo    Testing: GET http://localhost:8090/api/companies
echo.
curl -s -w "Response Code: %%{http_code}\nResponse Time: %%{time_total}s\n" http://localhost:8090/api/companies
if %errorlevel% == 0 (
    echo    ✅ Companies API responded
    echo    🎯 If response shows companies, dropdown should work!
) else (
    echo    ❌ Companies API failed
    echo    💡 This is likely why the dropdown is empty
)

echo.
echo 🔍 Step 5: Detailed Response Analysis
echo    Getting full response with headers...
curl -i -s http://localhost:8090/api/companies

echo.
echo 📋 Troubleshooting Guide:
echo    • If you see "503" or "migration required" = Run migrate-database.bat
echo    • If you see "[]" (empty array) = No companies in database
echo    • If you see company data = Frontend issue, check browser console
echo    • If no response = Backend connection issue

:end
echo.
pause
