/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { FC } from 'react';

interface WelcomeModalProps {
    isOpen: boolean;
    onClose: () => void;
}

export const WelcomeModal: FC<WelcomeModalProps> = ({ isOpen, onClose }) => {
    if (!isOpen) return null;

    const handleOverlayClick = (e: React.MouseEvent) => {
        if (e.target === e.currentTarget) {
            onClose();
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Escape') {
            onClose();
        }
    };

    return (
        <div 
            className="modal-overlay" 
            onClick={handleOverlayClick}
            onKeyDown={handleKeyDown}
            tabIndex={-1}
        >
            <div className="welcome-modal">
                <div className="welcome-content">
                    <img 
                        src="/FAR Logo 2.webp" 
                        alt="FAR Sighted Logo" 
                        className="welcome-logo" 
                    />
                    <h1 className="welcome-title">Welcome to FAR Sighted</h1>
                    <p className="welcome-subtitle">Fixed Asset Register Management System</p>
                    
                    <div className="welcome-description">
                        <p>
                            FAR Sighted is a comprehensive Fixed Asset Register management system 
                            designed to help you track, manage, and report on your organization's 
                            fixed assets with ease and precision.
                        </p>
                        
                        <div className="welcome-features">
                            <h3>Key Features:</h3>
                            <ul>
                                <li>Multi-company asset management</li>
                                <li>Automated depreciation calculations</li>
                                <li>Schedule III compliance reporting</li>
                                <li>Asset lifecycle tracking</li>
                                <li>Comprehensive audit trails</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div className="welcome-actions">
                        <button 
                            type="button" 
                            className="btn btn-primary welcome-btn"
                            onClick={onClose}
                        >
                            Get Started
                        </button>
                    </div>
                    
                    <div className="welcome-note">
                        <p>
                            <strong>Note:</strong> Select a company from the dropdown above to begin. 
                            You will be prompted to login when accessing company data.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};
