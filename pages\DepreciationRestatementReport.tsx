/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, useEffect, useMemo } from 'react';
import { api } from '../lib/api';
import type { Asset, AssetImpact } from '../lib/db-server';
import { DownloadIcon, FileTextIcon, CalendarIcon, TrendingUpIcon } from '../Icons';
import { formatIndianNumber, exportToExcel } from '../lib/utils';

interface DepreciationRestatementReportProps {
    companyId: string;
    companyName: string;
    showAlert: (title: string, message: string, type: 'success' | 'error' | 'info') => void;
}

interface YearlyImpact {
    year: string;
    totalOldDepreciation: number;
    totalNewDepreciation: number;
    totalOldClosingWDV: number;
    totalNewClosingWDV: number;
    affectedAssetsCount: number;
    impactPercentage: number;
}

interface RestatementData {
    changeDate: string;
    changeDescription: string;
    affectedYears: string[];
    yearlyImpacts: YearlyImpact[];
    assetImpacts: AssetImpact[];
    totalImpactAmount: number;
}

const DeltaValue: React.FC<{ value: number; showPercentage?: boolean }> = ({ value, showPercentage = false }) => {
    const formattedValue = formatIndianNumber(Math.abs(value));
    const color = value > 0 ? 'var(--accent-success)' : value < 0 ? 'var(--accent-danger)' : 'var(--text-secondary)';
    const sign = value > 0 ? '+' : value < 0 ? '-' : '';
    const percentage = showPercentage && value !== 0 ? ` (${(value > 0 ? '+' : '')}${(value * 100).toFixed(2)}%)` : '';
    
    return (
        <span style={{ color, fontWeight: 500 }}>
            {sign}{formattedValue}{percentage}
        </span>
    );
};

export function DepreciationRestatementReport({ companyId, companyName, showAlert }: DepreciationRestatementReportProps) {
    const [loading, setLoading] = useState(false);
    const [restatementData, setRestatementData] = useState<RestatementData[]>([]);
    const [selectedPeriod, setSelectedPeriod] = useState<string>('all');
    const [availableYears, setAvailableYears] = useState<string[]>([]);

    useEffect(() => {
        loadAvailableYears();
    }, [companyId]);

    const loadAvailableYears = async () => {
        try {
            // Get all financial years that have asset data
            const assets = await api.getAssets(companyId);
            const years = new Set<string>();
            
            // Extract years from asset data (this would need to be implemented in the API)
            // For now, we'll use a placeholder
            const currentYear = new Date().getFullYear();
            for (let i = 0; i < 5; i++) {
                years.add(`${currentYear - i}-${(currentYear - i + 1).toString().slice(-2)}`);
            }
            
            setAvailableYears(Array.from(years).sort().reverse());
        } catch (error) {
            console.error('Error loading available years:', error);
            showAlert('Error', 'Failed to load available years', 'error');
        }
    };

    const generateRestatementReport = async () => {
        setLoading(true);
        try {
            // This would need to be implemented in the backend
            // For now, we'll create a mock implementation
            const mockData: RestatementData[] = [
                {
                    changeDate: '2024-01-15',
                    changeDescription: 'Updated asset life from 5 to 10 years for Computer Equipment',
                    affectedYears: ['2022-23', '2023-24', '2024-25'],
                    yearlyImpacts: [
                        {
                            year: '2022-23',
                            totalOldDepreciation: 500000,
                            totalNewDepreciation: 250000,
                            totalOldClosingWDV: 1500000,
                            totalNewClosingWDV: 1750000,
                            affectedAssetsCount: 5,
                            impactPercentage: -50
                        },
                        {
                            year: '2023-24',
                            totalOldDepreciation: 300000,
                            totalNewDepreciation: 175000,
                            totalOldClosingWDV: 1200000,
                            totalNewClosingWDV: 1575000,
                            affectedAssetsCount: 5,
                            impactPercentage: -41.67
                        },
                        {
                            year: '2024-25',
                            totalOldDepreciation: 240000,
                            totalNewDepreciation: 157500,
                            totalOldClosingWDV: 960000,
                            totalNewClosingWDV: 1417500,
                            affectedAssetsCount: 5,
                            impactPercentage: -34.38
                        }
                    ],
                    assetImpacts: [], // Would contain detailed asset-level impacts
                    totalImpactAmount: 832500
                }
            ];

            setRestatementData(mockData);
            showAlert('Success', 'Depreciation re-statement report generated successfully', 'success');
        } catch (error) {
            console.error('Error generating restatement report:', error);
            showAlert('Error', 'Failed to generate depreciation re-statement report', 'error');
        } finally {
            setLoading(false);
        }
    };

    const exportReport = () => {
        if (restatementData.length === 0) {
            showAlert('No Data', 'No restatement data available to export', 'info');
            return;
        }

        // Prepare summary data for export
        const summaryData = restatementData.flatMap(data => 
            data.yearlyImpacts.map(impact => ({
                'Change Date': data.changeDate,
                'Change Description': data.changeDescription,
                'Financial Year': impact.year,
                'Affected Assets': impact.affectedAssetsCount,
                'Old Depreciation': impact.totalOldDepreciation,
                'New Depreciation': impact.totalNewDepreciation,
                'Depreciation Impact': impact.totalNewDepreciation - impact.totalOldDepreciation,
                'Old Closing WDV': impact.totalOldClosingWDV,
                'New Closing WDV': impact.totalNewClosingWDV,
                'WDV Impact': impact.totalNewClosingWDV - impact.totalOldClosingWDV,
                'Impact %': impact.impactPercentage.toFixed(2) + '%'
            }))
        );

        const filename = `Depreciation_Restatement_Report_${companyName.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}`;
        
        exportToExcel(summaryData, filename, `Depreciation Re-statement Report - ${companyName}`);
        showAlert('Success', `Report exported as ${filename}.xlsx`, 'success');
    };

    const filteredData = useMemo(() => {
        if (selectedPeriod === 'all') return restatementData;
        return restatementData.filter(data => 
            data.affectedYears.includes(selectedPeriod)
        );
    }, [restatementData, selectedPeriod]);

    const totalImpactSummary = useMemo(() => {
        const summary = filteredData.reduce((acc, data) => {
            data.yearlyImpacts.forEach(impact => {
                if (selectedPeriod === 'all' || impact.year === selectedPeriod) {
                    acc.totalDepreciationImpact += (impact.totalNewDepreciation - impact.totalOldDepreciation);
                    acc.totalWDVImpact += (impact.totalNewClosingWDV - impact.totalOldClosingWDV);
                    acc.affectedYears.add(impact.year);
                }
            });
            return acc;
        }, {
            totalDepreciationImpact: 0,
            totalWDVImpact: 0,
            affectedYears: new Set<string>()
        });

        return {
            ...summary,
            affectedYearsCount: summary.affectedYears.size
        };
    }, [filteredData, selectedPeriod]);

    return (
        <div className="page-container">
            <div className="view-header">
                <div className="view-header-content">
                    <FileTextIcon className="icon-primary" />
                    <h2>Depreciation Re-statement Report</h2>
                </div>
                <div className="actions">
                    <select 
                        value={selectedPeriod} 
                        onChange={(e) => setSelectedPeriod(e.target.value)}
                        className="form-select"
                        disabled={loading}
                    >
                        <option value="all">All Years</option>
                        {availableYears.map(year => (
                            <option key={year} value={year}>{year}</option>
                        ))}
                    </select>
                    <button 
                        type="button" 
                        className="btn btn-primary" 
                        onClick={generateRestatementReport}
                        disabled={loading}
                    >
                        <TrendingUpIcon /> {loading ? 'Generating...' : 'Generate Report'}
                    </button>
                    <button 
                        type="button" 
                        className="btn btn-excel" 
                        onClick={exportReport}
                        disabled={loading || restatementData.length === 0}
                    >
                        <DownloadIcon /> Export Report
                    </button>
                </div>
            </div>

            {restatementData.length > 0 && (
                <div className="summary-cards">
                    <div className="summary-card">
                        <div className="summary-card-header">
                            <CalendarIcon className="icon-info" />
                            <h3>Impact Summary</h3>
                        </div>
                        <div className="summary-card-content">
                            <div className="summary-stat">
                                <span className="summary-label">Affected Years:</span>
                                <span className="summary-value">{totalImpactSummary.affectedYearsCount}</span>
                            </div>
                            <div className="summary-stat">
                                <span className="summary-label">Total Depreciation Impact:</span>
                                <span className="summary-value">
                                    <DeltaValue value={totalImpactSummary.totalDepreciationImpact} />
                                </span>
                            </div>
                            <div className="summary-stat">
                                <span className="summary-label">Total WDV Impact:</span>
                                <span className="summary-value">
                                    <DeltaValue value={totalImpactSummary.totalWDVImpact} />
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {loading && (
                <div className="loading-indicator">
                    <div className="loading-spinner"></div>
                    <p>Generating depreciation re-statement report...</p>
                </div>
            )}

            {!loading && restatementData.length === 0 && (
                <div className="empty-state">
                    <FileTextIcon className="icon-secondary" />
                    <h3>No Re-statement Data</h3>
                    <p>Click "Generate Report" to analyze depreciation impacts from historical data changes.</p>
                </div>
            )}

            {filteredData.length > 0 && (
                <div className="restatement-data">
                    {filteredData.map((data, index) => (
                        <div key={index} className="restatement-section">
                            <div className="restatement-header">
                                <h3>Change on {new Date(data.changeDate).toLocaleDateString()}</h3>
                                <p>{data.changeDescription}</p>
                                <div className="affected-years">
                                    <strong>Affected Years:</strong> {data.affectedYears.join(', ')}
                                </div>
                            </div>

                            <div className="table-container">
                                <table className="data-table">
                                    <thead>
                                        <tr>
                                            <th>Financial Year</th>
                                            <th className="text-center">Affected Assets</th>
                                            <th className="text-right">Old Depreciation</th>
                                            <th className="text-right">New Depreciation</th>
                                            <th className="text-right">Depreciation Impact</th>
                                            <th className="text-right">Old Closing WDV</th>
                                            <th className="text-right">New Closing WDV</th>
                                            <th className="text-right">WDV Impact</th>
                                            <th className="text-center">Impact %</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {data.yearlyImpacts.map(impact => (
                                            <tr key={impact.year}>
                                                <td><strong>{impact.year}</strong></td>
                                                <td className="text-center">{impact.affectedAssetsCount}</td>
                                                <td className="text-right">{formatIndianNumber(impact.totalOldDepreciation)}</td>
                                                <td className="text-right">{formatIndianNumber(impact.totalNewDepreciation)}</td>
                                                <td className="text-right">
                                                    <DeltaValue value={impact.totalNewDepreciation - impact.totalOldDepreciation} />
                                                </td>
                                                <td className="text-right">{formatIndianNumber(impact.totalOldClosingWDV)}</td>
                                                <td className="text-right">{formatIndianNumber(impact.totalNewClosingWDV)}</td>
                                                <td className="text-right">
                                                    <DeltaValue value={impact.totalNewClosingWDV - impact.totalOldClosingWDV} />
                                                </td>
                                                <td className="text-center">
                                                    <DeltaValue value={impact.impactPercentage / 100} showPercentage />
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}
