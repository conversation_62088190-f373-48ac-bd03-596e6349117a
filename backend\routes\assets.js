import express from 'express';
import dbService from '../services/database.js';

const router = express.Router();

// Get assets for a company
router.get('/company/:companyId', async (req, res) => {
    try {
        const { companyId } = req.params;
        
        const assets = await dbService.all(
            `SELECT 
                id as assetDbId,
                record_id as recordId,
                asset_particulars as assetParticulars,
                book_entry_date as bookEntryDate,
                put_to_use_date as putToUseDate,
                basic_amount as basicAmount,
                duties_taxes as dutiesTaxes,
                gross_amount as grossAmount,
                vendor,
                invoice_no as invoiceNo,
                model_make as modelMake,
                location,
                asset_id as assetId,
                remarks,
                ledger_name_in_books as ledgerNameInBooks,
                asset_group as assetGroup,
                asset_sub_group as assetSubGroup,
                schedule_iii_classification as scheduleIIIClassification,
                disposal_date as disposalDate,
                disposal_amount as disposalAmount,
                salvage_percentage as salvagePercentage,
                wdv_of_adoption_date as wdvOfAdoptionDate,
                is_leasehold as isLeasehold,
                depreciation_method as depreciationMethod,
                life_in_years as lifeInYears,
                lease_period as leasePeriod,
                scrap_it as scrapIt
            FROM assets 
            WHERE company_id = ? 
            ORDER BY record_id`, [companyId]
        );
        
        // Get yearly data for each asset
        for (let asset of assets) {
            const yearlyData = await dbService.all(
                `SELECT 
                    year_range,
                    opening_wdv,
                    use_days,
                    depreciation_amount,
                    closing_wdv,
                    second_shift_days,
                    third_shift_days
                FROM asset_yearly_data 
                WHERE asset_id = ?`, [asset.assetDbId]
            );
            
            // Add yearly data as dynamic properties to match frontend format
            yearlyData.forEach(yd => {
                asset[`Opening-WDV-${yd.year_range}`] = yd.opening_wdv;
                asset[`Use Days-${yd.year_range}`] = yd.use_days;
                asset[`Depreciation-${yd.year_range}`] = yd.depreciation_amount;
                asset[`WDV-${yd.year_range}`] = yd.closing_wdv;
                asset[`2nd Shift Days-${yd.year_range}`] = yd.second_shift_days;
                asset[`3rd Shift Days-${yd.year_range}`] = yd.third_shift_days;
            });
            
            // Remove the database ID from response
            delete asset.assetDbId;
        }
        
        res.json(assets);
    } catch (error) {
        console.error('Error fetching assets:', error);
        res.status(500).json({ error: 'Failed to fetch assets' });
    }
});

// Update assets for a company
router.put('/company/:companyId', async (req, res) => {
    try {
        const { companyId } = req.params;
        const { assets } = req.body;
        
        if (!Array.isArray(assets)) {
            return res.status(400).json({ error: 'Assets must be an array' });
        }
        
        await dbService.beginTransaction();
        
        try {
            // Process assets that need new record IDs
            const existingAssets = await dbService.all(
                'SELECT record_id FROM assets WHERE company_id = ?', [companyId]
            );
            const existingIds = new Set(existingAssets.map(a => a.record_id));
            
            // Generate new record IDs for assets with temporary IDs
            const prefixCounts = new Map();
            for (const asset of assets) {
                if (asset.recordId.startsWith('new_')) {
                    const prefix = (asset.ledgerNameInBooks?.[0] || 'X').toUpperCase();
                    
                    // Find max existing number for this prefix
                    let maxNum = 0;
                    for (const existingId of existingIds) {
                        if (existingId.startsWith(prefix)) {
                            const num = parseInt(existingId.substring(1), 10);
                            if (!isNaN(num)) {
                                maxNum = Math.max(maxNum, num);
                            }
                        }
                    }
                    
                    const currentMax = prefixCounts.get(prefix) || maxNum;
                    const newNum = currentMax + 1;
                    prefixCounts.set(prefix, newNum);
                    
                    const newRecordId = `${prefix}${String(newNum).padStart(4, '0')}`;
                    asset.recordId = newRecordId;
                    existingIds.add(newRecordId);
                }
            }
            
            // Delete all existing assets for this company
            await dbService.run('DELETE FROM assets WHERE company_id = ?', [companyId]);
            
            // Insert updated assets
            for (const asset of assets) {
                await dbService.run(
                    `INSERT INTO assets (
                        company_id, record_id, asset_particulars, book_entry_date, put_to_use_date,
                        basic_amount, duties_taxes, gross_amount, vendor, invoice_no, model_make,
                        location, asset_id, remarks, ledger_name_in_books, asset_group,
                        asset_sub_group, schedule_iii_classification, disposal_date, disposal_amount,
                        salvage_percentage, wdv_of_adoption_date, is_leasehold, depreciation_method,
                        life_in_years, lease_period, scrap_it
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        companyId, asset.recordId, asset.assetParticulars, asset.bookEntryDate,
                        asset.putToUseDate, asset.basicAmount, asset.dutiesTaxes, asset.grossAmount,
                        asset.vendor, asset.invoiceNo, asset.modelMake, asset.location,
                        asset.assetId, asset.remarks, asset.ledgerNameInBooks, asset.assetGroup,
                        asset.assetSubGroup, asset.scheduleIIIClassification, asset.disposalDate,
                        asset.disposalAmount, asset.salvagePercentage, asset.wdvOfAdoptionDate,
                        asset.isLeasehold, asset.depreciationMethod, asset.lifeInYears,
                        asset.leasePeriod, asset.scrapIt
                    ]
                );
            }
            
            await dbService.commit();
            
            // Return updated assets
            const updatedAssets = await dbService.all(
                `SELECT 
                    record_id as recordId,
                    asset_particulars as assetParticulars,
                    book_entry_date as bookEntryDate,
                    put_to_use_date as putToUseDate,
                    basic_amount as basicAmount,
                    duties_taxes as dutiesTaxes,
                    gross_amount as grossAmount,
                    vendor, invoice_no as invoiceNo, model_make as modelMake,
                    location, asset_id as assetId, remarks,
                    ledger_name_in_books as ledgerNameInBooks,
                    asset_group as assetGroup,
                    asset_sub_group as assetSubGroup,
                    schedule_iii_classification as scheduleIIIClassification,
                    disposal_date as disposalDate,
                    disposal_amount as disposalAmount,
                    salvage_percentage as salvagePercentage,
                    wdv_of_adoption_date as wdvOfAdoptionDate,
                    is_leasehold as isLeasehold,
                    depreciation_method as depreciationMethod,
                    life_in_years as lifeInYears,
                    lease_period as leasePeriod,
                    scrap_it as scrapIt
                FROM assets WHERE company_id = ? ORDER BY record_id`, [companyId]
            );
            
            res.json(updatedAssets);
        } catch (error) {
            await dbService.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error updating assets:', error);
        res.status(500).json({ error: 'Failed to update assets' });
    }
});

export default router;
