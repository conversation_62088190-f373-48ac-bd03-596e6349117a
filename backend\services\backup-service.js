import fs from 'fs/promises';
import fsSync from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import archiver from 'archiver';
import cron from 'node-cron';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class BackupService {
    constructor(databaseManager) {
        this.databaseManager = databaseManager;
        this.backupPath = null;
        this.isScheduled = false;
        this.cronJob = null;
        this.maxBackups = 5;
        this.lastBackupHash = null;
    }

    /**
     * Set backup path and validate it
     */
    async setBackupPath(backupPath) {
        try {
            // Ensure the backup directory exists
            await fs.mkdir(backupPath, { recursive: true });
            this.backupPath = backupPath;
            
            // Save backup path to settings
            await this.saveBackupSettings();
            
            console.log(`[BackupService] Backup path set to: ${backupPath}`);
            return true;
        } catch (error) {
            console.error('[BackupService] Failed to set backup path:', error);
            throw new Error(`Failed to set backup path: ${error.message}`);
        }
    }

    /**
     * Get current backup path
     */
    getBackupPath() {
        return this.backupPath;
    }

    /**
     * Load backup settings from database
     */
    async loadBackupSettings() {
        try {
            const settings = await this.databaseManager.getMasterQuery(
                'SELECT backup_path, last_backup_hash FROM backup_settings WHERE id = 1'
            );
            
            if (settings) {
                this.backupPath = settings.backup_path;
                this.lastBackupHash = settings.last_backup_hash;
            }
        } catch (error) {
            console.log('[BackupService] No backup settings found, will create on first backup');
        }
    }

    /**
     * Save backup settings to database
     */
    async saveBackupSettings() {
        try {
            await this.databaseManager.runMasterQuery(
                `INSERT OR REPLACE INTO backup_settings (id, backup_path, last_backup_hash, updated_at)
                 VALUES (1, ?, ?, datetime('now'))`,
                [this.backupPath, this.lastBackupHash]
            );
        } catch (error) {
            console.error('[BackupService] Failed to save backup settings:', error);
        }
    }

    /**
     * Calculate hash of all data to detect changes
     */
    async calculateDataHash() {
        try {
            // Get audit log count and latest timestamp as a simple change indicator
            const auditInfo = await this.databaseManager.getMasterQuery(
                'SELECT COUNT(*) as count, MAX(created_at) as latest FROM global_audit_logs'
            );
            
            // Get companies count and latest update
            const companiesInfo = await this.databaseManager.getMasterQuery(
                'SELECT COUNT(*) as count, MAX(updated_at) as latest FROM companies'
            );
            
            // Create a simple hash from these values
            const hashData = `${auditInfo?.count || 0}-${auditInfo?.latest || ''}-${companiesInfo?.count || 0}-${companiesInfo?.latest || ''}`;
            return hashData;
        } catch (error) {
            console.error('[BackupService] Failed to calculate data hash:', error);
            return Date.now().toString(); // Fallback to timestamp
        }
    }

    /**
     * Check if backup is needed (data has changed)
     */
    async isBackupNeeded() {
        const currentHash = await this.calculateDataHash();
        const needed = this.lastBackupHash !== currentHash;
        
        if (needed) {
            console.log('[BackupService] Data changes detected, backup needed');
        } else {
            console.log('[BackupService] No data changes detected, skipping backup');
        }
        
        return needed;
    }

    /**
     * Create backup archive
     */
    async createBackup() {
        if (!this.backupPath) {
            throw new Error('Backup path not set. Please configure backup path first.');
        }

        // Check if backup is needed
        if (!(await this.isBackupNeeded())) {
            console.log('[BackupService] Backup skipped - no changes detected');
            return { success: true, skipped: true, message: 'No changes detected' };
        }

        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
            const backupFileName = `FAR-Backup-${timestamp}.zip`;
            const backupFilePath = path.join(this.backupPath, backupFileName);

            // Create backup archive
            const output = fsSync.createWriteStream(backupFilePath);
            const archive = archiver('zip', { zlib: { level: 9 } });

            return new Promise((resolve, reject) => {
                output.on('close', async () => {
                    try {
                        console.log(`[BackupService] Backup created: ${backupFilePath} (${archive.pointer()} bytes)`);
                        
                        // Update last backup hash
                        this.lastBackupHash = await this.calculateDataHash();
                        await this.saveBackupSettings();
                        
                        // Clean up old backups
                        await this.cleanupOldBackups();
                        
                        resolve({
                            success: true,
                            filePath: backupFilePath,
                            fileName: backupFileName,
                            size: archive.pointer()
                        });
                    } catch (error) {
                        reject(error);
                    }
                });

                archive.on('error', (err) => {
                    reject(err);
                });

                archive.pipe(output);

                // Add database files
                const masterDbPath = this.databaseManager.masterDbPath;
                const companiesDir = this.databaseManager.basePath;

                // Add master database
                if (fsSync.existsSync(masterDbPath)) {
                    archive.file(masterDbPath, { name: 'master.db' });
                }

                // Add all company databases
                if (fsSync.existsSync(companiesDir)) {
                    archive.directory(companiesDir, 'companies');
                }

                // Add any additional files if needed
                const configPath = path.join(__dirname, '../../config');
                if (fsSync.existsSync(configPath)) {
                    archive.directory(configPath, 'config');
                }

                archive.finalize();
            });
        } catch (error) {
            console.error('[BackupService] Backup failed:', error);
            throw error;
        }
    }

    /**
     * Clean up old backups, keeping only the latest 5
     */
    async cleanupOldBackups() {
        try {
            const files = await fs.readdir(this.backupPath);
            const backupFiles = files
                .filter(file => file.startsWith('FAR-Backup-') && file.endsWith('.zip'))
                .map(file => ({
                    name: file,
                    path: path.join(this.backupPath, file),
                    stat: fsSync.statSync(path.join(this.backupPath, file))
                }))
                .sort((a, b) => b.stat.mtime - a.stat.mtime);

            // Keep only the latest maxBackups files
            if (backupFiles.length > this.maxBackups) {
                const filesToDelete = backupFiles.slice(this.maxBackups);
                
                for (const file of filesToDelete) {
                    await fs.unlink(file.path);
                    console.log(`[BackupService] Deleted old backup: ${file.name}`);
                }
            }
        } catch (error) {
            console.error('[BackupService] Failed to cleanup old backups:', error);
        }
    }

    /**
     * Schedule automatic backups (every Saturday at noon)
     */
    startScheduledBackups() {
        if (this.isScheduled) {
            console.log('[BackupService] Scheduled backups already running');
            return;
        }

        // Schedule for every Saturday at 12:00 PM
        this.cronJob = cron.schedule('0 12 * * 6', async () => {
            console.log('[BackupService] Running scheduled backup...');
            try {
                const result = await this.createBackup();
                if (result.skipped) {
                    console.log('[BackupService] Scheduled backup skipped - no changes');
                } else {
                    console.log(`[BackupService] Scheduled backup completed: ${result.fileName}`);
                }
            } catch (error) {
                console.error('[BackupService] Scheduled backup failed:', error);
            }
        }, {
            scheduled: false,
            timezone: "Asia/Kolkata"
        });

        this.cronJob.start();
        this.isScheduled = true;
        console.log('[BackupService] Scheduled backups started (every Saturday at 12:00 PM)');
    }

    /**
     * Stop scheduled backups
     */
    stopScheduledBackups() {
        if (this.cronJob) {
            this.cronJob.stop();
            this.cronJob = null;
        }
        this.isScheduled = false;
        console.log('[BackupService] Scheduled backups stopped');
    }

    /**
     * Check if app was closed during scheduled backup time and run backup if needed
     */
    async checkMissedBackup() {
        try {
            const lastBackupTime = await this.getLastBackupTime();
            const now = new Date();
            const lastSaturday = new Date(now);
            
            // Find last Saturday at 12:00 PM
            lastSaturday.setDate(now.getDate() - ((now.getDay() + 1) % 7));
            lastSaturday.setHours(12, 0, 0, 0);
            
            // If last Saturday was more than a week ago, find the previous Saturday
            if (lastSaturday > now) {
                lastSaturday.setDate(lastSaturday.getDate() - 7);
            }

            // If no backup since last scheduled time, run backup
            if (!lastBackupTime || lastBackupTime < lastSaturday) {
                console.log('[BackupService] Missed backup detected, running backup...');
                const result = await this.createBackup();
                if (!result.skipped) {
                    console.log(`[BackupService] Missed backup completed: ${result.fileName}`);
                }
                return result;
            }
        } catch (error) {
            console.error('[BackupService] Failed to check missed backup:', error);
        }
        return null;
    }

    /**
     * Get last backup time from file system
     */
    async getLastBackupTime() {
        try {
            if (!this.backupPath || !fsSync.existsSync(this.backupPath)) {
                return null;
            }

            const files = await fs.readdir(this.backupPath);
            const backupFiles = files
                .filter(file => file.startsWith('FAR-Backup-') && file.endsWith('.zip'))
                .map(file => ({
                    name: file,
                    stat: fsSync.statSync(path.join(this.backupPath, file))
                }))
                .sort((a, b) => b.stat.mtime - a.stat.mtime);

            return backupFiles.length > 0 ? backupFiles[0].stat.mtime : null;
        } catch (error) {
            console.error('[BackupService] Failed to get last backup time:', error);
            return null;
        }
    }

    /**
     * Initialize backup service
     */
    async initialize() {
        try {
            // Create backup_settings table if it doesn't exist
            await this.databaseManager.runMasterQuery(`
                CREATE TABLE IF NOT EXISTS backup_settings (
                    id INTEGER PRIMARY KEY,
                    backup_path TEXT,
                    last_backup_hash TEXT,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // Load existing settings
            await this.loadBackupSettings();

            // Check for missed backups if backup path is set
            if (this.backupPath) {
                setTimeout(() => this.checkMissedBackup(), 5000); // Check after 5 seconds
                this.startScheduledBackups();
            }

            console.log('[BackupService] Initialized successfully');
        } catch (error) {
            console.error('[BackupService] Failed to initialize:', error);
        }
    }
}

export default BackupService;
