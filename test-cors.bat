@echo off
echo =============================================
echo FAR SIGHTED - CORS TEST
echo Professional Advisory Services - CA
echo =============================================
echo.

echo 🔍 Testing CORS configuration...
echo.

echo Test 1: Health check with CORS headers
echo ======================================
curl -s -H "Origin: http://localhost:9090" -i http://localhost:8090/api/health | findstr -i "access-control"
if %errorlevel% == 0 (
    echo ✅ CORS headers found in response
) else (
    echo ❌ CORS headers missing
)

echo.
echo Test 2: Companies API with CORS headers
echo =======================================
curl -s -H "Origin: http://localhost:9090" -H "Content-Type: application/json" http://localhost:8090/api/companies
echo.

echo Test 3: OPTIONS preflight request (what browser sends)
echo =====================================================
curl -s -X OPTIONS -H "Origin: http://localhost:9090" -H "Access-Control-Request-Method: GET" -i http://localhost:8090/api/companies | findstr -i "access-control"
if %errorlevel% == 0 (
    echo ✅ Preflight CORS request working
) else (
    echo ❌ Preflight CORS request failing
)

echo.
echo Test 4: Full response headers
echo =============================
curl -s -H "Origin: http://localhost:9090" -i http://localhost:8090/api/companies
echo.

echo =============================================
echo 💡 What to look for:
echo    ✅ Access-Control-Allow-Origin: http://localhost:9090
echo    ✅ Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
echo    ✅ Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept
echo    ✅ Company data in response body
echo.

pause
