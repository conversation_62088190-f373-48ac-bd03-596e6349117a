/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import * as XLSX from 'xlsx';
import type { Asset, CompanyData, StatutoryRate } from './db-server';
import type { AssetValidationErrors, ValidatedAsset } from './types';

// --- Helper Functions ---
export const formatIndianNumber = (num: number | null | undefined | string): string => {
    if (num === null || num === undefined) return '';
    if (typeof num === 'string' && num === 'Not Set') return '';
    if (typeof num !== 'number') return '';
    // Round the number before formatting to handle cases where calculations might still produce floats.
    const roundedNum = Math.round(num);
    return roundedNum.toLocaleString('en-IN', {
        maximumFractionDigits: 0,
    });
};

/**
 * Calculates the number of days between two dates, inclusive.
 * This function is timezone-safe by comparing dates in UTC.
 * @param d1 The start date.
 * @param d2 The end date.
 * @returns The number of days.
 */
export const inclusiveDateDiffInDays = (d1: Date, d2: Date): number => {
    if (!d1 || !d2) return 0;
    // Use UTC methods to get date components to avoid timezone issues.
    const utc1 = Date.UTC(d1.getUTCFullYear(), d1.getUTCMonth(), d1.getUTCDate());
    const utc2 = Date.UTC(d2.getUTCFullYear(), d2.getUTCMonth(), d2.getUTCDate());
    const msPerDay = 1000 * 60 * 60 * 24;
    // Add +1 for inclusivity
    return Math.floor((utc2 - utc1) / msPerDay) + 1;
};

/**
 * Checks if a given value is a string in 'YYYY-MM-DD' format and represents a valid date.
 * @param dateString The value to check.
 * @returns `true` if the string is a valid date, `false` otherwise.
 */
export const isValidDateString = (dateString: any): boolean => {
    if (!dateString || typeof dateString !== 'string') return false;
    // Check for YYYY-MM-DD format and valid date object
    const regex = /^\d{4}-\d{2}-\d{2}$/;
    if(!regex.test(dateString)) return false;
    const d = new Date(dateString);
    return d instanceof Date && !isNaN(d.getTime());
};

export function sortData<T>(
    data: T[],
    sortConfig: { key: string; direction: 'asc' | 'desc' } | null
): T[] {
    if (!sortConfig) {
        return data;
    }
    const sortableItems = [...data];
    sortableItems.sort((a, b) => {
        const aValue = (a as any)[sortConfig.key];
        const bValue = (b as any)[sortConfig.key];

        if (aValue === null || aValue === undefined) return 1;
        if (bValue === null || bValue === undefined) return -1;

        if (typeof aValue === 'number' && typeof bValue === 'number') {
            if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
            if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
            return 0;
        }

        if (typeof aValue === 'string' && typeof bValue === 'string') {
            // Check if strings are numeric and sort accordingly
            const numA = parseFloat(aValue);
            const numB = parseFloat(bValue);
            if (!isNaN(numA) && !isNaN(numB)) {
                if (numA < numB) return sortConfig.direction === 'asc' ? -1 : 1;
                if (numA > numB) return sortConfig.direction === 'asc' ? 1 : -1;
                return 0;
            }
            return sortConfig.direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
        }

        // Fallback for other types like booleans
        if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;

        return 0;
    });
    return sortableItems;
}

interface ExportToExcelOptions {
    data: any[];
    companyName: string;
    year: string;
    reportName: string;
    sheetName?: string;
}

/**
 * Exports an array of objects to an Excel file with a standardized filename.
 * @param options An object containing data and metadata for the export.
 * @returns {boolean} `true` if export was initiated, `false` if there was no data.
 */
export const exportToExcel = ({
    data,
    companyName,
    year,
    reportName,
    sheetName = 'Report'
}: ExportToExcelOptions): boolean => {
    if (!data || data.length === 0) {
        return false;
    }

    const companyNameClean = (companyName || "Company").split(' ')[0];
    const reportNameClean = reportName.replace(/ /g, '_');
    const finalFileName = `${companyNameClean}_${year}_${reportNameClean}`;

    const ws = XLSX.utils.json_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, sheetName);
    XLSX.writeFile(wb, `${finalFileName}.xlsx`);
    return true;
};


interface ExportToCSVOptions {
    data: any[];
    headers: string[];
    companyName: string;
    year: string;
    reportName: string;
}

const escapeCSV = (value: any): string => {
    const str = String(value ?? '');
    if (str.includes(',') || str.includes('"') || str.includes('\n')) {
        return `"${str.replace(/"/g, '""')}"`;
    }
    return str;
};

/**
 * Exports an array of objects to a CSV file.
 * @param options An object containing data and metadata for the CSV export.
 * @returns `true` if export was initiated, `false` if there was no data.
 */
export const exportToCSV = ({
    data,
    headers,
    companyName,
    year,
    reportName
}: ExportToCSVOptions): boolean => {
    if (!data || data.length === 0) {
        return false;
    }

    const companyNameClean = (companyName || "Company").split(' ')[0];
    const reportNameClean = reportName.replace(/ /g, '_');
    const finalFileName = `${companyNameClean}_${year}_${reportNameClean}.csv`;

    const headerRow = headers.join(',');
    const dataRows = data.map(row => {
        return headers.map(header => escapeCSV(row[header])).join(',');
    });

    const csvContent = [headerRow, ...dataRows].join('\n');
    const blob = new Blob([`\uFEFF${csvContent}`], { type: 'text/csv;charset=utf-8;' }); // Add BOM for Excel compatibility
    const link = document.createElement("a");
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", finalFileName);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
    
    return true;
};

interface ExportTemplateToExcelOptions {
    headers: string[];
    companyName: string;
    reportName: string;
    sheetName?: string;
}

/**
 * Exports a template with only headers to an Excel file.
 * @param options An object containing headers and metadata for the export.
 * @returns {boolean} `true` if export was initiated, `false` if there were no headers.
 */
export const exportTemplateToExcel = ({
    headers,
    companyName,
    reportName,
    sheetName = 'Template'
}: ExportTemplateToExcelOptions): boolean => {
    if (!headers || headers.length === 0) {
        return false;
    }

    const companyNameClean = (companyName || "Company").split(' ')[0];
    const reportNameClean = reportName.replace(/ /g, '_');
    const finalFileName = `${companyNameClean}_${reportNameClean}.xlsx`;

    // Create a worksheet from an array of arrays (aoa) with just the headers
    const ws = XLSX.utils.aoa_to_sheet([headers]);

    // Set column widths for better readability
    ws['!cols'] = headers.map(() => ({ wch: 30 }));

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, sheetName);
    XLSX.writeFile(wb, `${finalFileName}.xlsx`);
    return true;
};


export interface YearlyMetrics {
    openingGross: number;
    additionsGross: number;
    deletionsGross: number;
    closingGross: number;
    openingDepreciation: number;
    additionsDepreciation: number; // This is the depreciation for the year
    deletionsDepreciation: number;
    closingDepreciation: number;
    openingNetBlock: number;
    closingNetBlock: number;
    // For LedgerWise
    deletionSaleAmount: number;
    deletionWDV: number;
    profitOnDeletion: number;
}


export const calculateYearlyMetrics = (
    asset: Asset,
    year: string,
    companyData: Pick<CompanyData, 'info' | 'financialYears' | 'statutoryRates'>
): YearlyMetrics => {
    const { info, financialYears, statutoryRates } = companyData;
    const [startYearStr, endYearStr] = year.split('-');
    const fyStart = new Date(`${startYearStr}-04-01T00:00:00.000Z`);
    const fyEnd = new Date(`${endYearStr}-03-31T23:59:59.999Z`);
    const firstAdoptionDate = new Date(info.firstDateOfAdoption);
    
    const putToUseDate = new Date(asset.putToUseDate);
    const disposalDate = asset.disposalDate ? new Date(asset.disposalDate) : null;
    const grossAmount = asset.grossAmount || 0;
    const lifeInYears = asset.lifeInYears || 0;

    // Gross Block
    const openingGross = (putToUseDate < fyStart && (!disposalDate || disposalDate >= fyStart)) ? grossAmount : 0;
    const additionsGross = (putToUseDate >= fyStart && putToUseDate <= fyEnd) ? grossAmount : 0;
    const isDisposalInFY = disposalDate && disposalDate >= fyStart && disposalDate <= fyEnd;
    const deletionsGross = isDisposalInFY ? grossAmount : 0;
    const closingGross = openingGross + additionsGross - deletionsGross;

    // CORRECTED: Opening Depreciation & WDV Calculation
    const currentYearIndex = financialYears.indexOf(year);
    const isFirstYearInSystem = currentYearIndex === 0;
    const previousYears = financialYears.slice(0, currentYearIndex);
    
    let openingDepreciation = 0;
    let openingWDV = 0;

    /**
     * CASE 1: First Year of System Implementation
     */
    if (isFirstYearInSystem) {
        if (putToUseDate < firstAdoptionDate && asset.wdvOfAdoptionDate != null) {
            // Historical asset - use WDV at adoption date
            openingWDV = asset.wdvOfAdoptionDate;
            openingDepreciation = grossAmount - openingWDV;
        } else if (putToUseDate >= firstAdoptionDate && putToUseDate < fyStart) {
            // Asset added before first FY but after adoption - calculate partial depreciation
            const partialDepreciation = calculatePartialYearDepreciation(asset, firstAdoptionDate, fyStart);
            openingDepreciation = partialDepreciation;
            openingWDV = grossAmount - openingDepreciation;
        } else {
            // Asset exists at beginning of first year (not historical, not fresh addition)
            if (putToUseDate < fyStart) {
                // Asset was added in previous period within same first year
                // Calculate depreciation from put to use date to FY start
                const partialDepreciation = calculatePartialYearDepreciation(asset, putToUseDate, fyStart);
                openingDepreciation = partialDepreciation;
                openingWDV = Math.max(0, grossAmount - openingDepreciation);
            } else {
                // Fresh addition in current year
                openingDepreciation = 0;
                openingWDV = 0; // Asset will be added during the year
            }
        }
    }
    /**
     * CASE 2: Subsequent Years - CORRECTED LOGIC
     */
    else {
        if (putToUseDate < fyStart) {
            // Asset existed in previous years - calculate accumulated depreciation correctly
            let accumulatedDepreciation = 0;
            
            // Handle historical assets for first year calculation
            if (putToUseDate < firstAdoptionDate && asset.wdvOfAdoptionDate != null) {
                accumulatedDepreciation = grossAmount - asset.wdvOfAdoptionDate;
            } else if (putToUseDate >= firstAdoptionDate && putToUseDate < new Date(`${financialYears[0].split('-')[0]}-04-01`)) {
                accumulatedDepreciation = calculatePartialYearDepreciation(asset, firstAdoptionDate, new Date(`${financialYears[0].split('-')[0]}-04-01`));
            }
            
            // Add depreciation from all completed years
            for (const prevYear of previousYears) {
                const yearDepreciation = asset[`Depreciation-${prevYear}`] || 0;
                accumulatedDepreciation += yearDepreciation;
            }
            
            // CRITICAL FIX: If no yearly depreciation data exists, calculate fallback depreciation
            if (accumulatedDepreciation === 0 && previousYears.length > 0) {
                // Calculate fallback accumulated depreciation based on asset age and method
                const assetAgeInYears = previousYears.length;
                const salvagePercentage = asset.salvagePercentage || 0;
                const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));
                
                if (asset.depreciationMethod === 'SLM') {
                    // Straight Line Method: (Gross - Salvage) / Life * Years elapsed
                    const depreciableAmount = grossAmount - salvageValue;
                    const yearlyDepreciation = depreciableAmount / lifeInYears;
                    accumulatedDepreciation = Math.min(yearlyDepreciation * assetAgeInYears, depreciableAmount);
                } else if (asset.depreciationMethod === 'WDV' && lifeInYears > 0 && salvageValue < grossAmount) {
                    // Written Down Value Method: Apply rate for each year
                    const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
                    let currentWDV = grossAmount;
                    
                    for (let year = 0; year < assetAgeInYears; year++) {
                        const yearlyDepreciation = currentWDV * rate;
                        accumulatedDepreciation += yearlyDepreciation;
                        currentWDV -= yearlyDepreciation;
                        
                        // Don't depreciate below salvage value
                        if (currentWDV <= salvageValue) {
                            accumulatedDepreciation = grossAmount - salvageValue;
                            break;
                        }
                    }
                }
                
                // Ensure we don't exceed maximum depreciable amount
                const maxDepreciable = grossAmount - salvageValue;
                accumulatedDepreciation = Math.min(accumulatedDepreciation, maxDepreciable);
            }
            
            openingDepreciation = accumulatedDepreciation;
            openingWDV = Math.max(0, grossAmount - openingDepreciation);
        } else {
            // Asset to be added in current year
            openingDepreciation = 0;
            openingWDV = 0;
        }
    }
    
    // Depreciation for the Year (aka additionsDepreciation for reports)
    const salvagePercentage = asset.salvagePercentage || 0;
    const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));
    const startOfUseInFY = putToUseDate > fyStart ? putToUseDate : fyStart;
    const endOfUseInFY = disposalDate && disposalDate < fyEnd ? disposalDate : fyEnd;
    let useDaysInFY = 0;
    if (endOfUseInFY >= startOfUseInFY && putToUseDate <= endOfUseInFY) {
        useDaysInFY = inclusiveDateDiffInDays(startOfUseInFY, endOfUseInFY);
    }

    let depreciationForYear = 0;
    
    // COMPREHENSIVE FIX: Ensure depreciation is calculated for all valid scenarios
    const isCurrentYearAddition = (putToUseDate >= fyStart && putToUseDate <= fyEnd);
    
    // For additions in current year, use gross amount as base
    // For existing assets, use opening WDV (which should be > 0 for valid assets)
    let depreciationBase = 0;
    if (isCurrentYearAddition) {
        depreciationBase = grossAmount;
    } else if (openingWDV > 0) {
        depreciationBase = openingWDV;
    } else {
        // Fallback: if openingWDV is 0 but asset exists, use gross amount
        depreciationBase = grossAmount;
    }
    
    // Debug logging for troubleshooting
    console.log(`Asset ${asset.recordId}: isAddition=${isCurrentYearAddition}, base=${depreciationBase}, life=${lifeInYears}, useDays=${useDaysInFY}`);
    
    if (lifeInYears > 0 && depreciationBase > salvageValue && useDaysInFY > 0) {
        if (asset.depreciationMethod === 'SLM') {
            const depreciableAmount = grossAmount - salvageValue;
            const yearlyDepreciation = depreciableAmount / lifeInYears;
            depreciationForYear = (yearlyDepreciation / 365.25) * useDaysInFY;
        } else { // WDV
            if (grossAmount > 0 && salvageValue < grossAmount) {
                const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
                const yearlyDepreciation = depreciationBase * rate;
                depreciationForYear = (yearlyDepreciation / 365.25) * useDaysInFY;
            }
        }
        
        // Ensure we don't depreciate below salvage value
        const maxDepreciationAllowed = depreciationBase - salvageValue;
        depreciationForYear = Math.min(depreciationForYear, maxDepreciationAllowed);
    } else {
        // Log why depreciation was not calculated
        console.log(`No depreciation: life=${lifeInYears}, base=${depreciationBase}, salvage=${salvageValue}, days=${useDaysInFY}`);
    }
    
    depreciationForYear = Math.max(0, Math.round(depreciationForYear));
    console.log(`Final depreciation for ${asset.recordId}: ${depreciationForYear}`);
    
    // Extra Shift
    const statutoryRatesMap = new Map(statutoryRates.map(r => [r.assetSubGroup, r]));
    const statutoryInfo = statutoryRatesMap.get(asset.assetSubGroup);
    if (statutoryInfo?.extraShiftDepreciation === 'Yes') {
        const secondShiftDays = asset[`2nd Shift Days-${year}`] || 0;
        const thirdShiftDays = asset[`3rd Shift Days-${year}`] || 0;
        if (secondShiftDays > 0 || thirdShiftDays > 0) {
            const depreciableBase = grossAmount - salvageValue;
            if (depreciableBase > 0) {
                const singleShiftNormalDepr = (depreciableBase / lifeInYears) / 365.25;
                const extraShiftForDays = singleShiftNormalDepr * ((secondShiftDays * 0.5) + (thirdShiftDays * 1.0));
                depreciationForYear += extraShiftForDays;
            }
        }
    }

    const maxDepreciation = openingWDV - salvageValue;
    depreciationForYear = Math.round(Math.max(0, Math.min(depreciationForYear, maxDepreciation)));
    const additionsDepreciation = depreciationForYear;

    // Disposal Calculations
    let deletionsDepreciation = 0;
    let deletionWDV = 0;
    let profitOnDeletion = 0;
    const deletionSaleAmount = isDisposalInFY ? (asset.disposalAmount || 0) : 0;
    
    if (isDisposalInFY) {
        const totalAccumulatedDepreciationOnDisposal = openingDepreciation + additionsDepreciation;
        deletionsDepreciation = totalAccumulatedDepreciationOnDisposal; // For schedule III, this is the reversal amount
        deletionWDV = grossAmount - totalAccumulatedDepreciationOnDisposal;
        profitOnDeletion = deletionSaleAmount - deletionWDV;
    }

    // Final Closing Values
    const closingDepreciation = openingDepreciation + additionsDepreciation - deletionsDepreciation;
    const openingNetBlock = openingGross - openingDepreciation;
    const closingNetBlock = closingGross - closingDepreciation;
    
    return {
        openingGross,
        additionsGross,
        deletionsGross,
        closingGross,
        openingDepreciation,
        additionsDepreciation,
        deletionsDepreciation,
        closingDepreciation,
        openingNetBlock,
        closingNetBlock,
        deletionSaleAmount,
        deletionWDV,
        profitOnDeletion,
    };
};

/**
 * Helper function to calculate partial year depreciation
 * for assets added between adoption date and first financial year
 * @param asset The asset for which to calculate partial depreciation
 * @param startDate Start date for depreciation calculation
 * @param endDate End date for depreciation calculation
 * @returns Partial year depreciation amount
 */
const calculatePartialYearDepreciation = (
    asset: Asset,
    startDate: Date,
    endDate: Date
): number => {
    const grossAmount = asset.grossAmount || 0;
    const lifeInYears = asset.lifeInYears || 0;
    const salvagePercentage = asset.salvagePercentage || 0;
    const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));
    
    if (lifeInYears <= 0 || grossAmount <= salvageValue) return 0;
    
    const useDays = inclusiveDateDiffInDays(startDate, endDate);
    
    if (asset.depreciationMethod === 'SLM') {
        const depreciableAmount = grossAmount - salvageValue;
        const yearlyDepreciation = depreciableAmount / lifeInYears;
        return Math.round((yearlyDepreciation / 365.25) * useDays);
    } else { // WDV
        if (grossAmount > 0 && salvageValue < grossAmount) {
            const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
            const yearlyDepreciation = grossAmount * rate;
            return Math.round((yearlyDepreciation / 365.25) * useDays);
        }
    }
    return 0;
};


// --- IMPORT UTILS ---

const headerMapping: { [key: string]: keyof Asset } = {
    'Asset Particulars': 'assetParticulars', 'Book Entry Date': 'bookEntryDate', 'Put to use Date': 'putToUseDate', 'Basic Amount': 'basicAmount', 'Duties & Taxes': 'dutiesTaxes', 'Gross Amount': 'grossAmount', 'Vendor/ Source': 'vendor', 'Invoice/ Bill No.': 'invoiceNo', 'Model/ Make': 'modelMake', 'Location': 'location', 'Asset ID/ Serial No': 'assetId', 'Remarks': 'remarks', 'Ledger Name in Books': 'ledgerNameInBooks', 'Asset Group as per Co. Act (SCH II)': 'assetGroup', 'Nature of Asset as per Co. Act  (SCH II)': 'assetSubGroup', 'Schedule III Classification': 'scheduleIIIClassification', 'Disposal Date': 'disposalDate', 'Disposal Amount': 'disposalAmount', 'Salvage %age': 'salvagePercentage', 'WDV of Adoption Date': 'wdvOfAdoptionDate', 'Assets under leasehold': 'isLeasehold', 'Depreciation method': 'depreciationMethod', 'Life in years': 'lifeInYears', 'Lease Period': 'leasePeriod', 'Scrap It': 'scrapIt'
};

const defaultAssetValues: Asset = {
    recordId: '', assetParticulars: '', bookEntryDate: '', putToUseDate: '', basicAmount: 0, dutiesTaxes: 0, grossAmount: 0, vendor: '', invoiceNo: '', modelMake: '', location: '', assetId: '', remarks: '', ledgerNameInBooks: '', assetGroup: '', assetSubGroup: '', scheduleIIIClassification: '', disposalDate: null, disposalAmount: null, salvagePercentage: 5, wdvOfAdoptionDate: null, isLeasehold: false, depreciationMethod: 'WDV', lifeInYears: 0, leasePeriod: null, scrapIt: false,
};

export const parseAssetsFromExcel = (data: any[], companyId: string): Asset[] => {
    const assets: Asset[] = [];

    for (let i = 0; i < data.length; i++) {
        const rowData = data[i];
        const asset: Asset = { ...defaultAssetValues, recordId: `new_import_${companyId}_${i}` };

        for (const excelHeader in rowData) {
            const normalizedHeader = excelHeader.trim().replace(/\*$/, '');
            const mappedHeader = headerMapping[normalizedHeader];

            if (mappedHeader) {
                const val = rowData[excelHeader];

                switch (mappedHeader) {
                    case 'bookEntryDate':
                    case 'putToUseDate':
                    case 'disposalDate': {
                        if (typeof val === 'number' && val > 0) { // Excel's date serial number
                            const jsDate = new Date(Math.round((val - 25569) * 86400 * 1000));
                            (asset as any)[mappedHeader] = jsDate.toISOString().split('T')[0];
                        } else if (typeof val === 'string' && val.trim()) {
                            (asset as any)[mappedHeader] = val.trim();
                        } else {
                            (asset as any)[mappedHeader] = null;
                        }
                        break;
                    }
                    case 'basicAmount':
                    case 'dutiesTaxes':
                    case 'grossAmount':
                    case 'disposalAmount':
                    case 'wdvOfAdoptionDate': {
                        if (val === null || val === undefined || val === '') {
                            (asset as any)[mappedHeader] = null;
                            break;
                        }
                        const parsedInt = parseInt(String(val).replace(/,/g, ''), 10);
                        (asset as any)[mappedHeader] = !isNaN(parsedInt) ? parsedInt : null;
                        break;
                    }
                    case 'salvagePercentage':
                    case 'lifeInYears':
                    case 'leasePeriod': {
                        if (val === null || val === undefined || val === '') {
                            (asset as any)[mappedHeader] = null;
                            break;
                        }
                        const parsedFloat = parseFloat(String(val).replace(/,/g, ''));
                        (asset as any)[mappedHeader] = !isNaN(parsedFloat) ? parsedFloat : null;
                        break;
                    }
                    case 'isLeasehold':
                    case 'scrapIt':
                        (asset as any)[mappedHeader] = String(val).toLowerCase() === 'yes';
                        break;
                    default:
                        (asset as any)[mappedHeader] = val ? String(val).trim() : null;
                }
            }
        }
        
        if (asset.grossAmount === null && asset.basicAmount !== null) {
            asset.grossAmount = (asset.basicAmount || 0) + (asset.dutiesTaxes || 0);
        }
        assets.push(asset);
    }
    return assets;
};

export const parseAssetsFromCSV = (csvString: string, companyId: string): Asset[] => {
    // This is a more robust CSV parser that handles newlines and commas inside quoted fields.
    const rows: string[][] = [];
    let currentRow: string[] = [];
    let currentField = '';
    let inQuotedField = false;

    // Sanitize input
    const sanitizedCsv = csvString.trim().replace(/^\uFEFF/, '');

    for (let i = 0; i < sanitizedCsv.length; i++) {
        const char = sanitizedCsv[i];
        
        if (inQuotedField) {
            if (char === '"') {
                // Check for an escaped quote (" ")
                if (i + 1 < sanitizedCsv.length && sanitizedCsv[i + 1] === '"') {
                    currentField += '"';
                    i++; // Skip the second quote of the pair
                } else {
                    inQuotedField = false;
                }
            } else {
                currentField += char;
            }
        } else { // Not in a quoted field
            if (char === ',') {
                currentRow.push(currentField);
                currentField = '';
            } else if (char === '\n' || char === '\r') {
                currentRow.push(currentField);
                rows.push(currentRow);
                currentRow = [];
                currentField = '';
                // Handle CRLF by also skipping the '\n'
                if (char === '\r' && i + 1 < sanitizedCsv.length && sanitizedCsv[i + 1] === '\n') {
                    i++;
                }
            } else if (char === '"' && currentField.length === 0) {
                // A quote should only start a quoted field if it's the first character
                inQuotedField = true;
            } else {
                currentField += char;
            }
        }
    }
    // Add the last field and row
    currentRow.push(currentField);
    rows.push(currentRow);

    // Filter out completely empty rows that might be added at the end
    const nonEmptyRows = rows.filter(row => row.length > 1 || (row.length === 1 && row[0].trim() !== ''));

    if (nonEmptyRows.length < 2) return [];

    const headers = nonEmptyRows[0].map(h => h.trim());
    const dataRows = nonEmptyRows.slice(1);
    const assets: Asset[] = [];

    for (let i = 0; i < dataRows.length; i++) {
        const values = dataRows[i];
        if (values.length < headers.length) continue; 

        const asset: Asset = { ...defaultAssetValues, recordId: `new_import_${companyId}_${i}` };

        for (let j = 0; j < headers.length; j++) {
            const header = headers[j].replace('*', ''); // Handle compulsory headers
            const mappedHeader = headerMapping[header] || headerMapping[header.replace(/\s/g, '')]; // Handle different spacing
            if (mappedHeader) {
                const val = (values[j] || '').trim();
                
                switch (mappedHeader) {
                    case 'basicAmount':
                    case 'dutiesTaxes':
                    case 'grossAmount':
                    case 'disposalAmount':
                    case 'wdvOfAdoptionDate': {
                        if (!val) {
                            (asset as any)[mappedHeader] = null;
                            break;
                        }
                        const parsedInt = parseInt(val.replace(/,/g, ''), 10);
                        (asset as any)[mappedHeader] = !isNaN(parsedInt) ? parsedInt : null;
                        break;
                    }
                    case 'salvagePercentage':
                    case 'lifeInYears':
                    case 'leasePeriod': {
                        if (!val) {
                            (asset as any)[mappedHeader] = null;
                            break;
                        }
                        const parsedFloat = parseFloat(val.replace(/,/g, ''));
                        (asset as any)[mappedHeader] = !isNaN(parsedFloat) ? parsedFloat : null;
                        break;
                    }
                    case 'isLeasehold':
                    case 'scrapIt':
                        (asset as any)[mappedHeader] = val.toLowerCase() === 'yes';
                        break;
                    default:
                        (asset as any)[mappedHeader] = val || null;
                }
            }
        }
        
        if (asset.grossAmount === null && asset.basicAmount !== null) {
            asset.grossAmount = (asset.basicAmount || 0) + (asset.dutiesTaxes || 0);
        }
        assets.push(asset);
    }
    return assets;
};


export const validateAsset = (
    asset: Asset,
    rowNumber: number,
    compulsoryHeaders: string[],
    fieldMapping: { [key: string]: keyof Asset | 'actions' },
    companyInfo: CompanyData['info'],
    statutoryRates: StatutoryRate[],
    statutoryMap: Map<string, StatutoryRate>
): { errors: AssetValidationErrors, warnings: AssetValidationErrors } => {
    const errors: AssetValidationErrors = {};
    const warnings: AssetValidationErrors = {};
    
    compulsoryHeaders.forEach(header => {
        const fieldKey = fieldMapping[header] as keyof Asset;
        if (typeof fieldKey === 'symbol' || fieldKey === 'actions') return;
        if (asset[fieldKey] === null || asset[fieldKey] === undefined || String(asset[fieldKey]).trim() === '') {
            errors[fieldKey] = `${header} is required.`;
        }
    });

    if (asset.bookEntryDate && !isValidDateString(asset.bookEntryDate)) {
        errors.bookEntryDate = 'Invalid format. Use YYYY-MM-DD.';
    }
    if (asset.putToUseDate && !isValidDateString(asset.putToUseDate)) {
        errors.putToUseDate = 'Invalid format. Use YYYY-MM-DD.';
    }
    if (asset.disposalDate && !isValidDateString(asset.disposalDate)) {
        errors.disposalDate = 'Invalid format. Use YYYY-MM-DD.';
    }
    const effectivePutToUseDate = asset.putToUseDate || asset.bookEntryDate;

    if (isValidDateString(asset.bookEntryDate) && isValidDateString(effectivePutToUseDate)) {
        if (new Date(effectivePutToUseDate) < new Date(asset.bookEntryDate)) {
            errors.putToUseDate = 'Put to Use Date cannot be earlier than Book Entry Date.';
        }
    }
    if (isValidDateString(asset.disposalDate) && isValidDateString(effectivePutToUseDate)) {
        if (new Date(asset.disposalDate) < new Date(effectivePutToUseDate)) {
            errors.disposalDate = 'Disposal Date cannot be earlier than Put to Use Date.';
        }
    }

    if (effectivePutToUseDate && isValidDateString(effectivePutToUseDate) && new Date(effectivePutToUseDate) < new Date(companyInfo.firstDateOfAdoption)) {
        if (asset.wdvOfAdoptionDate === null || asset.wdvOfAdoptionDate === undefined) {
            errors.wdvOfAdoptionDate = 'WDV of Adoption Date is required for historical assets.';
        }
    }

    const validGroups = new Set(statutoryRates.map(r => r.assetGroup));
    if(asset.assetGroup && !validGroups.has(asset.assetGroup)) {
        errors.assetGroup = 'Invalid Asset Group.';
    }

    const validSubGroups = new Set(statutoryRates.filter(r => r.assetGroup === asset.assetGroup).map(r => r.assetSubGroup));
    if(asset.assetSubGroup && !validSubGroups.has(asset.assetSubGroup)) {
        errors.assetSubGroup = 'Invalid Sub-Group for the selected Asset Group.';
    }

    if (!['WDV', 'SLM'].includes(asset.depreciationMethod)) {
        errors.depreciationMethod = 'Must be WDV or SLM.';
    }
    
    const statutoryInfo = statutoryMap.get(`${asset.assetGroup}|${asset.assetSubGroup}`);
    if (statutoryInfo?.isStatutory === 'Yes' && asset.lifeInYears && asset.lifeInYears !== parseInt(statutoryInfo.usefulLifeYears, 10)) {
        warnings.lifeInYears = `Input life of ${asset.lifeInYears} ignored. Statutory life of ${statutoryInfo.usefulLifeYears} years will be applied.`;
    }
    
    return { errors, warnings };
};

export const exportImportErrorsToExcel = (
    invalidAssets: ValidatedAsset[],
    allHeaders: string[],
    fieldMapping: { [key: string]: keyof Asset | 'actions' },
    companyName: string
) => {
    if (invalidAssets.length === 0) return;
    
    const dataToExport = invalidAssets.map(asset => {
        const row: { [key: string]: any } = {};
        allHeaders.forEach(header => {
            const fieldKey = fieldMapping[header];
            if (fieldKey && fieldKey !== 'actions') {
                 if (typeof fieldKey !== 'symbol') { // Type guard
                    row[header] = asset[fieldKey];
                }
            }
        });
        row['Row Number'] = asset.rowNumber;
        row['Errors'] = Object.entries(asset.errors).map(([field, msg]) => `${field}: ${msg}`).join('; ');
        return row;
    });

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    exportToExcel({
        data: dataToExport,
        companyName: companyName || "System",
        year: 'Import',
        reportName: `Import_Errors_${timestamp}`,
        sheetName: 'Rejected_Rows'
    });
};