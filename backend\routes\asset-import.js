import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import dbService from '../services/database.js';

const router = express.Router();

// Import assets for a company
router.post('/company/:companyId/import', async (req, res) => {
    try {
        const { companyId } = req.params;
        const { assets } = req.body;
        
        if (!Array.isArray(assets)) {
            return res.status(400).json({ error: 'Assets must be an array' });
        }
        
        await dbService.beginTransaction();
        
        try {
            // Get existing assets to determine next record IDs
            const existingAssets = await dbService.all(
                'SELECT record_id FROM assets WHERE company_id = ?', [companyId]
            );
            const existingIds = new Set(existingAssets.map(a => a.record_id));
            
            const prefixCounts = new Map();
            
            // Process each asset for import
            for (const asset of assets) {
                // Generate new record ID
                const prefix = (asset.ledgerNameInBooks?.[0] || 'X').toUpperCase();
                
                // Find max existing number for this prefix
                let maxNum = 0;
                for (const existingId of existingIds) {
                    if (existingId.startsWith(prefix)) {
                        const num = parseInt(existingId.substring(1), 10);
                        if (!isNaN(num)) {
                            maxNum = Math.max(maxNum, num);
                        }
                    }
                }
                
                const currentMax = prefixCounts.get(prefix) || maxNum;
                const newNum = currentMax + 1;
                prefixCounts.set(prefix, newNum);
                
                const newRecordId = `${prefix}${String(newNum).padStart(4, '0')}`;
                asset.recordId = newRecordId;
                existingIds.add(newRecordId);
                
                // Insert the asset
                await dbService.run(
                    `INSERT INTO assets (
                        company_id, record_id, asset_particulars, book_entry_date, put_to_use_date,
                        basic_amount, duties_taxes, gross_amount, vendor, invoice_no, model_make,
                        location, asset_id, remarks, ledger_name_in_books, asset_group,
                        asset_sub_group, schedule_iii_classification, disposal_date, disposal_amount,
                        salvage_percentage, wdv_of_adoption_date, is_leasehold, depreciation_method,
                        life_in_years, lease_period, scrap_it
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        companyId, asset.recordId, asset.assetParticulars, asset.bookEntryDate,
                        asset.putToUseDate, asset.basicAmount, asset.dutiesTaxes, asset.grossAmount,
                        asset.vendor, asset.invoiceNo, asset.modelMake, asset.location,
                        asset.assetId, asset.remarks, asset.ledgerNameInBooks, asset.assetGroup,
                        asset.assetSubGroup, asset.scheduleIIIClassification, asset.disposalDate,
                        asset.disposalAmount, asset.salvagePercentage, asset.wdvOfAdoptionDate,
                        asset.isLeasehold, asset.depreciationMethod, asset.lifeInYears,
                        asset.leasePeriod, asset.scrapIt
                    ]
                );
            }
            
            await dbService.commit();
            res.json({ message: `Successfully imported ${assets.length} assets` });
            
        } catch (error) {
            await dbService.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error importing assets:', error);
        res.status(500).json({ error: 'Failed to import assets' });
    }
});

export default router;
