# FAR Sighted Multi-Database Implementation Guide

## Executive Summary

This document outlines the implementation of separate databases per company in the FAR Sighted application, addressing the major design deviation identified in the current system.

## Current Issue

The existing implementation uses a single SQLite database (`far_sighted.db`) shared by all companies, which deviates from the design requirement of separate databases per entity.

### Problems with Current Design:
- **Data Isolation**: Companies share the same database space
- **Security Risk**: Potential cross-company data access
- **Scalability Issues**: Single database performance bottleneck
- **Backup Complexity**: Cannot backup individual company data
- **Compliance Risk**: Audit trail mixing across companies

## Implemented Solution

### New Architecture

```
backend/
├── database/
│   ├── master.db                    # Master database (companies registry, users)
│   └── companies/                   # Company-specific databases
│       ├── c1234567890-ABC_Ltd/
│       │   └── company.db          # ABC Ltd's database
│       ├── c1234567891-XYZ_Corp/
│       │   └── company.db          # XYZ Corp's database
│       └── ...
├── services/
│   ├── database-manager/
│   │   ├── DatabaseManager.js      # Multi-database manager
│   │   ├── CompanyDatabaseService.js # Individual company DB service
│   │   └── DatabaseMigrator.js     # Migration utility
│   ├── database-new.js             # Updated database service
│   └── database.js                 # Original (to be replaced)
├── routes/
│   ├── companies-new.js            # Updated companies routes
│   ├── companies.js                # Original (to be replaced)
│   └── admin.js                    # Migration admin routes
└── scripts/
    └── migrate-database.js         # Migration script
```

### Database Structure

#### Master Database (`master.db`)
| Table | Purpose |
|-------|---------|
| `companies` | Company registry with database paths |
| `users` | Global user accounts |
| `global_audit_logs` | System-level audit trail |
| `global_settings` | Application-wide settings |

#### Company Database (`company.db`)
| Table | Purpose |
|-------|---------|
| `assets` | Company's fixed assets |
| `financial_years` | Company's financial years |
| `asset_yearly_data` | Depreciation calculations |
| `statutory_rates` | Asset classification rates |
| `extra_ledgers` | Additional ledger accounts |
| `license_history` | License activation history |
| `audit_logs` | Company-specific audit trail |
| `backup_logs` | Backup operation history |
| `company_settings` | Company-specific settings |

## Implementation Steps

### Step 1: Create New Database Structure

The new database manager files have been created:

- `DatabaseManager.js` - Handles multiple company databases
- `CompanyDatabaseService.js` - Manages individual company operations
- `DatabaseMigrator.js` - Migrates existing data
- `database-new.js` - Updated service with backward compatibility

### Step 2: Migration Process

#### Automatic Migration Script
```bash
cd backend/scripts
node migrate-database.js
```

#### Options:
- `--dry-run` - Preview migration without making changes
- `--force` - Force migration even if new structure exists

#### API-Based Migration
```http
POST /api/admin/migrate
Content-Type: application/json

{
  "force": false
}
```

### Step 3: Update Server Configuration

Replace the database service import in `server.js`:

```javascript
// Old import
import dbService from './services/database.js';

// New import
import dbService from './services/database-new.js';
```

Add admin routes:
```javascript
import adminRoutes from './routes/admin.js';
app.use('/api/admin', adminRoutes);
```

### Step 4: Update Route Files

Replace the companies routes:
```javascript
// Old import
import companyRoutes from './routes/companies.js';

// New import
import companyRoutes from './routes/companies-new.js';
```

## Migration Process Flow

```mermaid
graph TD
    A[Check Migration Status] --> B{Old DB Exists?}
    B -->|No| C[No Migration Needed]
    B -->|Yes| D{New Structure Exists?}
    D -->|Yes & No Force| E[Skip Migration]
    D -->|No or Force| F[Start Migration]
    F --> G[Extract Old Data]
    G --> H[Create Master DB]
    H --> I[Migrate Users]
    I --> J[For Each Company]
    J --> K[Create Company Folder]
    K --> L[Create Company DB]
    L --> M[Migrate Company Data]
    M --> N[Next Company]
    N --> J
    J --> O[Backup Old DB]
    O --> P[Migration Complete]
```

## Key Features

### 1. Data Isolation
- Each company has its own SQLite database
- Complete separation of company data
- Independent backup and restore per company

### 2. Scalability
- Database operations distributed across multiple files
- Reduced contention and improved performance
- Easy to scale individual company databases

### 3. Security
- No cross-company data access possible
- Company-specific audit trails
- Separate encryption possible per company

### 4. Backup & Recovery
- Individual company backups
- Company-specific restore operations
- System-wide backup including all companies

### 5. Compliance
- Separate audit trails per company
- Independent data retention policies possible
- Company-specific compliance requirements

## Usage Examples

### Setting Company Context
```javascript
// Set context to work with specific company
await dbService.setCompanyContext('c1234567890');

// Now all operations work on that company's database
const assets = await dbService.all('SELECT * FROM assets');
```

### Creating New Company
```javascript
const companyData = {
    companyName: 'ABC Private Limited',
    financialYearStart: '2024-04-01',
    financialYearEnd: '2025-03-31',
    firstDateOfAdoption: '2024-04-01',
    // ... other company fields
};

const newCompany = await dbService.createCompany(companyData);
// Creates company folder and database automatically
```

### Multi-Company Operations
```javascript
// Get all companies
const companies = await dbService.getAllCompanies();

// Work with each company
for (const company of companies) {
    await dbService.setCompanyContext(company.id);
    const assetCount = await dbService.get('SELECT COUNT(*) as count FROM assets');
    console.log(`${company.name}: ${assetCount.count} assets`);
}
```

## Migration Verification

### Pre-Migration Checklist
- [ ] Backup existing database file
- [ ] Verify all company data integrity
- [ ] Test new database structure in development
- [ ] Inform users of maintenance window

### Post-Migration Verification
- [ ] Verify all companies migrated successfully
- [ ] Check asset counts match original
- [ ] Verify user accounts transferred
- [ ] Test login and basic operations
- [ ] Confirm audit trails preserved

### Verification Queries

```sql
-- Check migration completeness
SELECT 
    c.company_name,
    (SELECT COUNT(*) FROM assets WHERE company_id = c.id) as old_assets,
    'Check new database' as new_assets
FROM companies c;
```

## Benefits Achieved

| Aspect | Before | After |
|--------|--------|-------|
| **Data Isolation** | Shared database | Separate databases |
| **Security** | Cross-company risk | Complete isolation |
| **Backup** | All-or-nothing | Per-company granular |
| **Performance** | Single bottleneck | Distributed load |
| **Scalability** | Limited | Horizontal scaling |
| **Compliance** | Mixed audit trails | Separate compliance |

## Backward Compatibility

The new implementation maintains full backward compatibility:

- All existing API endpoints work unchanged
- Database operations use same interface
- Company switching handled transparently
- Migration is non-destructive (original DB backed up)

## Error Handling

### Migration Errors
- Automatic rollback on failure
- Detailed error logging
- Original database preserved
- Partial migration recovery

### Runtime Errors
- Company context validation
- Database connection pooling
- Graceful degradation
- Comprehensive error messages

## Performance Considerations

### Optimization Features
- Database connection caching
- Lazy loading of company databases
- Connection pooling per company
- Efficient context switching

### Monitoring
- Company-specific performance metrics
- Database operation logging
- Resource usage tracking
- Performance bottleneck identification

## Security Enhancements

### Access Control
- Company context enforcement
- User-company relationship validation
- API endpoint protection
- Database-level isolation

### Audit Trail
- Complete operation logging
- User action tracking
- Data change history
- Security event monitoring

## Deployment Instructions

### Development Environment
1. Run migration script: `node scripts/migrate-database.js`
2. Update imports in server files
3. Test with existing data
4. Verify all functionality

### Production Environment
1. Schedule maintenance window
2. Backup existing database
3. Run migration script with monitoring
4. Verify migration success
5. Update application code
6. Monitor post-deployment

## Support and Troubleshooting

### Common Issues
1. **Migration fails**: Check database permissions and disk space
2. **Company context errors**: Ensure company exists in master database
3. **Performance issues**: Monitor database connections and optimize queries

### Debug Tools
- Migration status API: `GET /api/admin/migration-status`
- Database info API: `GET /api/admin/database-info`
- Test company creation: `POST /api/admin/test-company`

## Conclusion

The multi-database implementation successfully addresses the design deviation by providing:

✅ **Complete Data Isolation** - Each company has its own database
✅ **Enhanced Security** - No cross-company data access possible  
✅ **Improved Scalability** - Distributed database operations
✅ **Granular Backup/Restore** - Company-specific operations
✅ **Better Compliance** - Separate audit trails per company
✅ **Backward Compatibility** - Existing code works unchanged

The implementation is production-ready and includes comprehensive migration tools, error handling, and monitoring capabilities.