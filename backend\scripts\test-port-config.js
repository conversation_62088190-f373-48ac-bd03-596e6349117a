#!/usr/bin/env node

/**
 * Test Port Configuration
 * Tests the automatic port selection and fallback functionality
 */

import { getPortConfiguration, checkCommonPorts, isPortAvailable } from '../utils/port-manager.js';

async function testPortConfiguration() {
    console.log('🧪 TESTING PORT CONFIGURATION');
    console.log('=============================\n');

    try {
        // Test 1: Check common ports
        console.log('📋 Test 1: Checking common ports...');
        const busyPorts = await checkCommonPorts();
        
        // Test 2: Get port configuration
        console.log('\n📋 Test 2: Getting port configuration...');
        const config = await getPortConfiguration();
        
        // Test 3: Verify selected ports are actually available
        console.log('\n📋 Test 3: Verifying selected ports...');
        
        const backendAvailable = await isPortAvailable(config.backend.port);
        const frontendAvailable = await isPortAvailable(config.frontend.port);
        
        console.log(`Backend port ${config.backend.port}: ${backendAvailable ? '✅ Available' : '❌ Busy'}`);
        console.log(`Frontend port ${config.frontend.port}: ${frontendAvailable ? '✅ Available' : '❌ Busy'}`);
        
        // Test 4: Test port conflicts
        console.log('\n📋 Test 4: Testing port conflict scenarios...');
        
        const conflictPorts = [3000, 3001, 8080, 8081];
        for (const port of conflictPorts) {
            const available = await isPortAvailable(port);
            console.log(`Port ${port}: ${available ? '✅ Available' : '⚠️  Busy (will be avoided)'}`);
        }
        
        // Test 5: Summary
        console.log('\n📊 CONFIGURATION SUMMARY');
        console.log('========================');
        console.log(`✅ Backend will use port: ${config.backend.port}`);
        console.log(`✅ Frontend will use port: ${config.frontend.port}`);
        console.log(`✅ Avoided busy ports: ${busyPorts.join(', ') || 'None'}`);
        console.log(`✅ LAN access configured: Yes`);
        
        console.log('\n🚀 READY TO START');
        console.log('=================');
        console.log('Backend: npm start');
        console.log('Frontend: npm run dev');
        console.log(`Access: http://localhost:${config.frontend.port}`);
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}

// Run the test
testPortConfiguration().then(() => {
    console.log('\n🏁 Port configuration test completed successfully');
}).catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
});
