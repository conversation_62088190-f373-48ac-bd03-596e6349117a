#!/usr/bin/env node

/**
 * Populate Asset Yearly Data
 * Generates missing yearly depreciation data for all assets and financial years
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database path
const dbPath = join(__dirname, '../database/far_sighted.db');

// Helper function to run queries
const runQuery = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath);
        db.run(sql, params, function(err) {
            if (err) {
                reject(err);
            } else {
                resolve({ id: this.lastID, changes: this.changes });
            }
        });
        db.close();
    });
};

// Helper function to get data
const getData = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath);
        db.all(sql, params, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
        db.close();
    });
};

// Calculate depreciation for a specific year
const calculateYearlyDepreciation = (asset, yearRange) => {
    const [startYearStr, endYearStr] = yearRange.split('-');
    const fyStart = new Date(`${startYearStr}-04-01`);
    const fyEnd = new Date(`${endYearStr}-03-31`);
    
    const putToUseDate = new Date(asset.put_to_use_date);
    const disposalDate = asset.disposal_date ? new Date(asset.disposal_date) : null;
    const grossAmount = asset.gross_amount || 0;
    const lifeInYears = asset.life_in_years || 0;
    const salvagePercentage = asset.salvage_percentage || 0;
    const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));

    // Skip if asset not in use during this year
    if (putToUseDate > fyEnd || (disposalDate && disposalDate < fyStart)) {
        return {
            openingWdv: 0,
            useDays: 0,
            depreciationAmount: 0,
            closingWdv: 0
        };
    }

    // Calculate opening WDV
    let openingWdv = grossAmount;
    if (asset.wdv_of_adoption_date && putToUseDate < fyStart) {
        openingWdv = asset.wdv_of_adoption_date;
    }

    // For subsequent years, we need to calculate from previous years
    // For now, we'll use gross amount as opening WDV for first year
    
    // Calculate use days in financial year
    const startOfUseInFY = putToUseDate > fyStart ? putToUseDate : fyStart;
    const endOfUseInFY = disposalDate && disposalDate < fyEnd ? disposalDate : fyEnd;
    
    let useDays = 0;
    if (endOfUseInFY >= startOfUseInFY && putToUseDate <= endOfUseInFY) {
        useDays = Math.ceil((endOfUseInFY - startOfUseInFY) / (1000 * 60 * 60 * 24)) + 1;
    }

    // Calculate depreciation
    let depreciationAmount = 0;
    if (lifeInYears > 0 && openingWdv > salvageValue && useDays > 0) {
        if (asset.depreciation_method === 'SLM') {
            const depreciableAmount = grossAmount - salvageValue;
            const yearlyDepreciation = depreciableAmount / lifeInYears;
            depreciationAmount = (yearlyDepreciation / 365.25) * useDays;
        } else { // WDV
            if (grossAmount > 0 && salvageValue < grossAmount) {
                const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
                const yearlyDepreciation = openingWdv * rate;
                depreciationAmount = (yearlyDepreciation / 365.25) * useDays;
            }
        }
    }

    const closingWdv = Math.max(openingWdv - depreciationAmount, salvageValue);

    return {
        openingWdv: Math.round(openingWdv),
        useDays: Math.round(useDays),
        depreciationAmount: Math.round(depreciationAmount),
        closingWdv: Math.round(closingWdv)
    };
};

async function populateYearlyData() {
    console.log('🔄 POPULATING ASSET YEARLY DATA');
    console.log('================================\n');

    try {
        // Get all companies
        const companies = await getData('SELECT id, company_name FROM companies ORDER BY company_name');
        console.log(`📊 Found ${companies.length} companies\n`);

        for (const company of companies) {
            console.log(`🏢 Processing: ${company.company_name} (${company.id})`);
            
            // Get assets for this company
            const assets = await getData(`
                SELECT * FROM assets 
                WHERE company_id = ? 
                ORDER BY record_id
            `, [company.id]);

            // Get financial years for this company
            const financialYears = await getData(`
                SELECT year_range FROM financial_years 
                WHERE company_id = ? 
                ORDER BY year_range
            `, [company.id]);

            console.log(`   📋 Assets: ${assets.length}, Financial Years: ${financialYears.length}`);

            // Clear existing yearly data for this company
            await runQuery(`
                DELETE FROM asset_yearly_data 
                WHERE asset_id IN (
                    SELECT id FROM assets WHERE company_id = ?
                )
            `, [company.id]);

            let totalRecords = 0;

            // Generate yearly data for each asset and year combination
            for (const asset of assets) {
                console.log(`   🏭 Processing asset: ${asset.record_id}`);
                
                for (const fy of financialYears) {
                    const yearlyData = calculateYearlyDepreciation(asset, fy.year_range);
                    
                    // Insert yearly data
                    await runQuery(`
                        INSERT INTO asset_yearly_data (
                            asset_id, year_range, opening_wdv, use_days, 
                            depreciation_amount, closing_wdv, second_shift_days, third_shift_days
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        asset.id, fy.year_range, yearlyData.openingWdv, yearlyData.useDays,
                        yearlyData.depreciationAmount, yearlyData.closingWdv, 0, 0
                    ]);

                    totalRecords++;
                    
                    if (yearlyData.depreciationAmount > 0) {
                        console.log(`      📅 ${fy.year_range}: ₹${yearlyData.depreciationAmount.toLocaleString()} depreciation`);
                    }
                }
            }

            console.log(`   ✅ Generated ${totalRecords} yearly data records\n`);
        }

        // Verify the data was created
        const totalYearlyRecords = await getData('SELECT COUNT(*) as count FROM asset_yearly_data');
        console.log(`🎯 SUMMARY:`);
        console.log(`   Total yearly data records created: ${totalYearlyRecords[0].count}`);

        // Show first year data specifically
        const firstYearData = await getData(`
            SELECT ayd.*, a.record_id, a.asset_particulars
            FROM asset_yearly_data ayd
            JOIN assets a ON ayd.asset_id = a.id
            WHERE ayd.year_range = '2022-2023'
            AND a.company_id = 'c1001'
            ORDER BY a.record_id
        `);

        console.log(`\n🎯 FIRST YEAR (2022-2023) DATA VERIFICATION:`);
        firstYearData.forEach(row => {
            console.log(`   ${row.record_id}: ₹${row.depreciation_amount.toLocaleString()} depreciation, ${row.use_days} days`);
        });

        console.log('\n✅ Yearly data population completed successfully!');
        console.log('💡 First year depreciation should now be visible in the frontend.');

    } catch (error) {
        console.error('❌ Error populating yearly data:', error);
        throw error;
    }
}

// Run the population
populateYearlyData().then(() => {
    console.log('\n🏁 Script completed successfully');
}).catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
});
