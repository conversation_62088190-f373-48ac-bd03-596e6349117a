# FAR Sighted License Generator

A secure license generation system for the FAR Sighted Asset Management System.

## Features

- **Secure Encryption**: AES-256-CBC encryption with SHA-256 checksums
- **Company-Specific**: Licenses tied to specific company information
- **Validity Control**: Configurable validity dates and feature permissions
- **Tamper Detection**: Integrity verification and corruption detection
- **User Limits**: Configurable maximum users and companies
- **Interactive CLI**: Easy-to-use command-line interface

## Installation

```bash
cd license-generator
npm install
```

## Usage

### Interactive Mode
```bash
npm start
```

### Generate License
```bash
npm run generate
```

### Validate License
```bash
npm run validate
```

### Show Help
```bash
npm run help
```

## License Generation Process

1. **Company Information**
   - Company Name
   - PAN Number
   - CIN Number (optional)
   - Contact Person
   - Email Address
   - Mobile Number
   - Address

2. **License Details**
   - Valid From Date (YYYY-MM-DD)
   - Valid Until Date (YYYY-MM-DD)
   - Maximum Users
   - Maximum Companies
   - Feature Permissions

3. **Generated Files**
   - `FAR_LICENSE_[CompanyName]_[Timestamp].json` - Encrypted license file
   - `SUMMARY_[CompanyName]_[Timestamp].txt` - Human-readable summary

## License File Structure

```json
{
  "version": "1.0",
  "key": "FAR-2025-COMP-ABC123-TIMESTAMP",
  "data": "encrypted_license_data",
  "iv": "initialization_vector",
  "checksum": "sha256_checksum",
  "generatedAt": "2025-01-10T12:00:00.000Z"
}
```

## Security Features

### Encryption
- **Algorithm**: AES-256-CBC
- **Key Derivation**: PBKDF2 with scrypt
- **Initialization Vector**: Random 16-byte IV per license
- **Integrity**: SHA-256 checksum verification

### License Key Format
```
FAR-[YEAR]-[COMPANY_CODE]-[RANDOM]-[TIMESTAMP]
```

Example: `FAR-2025-TECH-ABC123-1A2B3C4D`

## Validation Process

The validator checks:
1. File structure integrity
2. Checksum verification
3. Decryption success
4. License expiry status
5. Company information match

## Integration with FAR Sighted

### Customer Instructions
1. Receive the license file from vendor
2. Place the `.json` file in FAR Sighted installation directory
3. Open FAR Sighted application
4. Go to License Management
5. Click "Activate License"
6. Select the license file
7. System will validate and activate the license

### Developer Integration
The main FAR Sighted application should include:
- License file reader
- Decryption module
- Validation logic
- Activation workflow
- Expiry monitoring

## File Locations

### Generated Licenses
- **Directory**: `license-generator/generated-licenses/`
- **License Files**: `FAR_LICENSE_*.json`
- **Summaries**: `SUMMARY_*.txt`

### Backup and Security
- Keep generated licenses secure
- Maintain customer records
- Regular backup of license database
- Secure key management

## Troubleshooting

### Common Issues

1. **"License file not found"**
   - Check file path
   - Ensure file exists
   - Verify file permissions

2. **"Invalid license file structure"**
   - File may be corrupted
   - Wrong file format
   - Missing required fields

3. **"Checksum mismatch"**
   - File has been modified
   - Corruption during transfer
   - Regenerate license

4. **"License has expired"**
   - Check validity dates
   - Generate new license
   - Update expiry date

### Support

For technical support:
- Check application logs
- Verify system requirements
- Contact development team

## Development

### Adding Features
1. Update license data structure
2. Modify encryption/decryption functions
3. Update validation logic
4. Test with sample licenses

### Testing
```bash
# Generate test license
npm run generate

# Validate test license
npm run validate
```

## Security Considerations

1. **Key Management**
   - Keep secret key secure
   - Regular key rotation
   - Secure key storage

2. **License Distribution**
   - Secure transmission
   - Verify recipient
   - Track license usage

3. **Monitoring**
   - License activation tracking
   - Expiry notifications
   - Usage monitoring

## Version History

- **v1.0** - Initial release
  - Basic license generation
  - AES-256-CBC encryption
  - Interactive CLI interface
  - Validation system

## License

Apache License 2.0 - See LICENSE file for details.

---

**FAR Sighted License Generator v1.0**  
*Secure License Management for Asset Management Systems*
