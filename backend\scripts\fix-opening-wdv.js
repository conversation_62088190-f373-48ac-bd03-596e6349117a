#!/usr/bin/env node

/**
 * Fix Opening WDV Calculation
 * Corrects the opening WDV to use previous year's closing WDV
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database path
const dbPath = join(__dirname, '../database/far_sighted.db');

// Helper function to run queries
const runQuery = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath);
        db.run(sql, params, function(err) {
            if (err) {
                reject(err);
            } else {
                resolve({ id: this.lastID, changes: this.changes });
            }
        });
        db.close();
    });
};

// Helper function to get data
const getData = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath);
        db.all(sql, params, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
        db.close();
    });
};

// Calculate proper depreciation with correct opening WDV
const recalculateDepreciation = (asset, yearRange, previousClosingWdv = null) => {
    const [startYearStr, endYearStr] = yearRange.split('-');
    const fyStart = new Date(`${startYearStr}-04-01`);
    const fyEnd = new Date(`${endYearStr}-03-31`);
    
    const putToUseDate = new Date(asset.put_to_use_date);
    const disposalDate = asset.disposal_date ? new Date(asset.disposal_date) : null;
    const grossAmount = asset.gross_amount || 0;
    const lifeInYears = asset.life_in_years || 0;
    const salvagePercentage = asset.salvage_percentage || 0;
    const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));

    // Skip if asset not in use during this year
    if (putToUseDate > fyEnd || (disposalDate && disposalDate < fyStart)) {
        return {
            openingWdv: 0,
            useDays: 0,
            depreciationAmount: 0,
            closingWdv: 0
        };
    }

    // Calculate opening WDV
    let openingWdv;
    if (previousClosingWdv !== null) {
        // Use previous year's closing WDV
        openingWdv = previousClosingWdv;
    } else if (asset.wdv_of_adoption_date && putToUseDate < fyStart) {
        // Use adoption WDV for old assets
        openingWdv = asset.wdv_of_adoption_date;
    } else {
        // Use gross amount for first year
        openingWdv = grossAmount;
    }

    // Calculate use days in financial year
    const startOfUseInFY = putToUseDate > fyStart ? putToUseDate : fyStart;
    const endOfUseInFY = disposalDate && disposalDate < fyEnd ? disposalDate : fyEnd;
    
    let useDays = 0;
    if (endOfUseInFY >= startOfUseInFY && putToUseDate <= endOfUseInFY) {
        useDays = Math.ceil((endOfUseInFY - startOfUseInFY) / (1000 * 60 * 60 * 24)) + 1;
    }

    // Calculate depreciation
    let depreciationAmount = 0;
    if (lifeInYears > 0 && openingWdv > salvageValue && useDays > 0) {
        if (asset.depreciation_method === 'SLM') {
            const depreciableAmount = grossAmount - salvageValue;
            const yearlyDepreciation = depreciableAmount / lifeInYears;
            depreciationAmount = (yearlyDepreciation / 365.25) * useDays;
        } else { // WDV
            if (grossAmount > 0 && salvageValue < grossAmount) {
                const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
                const yearlyDepreciation = openingWdv * rate;
                depreciationAmount = (yearlyDepreciation / 365.25) * useDays;
            }
        }
    }

    const closingWdv = Math.max(openingWdv - depreciationAmount, salvageValue);

    return {
        openingWdv: Math.round(openingWdv),
        useDays: Math.round(useDays),
        depreciationAmount: Math.round(depreciationAmount),
        closingWdv: Math.round(closingWdv)
    };
};

async function fixOpeningWdv() {
    console.log('🔧 FIXING OPENING WDV CALCULATIONS');
    console.log('==================================\n');

    try {
        // Get all assets with their yearly data
        const assets = await getData(`
            SELECT DISTINCT a.*, c.company_name
            FROM assets a
            JOIN companies c ON a.company_id = c.id
            ORDER BY c.company_name, a.record_id
        `);

        console.log(`📊 Found ${assets.length} assets to process\n`);

        for (const asset of assets) {
            console.log(`🏭 Processing: ${asset.record_id} - ${asset.asset_particulars}`);
            
            // Get financial years for this asset's company
            const financialYears = await getData(`
                SELECT year_range FROM financial_years 
                WHERE company_id = ? 
                ORDER BY year_range
            `, [asset.company_id]);

            let previousClosingWdv = null;

            for (const fy of financialYears) {
                // Recalculate with proper opening WDV
                const yearlyData = recalculateDepreciation(asset, fy.year_range, previousClosingWdv);
                
                // Update the yearly data
                await runQuery(`
                    UPDATE asset_yearly_data 
                    SET opening_wdv = ?, use_days = ?, depreciation_amount = ?, closing_wdv = ?
                    WHERE asset_id = ? AND year_range = ?
                `, [
                    yearlyData.openingWdv, yearlyData.useDays,
                    yearlyData.depreciationAmount, yearlyData.closingWdv,
                    asset.id, fy.year_range
                ]);

                // Set previous closing WDV for next year
                previousClosingWdv = yearlyData.closingWdv;

                if (yearlyData.depreciationAmount > 0) {
                    console.log(`   📅 ${fy.year_range}: Opening ₹${yearlyData.openingWdv.toLocaleString()}, Depreciation ₹${yearlyData.depreciationAmount.toLocaleString()}, Closing ₹${yearlyData.closingWdv.toLocaleString()}`);
                }
            }
            console.log('');
        }

        // Verify the corrections
        console.log('🎯 VERIFICATION - First Year vs Second Year Opening WDV:');
        const verificationData = await getData(`
            SELECT 
                a.record_id,
                a.asset_particulars,
                ayd1.year_range as first_year,
                ayd1.opening_wdv as first_opening,
                ayd1.closing_wdv as first_closing,
                ayd2.year_range as second_year,
                ayd2.opening_wdv as second_opening
            FROM assets a
            JOIN asset_yearly_data ayd1 ON a.id = ayd1.asset_id AND ayd1.year_range = '2022-2023'
            JOIN asset_yearly_data ayd2 ON a.id = ayd2.asset_id AND ayd2.year_range = '2023-2024'
            WHERE a.company_id = 'c1001'
            ORDER BY a.record_id
        `);

        verificationData.forEach(row => {
            const isCorrect = row.first_closing === row.second_opening;
            const status = isCorrect ? '✅' : '❌';
            console.log(`   ${status} ${row.record_id}: ${row.first_year} closing ₹${row.first_closing.toLocaleString()} → ${row.second_year} opening ₹${row.second_opening.toLocaleString()}`);
        });

        console.log('\n✅ Opening WDV corrections completed successfully!');

    } catch (error) {
        console.error('❌ Error fixing opening WDV:', error);
        throw error;
    }
}

// Run the fix
fixOpeningWdv().then(() => {
    console.log('\n🏁 Script completed successfully');
}).catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
});
