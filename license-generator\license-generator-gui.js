#!/usr/bin/env node

/**
 * FAR Sighted License Generator - GUI Version
 * Web-based interface for license generation
 */

import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Import license generation functions
import { 
    generateLicenseKey, 
    encryptLicenseData, 
    decryptLicenseData,
    LICENSE_CONFIG 
} from './license-generator.js';

const app = express();
const PORT = 9999;

// Password protection configuration
const PASSWORD_FILE = path.join(__dirname, '.admin-password');
let ADMIN_PASSWORD = process.env.LICENSE_ADMIN_PASSWORD || 'LicenseGen2024!';
const SESSION_SECRET = process.env.SESSION_SECRET || 'license-generator-secret-key-2024';

// Load password from file if it exists
function loadAdminPassword() {
    try {
        if (fs.existsSync(PASSWORD_FILE)) {
            const data = fs.readFileSync(PASSWORD_FILE, 'utf8');
            const parsed = JSON.parse(data);
            ADMIN_PASSWORD = parsed.password;
            console.log('📁 Admin password loaded from file');
        } else {
            console.log('🔑 Using default admin password');
        }
    } catch (error) {
        console.error('❌ Error loading admin password:', error.message);
        console.log('🔑 Using default admin password');
    }
}

// Save password to file
function saveAdminPassword(newPassword) {
    try {
        const data = {
            password: newPassword,
            updatedAt: new Date().toISOString()
        };
        fs.writeFileSync(PASSWORD_FILE, JSON.stringify(data, null, 2));
        ADMIN_PASSWORD = newPassword;
        console.log('💾 Admin password updated and saved');
        return true;
    } catch (error) {
        console.error('❌ Error saving admin password:', error.message);
        return false;
    }
}

// Initialize password on startup
loadAdminPassword();

// Configure multer for file uploads
const upload = multer({
    dest: path.join(__dirname, 'uploads'),
    limits: { fileSize: 1024 * 1024 } // 1MB limit
});

// Session configuration
const session = {
    secret: SESSION_SECRET,
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // Set to true in production with HTTPS
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
};

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Simple session middleware (for demo purposes)
const sessions = new Map();

// Authentication middleware
const requireAuth = (req, res, next) => {
    const sessionId = req.headers['x-session-id'] || req.query.sessionId;

    if (sessionId && sessions.has(sessionId)) {
        const session = sessions.get(sessionId);
        if (session.authenticated && session.expires > Date.now()) {
            return next();
        }
    }

    return res.status(401).json({ error: 'Authentication required' });
};

// Login page
app.get('/login', (req, res) => {
    res.send(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>License Generator - Login</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .login-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        .login-header p {
            color: #666;
            font-size: 0.9rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }
        input[type="password"] {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .error {
            color: #dc3545;
            font-size: 0.9rem;
            margin-top: 0.5rem;
            text-align: center;
        }
        .security-note {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1.5rem;
            font-size: 0.85rem;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🔐 License Generator</h1>
            <p>Secure Access Required</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="password">Administrator Password</label>
                <input type="password" id="password" name="password" required autofocus>
            </div>

            <button type="submit" class="btn" id="loginBtn">
                Access License Generator
            </button>

            <div id="error" class="error" style="display: none;"></div>
        </form>

        <div class="security-note">
            <strong>Security Notice:</strong> This is a protected system for authorized license generation only.
            All access attempts are logged and monitored.
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const errorDiv = document.getElementById('error');

            loginBtn.disabled = true;
            loginBtn.textContent = 'Authenticating...';
            errorDiv.style.display = 'none';

            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ password })
                });

                const result = await response.json();

                if (response.ok) {
                    // Store session ID and redirect
                    localStorage.setItem('sessionId', result.sessionId);
                    window.location.href = '/?sessionId=' + result.sessionId;
                } else {
                    errorDiv.textContent = result.error || 'Authentication failed';
                    errorDiv.style.display = 'block';
                }
            } catch (error) {
                errorDiv.textContent = 'Connection error. Please try again.';
                errorDiv.style.display = 'block';
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = 'Access License Generator';
            }
        });
    </script>
</body>
</html>
    `);
});

// Authentication endpoints
app.post('/auth/login', async (req, res) => {
    try {
        const { password } = req.body;

        if (!password) {
            return res.status(400).json({ error: 'Password is required' });
        }

        if (password !== ADMIN_PASSWORD) {
            // Log failed attempt
            console.log(`🚨 Failed login attempt from ${req.ip} at ${new Date().toISOString()}`);
            return res.status(401).json({ error: 'Invalid password' });
        }

        // Create session
        const sessionId = crypto.randomBytes(32).toString('hex');
        sessions.set(sessionId, {
            authenticated: true,
            expires: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
            ip: req.ip,
            loginTime: new Date().toISOString()
        });

        console.log(`✅ Successful login from ${req.ip} at ${new Date().toISOString()}`);

        res.json({
            success: true,
            sessionId: sessionId,
            message: 'Authentication successful'
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.post('/auth/logout', (req, res) => {
    const sessionId = req.headers['x-session-id'] || req.query.sessionId;

    if (sessionId && sessions.has(sessionId)) {
        sessions.delete(sessionId);
        console.log(`🔓 User logged out, session ${sessionId} terminated`);
    }

    res.json({ success: true, message: 'Logged out successfully' });
});

// Change password endpoint (protected)
app.post('/auth/change-password', requireAuth, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;

        if (!currentPassword || !newPassword) {
            return res.status(400).json({ error: 'Current password and new password are required' });
        }

        // Verify current password
        if (currentPassword !== ADMIN_PASSWORD) {
            console.log(`🚨 Failed password change attempt from ${req.ip} at ${new Date().toISOString()}`);
            return res.status(401).json({ error: 'Current password is incorrect' });
        }

        // Validate new password
        if (newPassword.length < 8) {
            return res.status(400).json({ error: 'New password must be at least 8 characters long' });
        }

        // Save new password
        if (saveAdminPassword(newPassword)) {
            console.log(`✅ Password changed successfully from ${req.ip} at ${new Date().toISOString()}`);
            res.json({
                success: true,
                message: 'Password changed successfully. Please use the new password for future logins.'
            });
        } else {
            res.status(500).json({ error: 'Failed to save new password. Please try again.' });
        }

    } catch (error) {
        console.error('Password change error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Serve the main GUI page (protected)
app.get('/', (req, res) => {
    const sessionId = req.query.sessionId;

    // Check authentication
    if (!sessionId || !sessions.has(sessionId)) {
        return res.redirect('/login');
    }

    const session = sessions.get(sessionId);
    if (!session.authenticated || session.expires < Date.now()) {
        sessions.delete(sessionId);
        return res.redirect('/login');
    }

    res.send(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAR Sighted License Generator</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { margin-bottom: 30px; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header-content { display: flex; justify-content: space-between; align-items: center; }
        .header-actions { display: flex; align-items: center; gap: 15px; }
        .session-info { font-size: 0.9rem; color: #28a745; font-weight: 500; }
        .card { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; }
        .tabs { display: flex; margin-bottom: 20px; }
        .tab { padding: 10px 20px; background: #e9ecef; border: none; cursor: pointer; }
        .tab.active { background: #007bff; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .result { margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .file-drop { border: 2px dashed #ddd; padding: 40px; text-align: center; border-radius: 8px; }
        .file-drop.dragover { border-color: #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div>
                    <h1>🔐 Universal License Generator</h1>
                    <p>Professional License Management System for Multiple Applications</p>
                </div>
                <div class="header-actions">
                    <span class="session-info">Authenticated Session</span>
                    <button class="btn btn-secondary" onclick="logout()">Logout</button>
                </div>
            </div>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('manual')">Manual Entry</button>
            <button class="tab" onclick="showTab('request')">From Request File</button>
            <button class="tab" onclick="showTab('validate')">Validate License</button>
            <button class="tab" onclick="showTab('admin')">Admin Settings</button>
        </div>

        <!-- Manual Entry Tab -->
        <div id="manual" class="tab-content active">
            <div class="card">
                <h3>Generate License - Manual Entry</h3>
                <form id="manualForm">
                    <div class="form-group">
                        <label>Application Type *</label>
                        <select name="applicationType" required onchange="toggleApplicationFields()">
                            <option value="">Select Application Type</option>
                            <option value="far-sighted">FAR Sighted Asset Management</option>
                            <option value="custom">Custom Application</option>
                        </select>
                    </div>
                    <div class="form-group" id="customAppName" style="display: none;">
                        <label>Application Name *</label>
                        <input type="text" name="applicationName" placeholder="Enter application name">
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Company Name *</label>
                            <input type="text" name="companyName" required>
                        </div>
                        <div class="form-group">
                            <label>PAN Number</label>
                            <input type="text" name="pan" placeholder="Required if no CIN">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>CIN Number</label>
                            <input type="text" name="cin" placeholder="Required if no PAN">
                        </div>
                        <div class="form-group">
                            <label>Contact Person</label>
                            <input type="text" name="contactPerson">
                        </div>
                    </div>
                    <div class="form-group">
                        <small style="color: #666;">* Either PAN or CIN must be provided</small>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" name="email">
                        </div>
                        <div class="form-group">
                            <label>Mobile</label>
                            <input type="tel" name="mobile">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Address</label>
                        <textarea name="address" rows="3"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Valid From *</label>
                            <input type="date" name="validFrom" required>
                        </div>
                        <div class="form-group">
                            <label>Valid Until *</label>
                            <input type="date" name="validUpto" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Max Users</label>
                            <input type="number" name="maxUsers" value="5" min="1">
                        </div>
                        <div class="form-group">
                            <label>Max Companies</label>
                            <input type="number" name="maxCompanies" value="1" min="1">
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Generate License</button>
                </form>
            </div>
        </div>

        <!-- Request File Tab -->
        <div id="request" class="tab-content">
            <div class="card">
                <h3>Generate License from Request File</h3>
                <div class="file-drop" id="fileDropZone">
                    <p>Drop license request file here or click to browse</p>
                    <input type="file" id="requestFile" accept=".json" style="display: none;">
                    <button type="button" class="btn btn-secondary" onclick="document.getElementById('requestFile').click()">Browse Files</button>
                </div>
                <div id="requestPreview"></div>
            </div>
        </div>

        <!-- Validate Tab -->
        <div id="validate" class="tab-content">
            <div class="card">
                <h3>Validate License File</h3>
                <div class="file-drop" id="validateDropZone">
                    <p>Drop license file here or click to browse</p>
                    <input type="file" id="validateFile" accept=".json" style="display: none;">
                    <button type="button" class="btn btn-secondary" onclick="document.getElementById('validateFile').click()">Browse Files</button>
                </div>
                <div id="validateResult"></div>
            </div>
        </div>

        <!-- Admin Settings Tab -->
        <div id="admin" class="tab-content">
            <div class="card">
                <h3>🔧 Administrator Settings</h3>
                <p style="color: #666; margin-bottom: 20px;">Manage system configuration and security settings.</p>

                <div class="card" style="background: #f8f9fa; border: 1px solid #dee2e6;">
                    <h4>🔐 Change Administrator Password</h4>
                    <p style="color: #666; font-size: 0.9rem; margin-bottom: 15px;">
                        Update the administrator password for enhanced security. The new password will be saved securely and take effect immediately.
                    </p>

                    <form id="passwordChangeForm">
                        <div class="form-group">
                            <label for="currentPassword">Current Password *</label>
                            <input type="password" id="currentPassword" name="currentPassword" required
                                   placeholder="Enter your current password">
                        </div>

                        <div class="form-group">
                            <label for="newPassword">New Password *</label>
                            <input type="password" id="newPassword" name="newPassword" required
                                   placeholder="Enter new password (minimum 8 characters)"
                                   minlength="8">
                        </div>

                        <div class="form-group">
                            <label for="confirmPassword">Confirm New Password *</label>
                            <input type="password" id="confirmPassword" name="confirmPassword" required
                                   placeholder="Confirm your new password"
                                   minlength="8">
                        </div>

                        <div style="display: flex; gap: 10px; align-items: center;">
                            <button type="submit" class="btn btn-primary" id="changePasswordBtn">
                                🔄 Change Password
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearPasswordForm()">
                                Clear Form
                            </button>
                        </div>
                    </form>

                    <div id="passwordChangeResult" style="margin-top: 15px;"></div>
                </div>

                <div class="card" style="background: #e7f3ff; border: 1px solid #b3d9ff; margin-top: 20px;">
                    <h4>ℹ️ Security Information</h4>
                    <ul style="margin: 10px 0; padding-left: 20px; color: #666;">
                        <li>Password changes take effect immediately</li>
                        <li>All active sessions remain valid after password change</li>
                        <li>Use a strong password with at least 8 characters</li>
                        <li>Include uppercase, lowercase, numbers, and special characters</li>
                        <li>Password is stored securely in encrypted format</li>
                    </ul>
                </div>
            </div>
        </div>

        <div id="result"></div>
    </div>

    <script>
        // Session management
        const sessionId = localStorage.getItem('sessionId') || new URLSearchParams(window.location.search).get('sessionId');

        if (!sessionId) {
            window.location.href = '/login';
        }

        // Add session ID to all API requests
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            options.headers = options.headers || {};
            options.headers['X-Session-ID'] = sessionId;
            return originalFetch(url, options);
        };

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('/auth/logout', {
                    method: 'POST',
                    headers: { 'X-Session-ID': sessionId }
                }).finally(() => {
                    localStorage.removeItem('sessionId');
                    window.location.href = '/login';
                });
            }
        }

        function showTab(tabName) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
        }

        function toggleApplicationFields() {
            const appType = document.querySelector('select[name="applicationType"]').value;
            const customAppName = document.getElementById('customAppName');
            const appNameInput = document.querySelector('input[name="applicationName"]');

            if (appType === 'custom') {
                customAppName.style.display = 'block';
                appNameInput.required = true;
            } else {
                customAppName.style.display = 'none';
                appNameInput.required = false;
                appNameInput.value = '';
            }
        }

        // Manual form submission
        document.getElementById('manualForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            try {
                const response = await fetch('/generate-manual', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                showResult(result, response.ok);
            } catch (error) {
                showResult({ error: error.message }, false);
            }
        });

        // File drop handlers
        setupFileDropZone('fileDropZone', 'requestFile', handleRequestFile);
        setupFileDropZone('validateDropZone', 'validateFile', handleValidateFile);

        function setupFileDropZone(dropZoneId, fileInputId, handler) {
            const dropZone = document.getElementById(dropZoneId);
            const fileInput = document.getElementById(fileInputId);
            
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });
            
            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });
            
            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handler(files[0]);
                }
            });
            
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handler(e.target.files[0]);
                }
            });
        }

        async function handleRequestFile(file) {
            const formData = new FormData();
            formData.append('requestFile', file);
            
            try {
                const response = await fetch('/generate-from-request', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                showResult(result, response.ok);
            } catch (error) {
                showResult({ error: error.message }, false);
            }
        }

        async function handleValidateFile(file) {
            const formData = new FormData();
            formData.append('licenseFile', file);
            
            try {
                const response = await fetch('/validate', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                document.getElementById('validateResult').innerHTML = formatValidationResult(result);
            } catch (error) {
                document.getElementById('validateResult').innerHTML = '<div class="result error">Error: ' + error.message + '</div>';
            }
        }

        function showResult(result, success) {
            const resultDiv = document.getElementById('result');
            if (success && result.downloadUrl) {
                resultDiv.innerHTML =
                    '<div class="result">' +
                        '<h4>✅ License Generated Successfully!</h4>' +
                        '<p><strong>License Key:</strong> ' + result.licenseKey + '</p>' +
                        '<p><strong>Company:</strong> ' + result.companyName + '</p>' +
                        '<p><strong>Valid Until:</strong> ' + result.validUpto + '</p>' +
                        '<a href="' + result.downloadUrl + '" class="btn btn-success" download>Download License File</a>' +
                    '</div>';
            } else {
                resultDiv.innerHTML = '<div class="result error">❌ Error: ' + (result.error || 'Unknown error') + '</div>';
            }
        }

        function formatValidationResult(result) {
            if (result.valid) {
                return
                    '<div class="result">' +
                        '<h4>✅ License Valid</h4>' +
                        '<p><strong>Company:</strong> ' + result.company + '</p>' +
                        '<p><strong>Valid Until:</strong> ' + result.validUpto + '</p>' +
                        '<p><strong>Status:</strong> ' + result.status + '</p>' +
                    '</div>';
            } else {
                return '<div class="result error">❌ Invalid License: ' + result.error + '</div>';
            }
        }

        // Password change functionality
        function clearPasswordForm() {
            document.getElementById('passwordChangeForm').reset();
            document.getElementById('passwordChangeResult').innerHTML = '';
        }

        // Password change form submission
        document.getElementById('passwordChangeForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const changeBtn = document.getElementById('changePasswordBtn');
            const resultDiv = document.getElementById('passwordChangeResult');

            // Clear previous results
            resultDiv.innerHTML = '';

            // Validate passwords match
            if (newPassword !== confirmPassword) {
                resultDiv.innerHTML = '<div class="result error">❌ New passwords do not match</div>';
                return;
            }

            // Validate password strength
            if (newPassword.length < 8) {
                resultDiv.innerHTML = '<div class="result error">❌ Password must be at least 8 characters long</div>';
                return;
            }

            // Disable button and show loading
            changeBtn.disabled = true;
            changeBtn.innerHTML = '🔄 Changing Password...';

            try {
                const response = await fetch('/auth/change-password', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        currentPassword: currentPassword,
                        newPassword: newPassword
                    })
                });

                const result = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = '<div class="result">✅ ' + result.message + '</div>';
                    // Clear the form
                    clearPasswordForm();
                } else {
                    resultDiv.innerHTML = '<div class="result error">❌ ' + result.error + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="result error">❌ Connection error. Please try again.</div>';
            } finally {
                changeBtn.disabled = false;
                changeBtn.innerHTML = '🔄 Change Password';
            }
        });
    </script>
</body>
</html>
    `);
});

// API Routes (all protected)
app.post('/generate-manual', requireAuth, async (req, res) => {
    try {
        const { applicationType, applicationName, companyName, pan, cin, contactPerson, email, mobile, address, validFrom, validUpto, maxUsers, maxCompanies } = req.body;

        // Validate that either PAN or CIN is provided
        if (!pan && !cin) {
            return res.status(400).json({ error: 'Either PAN or CIN must be provided' });
        }

        // Determine application name
        const finalApplicationName = applicationType === 'far-sighted' ? 'FAR Sighted Asset Management System' : applicationName;

        if (!finalApplicationName) {
            return res.status(400).json({ error: 'Application name is required for custom applications' });
        }

        const companyInfo = { companyName, pan, cin, contactPerson, email, mobile, address };
        const licenseDetails = {
            validFrom,
            validUpto,
            maxUsers: parseInt(maxUsers) || 5,
            maxCompanies: parseInt(maxCompanies) || 1,
            features: applicationType === 'far-sighted' ? {
                multiDatabase: true,
                reports: true,
                backup: true,
                audit: true,
                extraShiftCalculation: true,
                scheduleIIIReports: true
            } : {
                basicFeatures: true,
                reports: true,
                backup: true
            }
        };

        const result = await generateLicenseFile(companyInfo, licenseDetails, null, finalApplicationName);
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.post('/generate-from-request', requireAuth, upload.single('requestFile'), async (req, res) => {
    try {
        if (!req.file) {
            throw new Error('No request file uploaded');
        }
        
        const requestData = JSON.parse(fs.readFileSync(req.file.path, 'utf8'));
        
        const companyInfo = {
            companyName: requestData.company.name,
            pan: requestData.company.pan,
            cin: requestData.company.cin,
            contactPerson: requestData.company.contact.person,
            email: requestData.company.contact.email,
            mobile: requestData.company.contact.mobile,
            address: `${requestData.company.address.line1}, ${requestData.company.address.line2}, ${requestData.company.address.city} - ${requestData.company.address.pin}`
        };
        
        const licenseDetails = {
            validFrom: requestData.license.requestedValidFrom,
            validUpto: requestData.license.requestedValidUpto,
            maxUsers: requestData.license.maxUsers || 5,
            maxCompanies: requestData.license.maxCompanies || 1,
            features: requestData.license.features
        };
        
        const applicationName = requestData.applicationName || 'FAR Sighted Asset Management System';
        const result = await generateLicenseFile(companyInfo, licenseDetails, requestData.requestId, applicationName);

        // Clean up uploaded file
        fs.unlinkSync(req.file.path);
        
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.post('/validate', requireAuth, upload.single('licenseFile'), async (req, res) => {
    try {
        if (!req.file) {
            throw new Error('No license file uploaded');
        }
        
        const licenseFile = JSON.parse(fs.readFileSync(req.file.path, 'utf8'));
        
        // Validate structure
        if (!licenseFile.version || !licenseFile.key || !licenseFile.data || !licenseFile.iv) {
            throw new Error('Invalid license file structure');
        }
        
        // Validate checksum
        const crypto = await import('crypto');
        const calculatedChecksum = crypto.createHash('sha256').update(licenseFile.data).digest('hex');
        if (calculatedChecksum !== licenseFile.checksum) {
            throw new Error('License file corrupted - checksum mismatch');
        }
        
        // Decrypt and validate data
        const licenseData = decryptLicenseData(licenseFile.data, licenseFile.iv);
        
        const expiryDate = new Date(licenseData.license.validUpto);
        const today = new Date();
        const isExpired = expiryDate < today;
        
        // Clean up uploaded file
        fs.unlinkSync(req.file.path);
        
        res.json({
            valid: true,
            company: licenseData.company.companyName,
            validUpto: licenseData.license.validUpto,
            status: isExpired ? 'Expired' : 'Active',
            daysLeft: isExpired ? 0 : Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24))
        });
        
    } catch (error) {
        res.status(500).json({ valid: false, error: error.message });
    }
});

// Download generated license files
app.get('/download/:filename', (req, res) => {
    const filename = req.params.filename;
    const filePath = path.join(__dirname, 'generated-licenses', filename);
    
    if (fs.existsSync(filePath)) {
        res.download(filePath);
    } else {
        res.status(404).json({ error: 'File not found' });
    }
});

// Helper function to generate license file
async function generateLicenseFile(companyInfo, licenseDetails, requestId = null, applicationName = 'FAR Sighted Asset Management System') {
    const licenseKey = generateLicenseKey(companyInfo, applicationName);
    const licenseData = {
        version: LICENSE_CONFIG.version,
        key: licenseKey,
        application: applicationName,
        company: companyInfo,
        license: licenseDetails,
        requestId: requestId,
        generatedAt: new Date().toISOString(),
        generatedBy: 'Universal License Generator GUI v1.0'
    };
    
    const encrypted = encryptLicenseData(licenseData);
    
    const licenseFile = {
        version: LICENSE_CONFIG.version,
        key: licenseKey,
        data: encrypted.encrypted,
        iv: encrypted.iv,
        checksum: crypto.createHash('sha256').update(encrypted.encrypted).digest('hex'),
        generatedAt: licenseData.generatedAt
    };
    
    const appCode = applicationName.includes('FAR Sighted') ? 'FAR' :
                   applicationName.replace(/[^A-Za-z0-9]/g, '').substring(0, 3).toUpperCase();
    const fileName = `${appCode}_LICENSE_${companyInfo.companyName.replace(/[^A-Za-z0-9]/g, '_')}_${Date.now()}.json`;
    const filePath = path.join(__dirname, 'generated-licenses', fileName);
    
    // Create directory if it doesn't exist
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(filePath, JSON.stringify(licenseFile, null, 2));
    
    return {
        licenseKey,
        companyName: companyInfo.companyName,
        validUpto: licenseDetails.validUpto,
        fileName,
        downloadUrl: `/download/${fileName}`
    };
}

// Start the GUI server
app.listen(PORT, () => {
    console.log(`🌐 UNIVERSAL LICENSE GENERATOR GUI`);
    console.log(`📡 Server running on: http://localhost:${PORT}`);
    console.log(`🔗 Open your browser and navigate to the URL above`);
    console.log(`⚡ Features: Multi-application support, Manual entry, Request file processing, License validation`);
    console.log(`🎯 Supports: FAR Sighted, Custom applications with PAN/CIN`);
});

export { generateLicenseKey, encryptLicenseData, decryptLicenseData, LICENSE_CONFIG };
