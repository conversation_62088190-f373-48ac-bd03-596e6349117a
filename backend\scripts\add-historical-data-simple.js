/**
 * Simple script to add historical data via API calls
 * This uses the existing API endpoints to add previous years data
 */

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:8090/api';

// Historical assets data
const historicalAssets = [
    {
        recordId: 'HIST001',
        assetParticulars: 'Manufacturing Plant - Line 1',
        bookEntryDate: '2020-05-15',
        putToUseDate: '2020-06-01',
        basicAmount: 5000000,
        dutiesTaxes: 500000,
        grossAmount: 5500000,
        wdvOfAdoptionDate: 4200000,
        vendor: 'Industrial Equipment Ltd',
        invoiceNo: 'INV-2020-001',
        location: 'Factory Floor A',
        assetId: 'PLT-001',
        ledgerNameInBooks: 'Plant & Machinery',
        assetGroup: 'Plant & Machinery',
        assetSubGroup: 'Manufacturing Equipment',
        scheduleIIIClassification: 'Plant and machinery',
        depreciationMethod: 'WDV',
        lifeInYears: 15,
        salvagePercentage: 5,
        isLeasehold: false
    },
    {
        recordId: 'HIST002',
        assetParticulars: 'Office Building - Main Block',
        bookEntryDate: '2019-03-20',
        putToUseDate: '2019-04-01',
        basicAmount: 15000000,
        dutiesTaxes: 1500000,
        grossAmount: 16500000,
        wdvOfAdoptionDate: 14200000,
        vendor: 'Real Estate Developers',
        invoiceNo: 'SALE-2019-005',
        location: 'Corporate Office',
        assetId: 'BLDG-001',
        ledgerNameInBooks: 'Buildings',
        assetGroup: 'Buildings',
        assetSubGroup: 'Factory buildings',
        scheduleIIIClassification: 'Buildings',
        depreciationMethod: 'SLM',
        lifeInYears: 30,
        salvagePercentage: 5,
        isLeasehold: false
    },
    {
        recordId: 'HIST003',
        assetParticulars: 'Computer Server - Data Center',
        bookEntryDate: '2021-08-10',
        putToUseDate: '2021-09-01',
        basicAmount: 800000,
        dutiesTaxes: 144000,
        grossAmount: 944000,
        wdvOfAdoptionDate: 750000,
        vendor: 'Tech Solutions Inc',
        invoiceNo: 'INV-2021-089',
        location: 'Data Center',
        assetId: 'SRV-001',
        ledgerNameInBooks: 'Computer Equipment',
        assetGroup: 'Office equipment',
        assetSubGroup: 'Computers',
        scheduleIIIClassification: 'Office equipment',
        depreciationMethod: 'WDV',
        lifeInYears: 6,
        salvagePercentage: 10,
        isLeasehold: false,
        disposalDate: '2023-12-31',
        disposalAmount: 200000
    },
    {
        recordId: 'HIST004',
        assetParticulars: 'Delivery Vehicle - Truck',
        bookEntryDate: '2022-01-15',
        putToUseDate: '2022-02-01',
        basicAmount: 1200000,
        dutiesTaxes: 180000,
        grossAmount: 1380000,
        vendor: 'Commercial Vehicles Ltd',
        invoiceNo: 'VEH-2022-003',
        location: 'Transport Yard',
        assetId: 'VEH-001',
        ledgerNameInBooks: 'Motor Vehicles',
        assetGroup: 'Motor vehicles',
        assetSubGroup: 'Motor cars',
        scheduleIIIClassification: 'Motor vehicles',
        depreciationMethod: 'WDV',
        lifeInYears: 8,
        salvagePercentage: 5,
        isLeasehold: false
    }
];

// Function to make API calls
async function apiCall(endpoint, method = 'GET', data = null) {
    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
        }
    };
    
    if (data) {
        options.body = JSON.stringify(data);
    }
    
    const response = await fetch(`${API_BASE}${endpoint}`, options);
    
    if (!response.ok) {
        const error = await response.text();
        throw new Error(`API call failed: ${response.status} - ${error}`);
    }
    
    return await response.json();
}

// Function to add historical data to a company
async function addHistoricalDataToCompany(companyId) {
    console.log(`\n📊 Adding historical data to company: ${companyId}`);
    
    try {
        // Add historical assets
        for (const asset of historicalAssets) {
            console.log(`  📝 Adding asset: ${asset.recordId} - ${asset.assetParticulars}`);
            await apiCall(`/assets/company/${companyId}/asset`, 'POST', asset);
        }
        
        console.log(`  ✅ Added ${historicalAssets.length} historical assets`);
        
        // Add extra shift days for manufacturing equipment
        const extraShiftData = {
            'HIST001': { secondShiftDays: 120, thirdShiftDays: 60 }
        };
        
        for (const [assetId, shiftData] of Object.entries(extraShiftData)) {
            console.log(`  ⚙️ Adding extra shift data for: ${assetId}`);
            // Note: This would need the extra shift API endpoint
            // await apiCall(`/extra-shift/company/${companyId}`, 'POST', { assetId, ...shiftData });
        }
        
        console.log(`  ✅ Historical data added successfully`);
        
    } catch (error) {
        console.error(`  ❌ Error adding historical data: ${error.message}`);
        throw error;
    }
}

// Main execution
async function main() {
    console.log('🚀 Adding Previous Years Data for Schedule III Testing');
    console.log('====================================================\n');
    
    try {
        // Get list of companies
        console.log('📋 Fetching companies...');
        const companies = await apiCall('/companies');
        
        if (!companies || companies.length === 0) {
            console.error('❌ No companies found');
            process.exit(1);
        }
        
        console.log(`📁 Found ${companies.length} company(ies)`);
        
        // Add historical data to the first company only (for testing)
        const firstCompany = companies[0];
        console.log(`🎯 Adding data to: ${firstCompany.name} (${firstCompany.id})`);
        
        await addHistoricalDataToCompany(firstCompany.id);
        
        console.log('\n🎉 Historical data addition completed successfully!');
        console.log('\n📋 Summary of added data:');
        console.log(`   • ${historicalAssets.length} historical assets`);
        console.log('   • Assets from different years (2019-2022)');
        console.log('   • Both WDV and SLM depreciation methods');
        console.log('   • Asset disposal scenario (HIST003 disposed in 2023-24)');
        console.log('   • Different asset categories (Plant, Building, Office, Vehicle)');
        console.log('\n🧪 Test scenarios included:');
        console.log('   • Assets purchased before adoption date');
        console.log('   • Assets purchased after adoption date');
        console.log('   • Both WDV and SLM depreciation methods');
        console.log('   • Asset disposal with gain/loss calculation');
        console.log('   • Different asset categories');
        console.log('\n📊 You can now test Schedule III report with carry-forward data!');
        
    } catch (error) {
        console.error('❌ Script failed:', error.message);
        process.exit(1);
    }
}

// Run the script
main();
