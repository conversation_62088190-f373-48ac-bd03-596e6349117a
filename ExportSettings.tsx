/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect } from 'react';
import { api } from './lib/api';
import type { AppSettings } from './db-server';
import { DataViewProps } from './types';
import { SaveIcon } from './Icons';

export function ExportSettings({ showAlert, showConfirmation, loggedInUser }: DataViewProps) {
    const [settings, setSettings] = useState<AppSettings | null>(null);
    const [loading, setLoading] = useState(true);
    const [isSaving, setIsSaving] = useState(false);

    useEffect(() => {
        const fetchSettings = async () => {
            setLoading(true);
            try {
                const loadedSettings = await api.getSettings();
                setSettings(loadedSettings);
            } catch (error) {
                console.error("Failed to load settings:", error);
                showAlert('Error', 'Failed to load settings.', 'error');
            } finally {
                setLoading(false);
            }
        };
        fetchSettings();
    }, [showAlert]);
    
    const handleSave = async () => {
        if (!settings) return;
        setIsSaving(true);
        try {
            await api.updateSettings(settings);
             if (loggedInUser) {
                await api.addAuditLog({
                    userId: loggedInUser.id,
                    username: loggedInUser.username,
                    action: 'UPDATE_EXPORT_SETTINGS',
                    details: `Updated default export path to: "${settings.defaultExportPath}".`
                });
            }
            showAlert("Success", "Settings saved successfully.", 'success');
        } catch (error) {
            console.error("Failed to save settings:", error);
            showAlert("Error", "Error saving settings.", 'error');
        } finally {
            setIsSaving(false);
        }
    };

    if (loading) {
        return <div className="loading-indicator">Loading settings...</div>;
    }

    if (!settings) {
        return <div className="error-message">Could not load settings.</div>
    }

    return (
        <>
            <div className="view-header">
                <h2>Export Settings</h2>
            </div>
            <div className="settings-container">
                 <section>
                    <h3 className="settings-title">Default Export Folder</h3>
                    <p className="settings-description">Set a default folder path for all Excel exports. Note: Due to browser security, this path cannot be forced, but serves as a preference for future versions.</p>
                    <div className="form-group" style={{ maxWidth: '600px' }}>
                        <label htmlFor="exportPath">Folder Path</label>
                        <input
                            type="text"
                            id="exportPath"
                            name="exportPath"
                            value={settings.defaultExportPath}
                            onChange={(e) => setSettings({ ...settings, defaultExportPath: e.target.value })}
                            placeholder="e.g., C:\Users\<USER>\Documents\FAR Exports"
                        />
                    </div>
                </section>
                <div className="modal-actions" style={{justifyContent: 'flex-start', borderTop: 'none', paddingTop: '1.5rem'}}>
                    <button className="btn btn-primary" onClick={handleSave} disabled={isSaving}>
                        <SaveIcon /> {isSaving ? 'Saving...' : 'Save Settings'}
                    </button>
                </div>
            </div>
        </>
    );
}
