/**
 * OPENING NET BLOCK VERIFICATION SCRIPT
 * Professional Advisory Services - Chartered Accountant
 * 
 * This script specifically tests the Opening Net Block calculation
 * to ensure it shows calculated WDV instead of gross amounts.
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const dbPath = join(__dirname, '../database/far_sighted.db');

console.log('🔍 OPENING NET BLOCK VERIFICATION TEST');
console.log('=====================================');

const verifyOpeningNetBlock = async () => {
    const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
            console.error('❌ Error opening database:', err.message);
            process.exit(1);
        }
    });

    const runQuery = (sql, params = []) => {
        return new Promise((resolve, reject) => {
            db.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    };

    // Helper function to calculate depreciation like the frontend does
    const calculateYearlyMetrics = (asset, year, financialYears, firstAdoptionDate) => {
        const [startYearStr, endYearStr] = year.split('-');
        const fyStart = new Date(`${startYearStr}-04-01T00:00:00.000Z`);
        const putToUseDate = new Date(asset.put_to_use_date);
        const grossAmount = asset.gross_amount || 0;
        const lifeInYears = asset.life_in_years || 0;

        // Calculate opening gross
        const openingGross = (putToUseDate < fyStart) ? grossAmount : 0;

        // Calculate opening depreciation
        const currentYearIndex = financialYears.indexOf(year);
        const previousYears = financialYears.slice(0, currentYearIndex);
        let openingDepreciation = 0;

        if (currentYearIndex === 0) {
            // First year logic
            if (putToUseDate < new Date(firstAdoptionDate) && asset.wdv_of_adoption_date != null) {
                openingDepreciation = grossAmount - asset.wdv_of_adoption_date;
            } else if (putToUseDate >= new Date(firstAdoptionDate) && putToUseDate < fyStart) {
                // Calculate partial depreciation
                const useDays = Math.floor((fyStart - putToUseDate) / (1000 * 60 * 60 * 24)) + 1;
                const salvagePercentage = asset.salvage_percentage || 0;
                const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));
                
                if (asset.depreciation_method === 'SLM') {
                    const depreciableAmount = grossAmount - salvageValue;
                    const yearlyDepreciation = depreciableAmount / lifeInYears;
                    openingDepreciation = (yearlyDepreciation / 365.25) * useDays;
                } else if (lifeInYears > 0 && salvageValue < grossAmount) {
                    const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
                    const yearlyDepreciation = grossAmount * rate;
                    openingDepreciation = (yearlyDepreciation / 365.25) * useDays;
                }
            }
        } else {
            // Subsequent years - use fallback calculation
            if (putToUseDate < fyStart) {
                let accumulatedDepreciation = 0;
                
                // Handle historical assets
                if (putToUseDate < new Date(firstAdoptionDate) && asset.wdv_of_adoption_date != null) {
                    accumulatedDepreciation = grossAmount - asset.wdv_of_adoption_date;
                } else if (putToUseDate >= new Date(firstAdoptionDate)) {
                    // Calculate fallback accumulated depreciation
                    const assetAgeInYears = previousYears.length;
                    const salvagePercentage = asset.salvage_percentage || 0;
                    const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));
                    
                    if (asset.depreciation_method === 'SLM') {
                        const depreciableAmount = grossAmount - salvageValue;
                        const yearlyDepreciation = depreciableAmount / lifeInYears;
                        accumulatedDepreciation = Math.min(yearlyDepreciation * assetAgeInYears, depreciableAmount);
                    } else if (asset.depreciation_method === 'WDV' && lifeInYears > 0 && salvageValue < grossAmount) {
                        const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
                        let currentWDV = grossAmount;
                        
                        for (let yearIdx = 0; yearIdx < assetAgeInYears; yearIdx++) {
                            const yearlyDepreciation = currentWDV * rate;
                            accumulatedDepreciation += yearlyDepreciation;
                            currentWDV -= yearlyDepreciation;
                            
                            if (currentWDV <= salvageValue) {
                                accumulatedDepreciation = grossAmount - salvageValue;
                                break;
                            }
                        }
                    }
                    
                    const maxDepreciable = grossAmount - salvageValue;
                    accumulatedDepreciation = Math.min(accumulatedDepreciation, maxDepreciable);
                }
                
                openingDepreciation = accumulatedDepreciation;
            }
        }

        const openingNetBlock = openingGross - openingDepreciation;
        const openingWDV = Math.max(0, grossAmount - openingDepreciation);

        return {
            openingGross,
            openingDepreciation: Math.round(openingDepreciation),
            openingNetBlock: Math.round(openingNetBlock),
            openingWDV: Math.round(openingWDV)
        };
    };

    try {
        console.log('📊 Testing Opening Net Block calculations...\n');

        // Get test assets
        const assets = await runQuery(`
            SELECT record_id, asset_particulars, gross_amount, put_to_use_date, 
                   depreciation_method, life_in_years, salvage_percentage, wdv_of_adoption_date
            FROM assets 
            WHERE company_id = 'c1001' 
            ORDER BY record_id
        `);

        const financialYears = ['2022-2023', '2023-2024', '2024-2025'];
        const firstAdoptionDate = '2018-04-01';
        const testYear = '2024-2025';

        console.log(`Testing for Financial Year: ${testYear}\n`);

        let allTestsPassed = true;

        for (const asset of assets) {
            console.log(`🧪 TESTING: ${asset.record_id} - ${asset.asset_particulars}`);
            console.log('─'.repeat(70));

            const metrics = calculateYearlyMetrics(asset, testYear, financialYears, firstAdoptionDate);

            console.log(`📊 Gross Amount: ₹${asset.gross_amount.toLocaleString()}`);
            console.log(`📊 Opening Gross: ₹${metrics.openingGross.toLocaleString()}`);
            console.log(`📊 Opening Depreciation: ₹${metrics.openingDepreciation.toLocaleString()}`);
            console.log(`📊 Opening Net Block: ₹${metrics.openingNetBlock.toLocaleString()}`);
            console.log(`📊 Opening WDV: ₹${metrics.openingWDV.toLocaleString()}`);

            // Test: Opening Net Block should equal Opening Gross - Opening Depreciation
            const expectedNetBlock = metrics.openingGross - metrics.openingDepreciation;
            
            if (Math.abs(metrics.openingNetBlock - expectedNetBlock) < 1) {
                console.log(`✅ PASS: Opening Net Block = Opening Gross - Opening Depreciation`);
            } else {
                console.log(`❌ FAIL: Opening Net Block (₹${metrics.openingNetBlock.toLocaleString()}) ≠ Expected (₹${expectedNetBlock.toLocaleString()})`);
                allTestsPassed = false;
            }

            // Test: Opening Net Block should NOT equal Opening Gross (unless depreciation is 0)
            if (metrics.openingDepreciation > 0) {
                if (metrics.openingNetBlock < metrics.openingGross) {
                    console.log(`✅ PASS: Opening Net Block < Opening Gross (depreciation applied)`);
                } else {
                    console.log(`❌ FAIL: Opening Net Block equals Opening Gross despite depreciation > 0`);
                    allTestsPassed = false;
                }
            } else {
                console.log(`ℹ️ INFO: No opening depreciation for this asset`);
            }

            // Special tests for specific scenarios
            if (asset.record_id === 'HIST001') {
                const expectedDepreciation = asset.gross_amount - asset.wdv_of_adoption_date;
                if (Math.abs(metrics.openingDepreciation - expectedDepreciation) < 1) {
                    console.log(`✅ PASS: Historical asset uses adoption WDV correctly`);
                } else {
                    console.log(`❌ FAIL: Historical asset depreciation calculation wrong`);
                    allTestsPassed = false;
                }
            }

            console.log('');
        }

        // Summary
        console.log('🎯 VERIFICATION SUMMARY');
        console.log('═'.repeat(50));

        if (allTestsPassed) {
            console.log('✅ ALL TESTS PASSED!');
            console.log('✅ Opening Net Block calculations are working correctly');
            console.log('✅ Opening Net Block ≠ Opening Gross Block (where depreciation exists)');
            console.log('✅ Formula: Opening Net Block = Opening Gross - Opening Depreciation');
        } else {
            console.log('❌ SOME TESTS FAILED');
            console.log('❌ Opening Net Block calculations need further review');
        }

        console.log('\n💡 NEXT STEPS:');
        console.log('1. Run Schedule III report in the application');
        console.log('2. Verify Opening Net Block column shows calculated values');
        console.log('3. Confirm Opening Net Block ≠ Opening Gross Block');
        console.log('4. Check that Net Block = Gross Block - Depreciation');

    } catch (error) {
        console.error('❌ Verification error:', error);
    } finally {
        db.close();
    }
};

verifyOpeningNetBlock();