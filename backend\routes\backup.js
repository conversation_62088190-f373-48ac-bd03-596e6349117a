import express from 'express';
import path from 'path';

const router = express.Router();

// Get backup settings
router.get('/settings', async (req, res) => {
    try {
        const backupService = req.app.locals.backupService;
        if (!backupService) {
            return res.status(500).json({ error: 'Backup service not available' });
        }

        const backupPath = backupService.getBackupPath();
        const isScheduled = backupService.isScheduled;

        res.json({
            backupPath,
            isScheduled,
            maxBackups: backupService.maxBackups
        });
    } catch (error) {
        console.error('Error fetching backup settings:', error);
        res.status(500).json({ error: 'Failed to fetch backup settings' });
    }
});

// Set backup path
router.post('/settings/path', async (req, res) => {
    try {
        const { backupPath } = req.body;

        if (!backupPath) {
            return res.status(400).json({ error: 'Backup path is required' });
        }

        const backupService = req.app.locals.backupService;
        if (!backupService) {
            return res.status(500).json({ error: 'Backup service not available' });
        }

        await backupService.setBackupPath(backupPath);

        // Start scheduled backups if not already started
        if (!backupService.isScheduled) {
            backupService.startScheduledBackups();
        }

        res.json({
            success: true,
            message: 'Backup path set successfully',
            backupPath
        });
    } catch (error) {
        console.error('Error setting backup path:', error);
        res.status(500).json({ error: error.message || 'Failed to set backup path' });
    }
});

// Create manual backup
router.post('/create', async (req, res) => {
    try {
        const backupService = req.app.locals.backupService;
        if (!backupService) {
            return res.status(500).json({ error: 'Backup service not available' });
        }

        if (!backupService.getBackupPath()) {
            return res.status(400).json({
                error: 'Backup path not configured',
                message: 'Please set backup path first'
            });
        }

        const result = await backupService.createBackup();

        if (result.skipped) {
            res.json({
                success: true,
                skipped: true,
                message: result.message
            });
        } else {
            res.json({
                success: true,
                fileName: result.fileName,
                filePath: result.filePath,
                size: result.size,
                message: 'Backup created successfully'
            });
        }
    } catch (error) {
        console.error('Error creating backup:', error);
        res.status(500).json({ error: error.message || 'Failed to create backup' });
    }
});

// Check if backup path is set
router.get('/path-check', async (req, res) => {
    try {
        const backupService = req.app.locals.backupService;
        if (!backupService) {
            return res.status(500).json({ error: 'Backup service not available' });
        }

        const backupPath = backupService.getBackupPath();

        res.json({
            isSet: !!backupPath,
            backupPath: backupPath || null,
            message: backupPath ? 'Backup path is configured' : 'Backup path not set'
        });
    } catch (error) {
        console.error('Error checking backup path:', error);
        res.status(500).json({ error: 'Failed to check backup path' });
    }
});

export default router;
