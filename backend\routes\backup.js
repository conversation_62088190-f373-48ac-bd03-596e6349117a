import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import dbService from '../services/database.js';

const router = express.Router();

// Get backup logs
router.get('/logs', async (req, res) => {
    try {
        const backupLogs = await dbService.all(
            `SELECT 
                id,
                timestamp,
                action,
                initiated_by as initiatedBy,
                details,
                created_at as createdAt
            FROM backup_logs 
            ORDER BY timestamp DESC, created_at DESC`
        );
        
        res.json(backupLogs);
    } catch (error) {
        console.error('Error fetching backup logs:', error);
        res.status(500).json({ error: 'Failed to fetch backup logs' });
    }
});

// Add backup log entry
router.post('/logs', async (req, res) => {
    try {
        const { action, initiatedBy, details } = req.body;
        
        if (!action || !initiatedBy) {
            return res.status(400).json({ 
                error: 'action and initiatedBy are required' 
            });
        }
        
        const logId = uuidv4();
        const timestamp = new Date().toISOString();
        
        await dbService.run(
            `INSERT INTO backup_logs (id, timestamp, action, initiated_by, details)
             VALUES (?, ?, ?, ?, ?)`,
            [logId, timestamp, action, initiatedBy, details || '']
        );
        
        res.status(201).json({ 
            message: 'Backup log entry created',
            id: logId 
        });
    } catch (error) {
        console.error('Error creating backup log:', error);
        res.status(500).json({ error: 'Failed to create backup log' });
    }
});

// Create automatic backup
router.post('/auto', async (req, res) => {
    try {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupName = `auto_backup_${timestamp}`;
        
        // Export all data (excluding backup logs to avoid circular references)
        const companies = await dbService.all('SELECT * FROM companies');
        const users = await dbService.all('SELECT * FROM users');
        const assets = await dbService.all('SELECT * FROM assets');
        const financialYears = await dbService.all('SELECT * FROM financial_years');
        const statutoryRates = await dbService.all('SELECT * FROM statutory_rates');
        const extraLedgers = await dbService.all('SELECT * FROM extra_ledgers');
        const licenseHistory = await dbService.all('SELECT * FROM license_history');
        const assetYearlyData = await dbService.all('SELECT * FROM asset_yearly_data');
        const auditLogs = await dbService.all('SELECT * FROM audit_logs');
        const settings = await dbService.get('SELECT * FROM app_settings WHERE id = 1');
        
        const backupData = {
            version: '1.0',
            timestamp: new Date().toISOString(),
            data: {
                companies,
                users: users.map(u => ({ ...u, password_hash: undefined })), // Don't backup passwords
                assets,
                financialYears,
                statutoryRates,
                extraLedgers,
                licenseHistory,
                assetYearlyData,
                auditLogs,
                settings
            }
        };
        
        // Add backup log entry
        const logId = uuidv4();
        await dbService.run(
            `INSERT INTO backup_logs (id, timestamp, action, initiated_by, details)
             VALUES (?, ?, ?, ?, ?)`,
            [logId, new Date().toISOString(), 'Backup', 'Auto (Pre-Action)', `Success: ${backupName}`]
        );
        
        res.json({ 
            message: 'Backup created successfully',
            backupName,
            backupData 
        });
    } catch (error) {
        console.error('Error creating backup:', error);
        res.status(500).json({ error: 'Failed to create backup' });
    }
});

// Export database
router.get('/export', async (req, res) => {
    try {
        // Export all data excluding backup logs
        const companies = await dbService.all('SELECT * FROM companies');
        const users = await dbService.all('SELECT * FROM users');
        const assets = await dbService.all('SELECT * FROM assets');
        const financialYears = await dbService.all('SELECT * FROM financial_years');
        const statutoryRates = await dbService.all('SELECT * FROM statutory_rates');
        const extraLedgers = await dbService.all('SELECT * FROM extra_ledgers');
        const licenseHistory = await dbService.all('SELECT * FROM license_history');
        const assetYearlyData = await dbService.all('SELECT * FROM asset_yearly_data');
        const auditLogs = await dbService.all('SELECT * FROM audit_logs');
        const settings = await dbService.get('SELECT * FROM app_settings WHERE id = 1');
        
        const exportData = {
            companies,
            users: users.map(u => ({ ...u, password_hash: undefined })), // Don't export passwords
            assets,
            financialYears,
            statutoryRates,
            extraLedgers,
            licenseHistory,
            assetYearlyData,
            auditLogs,
            settings
        };
        
        res.json(exportData);
    } catch (error) {
        console.error('Error exporting database:', error);
        res.status(500).json({ error: 'Failed to export database' });
    }
});

export default router;
