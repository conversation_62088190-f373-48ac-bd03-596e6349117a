/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useMemo, useEffect } from 'react';
import { ScheduleIIIDetailRow } from '../lib/types';
import { formatIndianNumber, sortData, exportToExcel } from '../lib/utils';
import { DownloadIcon } from '../Icons';

interface ModalProps {
    isOpen: boolean;
    onClose: () => void;
    title: string | null;
    data: ScheduleIIIDetailRow[];
    year: string;
    companyId: string | null;
    companyName: string | null;
    showAlert: (title: string, message: string, type: 'success' | 'error' | 'info') => void;
}

type SortableKeys = keyof ScheduleIIIDetailRow;

export function MethodWiseDetailModal({ isOpen, onClose, title, data, year, companyId, companyName, showAlert }: ModalProps) {
    const [sortConfig, setSortConfig] = useState<{ key: SortableKeys; direction: 'asc' | 'desc' } | null>({ key: 'recordId', direction: 'asc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);

    useEffect(() => {
        setSelectedRowId(null);
        setSelectedColumnKey(null);
    }, [data]);

    const sortedData = useMemo(() => {
        return sortData(data, sortConfig);
    }, [data, sortConfig]);

    const requestSort = (key: SortableKeys) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: SortableKeys) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };
    
    const handleExport = () => {
        if (!companyId || !companyName || !year || !title) return;
        
        const dataToExport = sortedData.map(row => ({
            "Record ID": row.recordId,
            "Asset Particulars": row.assetParticulars,
            "Gross Block - Opening": row.openingGross,
            "Gross Block - Additions": row.additionsGross,
            "Gross Block - Deletions": row.deletionsGross,
            "Gross Block - Closing": row.closingGross,
            "Depreciation - Opening": row.openingDepreciation,
            "Depreciation - For the Year": row.additionsDepreciation,
            "Depreciation - On Disposals": row.deletionsDepreciation,
            "Depreciation - Closing": row.closingDepreciation,
            "Net Block - Opening": row.openingNetBlock,
            "Net Block - Closing": row.closingNetBlock,
        }));
        
        const safeTitle = title.replace(/[^a-z0-9]/gi, '_').toLowerCase();
        if (!exportToExcel({ data: dataToExport, companyName, year, reportName: `Method_Wise_Details_${safeTitle}` })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    const getHeaderClass = (key: SortableKeys) => {
        const classes = ['sortable'];
        if (!['recordId', 'assetParticulars', 'assetType'].includes(key)) classes.push('text-right');
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        return classes.join(' ');
    };

    const getColumnClass = (key: SortableKeys) => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if (!['recordId', 'assetParticulars', 'assetType'].includes(key)) classes.push('text-right');
        if (key === 'assetParticulars') classes.push('td-wrap');
        return classes.join(' ');
    };

    if (!isOpen) return null;

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content modal-xl" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>{title}</h2>
                    <div className="actions">
                         <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export to Excel</button>
                         <button className="modal-close-btn" style={{marginLeft: '1rem'}} onClick={onClose}>&times;</button>
                    </div>
                </div>
                 <div className="table-container" style={{maxHeight: 'calc(80vh - 100px)'}}>
                    <table>
                        <thead>
                            <tr>
                                <th className={getHeaderClass('recordId')} onClick={() => requestSort('recordId')}>Record ID{getSortIndicator('recordId')}</th>
                                <th className={getHeaderClass('assetParticulars')} onClick={() => requestSort('assetParticulars')}>Asset Particulars{getSortIndicator('assetParticulars')}</th>
                                <th className={getHeaderClass('openingGross')} onClick={() => requestSort('openingGross')}>Gross Block - Opening{getSortIndicator('openingGross')}</th>
                                <th className={getHeaderClass('additionsGross')} onClick={() => requestSort('additionsGross')}>Gross Block - Additions{getSortIndicator('additionsGross')}</th>
                                <th className={getHeaderClass('deletionsGross')} onClick={() => requestSort('deletionsGross')}>Gross Block - Deletions{getSortIndicator('deletionsGross')}</th>
                                <th className={getHeaderClass('closingGross')} onClick={() => requestSort('closingGross')}>Gross Block - Closing{getSortIndicator('closingGross')}</th>
                                <th className={getHeaderClass('openingDepreciation')} onClick={() => requestSort('openingDepreciation')}>Depreciation - Opening{getSortIndicator('openingDepreciation')}</th>
                                <th className={getHeaderClass('additionsDepreciation')} onClick={() => requestSort('additionsDepreciation')}>Depreciation - For the Year{getSortIndicator('additionsDepreciation')}</th>
                                <th className={getHeaderClass('deletionsDepreciation')} onClick={() => requestSort('deletionsDepreciation')}>Depreciation - On Disposals{getSortIndicator('deletionsDepreciation')}</th>
                                <th className={getHeaderClass('closingDepreciation')} onClick={() => requestSort('closingDepreciation')}>Depreciation - Closing{getSortIndicator('closingDepreciation')}</th>
                                <th className={getHeaderClass('openingNetBlock')} onClick={() => requestSort('openingNetBlock')}>Net Block - Opening{getSortIndicator('openingNetBlock')}</th>
                                <th className={getHeaderClass('closingNetBlock')} onClick={() => requestSort('closingNetBlock')}>Net Block - Closing{getSortIndicator('closingNetBlock')}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {sortedData.length > 0 ? sortedData.map((row) => (
                                <tr key={row.recordId} onClick={() => setSelectedRowId(row.recordId)} className={row.recordId === selectedRowId ? 'selected-row' : ''}>
                                    <td className={getColumnClass('recordId')}>{row.recordId}</td>
                                    <td className={getColumnClass('assetParticulars')}>{row.assetParticulars}</td>
                                    <td className={getColumnClass('openingGross')}>{formatIndianNumber(row.openingGross)}</td>
                                    <td className={getColumnClass('additionsGross')}>{formatIndianNumber(row.additionsGross)}</td>
                                    <td className={getColumnClass('deletionsGross')}>{formatIndianNumber(row.deletionsGross)}</td>
                                    <td className={getColumnClass('closingGross')}>{formatIndianNumber(row.closingGross)}</td>
                                    <td className={getColumnClass('openingDepreciation')}>{formatIndianNumber(row.openingDepreciation)}</td>
                                    <td className={getColumnClass('additionsDepreciation')}>{formatIndianNumber(row.additionsDepreciation)}</td>
                                    <td className={getColumnClass('deletionsDepreciation')}>{formatIndianNumber(row.deletionsDepreciation)}</td>
                                    <td className={getColumnClass('closingDepreciation')}>{formatIndianNumber(row.closingDepreciation)}</td>
                                    <td className={getColumnClass('openingNetBlock')}>{formatIndianNumber(row.openingNetBlock)}</td>
                                    <td className={getColumnClass('closingNetBlock')}>{formatIndianNumber(row.closingNetBlock)}</td>
                                </tr>
                            )) : (
                                <tr><td colSpan={12} style={{textAlign: 'center', padding: '2rem'}}>No asset details to display.</td></tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}

