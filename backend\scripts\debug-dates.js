#!/usr/bin/env node

/**
 * Debug Asset Dates
 * Check the actual dates in the database
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database path
const dbPath = join(__dirname, '../database/far_sighted.db');

// Helper function to get data
const getData = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath);
        db.all(sql, params, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
        db.close();
    });
};

async function debugDates() {
    console.log('🔍 DEBUGGING ASSET DATES');
    console.log('========================\n');

    try {
        // Get asset dates and company adoption date
        const data = await getData(`
            SELECT 
                a.record_id,
                a.asset_particulars,
                a.put_to_use_date,
                a.wdv_of_adoption_date,
                a.gross_amount,
                c.first_date_of_adoption,
                c.company_name
            FROM assets a
            JOIN companies c ON a.company_id = c.id
            WHERE a.company_id = 'c1001'
            ORDER BY a.record_id
        `);

        console.log(`📊 Company: ${data[0].company_name}`);
        console.log(`📅 First Date of Adoption: ${data[0].first_date_of_adoption}\n`);

        data.forEach(asset => {
            const putToUseDate = new Date(asset.put_to_use_date);
            const adoptionDate = new Date(asset.first_date_of_adoption);
            const isOldAsset = putToUseDate <= adoptionDate;
            
            console.log(`🏭 Asset: ${asset.record_id}`);
            console.log(`   Put to Use Date: ${asset.put_to_use_date}`);
            console.log(`   Adoption Date: ${asset.first_date_of_adoption}`);
            console.log(`   Is Old Asset: ${isOldAsset ? 'YES' : 'NO'}`);
            console.log(`   Adoption WDV: ${asset.wdv_of_adoption_date ? '₹' + asset.wdv_of_adoption_date.toLocaleString() : 'NULL'}`);
            console.log(`   Gross Amount: ₹${asset.gross_amount.toLocaleString()}`);
            
            if (isOldAsset && asset.wdv_of_adoption_date) {
                console.log(`   ✅ Should use Adoption WDV for first year: ₹${asset.wdv_of_adoption_date.toLocaleString()}`);
            } else {
                console.log(`   ✅ Should use Gross Amount for first year: ₹${asset.gross_amount.toLocaleString()}`);
            }
            console.log('');
        });

    } catch (error) {
        console.error('❌ Error debugging dates:', error);
    }
}

// Run the debug
debugDates().then(() => {
    console.log('🏁 Debug completed');
}).catch(error => {
    console.error('❌ Debug failed:', error);
});
