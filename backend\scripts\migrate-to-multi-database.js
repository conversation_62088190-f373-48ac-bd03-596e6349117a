/**
 * DATABASE MIGRATION SCRIPT
 * Professional Advisory Services - Chartered Accountant
 * 
 * This script migrates from single database with company_id separation
 * to multiple databases (one per company) as per original design.
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Paths
const OLD_DB_PATH = join(__dirname, '../database/far_sighted.db');
const BASE_DATA_DIR = path.join(__dirname, '../../data');
const MASTER_DB_PATH = join(__dirname, '../database/master.db');

console.log('🔄 DATABASE MIGRATION: Single DB → Multiple DBs');
console.log('===============================================');

const migrateDatabase = async () => {
    try {
        console.log('📊 Step 1: Analyzing current database...\n');

        // Check if old database exists
        try {
            await fs.access(OLD_DB_PATH);
        } catch (error) {
            console.log('❌ Old database not found. Creating fresh multi-database setup...');
            await createFreshSetup();
            return;
        }

        // Connect to old database
        const oldDb = new sqlite3.Database(OLD_DB_PATH, (err) => {
            if (err) {
                console.error('❌ Error connecting to old database:', err.message);
                throw err;
            }
            console.log('✅ Connected to old database');
        });

        // Helper functions for old database
        const oldRun = (sql, params = []) => {
            return new Promise((resolve, reject) => {
                oldDb.run(sql, params, function(err) {
                    if (err) reject(err);
                    else resolve({ id: this.lastID, changes: this.changes });
                });
            });
        };

        const oldGet = (sql, params = []) => {
            return new Promise((resolve, reject) => {
                oldDb.get(sql, params, (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                });
            });
        };

        const oldAll = (sql, params = []) => {
            return new Promise((resolve, reject) => {
                oldDb.all(sql, params, (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                });
            });
        };

        // Get all companies from old database
        const companies = await oldAll('SELECT * FROM companies ORDER BY company_name');
        console.log(`📈 Found ${companies.length} companies to migrate:`);
        companies.forEach(company => {
            console.log(`   • ${company.company_name} (${company.id})`);
        });

        if (companies.length === 0) {
            console.log('⚠️ No companies found. Creating fresh setup...');
            await createFreshSetup();
            return;
        }

        console.log('\n📊 Step 2: Creating master database...\n');

        // Create master database
        await fs.mkdir(path.dirname(MASTER_DB_PATH), { recursive: true });
        const masterDb = new sqlite3.Database(MASTER_DB_PATH, (err) => {
            if (err) {
                console.error('❌ Error creating master database:', err.message);
                throw err;
            }
            console.log('✅ Created master database');
        });

        // Helper functions for master database
        const masterRun = (sql, params = []) => {
            return new Promise((resolve, reject) => {
                masterDb.run(sql, params, function(err) {
                    if (err) reject(err);
                    else resolve({ id: this.lastID, changes: this.changes });
                });
            });
        };

        // Create master database tables
        await createMasterTables(masterRun);

        // Migrate users to master database
        console.log('👥 Migrating users to master database...');
        const users = await oldAll('SELECT * FROM users');
        for (const user of users) {
            await masterRun(
                `INSERT INTO users (id, username, password_hash, role, recovery_key_hash, has_saved_recovery_key, is_active)
                 VALUES (?, ?, ?, ?, ?, ?, ?)`,
                [user.id, user.username, user.password_hash, user.role, user.recovery_key_hash, user.has_saved_recovery_key, true]
            );
        }
        console.log(`✅ Migrated ${users.length} users`);

        console.log('\n📊 Step 3: Creating individual company databases...\n');

        // Migrate each company
        for (const company of companies) {
            console.log(`🏢 Migrating: ${company.company_name}`);
            
            // Create company folder
            const companyFolderName = company.company_name.replace(/[^a-zA-Z0-9]/g, '_');
            const dataFolderPath = path.join(BASE_DATA_DIR, companyFolderName);
            const databasePath = path.join(dataFolderPath, 'company.db');

            await fs.mkdir(dataFolderPath, { recursive: true });

            // Add company to master database
            await masterRun(
                'INSERT INTO companies (id, company_name, database_path, data_folder_path) VALUES (?, ?, ?, ?)',
                [company.id, company.company_name, databasePath, dataFolderPath]
            );

            // Create company database
            const companyDb = new sqlite3.Database(databasePath, (err) => {
                if (err) {
                    console.error(`❌ Error creating database for ${company.company_name}:`, err.message);
                    throw err;
                }
            });

            // Helper functions for company database
            const companyRun = (sql, params = []) => {
                return new Promise((resolve, reject) => {
                    companyDb.run(sql, params, function(err) {
                        if (err) reject(err);
                        else resolve({ id: this.lastID, changes: this.changes });
                    });
                });
            };

            // Create company database tables
            await createCompanyTables(companyRun);

            // Migrate company info
            await companyRun(
                `INSERT INTO company_info (
                    id, company_name, pan, cin, date_of_incorporation,
                    financial_year_start, financial_year_end, first_date_of_adoption,
                    address_line1, address_line2, city, pin,
                    email, mobile, contact_person, license_valid_upto
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    company.id, company.company_name, company.pan, company.cin,
                    company.date_of_incorporation, company.financial_year_start,
                    company.financial_year_end, company.first_date_of_adoption,
                    company.address_line1, company.address_line2, company.city,
                    company.pin, company.email, company.mobile,
                    company.contact_person, company.license_valid_upto
                ]
            );

            // Migrate financial years
            const financialYears = await oldAll('SELECT * FROM financial_years WHERE company_id = ?', [company.id]);
            for (const fy of financialYears) {
                await companyRun(
                    'INSERT INTO financial_years (year_range, is_locked) VALUES (?, ?)',
                    [fy.year_range, fy.is_locked]
                );
            }

            // Migrate assets (remove company_id)
            const assets = await oldAll('SELECT * FROM assets WHERE company_id = ?', [company.id]);
            for (const asset of assets) {
                await companyRun(
                    `INSERT INTO assets (
                        record_id, asset_particulars, book_entry_date, put_to_use_date,
                        basic_amount, duties_taxes, gross_amount, vendor, invoice_no, model_make,
                        location, asset_id, remarks, ledger_name_in_books, asset_group,
                        asset_sub_group, schedule_iii_classification, disposal_date, disposal_amount,
                        salvage_percentage, wdv_of_adoption_date, is_leasehold, depreciation_method,
                        life_in_years, lease_period, scrap_it
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        asset.record_id, asset.asset_particulars, asset.book_entry_date, asset.put_to_use_date,
                        asset.basic_amount, asset.duties_taxes, asset.gross_amount, asset.vendor, asset.invoice_no,
                        asset.model_make, asset.location, asset.asset_id, asset.remarks, asset.ledger_name_in_books,
                        asset.asset_group, asset.asset_sub_group, asset.schedule_iii_classification,
                        asset.disposal_date, asset.disposal_amount, asset.salvage_percentage, asset.wdv_of_adoption_date,
                        asset.is_leasehold, asset.depreciation_method, asset.life_in_years, asset.lease_period, asset.scrap_it
                    ]
                );
            }

            // Migrate statutory rates
            const statutoryRates = await oldAll('SELECT * FROM statutory_rates WHERE company_id = ?', [company.id]);
            for (const rate of statutoryRates) {
                await companyRun(
                    `INSERT INTO statutory_rates (
                        is_statutory, tangibility, asset_group, asset_sub_group,
                        extra_shift_depreciation, useful_life_years, schedule_ii_classification
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
                    [
                        rate.is_statutory, rate.tangibility, rate.asset_group, rate.asset_sub_group,
                        rate.extra_shift_depreciation, rate.useful_life_years, rate.schedule_ii_classification
                    ]
                );
            }

            // Migrate asset yearly data
            const yearlyData = await oldAll(`
                SELECT ayd.* FROM asset_yearly_data ayd
                JOIN assets a ON ayd.asset_id = a.id
                WHERE a.company_id = ?
            `, [company.id]);
            
            for (const data of yearlyData) {
                // Get the new asset ID in the company database
                const newAsset = await new Promise((resolve, reject) => {
                    companyDb.get('SELECT id FROM assets WHERE record_id = ?', [data.record_id || 'unknown'], (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    });
                });

                if (newAsset) {
                    await companyRun(
                        `INSERT INTO asset_yearly_data (
                            asset_id, year_range, opening_wdv, use_days, depreciation_amount,
                            closing_wdv, second_shift_days, third_shift_days
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                            newAsset.id, data.year_range, data.opening_wdv, data.use_days,
                            data.depreciation_amount, data.closing_wdv, data.second_shift_days, data.third_shift_days
                        ]
                    );
                }
            }

            // Migrate extra ledgers
            const extraLedgers = await oldAll('SELECT * FROM extra_ledgers WHERE company_id = ?', [company.id]);
            for (const ledger of extraLedgers) {
                await companyRun('INSERT INTO extra_ledgers (ledger_name) VALUES (?)', [ledger.ledger_name]);
            }

            // Migrate license history
            const licenseHistory = await oldAll('SELECT * FROM license_history WHERE company_id = ?', [company.id]);
            for (const license of licenseHistory) {
                await companyRun(
                    'INSERT INTO license_history (id, license_key, valid_from, valid_upto, activated_at) VALUES (?, ?, ?, ?, ?)',
                    [license.id, license.license_key, license.valid_from, license.valid_upto, license.activated_at]
                );
            }

            // Migrate audit logs
            const auditLogs = await oldAll('SELECT * FROM audit_logs WHERE id LIKE ?', [`%${company.id}%`]);
            for (const log of auditLogs) {
                await companyRun(
                    'INSERT INTO audit_logs (id, timestamp, user_id, username, action, details, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                    [log.id, log.timestamp, log.user_id, log.username, log.action, log.details, log.ip_address, log.user_agent]
                );
            }

            // Close company database
            await new Promise((resolve, reject) => {
                companyDb.close((err) => {
                    if (err) reject(err);
                    else resolve();
                });
            });

            console.log(`   ✅ Migrated ${company.company_name} (${assets.length} assets, ${financialYears.length} FYs)`);
        }

        // Close databases
        await new Promise((resolve, reject) => {
            oldDb.close((err) => {
                if (err) reject(err);
                else resolve();
            });
        });

        await new Promise((resolve, reject) => {
            masterDb.close((err) => {
                if (err) reject(err);
                else resolve();
            });
        });

        console.log('\n📊 Step 4: Creating backup of old database...\n');
        
        // Backup old database
        const backupPath = OLD_DB_PATH.replace('.db', '_backup_before_migration.db');
        await fs.copyFile(OLD_DB_PATH, backupPath);
        console.log(`✅ Backup created: ${backupPath}`);

        console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY!');
        console.log('=====================================');
        console.log(`✅ Migrated ${companies.length} companies to separate databases`);
        console.log(`✅ Created master database for user management`);
        console.log(`✅ Data organized in separate folders per company`);
        console.log(`✅ Original database backed up`);
        
        console.log('\n📁 New Structure:');
        console.log(`📂 data/`);
        for (const company of companies) {
            const folderName = company.company_name.replace(/[^a-zA-Z0-9]/g, '_');
            console.log(`   📂 ${folderName}/`);
            console.log(`      📄 company.db`);
        }
        console.log(`📂 backend/database/`);
        console.log(`   📄 master.db`);

        console.log('\n💡 Next Steps:');
        console.log('1. Update your application to use the new multi-database service');
        console.log('2. Test the migrated data');
        console.log('3. Update any backup scripts to handle multiple databases');

    } catch (error) {
        console.error('❌ Migration failed:', error);
        throw error;
    }
};

async function createFreshSetup() {
    console.log('🆕 Creating fresh multi-database setup...');
    
    // Create master database
    await fs.mkdir(path.dirname(MASTER_DB_PATH), { recursive: true });
    const masterDb = new sqlite3.Database(MASTER_DB_PATH);
    
    const masterRun = (sql, params = []) => {
        return new Promise((resolve, reject) => {
            masterDb.run(sql, params, function(err) {
                if (err) reject(err);
                else resolve({ id: this.lastID, changes: this.changes });
            });
        });
    };

    await createMasterTables(masterRun);
    
    await new Promise((resolve, reject) => {
        masterDb.close((err) => {
            if (err) reject(err);
            else resolve();
        });
    });

    console.log('✅ Fresh multi-database setup created');
}

async function createMasterTables(masterRun) {
    const tables = [
        `CREATE TABLE IF NOT EXISTS companies (
            id TEXT PRIMARY KEY,
            company_name TEXT NOT NULL,
            database_path TEXT NOT NULL UNIQUE,
            data_folder_path TEXT NOT NULL,
            is_active BOOLEAN DEFAULT true,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        
        `CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT NOT NULL CHECK (role IN ('Admin', 'Data Entry', 'Report Viewer')),
            recovery_key_hash TEXT,
            has_saved_recovery_key BOOLEAN DEFAULT false,
            is_active BOOLEAN DEFAULT true,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        
        `CREATE TABLE IF NOT EXISTS user_company_access (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            company_id TEXT NOT NULL,
            access_level TEXT NOT NULL CHECK (access_level IN ('full', 'read_only', 'reports_only')),
            granted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            granted_by TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
            FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE,
            UNIQUE(user_id, company_id)
        )`,
        
        `CREATE TABLE IF NOT EXISTS system_settings (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL,
            description TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        
        `CREATE TABLE IF NOT EXISTS global_audit_logs (
            id TEXT PRIMARY KEY,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            user_id TEXT,
            company_id TEXT,
            username TEXT,
            action TEXT NOT NULL,
            details TEXT,
            ip_address TEXT,
            user_agent TEXT
        )`
    ];

    for (const table of tables) {
        await masterRun(table);
    }
}

async function createCompanyTables(companyRun) {
    const tables = [
        `CREATE TABLE IF NOT EXISTS company_info (
            id TEXT PRIMARY KEY,
            company_name TEXT NOT NULL,
            pan TEXT,
            cin TEXT,
            date_of_incorporation DATE,
            financial_year_start DATE NOT NULL,
            financial_year_end DATE NOT NULL,
            first_date_of_adoption DATE NOT NULL,
            address_line1 TEXT,
            address_line2 TEXT,
            city TEXT,
            pin TEXT,
            email TEXT,
            mobile TEXT,
            contact_person TEXT,
            license_valid_upto DATE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,

        `CREATE TABLE IF NOT EXISTS financial_years (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            year_range TEXT NOT NULL UNIQUE,
            is_locked BOOLEAN DEFAULT false,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,

        `CREATE TABLE IF NOT EXISTS assets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            record_id TEXT NOT NULL UNIQUE,
            asset_particulars TEXT NOT NULL,
            book_entry_date DATE,
            put_to_use_date DATE NOT NULL,
            basic_amount INTEGER,
            duties_taxes INTEGER,
            gross_amount INTEGER NOT NULL,
            vendor TEXT,
            invoice_no TEXT,
            model_make TEXT,
            location TEXT,
            asset_id TEXT,
            remarks TEXT,
            ledger_name_in_books TEXT,
            asset_group TEXT,
            asset_sub_group TEXT,
            schedule_iii_classification TEXT,
            disposal_date DATE,
            disposal_amount INTEGER,
            salvage_percentage DECIMAL(5,2) DEFAULT 5.0,
            wdv_of_adoption_date INTEGER,
            is_leasehold BOOLEAN DEFAULT false,
            depreciation_method TEXT CHECK (depreciation_method IN ('WDV', 'SLM')) DEFAULT 'WDV',
            life_in_years INTEGER,
            lease_period INTEGER,
            scrap_it BOOLEAN DEFAULT false,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,

        `CREATE TABLE IF NOT EXISTS statutory_rates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            is_statutory TEXT CHECK (is_statutory IN ('Yes', 'No')) DEFAULT 'Yes',
            tangibility TEXT CHECK (tangibility IN ('Tangible', 'Intangible')) DEFAULT 'Tangible',
            asset_group TEXT NOT NULL,
            asset_sub_group TEXT NOT NULL,
            extra_shift_depreciation TEXT,
            useful_life_years TEXT NOT NULL,
            schedule_ii_classification TEXT,
            UNIQUE(asset_group, asset_sub_group)
        )`,

        `CREATE TABLE IF NOT EXISTS asset_yearly_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            asset_id INTEGER NOT NULL,
            year_range TEXT NOT NULL,
            opening_wdv INTEGER DEFAULT 0,
            use_days INTEGER DEFAULT 0,
            depreciation_amount INTEGER DEFAULT 0,
            closing_wdv INTEGER DEFAULT 0,
            second_shift_days INTEGER DEFAULT 0,
            third_shift_days INTEGER DEFAULT 0,
            FOREIGN KEY (asset_id) REFERENCES assets (id) ON DELETE CASCADE,
            FOREIGN KEY (year_range) REFERENCES financial_years (year_range),
            UNIQUE(asset_id, year_range)
        )`,

        `CREATE TABLE IF NOT EXISTS extra_ledgers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ledger_name TEXT NOT NULL UNIQUE
        )`,

        `CREATE TABLE IF NOT EXISTS license_history (
            id TEXT PRIMARY KEY,
            license_key TEXT NOT NULL,
            valid_from DATE NOT NULL,
            valid_upto DATE NOT NULL,
            activated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,

        `CREATE TABLE IF NOT EXISTS audit_logs (
            id TEXT PRIMARY KEY,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            user_id TEXT,
            username TEXT,
            action TEXT NOT NULL,
            details TEXT,
            ip_address TEXT,
            user_agent TEXT
        )`,

        `CREATE TABLE IF NOT EXISTS backup_logs (
            id TEXT PRIMARY KEY,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            action TEXT NOT NULL CHECK (action IN ('Backup', 'Restore')),
            initiated_by TEXT,
            details TEXT,
            file_path TEXT,
            file_size INTEGER,
            status TEXT DEFAULT 'Completed'
        )`
    ];

    for (const table of tables) {
        await companyRun(table);
    }
}

migrateDatabase().catch(console.error);