# Automatic Backup System Implementation

## Overview
Implemented a comprehensive automatic backup system for FAR Sighted that creates scheduled backups, manages backup retention, and provides manual backup capabilities with change detection.

## Key Features

### 1. Scheduled Automatic Backups
- **Schedule**: Every Saturday at 12:00 PM (noon)
- **Timezone**: Asia/Kolkata
- **Missed Backup Detection**: If app is not running during scheduled time, backup runs on next app start
- **Change Detection**: Backups only created if data has changed since last backup

### 2. Backup Path Configuration
- User-configurable backup directory
- Automatic directory creation if it doesn't exist
- Path validation and error handling
- Settings persistence in database

### 3. Backup Retention Management
- Maximum 5 backup files maintained
- Automatic cleanup of older backups
- Date/time stamped backup files

### 4. Manual Backup Creation
- On-demand backup creation
- Same change detection as automatic backups
- User feedback for skipped/successful backups

## Technical Implementation

### 1. Backend Services

#### BackupService Class
**File**: `backend/services/backup-service.js`

**Key Methods**:
- `setBackupPath(backupPath)` - Configure backup directory
- `createBackup()` - Create backup with change detection
- `startScheduledBackups()` - Enable automatic scheduling
- `checkMissedBackup()` - Check and run missed backups
- `calculateDataHash()` - Detect data changes
- `cleanupOldBackups()` - Maintain backup retention

**Dependencies**:
- `archiver` - ZIP file creation
- `node-cron` - Scheduled task management
- `fs` - File system operations

#### Backup Routes
**File**: `backend/routes/backup.js`

**Endpoints**:
- `GET /api/backup/settings` - Get backup configuration
- `POST /api/backup/settings/path` - Set backup path
- `POST /api/backup/create` - Create manual backup
- `GET /api/backup/path-check` - Check if backup path is configured

### 2. Database Integration

#### Backup Settings Table
```sql
CREATE TABLE backup_settings (
    id INTEGER PRIMARY KEY,
    backup_path TEXT,
    last_backup_hash TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
```

#### Change Detection Logic
Uses audit log count and timestamps plus company data to create a simple hash for change detection:
```javascript
const hashData = `${auditInfo?.count || 0}-${auditInfo?.latest || ''}-${companiesInfo?.count || 0}-${companiesInfo?.latest || ''}`;
```

### 3. Frontend Integration

#### API Functions
**File**: `lib/api.ts`

**Added Functions**:
- `getBackupSettings()` - Retrieve backup configuration
- `setBackupPath(backupPath)` - Configure backup directory
- `createBackup()` - Trigger manual backup
- `checkBackupPath()` - Verify backup path status

#### BackupSettings Component
**File**: `BackupSettings.tsx`

**Features**:
- Backup status display
- Path configuration interface
- Manual backup creation
- Comprehensive backup information

### 4. Server Integration

#### Initialization
**File**: `backend/server.js`

```javascript
// Initialize backup service
console.log('🔄 Initializing backup service...');
const backupService = new BackupService(dbService.getDatabaseManager());
await backupService.initialize();

// Make backup service available globally
app.locals.backupService = backupService;
```

## Backup Process Flow

### 1. Automatic Backup Flow
```
Saturday 12:00 PM → Cron Job Triggers → Check Data Changes → Create Backup (if changed) → Cleanup Old Backups
```

### 2. Missed Backup Flow
```
App Startup → Check Last Backup Time → Compare with Last Saturday → Run Backup (if missed) → Continue Normal Operation
```

### 3. Manual Backup Flow
```
User Request → Check Backup Path → Check Data Changes → Create Backup → User Notification
```

### 4. Change Detection Flow
```
Calculate Current Hash → Compare with Last Backup Hash → Return True/False → Update Hash on Backup
```

## Backup File Structure

### 1. File Naming Convention
```
FAR-Backup-YYYY-MM-DD-HH-MM-SS.zip
Example: FAR-Backup-2025-07-10-12-00-00.zip
```

### 2. Backup Contents
- **Database Files**: All company databases and master database
- **Configuration**: Application configuration files
- **Compressed Format**: ZIP with maximum compression

### 3. Directory Structure in Backup
```
backup.zip
├── data/
│   ├── master.db
│   └── companies/
│       ├── company1.db
│       └── company2.db
└── config/
    └── [configuration files]
```

## User Interface

### 1. Backup Status Display
- Current backup path
- Scheduled backup status
- Maximum backup files setting
- Visual status indicators (✅/⚠️)

### 2. Path Configuration
- Text input for backup path
- Browse button (with instructions)
- Set Path button with validation
- Real-time status updates

### 3. Manual Backup
- Create Backup Now button
- Progress indication
- Success/skip notifications
- Path requirement validation

### 4. Information Section
- Automatic backup schedule details
- What is backed up
- File naming convention
- Important usage notes

## Error Handling

### 1. Path Configuration Errors
- Invalid path format
- Insufficient permissions
- Disk space issues
- Network path problems

### 2. Backup Creation Errors
- File system errors
- Compression failures
- Database access issues
- Audit logging failures (non-blocking)

### 3. Scheduling Errors
- Cron job failures
- Missed backup detection
- Service initialization issues

## Security Considerations

### 1. Data Protection
- Backup files contain sensitive company data
- User responsible for backup file security
- No password encryption (user should secure backup location)

### 2. Path Validation
- Basic path validation and sanitization
- Directory creation with proper permissions
- Error handling for access issues

### 3. Audit Trail
- Backup operations logged in audit trail
- Success/failure tracking
- User attribution for manual backups

## Configuration Options

### 1. Backup Retention
- Default: 5 backup files
- Configurable via `maxBackups` property
- Automatic cleanup of older files

### 2. Schedule Configuration
- Default: Every Saturday at 12:00 PM
- Timezone: Asia/Kolkata
- Configurable via cron expression

### 3. Change Detection Sensitivity
- Based on audit logs and company data
- Configurable hash calculation method
- Fallback to timestamp if hash fails

## Monitoring and Maintenance

### 1. Backup Success Monitoring
- Console logging for all backup operations
- Success/failure status in API responses
- User notifications for manual operations

### 2. Disk Space Management
- Automatic cleanup of old backups
- User responsibility for backup path disk space
- Compression to minimize file sizes

### 3. Performance Considerations
- Backup operations run in background
- Non-blocking for normal application operations
- Efficient change detection to avoid unnecessary backups

## Usage Instructions

### 1. Initial Setup
1. Navigate to Backup Settings
2. Enter backup folder path
3. Click "Set Path" to enable automatic backups
4. Verify status shows "Enabled"

### 2. Manual Backup
1. Ensure backup path is configured
2. Click "Create Backup Now"
3. Wait for completion notification
4. Check backup folder for new file

### 3. Backup Restoration
- Backup files are ZIP archives
- Extract to restore data
- Manual restoration process (not automated)
- Consult documentation for restoration procedures

## Future Enhancements

### 1. Backup Encryption
- Password-protected backup files
- AES encryption for sensitive data
- Key management system

### 2. Cloud Backup Integration
- Support for cloud storage providers
- Automatic upload to cloud services
- Sync with multiple backup locations

### 3. Incremental Backups
- Only backup changed data
- Faster backup operations
- Reduced storage requirements

### 4. Backup Verification
- Automatic backup integrity checks
- Restoration testing
- Corruption detection

### 5. Advanced Scheduling
- Multiple backup schedules
- Different retention policies
- Custom backup triggers

---
**Completed**: 2025-07-10
**Status**: ✅ Complete
**Impact**: Comprehensive backup system with automatic scheduling, change detection, and user-friendly management interface
