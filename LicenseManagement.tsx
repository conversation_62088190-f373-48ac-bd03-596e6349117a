/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, FC, useRef } from 'react';
import { api } from './lib/api';
import type { CompanyData, License } from './db-server';
import { DataViewProps } from './types';
import { UploadIcon, KeyIcon, InfoIcon, AlertTriangleIcon, CheckCircleIcon, XCircleIcon } from './Icons';

const LicenseStatusCard: FC<{ licenseValidUpto: string | null }> = ({ licenseValidUpto }) => {
    let status: 'Active' | 'Expires Soon' | 'Expired' | 'Missing' = 'Missing';
    let message = 'No valid license found for this company.';
    let Icon = AlertTriangleIcon;
    let colorClass = 'status-danger';

    if (licenseValidUpto) {
        const expiryDate = new Date(licenseValidUpto);
        const today = new Date();
        const daysUntilExpiry = (expiryDate.getTime() - today.getTime()) / (1000 * 3600 * 24);

        if (daysUntilExpiry < 0) {
            status = 'Expired';
            message = `License expired on ${expiryDate.toLocaleDateString()}.`;
            Icon = XCircleIcon;
            colorClass = 'status-danger';
        } else if (daysUntilExpiry <= 30) {
            status = 'Expires Soon';
            message = `License will expire on ${expiryDate.toLocaleDateString()}.`;
            Icon = AlertTriangleIcon;
            colorClass = 'status-warning';
        } else {
            status = 'Active';
            message = `License is active and valid until ${expiryDate.toLocaleDateString()}.`;
            Icon = CheckCircleIcon;
            colorClass = 'status-success';
        }
    }

    return (
        <div className={`license-status-card ${colorClass}`}>
            <div className="license-status-icon"><Icon size={32} /></div>
            <div className="license-status-content">
                <h4>{status}</h4>
                <p>{message}</p>
            </div>
        </div>
    );
};

export const LicenseManagement: FC<DataViewProps> = ({ companyId, onDataChange, showAlert }) => {
    const [companyData, setCompanyData] = useState<CompanyData | null>(null);
    const [loading, setLoading] = useState(true);
    const [isSaving, setIsSaving] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        const fetchData = async () => {
            if (!companyId) {
                setCompanyData(null);
                setLoading(false);
                return;
            }
            setLoading(true);
            try {
                const data = await api.getCompanyData(companyId);
                setCompanyData(data);
            } catch (error) {
                console.error("Failed to load company data:", error);
                showAlert('Error', 'Failed to load company license data.', 'error');
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [companyId]);
    
    const handleActivate = async (licenseData: any) => {
        if (!companyId) return;
        setIsSaving(true);
        try {
            await api.activateLicense(companyId, licenseData);
            showAlert('Success', 'License file activated successfully!', 'success');
            // Trigger a full data refresh in the main App component
            if (onDataChange) {
                onDataChange();
            }
            // Refetch local data for this view
            const data = await api.getCompanyData(companyId);
            setCompanyData(data);
        } catch (error) {
            console.error("Failed to activate license:", error);
            showAlert('Activation Failed', error instanceof Error ? error.message : 'An unknown error occurred.', 'error');
        } finally {
            setIsSaving(false);
        }
    };
    
     const handleFileSelected = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = async (e) => {
            const content = e.target?.result as string;
            try {
                const licenseData = JSON.parse(content);
                await handleActivate(licenseData);
            } catch (err) {
                showAlert('Invalid File', 'The selected file is not a valid JSON license file.', 'error');
            }
        };
        reader.readAsText(file);
        event.target.value = ''; // Reset input
    };
    
    if (loading) {
        return <div className="loading-indicator">Loading License Information...</div>;
    }
    
    if (!companyId || !companyData) {
        return <div className="company-info-container"><p>Please select a company to manage its license.</p></div>;
    }

    return (
        <>
            <div className="view-header">
                <h2>License Management</h2>
            </div>
            <div className="settings-container" style={{ display: 'flex', flexDirection: 'column', gap: '2.5rem' }}>
                <section>
                    <h3 className="settings-title">Current Status</h3>
                    <LicenseStatusCard licenseValidUpto={companyData.info.licenseValidUpto} />
                </section>
                
                <section>
                    <h3 className="settings-title">Activate or Renew License</h3>
                    <p className="settings-description">Upload the `.json` license file provided by support to activate or extend your subscription.</p>
                     <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileSelected}
                        style={{ display: 'none' }}
                        accept=".json,application/json"
                    />
                    <button className="btn btn-primary" onClick={() => fileInputRef.current?.click()} disabled={isSaving}>
                        <UploadIcon /> {isSaving ? 'Activating...' : 'Upload & Activate License'}
                    </button>
                </section>

                <section>
                    <h3 className="settings-title">License History</h3>
                    <p className="settings-description">A log of all licenses activated for this company.</p>
                    <div className="table-container" style={{maxHeight: '400px'}}>
                        <table>
                            <thead>
                                <tr>
                                    <th>License Key</th>
                                    <th>Valid From</th>
                                    <th>Valid Upto</th>
                                    <th>Activated On</th>
                                </tr>
                            </thead>
                            <tbody>
                                {companyData.licenseHistory.length === 0 ? (
                                     <tr><td colSpan={4} style={{textAlign: 'center', padding: '2rem'}}>No license history found.</td></tr>
                                ) : (
                                    [...companyData.licenseHistory].reverse().map(log => (
                                        <tr key={log.id}>
                                            <td>****-****-****-{log.key.slice(-4)}</td>
                                            <td>{new Date(log.validFrom).toLocaleDateString()}</td>
                                            <td>{new Date(log.validUpto).toLocaleDateString()}</td>
                                            <td>{new Date(log.activatedAt).toLocaleString()}</td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                </section>
            </div>
        </>
    );
};
