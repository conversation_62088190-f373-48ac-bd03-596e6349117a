
/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/

/*
 * Styles for all report views.
 */

/* Container for all report component views */
.report-view .table-container {
  overflow: auto; /* Enable horizontal and vertical scrolling */
  flex-grow: 1;
  min-height: 0;
  background-color: var(--background-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.report-view table {
  width: 100%;
  border-collapse: collapse;
  /* Use auto layout to allow content to dictate column width, which is better for reports */
  table-layout: auto; 
}

/* Report headers are sticky */
.report-view th {
  background-color: var(--background-tertiary);
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 16px 15px;
  text-align: center;
  border-bottom: 1px solid var(--border-primary);
  white-space: normal; /* Allow headers to wrap */
  /* Ensure report headers are sticky */
  position: sticky;
  top: 0;
  z-index: 15; /* Higher than regular table headers */
}

.report-view td {
  padding: 8px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
  vertical-align: middle;
  white-space: nowrap;
}

.report-view .td-wrap {
  white-space: normal;
}

/* Zebra & Checkerboard Striping */
.report-view tbody tr:nth-of-type(odd) > td {
  background-color: var(--background-stripe-row);
}
.report-view tbody tr > td:nth-of-type(odd) {
  background-color: var(--background-stripe-col);
}


.report-view tbody tr:hover td {
  background-color: var(--background-hover);
}

.report-view tbody tr:last-child td {
  border-bottom: none;
}

.report-view tfoot {
    font-weight: 700;
    background-color: var(--background-tertiary);
}

.report-view tfoot td {
    border-top: 2px solid var(--border-primary);
    color: var(--text-primary);
}

/* Utility and state classes, scoped to reports */
.report-view .text-right { text-align: right; }
.report-view .clickable-row { cursor: pointer; }

.report-view tbody tr.selected-row td,
.report-view tbody tr.selected-row:hover td {
  background-color: var(--background-selected) !important;
}

/* Sorting styles for report tables */
.report-view th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.report-view th.sortable:hover {
    background-color: var(--background-hover);
}

.report-view .sort-indicator {
    display: inline-block;
    vertical-align: middle;
    margin-left: 0.25rem;
    opacity: 0.5;
    width: 1em;
    height: 1em;
    line-height: 1;
    font-size: 0.8em;
}

.report-view th.sortable-active .sort-indicator {
    opacity: 1;
}

/* --- Selected Column Highlight for Reports --- */
/* These rules have higher specificity to override base report styles */
.report-view th.selected-col,
.report-view tbody td.selected-col {
    background-color: rgba(59, 130, 246, 0.15) !important;
}
.report-view tbody tr:hover td.selected-col {
    background-color: rgba(59, 130, 246, 0.20) !important;
}
.report-view tbody tr.selected-row td.selected-col,
.report-view tbody tr.selected-row:hover td.selected-col {
    background-color: rgba(59, 130, 246, 0.35) !important;
}