{"name": "far-sighted-backend", "version": "1.0.0", "description": "Backend API server for FAR Sighted Asset Management System", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js"}, "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "sqlite3": "^5.1.7", "uuid": "^10.0.0"}, "devDependencies": {"@types/node": "^22.14.0", "nodemon": "^3.1.4"}, "keywords": ["fixed-assets", "depreciation", "asset-management", "sqlite", "api"], "author": "FAR Sighted Development Team", "license": "Apache-2.0"}