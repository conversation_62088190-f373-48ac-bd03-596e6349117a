{"name": "far-sighted-backend", "version": "2.0.0", "description": "Backend API server for FAR Sighted Asset Management System - Multi-Database Structure", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "start:old": "node server-old.js", "dev": "nodemon server.js", "dev:old": "nodemon server-old.js", "migrate": "node scripts/migrate-database.js", "migrate:dry-run": "node scripts/migrate-database.js --dry-run", "migrate:force": "node scripts/migrate-database.js --force", "init-db": "node scripts/init-database.js", "init-db:new": "echo 'New structure initializes automatically on first run'", "test:migration": "npm run migrate:dry-run", "test:company": "curl -X POST http://localhost:3001/api/admin/test-company -H 'Content-Type: application/json'", "status": "curl -s http://localhost:3001/api/migration-status | node -p 'JSON.stringify(JSON.parse(require(\"fs\").readFileSync(0)), null, 2)'", "health": "curl -s http://localhost:3001/api/health | node -p 'JSON.stringify(JSON.parse(require(\"fs\").readFileSync(0)), null, 2)'", "system-info": "curl -s http://localhost:3001/api/system-info | node -p 'JSON.stringify(JSON.parse(require(\"fs\").readFileSync(0)), null, 2)'"}, "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "sqlite3": "^5.1.7", "uuid": "^10.0.0"}, "devDependencies": {"@types/node": "^22.14.0", "nodemon": "^3.1.4"}, "keywords": ["fixed-assets", "depreciation", "asset-management", "sqlite", "api", "multi-database", "company-isolation"], "author": "FAR Sighted Development Team", "license": "Apache-2.0", "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "FAR Sighted Asset Management System"}, "database": {"structure": "multi-database", "masterDatabase": "backend/database/master.db", "companyDatabases": "backend/database/companies/*/company.db", "migrationSupported": true}}