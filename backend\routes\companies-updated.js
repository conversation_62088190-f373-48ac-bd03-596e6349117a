/**
 * UPDATED COMPANY ROUTES FOR SEPARATE DATABASE ARCHITECTURE
 * Professional Advisory Services - Chartered Accountant
 * 
 * This file shows how API routes should be updated to work with
 * separate databases per company.
 */

import express from 'express';
import dbManager from '../services/multi-company-database.js';
import { authenticateToken, checkCompanyAccess } from '../middleware/auth.js';

const router = express.Router();

// Middleware to validate company access
const validateCompanyAccess = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        const { userId } = req.user;

        // Check if user has access to this company
        const access = await dbManager.checkUserAccess(userId, companyId);
        if (!access) {
            return res.status(403).json({ 
                error: 'Access denied to this company',
                companyId 
            });
        }

        // Add access level to request
        req.userAccess = access.access_level;
        next();

    } catch (error) {
        console.error('Company access validation error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// ===================================
// COMPANY MANAGEMENT ROUTES
// ===================================

// Get all companies for current user
router.get('/', authenticateToken, async (req, res) => {
    try {
        const { userId } = req.user;
        const companies = await dbManager.getUserCompanies(userId);
        res.json(companies);
    } catch (error) {
        console.error('Error fetching user companies:', error);
        res.status(500).json({ error: 'Failed to fetch companies' });
    }
});

// Get specific company information
router.get('/:companyId', authenticateToken, validateCompanyAccess, async (req, res) => {
    try {
        const { companyId } = req.params;
        
        // Get company info from master database
        const masterInfo = await dbManager.getFromMaster(
            'SELECT * FROM companies WHERE id = ?',
            [companyId]
        );

        // Get detailed company info from company database
        const detailedInfo = await dbManager.getFromCompanyDb(
            companyId,
            'SELECT * FROM company_info WHERE id = ?',
            ['main']
        );

        if (!masterInfo) {
            return res.status(404).json({ error: 'Company not found' });
        }

        res.json({
            ...masterInfo,
            ...detailedInfo
        });

    } catch (error) {
        console.error('Error fetching company info:', error);
        res.status(500).json({ error: 'Failed to fetch company information' });
    }
});

// Create new company
router.post('/', authenticateToken, async (req, res) => {
    try {
        const { userId } = req.user;
        const companyData = req.body;

        // Validate required fields
        if (!companyData.company_name || !companyData.id) {
            return res.status(400).json({ error: 'Company name and ID are required' });
        }

        // Create company database
        await dbManager.createCompanyDatabase(companyData);

        // Grant admin access to creator
        await dbManager.grantUserAccess(userId, companyData.id, 'admin', userId);

        // Log the action
        await dbManager.runOnMaster(
            'INSERT INTO system_audit_logs (id, user_id, company_id, action, details) VALUES (?, ?, ?, ?, ?)',
            [
                `audit_${Date.now()}`,
                userId,
                companyData.id,
                'CREATE_COMPANY',
                `Created company: ${companyData.company_name}`
            ]
        );

        res.status(201).json({ 
            message: 'Company created successfully',
            companyId: companyData.id 
        });

    } catch (error) {
        console.error('Error creating company:', error);
        res.status(500).json({ error: 'Failed to create company' });
    }
});

// ===================================
// ASSET ROUTES (COMPANY-SPECIFIC)
// ===================================

// Get all assets for a company
router.get('/:companyId/assets', authenticateToken, validateCompanyAccess, async (req, res) => {
    try {
        const { companyId } = req.params;
        const assets = await dbManager.allFromCompanyDb(
            companyId,
            'SELECT * FROM assets ORDER BY record_id'
        );
        res.json(assets);
    } catch (error) {
        console.error('Error fetching assets:', error);
        res.status(500).json({ error: 'Failed to fetch assets' });
    }
});

// Get specific asset
router.get('/:companyId/assets/:assetId', authenticateToken, validateCompanyAccess, async (req, res) => {
    try {
        const { companyId, assetId } = req.params;
        const asset = await dbManager.getFromCompanyDb(
            companyId,
            'SELECT * FROM assets WHERE id = ?',
            [assetId]
        );

        if (!asset) {
            return res.status(404).json({ error: 'Asset not found' });
        }

        res.json(asset);
    } catch (error) {
        console.error('Error fetching asset:', error);
        res.status(500).json({ error: 'Failed to fetch asset' });
    }
});

// Create new asset
router.post('/:companyId/assets', authenticateToken, validateCompanyAccess, async (req, res) => {
    try {
        const { companyId } = req.params;
        const { userId } = req.user;
        const assetData = req.body;

        // Check write access
        if (req.userAccess === 'read') {
            return res.status(403).json({ error: 'Read-only access - cannot create assets' });
        }

        // Insert asset into company database
        const result = await dbManager.runOnCompanyDb(
            companyId,
            `INSERT INTO assets (
                record_id, asset_particulars, book_entry_date, put_to_use_date,
                basic_amount, duties_taxes, gross_amount, vendor, invoice_no,
                model_make, location, asset_id, remarks, ledger_name_in_books,
                asset_group, asset_sub_group, schedule_iii_classification,
                salvage_percentage, wdv_of_adoption_date, is_leasehold,
                depreciation_method, life_in_years, lease_period, scrap_it
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                assetData.record_id, assetData.asset_particulars, assetData.book_entry_date,
                assetData.put_to_use_date, assetData.basic_amount, assetData.duties_taxes,
                assetData.gross_amount, assetData.vendor, assetData.invoice_no,
                assetData.model_make, assetData.location, assetData.asset_id,
                assetData.remarks, assetData.ledger_name_in_books, assetData.asset_group,
                assetData.asset_sub_group, assetData.schedule_iii_classification,
                assetData.salvage_percentage, assetData.wdv_of_adoption_date,
                assetData.is_leasehold, assetData.depreciation_method,
                assetData.life_in_years, assetData.lease_period, assetData.scrap_it
            ]
        );

        // Log the action in company audit log
        await dbManager.runOnCompanyDb(
            companyId,
            'INSERT INTO audit_logs (id, user_id, action, details) VALUES (?, ?, ?, ?)',
            [
                `audit_${Date.now()}`,
                userId,
                'CREATE_ASSET',
                `Created asset: ${assetData.asset_particulars} (${assetData.record_id})`
            ]
        );

        res.status(201).json({ 
            message: 'Asset created successfully',
            assetId: result.id 
        });

    } catch (error) {
        console.error('Error creating asset:', error);
        res.status(500).json({ error: 'Failed to create asset' });
    }
});

// Update asset
router.put('/:companyId/assets/:assetId', authenticateToken, validateCompanyAccess, async (req, res) => {
    try {
        const { companyId, assetId } = req.params;
        const { userId } = req.user;
        const assetData = req.body;

        // Check write access
        if (req.userAccess === 'read') {
            return res.status(403).json({ error: 'Read-only access - cannot update assets' });
        }

        // Update asset in company database
        const result = await dbManager.runOnCompanyDb(
            companyId,
            `UPDATE assets SET 
                asset_particulars = ?, book_entry_date = ?, put_to_use_date = ?,
                basic_amount = ?, duties_taxes = ?, gross_amount = ?,
                vendor = ?, invoice_no = ?, model_make = ?, location = ?,
                asset_id = ?, remarks = ?, ledger_name_in_books = ?,
                asset_group = ?, asset_sub_group = ?, schedule_iii_classification = ?,
                disposal_date = ?, disposal_amount = ?, salvage_percentage = ?,
                wdv_of_adoption_date = ?, is_leasehold = ?, depreciation_method = ?,
                life_in_years = ?, lease_period = ?, scrap_it = ?,
                updated_at = CURRENT_TIMESTAMP
             WHERE id = ?`,
            [
                assetData.asset_particulars, assetData.book_entry_date, assetData.put_to_use_date,
                assetData.basic_amount, assetData.duties_taxes, assetData.gross_amount,
                assetData.vendor, assetData.invoice_no, assetData.model_make, assetData.location,
                assetData.asset_id, assetData.remarks, assetData.ledger_name_in_books,
                assetData.asset_group, assetData.asset_sub_group, assetData.schedule_iii_classification,
                assetData.disposal_date, assetData.disposal_amount, assetData.salvage_percentage,
                assetData.wdv_of_adoption_date, assetData.is_leasehold, assetData.depreciation_method,
                assetData.life_in_years, assetData.lease_period, assetData.scrap_it,
                assetId
            ]
        );

        if (result.changes === 0) {
            return res.status(404).json({ error: 'Asset not found' });
        }

        // Log the action
        await dbManager.runOnCompanyDb(
            companyId,
            'INSERT INTO audit_logs (id, user_id, action, details) VALUES (?, ?, ?, ?)',
            [
                `audit_${Date.now()}`,
                userId,
                'UPDATE_ASSET',
                `Updated asset: ${assetData.asset_particulars} (ID: ${assetId})`
            ]
        );

        res.json({ message: 'Asset updated successfully' });

    } catch (error) {
        console.error('Error updating asset:', error);
        res.status(500).json({ error: 'Failed to update asset' });
    }
});

// Delete asset
router.delete('/:companyId/assets/:assetId', authenticateToken, validateCompanyAccess, async (req, res) => {
    try {
        const { companyId, assetId } = req.params;
        const { userId } = req.user;

        // Check admin access for deletion
        if (req.userAccess !== 'admin') {
            return res.status(403).json({ error: 'Admin access required for deletion' });
        }

        // Get asset details for logging
        const asset = await dbManager.getFromCompanyDb(
            companyId,
            'SELECT asset_particulars, record_id FROM assets WHERE id = ?',
            [assetId]
        );

        if (!asset) {
            return res.status(404).json({ error: 'Asset not found' });
        }

        // Delete asset from company database
        const result = await dbManager.runOnCompanyDb(
            companyId,
            'DELETE FROM assets WHERE id = ?',
            [assetId]
        );

        // Log the action
        await dbManager.runOnCompanyDb(
            companyId,
            'INSERT INTO audit_logs (id, user_id, action, details) VALUES (?, ?, ?, ?)',
            [
                `audit_${Date.now()}`,
                userId,
                'DELETE_ASSET',
                `Deleted asset: ${asset.asset_particulars} (${asset.record_id})`
            ]
        );

        res.json({ message: 'Asset deleted successfully' });

    } catch (error) {
        console.error('Error deleting asset:', error);
        res.status(500).json({ error: 'Failed to delete asset' });
    }
});

// ===================================
// SCHEDULE III REPORT ROUTE
// ===================================

// Generate Schedule III report for a company
router.get('/:companyId/reports/schedule-iii/:year', authenticateToken, validateCompanyAccess, async (req, res) => {
    try {
        const { companyId, year } = req.params;

        // Get company information
        const companyInfo = await dbManager.getFromCompanyDb(
            companyId,
            'SELECT * FROM company_info WHERE id = ?',
            ['main']
        );

        // Get financial years
        const financialYears = await dbManager.allFromCompanyDb(
            companyId,
            'SELECT year_range FROM financial_years ORDER BY year_range'
        );

        // Get assets
        const assets = await dbManager.allFromCompanyDb(
            companyId,
            'SELECT * FROM assets'
        );

        // Get statutory rates
        const statutoryRates = await dbManager.allFromCompanyDb(
            companyId,
            'SELECT * FROM statutory_rates'
        );

        // Prepare company data structure
        const companyData = {
            info: companyInfo,
            financialYears: financialYears.map(fy => fy.year_range),
            assets: assets,
            statutoryRates: statutoryRates
        };

        // Generate Schedule III report (using existing calculation logic)
        const reportData = generateScheduleIII(companyData, year);

        // Log the action
        await dbManager.runOnCompanyDb(
            companyId,
            'INSERT INTO audit_logs (id, user_id, action, details) VALUES (?, ?, ?, ?)',
            [
                `audit_${Date.now()}`,
                req.user.userId,
                'GENERATE_REPORT',
                `Generated Schedule III report for FY ${year}`
            ]
        );

        res.json(reportData);

    } catch (error) {
        console.error('Error generating Schedule III report:', error);
        res.status(500).json({ error: 'Failed to generate report' });
    }
});

// ===================================
// USER ACCESS MANAGEMENT
// ===================================

// Get users with access to company
router.get('/:companyId/users', authenticateToken, validateCompanyAccess, async (req, res) => {
    try {
        const { companyId } = req.params;

        // Check admin access
        if (req.userAccess !== 'admin') {
            return res.status(403).json({ error: 'Admin access required' });
        }

        const users = await dbManager.allFromMaster(
            `SELECT u.id, u.username, u.role, uca.access_level, uca.granted_at
             FROM users u 
             JOIN user_company_access uca ON u.id = uca.user_id 
             WHERE uca.company_id = ?
             ORDER BY u.username`,
            [companyId]
        );

        res.json(users);

    } catch (error) {
        console.error('Error fetching company users:', error);
        res.status(500).json({ error: 'Failed to fetch users' });
    }
});

// Grant user access to company
router.post('/:companyId/users/:userId/access', authenticateToken, validateCompanyAccess, async (req, res) => {
    try {
        const { companyId, userId: targetUserId } = req.params;
        const { access_level } = req.body;
        const { userId: grantorId } = req.user;

        // Check admin access
        if (req.userAccess !== 'admin') {
            return res.status(403).json({ error: 'Admin access required' });
        }

        // Validate access level
        if (!['read', 'write', 'admin'].includes(access_level)) {
            return res.status(400).json({ error: 'Invalid access level' });
        }

        // Grant access
        await dbManager.grantUserAccess(targetUserId, companyId, access_level, grantorId);

        // Log the action
        await dbManager.runOnMaster(
            'INSERT INTO system_audit_logs (id, user_id, company_id, action, details) VALUES (?, ?, ?, ?, ?)',
            [
                `audit_${Date.now()}`,
                grantorId,
                companyId,
                'GRANT_ACCESS',
                `Granted ${access_level} access to user ${targetUserId}`
            ]
        );

        res.json({ message: 'Access granted successfully' });

    } catch (error) {
        console.error('Error granting access:', error);
        res.status(500).json({ error: 'Failed to grant access' });
    }
});

// Helper function to generate Schedule III report
function generateScheduleIII(companyData, year) {
    // This would use the existing Schedule III calculation logic
    // from utils.ts with the calculateYearlyMetrics function
    
    // Implementation would be the same as current Schedule III logic
    // but operating on the company-specific data structure
    
    return {
        year: year,
        companyName: companyData.info.company_name,
        reportData: [], // Calculated Schedule III data
        totals: {}     // Calculated totals
    };
}

export default router;