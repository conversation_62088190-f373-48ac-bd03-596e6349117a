#!/usr/bin/env node

/**
 * Test LAN Access and Concurrency
 * Verifies multi-PC LAN access functionality
 */

import http from 'http';
import os from 'os';

// Get local IP addresses
function getLocalIPs() {
    const interfaces = os.networkInterfaces();
    const ips = [];
    
    for (const name of Object.keys(interfaces)) {
        for (const iface of interfaces[name]) {
            // Skip internal and non-IPv4 addresses
            if (iface.family === 'IPv4' && !iface.internal) {
                ips.push(iface.address);
            }
        }
    }
    
    return ips;
}

// Test HTTP endpoint
function testEndpoint(host, port, path) {
    return new Promise((resolve) => {
        const options = {
            hostname: host,
            port: port,
            path: path,
            method: 'GET',
            timeout: 5000
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                resolve({
                    success: true,
                    status: res.statusCode,
                    data: data,
                    headers: res.headers
                });
            });
        });

        req.on('error', (err) => {
            resolve({
                success: false,
                error: err.message
            });
        });

        req.on('timeout', () => {
            req.destroy();
            resolve({
                success: false,
                error: 'Request timeout'
            });
        });

        req.end();
    });
}

// Test concurrent requests
async function testConcurrency(host, port, path, concurrentRequests = 5) {
    console.log(`🔄 Testing ${concurrentRequests} concurrent requests to ${host}:${port}${path}`);
    
    const promises = [];
    for (let i = 0; i < concurrentRequests; i++) {
        promises.push(testEndpoint(host, port, path));
    }
    
    const results = await Promise.all(promises);
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log(`   ✅ Successful: ${successful}`);
    console.log(`   ❌ Failed: ${failed}`);
    
    if (failed > 0) {
        console.log('   Failed requests:');
        results.filter(r => !r.success).forEach((result, index) => {
            console.log(`     ${index + 1}. ${result.error}`);
        });
    }
    
    return { successful, failed, results };
}

async function testLANAccess() {
    console.log('🌐 FAR SIGHTED LAN ACCESS TEST');
    console.log('==============================\n');

    // Get local IP addresses
    const localIPs = getLocalIPs();
    console.log('📍 Local IP Addresses:');
    localIPs.forEach(ip => {
        console.log(`   ${ip}`);
    });
    console.log('');

    // Test configuration
    const backendPort = 8080;
    const frontendPort = 9091;
    const testPaths = {
        health: '/api/health',
        companies: '/api/companies',
        systemInfo: '/api/system-info'
    };

    // Test localhost first
    console.log('🏠 TESTING LOCALHOST ACCESS');
    console.log('===========================');
    
    for (const [name, path] of Object.entries(testPaths)) {
        console.log(`\n📡 Testing ${name} endpoint: http://localhost:${backendPort}${path}`);
        const result = await testEndpoint('localhost', backendPort, path);
        
        if (result.success) {
            console.log(`   ✅ Status: ${result.status}`);
            if (result.status === 200) {
                try {
                    const data = JSON.parse(result.data);
                    if (name === 'health') {
                        console.log(`   📊 Version: ${data.version || 'N/A'}`);
                        console.log(`   📊 Status: ${data.status || 'N/A'}`);
                    } else if (name === 'companies') {
                        console.log(`   📊 Companies found: ${Array.isArray(data) ? data.length : 'N/A'}`);
                    }
                } catch (e) {
                    console.log(`   📊 Response: ${result.data.substring(0, 100)}...`);
                }
            }
        } else {
            console.log(`   ❌ Error: ${result.error}`);
        }
    }

    // Test LAN access
    console.log('\n\n🌐 TESTING LAN ACCESS');
    console.log('=====================');
    
    for (const ip of localIPs) {
        console.log(`\n📍 Testing IP: ${ip}`);
        
        // Test backend API
        console.log(`   🔧 Backend API: http://${ip}:${backendPort}/api/health`);
        const backendResult = await testEndpoint(ip, backendPort, '/api/health');
        
        if (backendResult.success) {
            console.log(`   ✅ Backend accessible from LAN`);
            console.log(`   📊 Status: ${backendResult.status}`);
        } else {
            console.log(`   ❌ Backend not accessible: ${backendResult.error}`);
        }
        
        // Test frontend (basic connectivity)
        console.log(`   🖥️  Frontend: http://${ip}:${frontendPort}/`);
        const frontendResult = await testEndpoint(ip, frontendPort, '/');
        
        if (frontendResult.success) {
            console.log(`   ✅ Frontend accessible from LAN`);
            console.log(`   📊 Status: ${frontendResult.status}`);
        } else {
            console.log(`   ❌ Frontend not accessible: ${frontendResult.error}`);
        }
    }

    // Test concurrency
    console.log('\n\n⚡ TESTING CONCURRENCY');
    console.log('======================');
    
    if (localIPs.length > 0) {
        const testIP = localIPs[0];
        console.log(`Using IP: ${testIP}\n`);
        
        // Test concurrent API calls
        await testConcurrency(testIP, backendPort, '/api/health', 10);
        await testConcurrency(testIP, backendPort, '/api/companies', 5);
    }

    // Test CORS headers
    console.log('\n\n🔒 TESTING CORS CONFIGURATION');
    console.log('=============================');
    
    const corsResult = await testEndpoint('localhost', backendPort, '/api/health');
    if (corsResult.success && corsResult.headers) {
        const corsHeaders = [
            'access-control-allow-origin',
            'access-control-allow-credentials',
            'access-control-allow-methods',
            'access-control-allow-headers'
        ];
        
        console.log('📋 CORS Headers:');
        corsHeaders.forEach(header => {
            const value = corsResult.headers[header];
            if (value) {
                console.log(`   ${header}: ${value}`);
            } else {
                console.log(`   ${header}: Not set`);
            }
        });
    }

    // Summary and recommendations
    console.log('\n\n📋 SUMMARY AND RECOMMENDATIONS');
    console.log('==============================');
    
    console.log('✅ Configuration Status:');
    console.log('   - Server binding: 0.0.0.0 (all interfaces)');
    console.log('   - CORS: Configured for LAN access');
    console.log('   - Ports: Backend 8080, Frontend 9091');
    
    console.log('\n🔧 For LAN Access:');
    localIPs.forEach(ip => {
        console.log(`   Backend API: http://${ip}:${backendPort}/api`);
        console.log(`   Frontend URL: http://${ip}:${frontendPort}/`);
    });
    
    console.log('\n⚠️  Firewall Requirements:');
    console.log('   - Allow inbound TCP port 8080 (Backend)');
    console.log('   - Allow inbound TCP port 9091 (Frontend)');
    console.log('   - Ensure Windows Firewall allows Node.js');
    
    console.log('\n📱 Client PC Instructions:');
    console.log('   1. Open web browser');
    console.log('   2. Navigate to: http://[SERVER_IP]:9091');
    console.log('   3. Login with existing credentials');
    console.log('   4. Verify all functionality works');
    
    console.log('\n🔍 Troubleshooting:');
    console.log('   - If connection fails, check firewall settings');
    console.log('   - Verify server and client are on same network');
    console.log('   - Test with: ping [SERVER_IP]');
    console.log('   - Test port: telnet [SERVER_IP] 8080');
}

// Run the test
testLANAccess().then(() => {
    console.log('\n🏁 LAN access test completed');
}).catch(error => {
    console.error('❌ Test failed:', error);
});
