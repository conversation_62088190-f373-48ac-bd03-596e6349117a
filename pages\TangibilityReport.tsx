/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo } from 'react';
import { api } from '../lib/api';
import type { CompanyData } from '../lib/db-server';
import { DataViewProps, ScheduleIIIDetailRow } from '../lib/types';
import { formatIndianNumber, calculateYearlyMetrics, sortData, exportToExcel } from '../lib/utils';
import { TangibilityDetailModal } from './TangibilityDetailModal';
import { DownloadIcon } from '../Icons';

// Define the structure for a row in the Tangibility Report
interface TangibilityReportRow {
    tangibility: string;
    openingGross: number;
    additionsGross: number;
    deletionsGross: number;
    closingGross: number;
    openingDepreciation: number;
    additionsDepreciation: number;
    deletionsDepreciation: number;
    closingDepreciation: number;
    openingNetBlock: number;
    closingNetBlock: number;
}

export function TangibilityReport({ companyId, companyName, year, showAlert }: DataViewProps) {
    const [reportData, setReportData] = useState<TangibilityReportRow[]>([]);
    const [totals, setTotals] = useState<TangibilityReportRow | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [sortConfig, setSortConfig] = useState<{ key: keyof TangibilityReportRow; direction: 'asc' | 'desc' } | null>({ key: 'tangibility', direction: 'asc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);
    
    // For drill-down modal
    const [companyData, setCompanyData] = useState<CompanyData | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalData, setModalData] = useState<ScheduleIIIDetailRow[]>([]);
    const [modalTitle, setModalTitle] = useState('');

    useEffect(() => {
        const fetchDataAndCalculate = async () => {
            if (!companyId || !year) {
                setLoading(false);
                setReportData([]);
                setTotals(null);
                setCompanyData(null);
                return;
            }

            setLoading(true);
            setError(null);
            setSelectedRowId(null);
            setSelectedColumnKey(null);

            try {
                const data = await api.getCompanyData(companyId);
                setCompanyData(data);
                
                if (!data || !data.assets || data.assets.length === 0 || !data.statutoryRates || data.statutoryRates.length === 0) {
                     setReportData([]);
                     setTotals(null);
                     setLoading(false);
                     return;
                }

                const subGroupToTangibilityMap = new Map<string, string>();
                data.statutoryRates.forEach(rate => {
                    subGroupToTangibilityMap.set(rate.assetSubGroup, rate.tangibility);
                });
                
                const reportMap: Record<string, TangibilityReportRow> = {
                    'Tangible': { tangibility: 'Tangible', openingGross: 0, additionsGross: 0, deletionsGross: 0, closingGross: 0, openingDepreciation: 0, additionsDepreciation: 0, deletionsDepreciation: 0, closingDepreciation: 0, openingNetBlock: 0, closingNetBlock: 0 },
                    'Intangible': { tangibility: 'Intangible', openingGross: 0, additionsGross: 0, deletionsGross: 0, closingGross: 0, openingDepreciation: 0, additionsDepreciation: 0, deletionsDepreciation: 0, closingDepreciation: 0, openingNetBlock: 0, closingNetBlock: 0 },
                };

                for (const asset of data.assets) {
                    if (!asset.putToUseDate || !asset.assetSubGroup) continue;
                    
                    const tangibility = subGroupToTangibilityMap.get(asset.assetSubGroup);
                    if (!tangibility || !reportMap[tangibility]) continue;

                    const metrics = calculateYearlyMetrics(asset, year, data);
                    
                    reportMap[tangibility].openingGross += metrics.openingGross;
                    reportMap[tangibility].additionsGross += metrics.additionsGross;
                    reportMap[tangibility].deletionsGross += metrics.deletionsGross;
                    reportMap[tangibility].openingDepreciation += metrics.openingDepreciation;
                    reportMap[tangibility].additionsDepreciation += metrics.additionsDepreciation;
                    reportMap[tangibility].deletionsDepreciation += metrics.deletionsDepreciation;
                }

                const finalReportData: TangibilityReportRow[] = Object.values(reportMap);
                
                const totalRow: TangibilityReportRow = { tangibility: 'Total', openingGross: 0, additionsGross: 0, deletionsGross: 0, closingGross: 0, openingDepreciation: 0, additionsDepreciation: 0, deletionsDepreciation: 0, closingDepreciation: 0, openingNetBlock: 0, closingNetBlock: 0 };

                finalReportData.forEach(row => {
                    row.closingGross = row.openingGross + row.additionsGross - row.deletionsGross;
                    row.closingDepreciation = row.openingDepreciation + row.additionsDepreciation - row.deletionsDepreciation;
                    row.openingNetBlock = row.openingGross - row.openingDepreciation;
                    row.closingNetBlock = row.closingGross - row.closingDepreciation;

                    totalRow.openingGross += row.openingGross;
                    totalRow.additionsGross += row.additionsGross;
                    totalRow.deletionsGross += row.deletionsGross;
                    totalRow.closingGross += row.closingGross;
                    totalRow.openingDepreciation += row.openingDepreciation;
                    totalRow.additionsDepreciation += row.additionsDepreciation;
                    totalRow.deletionsDepreciation += row.deletionsDepreciation;
                    totalRow.closingDepreciation += row.closingDepreciation;
                    totalRow.openingNetBlock += row.openingNetBlock;
                    totalRow.closingNetBlock += row.closingNetBlock;
                });
                
                setReportData(finalReportData);
                setTotals(totalRow);
            } catch (err) {
                setError('Failed to generate Tangibility report.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchDataAndCalculate();
    }, [companyId, year]);
    
    const handleRowDoubleClick = (tangibility: string) => {
        if (!tangibility || tangibility === 'Total' || !companyData) return;

        const subGroupToTangibilityMap = new Map<string, string>();
        companyData.statutoryRates.forEach(rate => {
            subGroupToTangibilityMap.set(rate.assetSubGroup, rate.tangibility);
        });

        const filtered = companyData.assets.filter(a => a.assetSubGroup && subGroupToTangibilityMap.get(a.assetSubGroup) === tangibility);
        
        const details: ScheduleIIIDetailRow[] = filtered.map((asset) => {
            const metrics = calculateYearlyMetrics(asset, year, companyData);
            
            return {
                recordId: asset.recordId,
                assetParticulars: asset.assetParticulars,
                assetType: tangibility,
                openingGross: metrics.openingGross,
                additionsGross: metrics.additionsGross,
                deletionsGross: metrics.deletionsGross,
                closingGross: metrics.closingGross,
                openingDepreciation: metrics.openingDepreciation,
                additionsDepreciation: metrics.additionsDepreciation,
                deletionsDepreciation: metrics.deletionsDepreciation,
                closingDepreciation: metrics.closingDepreciation,
                openingNetBlock: metrics.openingNetBlock,
                closingNetBlock: metrics.closingNetBlock,
                assetCount: 1,
            };
        });

        setModalData(details);
        setModalTitle(`Details for: ${tangibility} Assets`);
        setIsModalOpen(true);
    };

    const sortedReportData = useMemo(() => {
        return sortData(reportData, sortConfig);
    }, [reportData, sortConfig]);

    const requestSort = (key: keyof TangibilityReportRow) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: keyof TangibilityReportRow) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };

    const getHeaderClass = (key: keyof TangibilityReportRow) => {
        const classes = ['sortable'];
        if (key !== 'tangibility') classes.push('text-right');
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        return classes.join(' ');
    };

    const getColumnClass = (key: keyof TangibilityReportRow) => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if (key !== 'tangibility') classes.push('text-right');
        return classes.join(' ');
    };

    const handleExport = () => {
        if (!companyId || !companyName || !year) return;
        
        const dataToExport = [...sortedReportData, totals]
            .filter(Boolean)
            .map(row => ({
                "Tangibility": row!.tangibility,
                "Gross Block - Opening": row!.openingGross,
                "Gross Block - Additions": row!.additionsGross,
                "Gross Block - Deletions": row!.deletionsGross,
                "Gross Block - Closing": row!.closingGross,
                "Depreciation - Opening": row!.openingDepreciation,
                "Depreciation - For the Year": row!.additionsDepreciation,
                "Depreciation - On Disposals": row!.deletionsDepreciation,
                "Depreciation - Closing": row!.closingDepreciation,
                "Net Block - Opening": row!.openingNetBlock,
                "Net Block - Closing": row!.closingNetBlock,
            }));

        if (!exportToExcel({ data: dataToExport, companyName, year, reportName: 'Tangibility_Report' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    if (loading) return <div className="loading-indicator">Generating Tangibility Report...</div>;
    if (error) return <div className="error-message">{error}</div>;
    if (!companyId) return null;
    if (reportData.length === 0) return <div className="company-info-container"><p>No asset data available to generate the report for the selected year.</p></div>;

    return (
        <div className="report-view">
            <div className="view-header">
                <h2>Tangibility Report for FY {year}</h2>
                <div className="actions">
                    <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export to Excel</button>
                </div>
            </div>
            <div className="table-container">
                <table>
                    <thead>
                        <tr>
                            <th className={getHeaderClass('tangibility')} onClick={() => requestSort('tangibility')}>Tangibility{getSortIndicator('tangibility')}</th>
                            <th className={getHeaderClass('openingGross')} onClick={() => requestSort('openingGross')}>Gross Block - Opening{getSortIndicator('openingGross')}</th>
                            <th className={getHeaderClass('additionsGross')} onClick={() => requestSort('additionsGross')}>Gross Block - Additions{getSortIndicator('additionsGross')}</th>
                            <th className={getHeaderClass('deletionsGross')} onClick={() => requestSort('deletionsGross')}>Gross Block - Deletions{getSortIndicator('deletionsGross')}</th>
                            <th className={getHeaderClass('closingGross')} onClick={() => requestSort('closingGross')}>Gross Block - Closing{getSortIndicator('closingGross')}</th>
                            <th className={getHeaderClass('openingDepreciation')} onClick={() => requestSort('openingDepreciation')}>Depreciation - Opening{getSortIndicator('openingDepreciation')}</th>
                            <th className={getHeaderClass('additionsDepreciation')} onClick={() => requestSort('additionsDepreciation')}>Depreciation - For the Year{getSortIndicator('additionsDepreciation')}</th>
                            <th className={getHeaderClass('deletionsDepreciation')} onClick={() => requestSort('deletionsDepreciation')}>Depreciation - On Disposals{getSortIndicator('deletionsDepreciation')}</th>
                            <th className={getHeaderClass('closingDepreciation')} onClick={() => requestSort('closingDepreciation')}>Depreciation - Closing{getSortIndicator('closingDepreciation')}</th>
                            <th className={getHeaderClass('openingNetBlock')} onClick={() => requestSort('openingNetBlock')}>Net Block - Opening{getSortIndicator('openingNetBlock')}</th>
                            <th className={getHeaderClass('closingNetBlock')} onClick={() => requestSort('closingNetBlock')}>Net Block - Closing{getSortIndicator('closingNetBlock')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {sortedReportData.map((row) => (
                            <tr key={row.tangibility} onDoubleClick={() => handleRowDoubleClick(row.tangibility)} onClick={() => setSelectedRowId(row.tangibility)} className={`clickable-row ${row.tangibility === selectedRowId ? 'selected-row' : ''}`}>
                                <td className={getColumnClass('tangibility')}>{row.tangibility}</td>
                                <td className={getColumnClass('openingGross')}>{formatIndianNumber(row.openingGross)}</td>
                                <td className={getColumnClass('additionsGross')}>{formatIndianNumber(row.additionsGross)}</td>
                                <td className={getColumnClass('deletionsGross')}>{formatIndianNumber(row.deletionsGross)}</td>
                                <td className={getColumnClass('closingGross')}>{formatIndianNumber(row.closingGross)}</td>
                                <td className={getColumnClass('openingDepreciation')}>{formatIndianNumber(row.openingDepreciation)}</td>
                                <td className={getColumnClass('additionsDepreciation')}>{formatIndianNumber(row.additionsDepreciation)}</td>
                                <td className={getColumnClass('deletionsDepreciation')}>{formatIndianNumber(row.deletionsDepreciation)}</td>
                                <td className={getColumnClass('closingDepreciation')}>{formatIndianNumber(row.closingDepreciation)}</td>
                                <td className={getColumnClass('openingNetBlock')}>{formatIndianNumber(row.openingNetBlock)}</td>
                                <td className={getColumnClass('closingNetBlock')}>{formatIndianNumber(row.closingNetBlock)}</td>
                            </tr>
                        ))}
                    </tbody>
                    {totals && (
                        <tfoot>
                            <tr>
                                <td className={getColumnClass('tangibility')}>{totals.tangibility}</td>
                                <td className={getColumnClass('openingGross')}>{formatIndianNumber(totals.openingGross)}</td>
                                <td className={getColumnClass('additionsGross')}>{formatIndianNumber(totals.additionsGross)}</td>
                                <td className={getColumnClass('deletionsGross')}>{formatIndianNumber(totals.deletionsGross)}</td>
                                <td className={getColumnClass('closingGross')}>{formatIndianNumber(totals.closingGross)}</td>
                                <td className={getColumnClass('openingDepreciation')}>{formatIndianNumber(totals.openingDepreciation)}</td>
                                <td className={getColumnClass('additionsDepreciation')}>{formatIndianNumber(totals.additionsDepreciation)}</td>
                                <td className={getColumnClass('deletionsDepreciation')}>{formatIndianNumber(totals.deletionsDepreciation)}</td>
                                <td className={getColumnClass('closingDepreciation')}>{formatIndianNumber(totals.closingDepreciation)}</td>
                                <td className={getColumnClass('openingNetBlock')}>{formatIndianNumber(totals.openingNetBlock)}</td>
                                <td className={getColumnClass('closingNetBlock')}>{formatIndianNumber(totals.closingNetBlock)}</td>
                            </tr>
                        </tfoot>
                    )}
                </table>
            </div>
            <TangibilityDetailModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                title={modalTitle}
                data={modalData}
                year={year}
                companyId={companyId}
                companyName={companyName}
                showAlert={showAlert}
            />
        </div>
    );
}

