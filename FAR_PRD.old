# Product Requirements Document (PRD)

## Title: Fixed Asset Register (FAR) Management System

### Platform
- Desktop App

### Tech Stack
- Frontend: React, TypeScript/JavaScript
- Backend: Simulated via a client-side API layer. The architecture is backend-agnostic, preparing for future integration with a live database.

---

## 1. Core Functional Requirements

### 1.1 Multi-Entity Support
- The application supports multiple distinct companies.
- Each company's data is stored and managed separately.

### 1.2 Licensing Control
- Licensing is per entity per financial year.
- If license is invalid:
  - Data entry allowed only for 3 months from start of FY.
  - Report generation is blocked after that.
- Licensing data is stored within the company's data.

### 1.3 Company Master
- Fields:
  - Company Name, CIN, Date of Incorporation, Financial Year Start/End (default: Apr 1 – Mar 31), First date of adoption, Data Folder Path, Address, City, PIN, Email, Mobile, Contact Person, License Valid Upto.
- The Asset Classification master is loaded per company.
  - 'Useful Life' for statutory items is non-editable.
  - 'Useful Life' for non-statutory items is editable.

---

## 2. Asset Management

### 2.1 Asset Classification Master
- Defines asset categories based on statutory requirements but can be extended with custom classifications.
- **Key Fields**: `Asset Group`, `Asset Sub-Group`, `Useful Life (Yrs)`, `Extra Shift Depr.`, `Schedule III Classification`, `Tangibility`, `Is Statutory`.

### 2.2 Asset Records
- A system-generated, unique "Record ID" is the primary identifier for each asset. For new, unsaved assets, this field displays a placeholder (e.g., `<Auto-Generated>`) to indicate that a permanent ID will be assigned upon saving.
- Editing is locked for all financial years except the most recent one (unless an admin unlocks a past year).
- **Key Fields**: The structure of asset fields is defined in the "Assets Structure.csv" document. To maintain a clean interface, disposal and scrapping information (`Disposal Date`, `Disposal Amount`, `Scrap It`) is managed via a dedicated "Dispose" action on each asset row, which opens a focused modal.
- **Auto-Calculation**: 'Gross Amount' is automatically calculated as `Basic Amount` + `Duties & Taxes`.
- **Date Validation**:
  - 'Put to Use Date' defaults to 'Book Entry Date'.
  - 'Put to Use Date' cannot be before 'Book Entry Date'.
  - 'Disposal Date' must be on or after 'Put to Use Date' and within the selected financial year.
- **Field Dependencies**:
  - Changing 'Asset Group' clears dependent fields ('Asset Sub-Group', 'Life in years', 'Schedule III Classification').
  - 'WDV on Adoption Date' is enabled only for assets whose 'Put to use Date' is before the company's adoption date.
- Compulsory fields are marked with an asterisk (*).

### 2.3 Create Next Financial Year
- A function to close the current financial year, finalize all calculations, and prepare the system for the next one.
- **Trigger**: A "Create Next FY" button, enabled only for the most recent financial year.
- **Process**: An atomic transaction that validates data, calculates depreciation and closing WDV for every asset, and stores the final values. If any error occurs, the process halts, and no data is changed.

### 2.4 Shift-Based Depreciation
- A dedicated "Extra Shift Days" view allows users to input 2nd and 3rd shift days for eligible assets for the selected year.

### 2.5 Depreciation Calculation
- All financial calculations are rounded to the nearest whole number.
- Depreciation is prorated based on the exact number of days an asset was in use during the financial year.
- **Opening WDV**: For assets from before the adoption date, 'WDV of Adoption Date' is used. For subsequent years and new assets, the previous year's closing WDV is used.
- **Data Storage**: Year-specific calculated values (e.g., `Depreciation-2023-2024`) are stored as new properties on each asset record.

### 2.6 Administrator Override: Unlocking a Financial Year
- A high-risk, admin-only function to allow for corrections to historical data.
- **Workflow**:
  - An admin selects a locked year from the "Unlock Financial Year" view.
  - The system requires password re-authentication and warns of the risks.
  - An automatic backup is created, and an audit log is generated.
  - The year is unlocked for editing, and a prominent "Lock & Recalculate" button appears.
  - When re-locking, an **Impact Analysis modal** shows the admin the full consequences of their changes before confirming.
  - Upon confirmation, the system performs a full recalculation from the unlocked year forward and re-locks the year.

---

## 3. Reporting

### 3.1 Reports
- **Asset Group Report**: Aggregates financial data by "Asset Group" and "Asset Sub-Group".
- **Schedule III Format**: A summary report as per Schedule III, including an asset count.
- **Ledger-wise**: Groups financial movements by 'Ledger Name'.
- **Method-wise (SLM / WDV)**: Separates asset values by depreciation method.
- **Tangibility-wise**: Groups assets into Tangible and Intangible categories.
- **Scrap & End of Life Report**: Lists all assets that have been disposed of, are marked with `Scrap It: Yes`, or have reached their calculated end-of-life date. This report can be filtered by each category and exported.
- **Drill-Down Feature**: Most summary reports support double-clicking a row to open a modal with the detailed underlying asset data.
- All reports support column sorting and exporting to Excel.

---

## 4. User Management & Security

### 4.1 Roles
- **Admin**: Full control over all data, settings, and user management.
- **Data Entry**: Can manage asset data and masters but cannot access administrative functions.
- **Report Viewer**: Read-only access to all reports.

### 4.2 Authentication
- The application starts with a secure login screen requiring a username and password.

### 4.3 Password & Account Recovery
- **Recovery Key System**: The application uses a secure, offline recovery key system.
  - When a user's password is set (on creation or change), a unique, **one-time Recovery Key** is generated.
  - The user (or the admin performing the action) is prompted to immediately copy and save this key in a secure location (e.g., a password manager). **The key is never shown again.**
  - If a user forgets their password, they can use the "Forgot Password?" link on the login screen. The recovery process requires their username and their saved Recovery Key to set a new password.
- **No Backdoor**: There is no developer-side "master key" or password reset mechanism to ensure maximum data privacy and security. The user is in full control of their account recovery.

### 4.4 Administrator Security Best Practices
- **Dynamic Admin Warning**: To prevent lockouts, the system dynamically checks the number of administrator accounts. If an admin logs in and there is only one admin account in the system, a prominent warning modal appears, strongly recommending the creation of a second admin account for backup and recovery purposes. This warning will appear on every login until a second admin is created.

### 4.5 Idle Timeout
- A configurable idle logout timer is available in `General Settings`.
- It defaults to 60 minutes of inactivity, after which the user is automatically logged out for security.
- This can be changed by an administrator, or set to 0 to be disabled.

---

## 5. Backup & Restore

- A dedicated "Backup & Restore" view handles all data management.
- **Manual Backup**: Users can generate a full JSON backup of all application data.
- **Manual Restore**: Users can restore the application's state from a backup file. This is a destructive action that overwrites all existing data and requires confirmation.
- **Automatic Backups**: The system automatically creates a backup before high-risk operations like restoring data or unlocking a financial year.
- **Operation History**: A log of all recent backup and restore operations is maintained.

---

## 6. Audit Trail

- A comprehensive and immutable record of all significant CUD (Create, Update, Delete) actions.
- Access is restricted to 'Admin' users.
- **Log Fields**: `Timestamp`, `User`, `Action`, and `Details`. For updates, the details include the old and new values of changed fields.

---

## 7. UI/UX

### 7.1 Layout and Controls
- **Layout**: Auto-fitting layout with horizontal scrolling for wide tables like the `Asset Calculations` report. The first two columns of this table (`Record ID`, `Asset Particulars`) are "sticky" to maintain context during horizontal scrolling.
- **Controls**: All data tables feature text-based filtering and column sorting. An "Export to Excel" button is available on all tables and reports.
- **Data Integrity**: To prevent conflicting operations, actions such as bulk `Import`, `Export`, and `Template Download` are disabled in views like `Asset Records` and `Ledger Master` while a new, unsaved row is being added.

### 7.2 Interactive Views
- **Calculation Modals**: In the `Asset Calculations` view, cells for `Depreciation Rate`, `Depreciation for Year`, and `Disposal WDV` are interactive. Double-clicking them opens a detailed modal explaining exactly how the value was calculated.
- **Report Drill-Down**: In summary reports, double-clicking a row opens a modal listing the individual assets that constitute the summary figures.

### 7.3 Visual Cues
- **New Rows**: Newly added, unsaved rows in data-entry tables (e.g., Asset Records) are highlighted with a distinct background color and indicator to make them easily identifiable before they are saved.
- **Disposed Assets**: Rows for disposed assets are visually de-emphasized with a red strikethrough.
- **Scrapped Assets**: Rows for assets marked with `Scrap It: Yes` are visually highlighted with a distinct background color (without a strikethrough) for easy identification.
- **Extra Shift**: The 'Depreciation for Year' cell is highlighted for any asset that has extra shift depreciation applied.
- **Selection & Search**: The application provides clear visual feedback with highlighting for selected rows, selected columns, and matching search terms in all filterable views.

### 7.4 Sidebar Navigation
```
Company Masters
  └ Company Info
  └ Asset Classification
  └ Ledger Master
Assets Data
  └ Extra Shift Days
  └ Asset Records
  └ Asset Calculations
Reports
  └ Asset Group Report
  └ Schedule III
  └ Ledger-wise
  └ Method-wise
  └ Tangibility-wise
  └ Scrap & End of Life
Users
  └ User List
  └ Add User
Settings
  └ Theme
  └ Backup & Restore
  └ General Settings
Administration (Admin-only)
  └ Audit Trail
  └ Unlock Financial Year
Help & About
  └ User Manual
  └ About & Help
```

---

## 8. Import/Export

- The application supports bulk import for Assets and Ledgers from **Excel (.xlsx) or CSV files**. Downloadable templates are provided in the user-friendly **Excel format**, with all compulsory fields clearly marked with an asterisk (*) to reduce errors.
- **Asset Import Workflow**: A robust workflow with an in-modal preview, live validation, and in-place correction to ensure data integrity before importing. The modal also detects and lists new `Ledger Names` or `Asset Classifications` for user review, and provides clear warnings (e.g., when a user-provided 'Life in years' for a statutory asset is automatically corrected). Users can download a report of just the rows with errors to facilitate easy correction and re-import.