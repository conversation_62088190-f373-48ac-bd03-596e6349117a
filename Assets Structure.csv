Column,Details,Input or calculated,Data Type,Remarks
Asset Particulars,Particulars of the Asset,Input,Text,
Book Entry Date,Date of entry in books,Input,Date,Compulsory.
Put to use Date,"Date when asset was put to use. If blank, defaults to Book Entry Date.",Input,Date,
Basic Amount,Basic cost of the asset,Input,Integer,
"Duties & Taxes","Taxes to be included, if any",Input,Integer,
Gross Amount,Total of basic & taxes,Input,Integer,
Vendor/ Source,Name of Vendor,Input,Text,
Invoice/ Bill No.,Invoice of bill number,Input,Text,
Model/ Make,Model or Make of asset,Input,Text,
Location,Location where the asset is located,Input,Text,
Asset ID/ Serial No,Unique identification to be printed on asset,Input,Text,
Asset Barcode/QR Code,,Input,,
Remarks,Any additional information about the asset,Input,Text,
Ledger Name in Books,Ledger in Books where the asset purchase is recorded,Input,Text,
Asset Group as per Co. Act (SCH II),Asset group as per Asset group,Input,Text,
Nature of Asset as per Co. Act  (SCH II),Asset Sub-group,Input,Text,
Schedule III Classification,Schedule III Classification,Input,Text,
Salvage %age,Salvage percentage of Gross Amount. Default is 5%,Input,,
Salvage Amount,Gross amount * Salvage %age,Calculated,Integer,
WDV of Adoption Date,"WDV of asset when this app was adopted. Compulsory if 'Put to Use Date' is before company adoption date, otherwise disabled.",Input,Integer,
Assets under leasehold,Whether asset is under leasehold,Input,Boolean (Yes/No),
Depreciation method,WDV or SLM. Default WDV. For Leasehold default SLM,Input,Boolean (WDV/SLM),
"Depreciation Rate","For WDV: 1 - ( (Salvage / Gross) ^ (1 / Life) ) | For SLM: 1 / Life in Years",Calculated,Percent,
Depreciable Amount,Amount which needs to be depreciated : Gross - Salvage Amt,Calculated,Integer,
End of Life Date,Date upto which deperciation can be calculated,Calculated,Date,
Life in years,Default number is from linked asset sub-group. User input cannot exceed this value,Input,Integer,
Disposal WDV,WDV on the date of disposal,Calculated,Integer,
Lease Period,If leased total lease years,Input,Integer,
2 Shift Days-Year[*],Seperate column for each year. User will input days,Input,Integer,These following columns will be added for each year.
3 Shift Days-Year[*],Seperate column for each year. User will input days,Input,Integer,These following columns will be added for each year.
Use Days-Year[*],"Seperate column for each year. Calculated using total days in the year, put to use date & disposal date",Calculated,Integer,These following columns will be added for each year.
Depreciation-Year[*],Depreciation for the year calculated,Calculated,Integer,These following columns will be added for each year.
WDV-Year[*],WDV of the asset after deprecaition for the year,Calculated,Integer,These following columns will be added for each year.
Add any other columns as per the app requirements,,,,