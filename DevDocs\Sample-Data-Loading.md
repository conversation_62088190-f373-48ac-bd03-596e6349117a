# Sample Data Loading Documentation

## Overview
Comprehensive sample data has been loaded into all companies to enable thorough testing of the FAR Sighted application features.

## Data Loading Script
- **Location**: `backend/scripts/load-sample-data.js`
- **Purpose**: Populate all companies with realistic test data for various testing scenarios

## Sample Data Details

### Asset Types Included
1. **Buildings**
   - Corporate Office Building (Mumbai) - ₹5.5 Cr
   - Factory Building (Pune) - ₹3.3 Cr

2. **Plant and Machinery**
   - CNC Machining Center (Haas VF-2) - ₹29.5 L
   - Continuous Process Plant (Chemical Reactor) - ₹1.77 Cr

3. **Computer Equipment**
   - Dell Workstation (Precision 7000 Series) - ₹1.77 L
   - Old Desktop Computer (HP EliteDesk) - ₹53,100 (Disposed)

4. **Vehicles**
   - Toyota Innova Crysta - ₹21.24 L

5. **Furniture**
   - Executive Office Furniture Set - ₹2.95 L

### Testing Scenarios Covered

#### 1. Adoption Date Testing
- **Regular Companies**: Adoption date 01/04/2024
- **Test Companies**: Adoption date 01/04/2022 (for carry-forward testing)

#### 2. Depreciation Methods
- **WDV (Written Down Value)**: Most assets
- **SLM (Straight Line Method)**: Available for testing

#### 3. Asset Categories
- **Statutory Assets**: Using predefined depreciation rates
- **Non-Statutory Assets**: Custom depreciation rates

#### 4. Asset Lifecycle
- **Active Assets**: Currently in use
- **Disposed Assets**: One computer disposed on 31/03/2023 for ₹5,000

#### 5. Historical Assets
- Assets with put-to-use dates before adoption date
- WDV of adoption date values for carry-forward calculations

## Companies Populated
1. **Green Energy Solutions Pvt Ltd**
2. **Maharashtra Manufacturing Ltd**
3. **Tech Innovations Pvt Ltd**
4. **Test Company Final Ltd** (2022 adoption date)
5. **Test Company Perfect Ltd** (2022 adoption date)

## Ledger Names Added
- Buildings
- Factory Buildings
- Plant and Machinery
- Process Equipment
- Computer Equipment
- Motor Vehicles
- Furniture and Fixtures
- Office Equipment

## Key Features for Testing

### 1. First Year Depreciation
- Assets with adoption date WDV for proper first-year calculations
- Mix of assets acquired before and after adoption date

### 2. Multi-Year Calculations
- Test companies with 2022 adoption date allow testing 3+ years of depreciation
- Carry-forward scenarios from previous financial years

### 3. Asset Groups and Classifications
- Complete Schedule II classifications
- Various asset groups and sub-groups
- Proper Schedule III classifications

### 4. Disposal Testing
- One disposed asset (HP Desktop) for testing disposal functionality
- Disposal date: 31/03/2023
- Disposal amount: ₹5,000

## Usage Instructions

### Running the Script
```bash
cd backend
node scripts/load-sample-data.js
```

### Verification
1. Login to any company
2. Navigate to Asset Data > Asset Records
3. Verify 8 sample assets are loaded
4. Check various asset types and values
5. Test depreciation calculations in reports

## Database Schema Compliance
- All data follows the company-specific database schema
- Proper data types and constraints respected
- Boolean fields use 'Yes'/'No' as per schema requirements

## Testing Recommendations

### 1. Depreciation Calculations
- Test first-year depreciation for historical assets
- Verify WDV calculations across multiple years
- Check extra shift depreciation functionality

### 2. Reports Generation
- Generate all reports for different financial years
- Test Schedule III report with comprehensive data
- Verify asset group and tangibility reports

### 3. Asset Management
- Test asset addition, modification, and disposal
- Verify data validation and constraints
- Test import/export functionality

### 4. Multi-Company Testing
- Switch between companies and verify separate data
- Test company-specific user access
- Verify database isolation

## Data Integrity
- All assets have complete required fields
- Proper vendor and invoice information
- Realistic depreciation rates and life spans
- Valid date ranges and financial values

## Future Enhancements
- Additional asset types can be added to the script
- More complex disposal scenarios
- Leasehold assets for testing lease functionality
- Assets with extra shift depreciation

---
**Last Updated**: 2025-07-10
**Script Version**: 1.0
**Total Assets per Company**: 8
**Total Companies**: 5
