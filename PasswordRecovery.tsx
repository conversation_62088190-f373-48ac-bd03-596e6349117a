/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, FC } from 'react';
import { api } from './lib/api';
import { RecoveryKeyModal } from './RecoveryKeyModal';

interface PasswordRecoveryProps {
    onBackToLogin: () => void;
}

export const PasswordRecovery: FC<PasswordRecoveryProps> = ({ onBackToLogin }) => {
    const [step, setStep] = useState(1);
    const [username, setUsername] = useState('');
    const [recoveryKey, setRecoveryKey] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    
    const [newKeyInfo, setNewKeyInfo] = useState<{ isOpen: boolean; key: string | null }>({ isOpen: false, key: null });

    const handleUsernameSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);
        try {
            // We don't actually need to check if user exists on the backend,
            // as the final recovery step will fail anyway. This simplifies the API.
            // We just move to the next step on the frontend.
            setStep(2);
        } finally {
            setIsLoading(false);
        }
    };
    
    const handleRecoverySubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');
        if (newPassword !== confirmPassword) {
            setError('New passwords do not match.');
            return;
        }
        if (!newPassword) {
            setError('New password cannot be empty.');
            return;
        }
        
        setIsLoading(true);
        try {
            const result = await api.recoverUserPassword(username, recoveryKey, newPassword);
            setNewKeyInfo({ isOpen: true, key: result.recoveryKey });
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Recovery failed. Please check your details.');
        } finally {
            setIsLoading(false);
        }
    };

    if (newKeyInfo.isOpen && newKeyInfo.key) {
        return (
            <RecoveryKeyModal
                isOpen={true}
                recoveryKey={newKeyInfo.key}
                onConfirm={onBackToLogin}
                isRecoveryFlow={true}
                usernameFor={username}
            />
        );
    }

    return (
        <div className="login-container">
            <div className="login-box">
                <h1 className="login-title">Account Recovery</h1>

                {step === 1 && (
                    <form onSubmit={handleUsernameSubmit} className="login-form">
                        <p className="login-subtitle">Enter your username to begin.</p>
                        <div className="form-group">
                            <label htmlFor="username">Username<span className="required-asterisk">*</span></label>
                            <input
                                type="text"
                                id="username"
                                value={username}
                                onChange={(e) => setUsername(e.target.value)}
                                required
                                autoFocus
                            />
                        </div>
                        {error && <p className="login-error">{error}</p>}
                        <button type="submit" className="btn btn-primary" style={{ width: '100%', marginTop: '1rem' }} disabled={isLoading}>
                            {isLoading ? 'Checking...' : 'Next'}
                        </button>
                    </form>
                )}

                {step === 2 && (
                    <form onSubmit={handleRecoverySubmit} className="login-form">
                         <p className="login-subtitle">Enter your Recovery Key and a new password.</p>
                        <div className="form-group">
                            <label htmlFor="recoveryKey">Recovery Key<span className="required-asterisk">*</span></label>
                            <input
                                type="text"
                                id="recoveryKey"
                                value={recoveryKey}
                                onChange={(e) => setRecoveryKey(e.target.value)}
                                required
                                autoFocus
                            />
                        </div>
                        <div className="form-group">
                            <label htmlFor="newPassword">New Password<span className="required-asterisk">*</span></label>
                            <input
                                type="password"
                                id="newPassword"
                                value={newPassword}
                                onChange={(e) => setNewPassword(e.target.value)}
                                required
                            />
                        </div>
                        <div className="form-group">
                            <label htmlFor="confirmPassword">Confirm New Password<span className="required-asterisk">*</span></label>
                            <input
                                type="password"
                                id="confirmPassword"
                                value={confirmPassword}
                                onChange={(e) => setConfirmPassword(e.target.value)}
                                required
                            />
                        </div>
                        {error && <p className="login-error">{error}</p>}
                        <button type="submit" className="btn btn-primary" style={{ width: '100%', marginTop: '1rem' }} disabled={isLoading}>
                            {isLoading ? 'Resetting...' : 'Reset Password'}
                        </button>
                    </form>
                )}
                 <div className="login-footer">
                    <a href="#" onClick={onBackToLogin} className="forgot-password-link">
                        Back to Login
                    </a>
                </div>
            </div>
        </div>
    );
};
