/**
 * MULTI-DATABASE SERVICE
 * Professional Advisory Services - Chartered Accountant
 * 
 * This service manages separate SQLite databases for each company
 * in their respective folders as per the original design.
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Base directory for all company databases
const BASE_DATA_DIR = path.join(__dirname, '../../data');

class MultiDatabaseService {
    constructor() {
        this.connections = new Map(); // Store active database connections
        this.masterDbPath = join(__dirname, '../database/master.db');
        this.masterDb = null;
        this.initializeMasterDatabase();
    }

    /**
     * Initialize master database for company registry and user management
     */
    async initializeMasterDatabase() {
        try {
            // Ensure master database directory exists
            await fs.mkdir(path.dirname(this.masterDbPath), { recursive: true });
            
            this.masterDb = new sqlite3.Database(this.masterDbPath, (err) => {
                if (err) {
                    console.error('❌ Error connecting to master database:', err.message);
                    throw err;
                }
                console.log('✅ Connected to master database');
            });

            // Enable foreign key constraints
            await this.masterRun('PRAGMA foreign_keys = ON;');
            
            // Create master database tables
            await this.createMasterTables();
            
        } catch (error) {
            console.error('❌ Failed to initialize master database:', error);
            throw error;
        }
    }

    /**
     * Create master database tables (company registry, users, global settings)
     */
    async createMasterTables() {
        const tables = [
            // Companies registry
            `CREATE TABLE IF NOT EXISTS companies (
                id TEXT PRIMARY KEY,
                company_name TEXT NOT NULL,
                database_path TEXT NOT NULL UNIQUE,
                data_folder_path TEXT NOT NULL,
                is_active BOOLEAN DEFAULT true,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Users (global across all companies)
            `CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL CHECK (role IN ('Admin', 'Data Entry', 'Report Viewer')),
                recovery_key_hash TEXT,
                has_saved_recovery_key BOOLEAN DEFAULT false,
                is_active BOOLEAN DEFAULT true,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // User-Company access permissions
            `CREATE TABLE IF NOT EXISTS user_company_access (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                company_id TEXT NOT NULL,
                access_level TEXT NOT NULL CHECK (access_level IN ('full', 'read_only', 'reports_only')),
                granted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                granted_by TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE,
                UNIQUE(user_id, company_id)
            )`,
            
            // Global system settings
            `CREATE TABLE IF NOT EXISTS system_settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Global audit logs
            `CREATE TABLE IF NOT EXISTS global_audit_logs (
                id TEXT PRIMARY KEY,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                user_id TEXT,
                company_id TEXT,
                username TEXT,
                action TEXT NOT NULL,
                details TEXT,
                ip_address TEXT,
                user_agent TEXT
            )`
        ];

        for (const table of tables) {
            await this.masterRun(table);
        }
        
        console.log('✅ Master database tables created/verified');
    }

    /**
     * Get company database connection
     */
    async getCompanyDatabase(companyId) {
        try {
            // Check if connection already exists
            if (this.connections.has(companyId)) {
                return this.connections.get(companyId);
            }

            // Get company info from master database
            const company = await this.masterGet(
                'SELECT * FROM companies WHERE id = ? AND is_active = true', 
                [companyId]
            );

            if (!company) {
                throw new Error(`Company ${companyId} not found or inactive`);
            }

            // Ensure company data directory exists
            await fs.mkdir(company.data_folder_path, { recursive: true });

            // Connect to company database
            const dbPath = company.database_path;
            const db = new sqlite3.Database(dbPath, (err) => {
                if (err) {
                    console.error(`❌ Error connecting to company database ${companyId}:`, err.message);
                    throw err;
                }
                console.log(`✅ Connected to database for ${company.company_name}`);
            });

            // Enable foreign key constraints
            await this.companyRun(db, 'PRAGMA foreign_keys = ON;');

            // Store connection
            this.connections.set(companyId, { db, company });

            return { db, company };
        } catch (error) {
            console.error(`❌ Failed to get database for company ${companyId}:`, error);
            throw error;
        }
    }

    /**
     * Create new company database
     */
    async createCompanyDatabase(companyInfo) {
        try {
            const companyId = `c${Date.now()}`;
            const companyFolderName = companyInfo.companyName.replace(/[^a-zA-Z0-9]/g, '_');
            const dataFolderPath = path.join(BASE_DATA_DIR, companyFolderName);
            const databasePath = path.join(dataFolderPath, 'company.db');

            // Ensure data directory exists
            await fs.mkdir(dataFolderPath, { recursive: true });

            // Create company record in master database
            await this.masterRun(
                `INSERT INTO companies (id, company_name, database_path, data_folder_path)
                 VALUES (?, ?, ?, ?)`,
                [companyId, companyInfo.companyName, databasePath, dataFolderPath]
            );

            // Create and initialize company database
            const db = new sqlite3.Database(databasePath, (err) => {
                if (err) {
                    console.error('❌ Error creating company database:', err.message);
                    throw err;
                }
                console.log(`✅ Created database for ${companyInfo.companyName}`);
            });

            // Enable foreign key constraints
            await this.companyRun(db, 'PRAGMA foreign_keys = ON;');

            // Create company database tables
            await this.createCompanyTables(db);

            // Insert company information
            await this.companyRun(db,
                `INSERT INTO company_info (
                    id, company_name, pan, cin, date_of_incorporation,
                    financial_year_start, financial_year_end, first_date_of_adoption,
                    address_line1, address_line2, city, pin,
                    email, mobile, contact_person, license_valid_upto
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    companyId, companyInfo.companyName, companyInfo.pan, companyInfo.cin,
                    companyInfo.dateOfIncorporation, companyInfo.financialYearStart,
                    companyInfo.financialYearEnd, companyInfo.firstDateOfAdoption,
                    companyInfo.addressLine1, companyInfo.addressLine2, companyInfo.city,
                    companyInfo.pin, companyInfo.email, companyInfo.mobile,
                    companyInfo.contactPerson, companyInfo.licenseValidUpto
                ]
            );

            // Add default financial year
            const startYear = new Date(companyInfo.financialYearStart).getFullYear();
            const endYear = new Date(companyInfo.financialYearEnd).getFullYear();
            const yearRange = `${startYear}-${endYear}`;

            await this.companyRun(db,
                'INSERT INTO financial_years (year_range, is_locked) VALUES (?, ?)',
                [yearRange, false]
            );

            // Store connection
            const company = {
                id: companyId,
                company_name: companyInfo.companyName,
                database_path: databasePath,
                data_folder_path: dataFolderPath
            };
            this.connections.set(companyId, { db, company });

            console.log(`✅ Company ${companyInfo.companyName} created successfully`);
            return { companyId, company };

        } catch (error) {
            console.error('❌ Failed to create company database:', error);
            throw error;
        }
    }

    /**
     * Create company database tables (no company_id fields needed)
     */
    async createCompanyTables(db) {
        const tables = [
            // Company information (single record)
            `CREATE TABLE IF NOT EXISTS company_info (
                id TEXT PRIMARY KEY,
                company_name TEXT NOT NULL,
                pan TEXT,
                cin TEXT,
                date_of_incorporation DATE,
                financial_year_start DATE NOT NULL,
                financial_year_end DATE NOT NULL,
                first_date_of_adoption DATE NOT NULL,
                address_line1 TEXT,
                address_line2 TEXT,
                city TEXT,
                pin TEXT,
                email TEXT,
                mobile TEXT,
                contact_person TEXT,
                license_valid_upto DATE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Financial years
            `CREATE TABLE IF NOT EXISTS financial_years (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                year_range TEXT NOT NULL UNIQUE,
                is_locked BOOLEAN DEFAULT false,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Assets (no company_id needed)
            `CREATE TABLE IF NOT EXISTS assets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                record_id TEXT NOT NULL UNIQUE,
                asset_particulars TEXT NOT NULL,
                book_entry_date DATE,
                put_to_use_date DATE NOT NULL,
                basic_amount INTEGER,
                duties_taxes INTEGER,
                gross_amount INTEGER NOT NULL,
                vendor TEXT,
                invoice_no TEXT,
                model_make TEXT,
                location TEXT,
                asset_id TEXT,
                remarks TEXT,
                ledger_name_in_books TEXT,
                asset_group TEXT,
                asset_sub_group TEXT,
                schedule_iii_classification TEXT,
                disposal_date DATE,
                disposal_amount INTEGER,
                salvage_percentage DECIMAL(5,2) DEFAULT 5.0,
                wdv_of_adoption_date INTEGER,
                is_leasehold BOOLEAN DEFAULT false,
                depreciation_method TEXT CHECK (depreciation_method IN ('WDV', 'SLM')) DEFAULT 'WDV',
                life_in_years INTEGER,
                lease_period INTEGER,
                scrap_it BOOLEAN DEFAULT false,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Statutory rates
            `CREATE TABLE IF NOT EXISTS statutory_rates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                is_statutory TEXT CHECK (is_statutory IN ('Yes', 'No')) DEFAULT 'Yes',
                tangibility TEXT CHECK (tangibility IN ('Tangible', 'Intangible')) DEFAULT 'Tangible',
                asset_group TEXT NOT NULL,
                asset_sub_group TEXT NOT NULL,
                extra_shift_depreciation TEXT,
                useful_life_years TEXT NOT NULL,
                schedule_ii_classification TEXT,
                UNIQUE(asset_group, asset_sub_group)
            )`,

            // Asset yearly data
            `CREATE TABLE IF NOT EXISTS asset_yearly_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                asset_id INTEGER NOT NULL,
                year_range TEXT NOT NULL,
                opening_wdv INTEGER DEFAULT 0,
                use_days INTEGER DEFAULT 0,
                depreciation_amount INTEGER DEFAULT 0,
                closing_wdv INTEGER DEFAULT 0,
                second_shift_days INTEGER DEFAULT 0,
                third_shift_days INTEGER DEFAULT 0,
                FOREIGN KEY (asset_id) REFERENCES assets (id) ON DELETE CASCADE,
                FOREIGN KEY (year_range) REFERENCES financial_years (year_range),
                UNIQUE(asset_id, year_range)
            )`,

            // Extra ledgers
            `CREATE TABLE IF NOT EXISTS extra_ledgers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ledger_name TEXT NOT NULL UNIQUE
            )`,

            // License history
            `CREATE TABLE IF NOT EXISTS license_history (
                id TEXT PRIMARY KEY,
                license_key TEXT NOT NULL,
                valid_from DATE NOT NULL,
                valid_upto DATE NOT NULL,
                activated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Company-specific audit logs
            `CREATE TABLE IF NOT EXISTS audit_logs (
                id TEXT PRIMARY KEY,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                user_id TEXT,
                username TEXT,
                action TEXT NOT NULL,
                details TEXT,
                ip_address TEXT,
                user_agent TEXT
            )`,

            // Backup logs
            `CREATE TABLE IF NOT EXISTS backup_logs (
                id TEXT PRIMARY KEY,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                action TEXT NOT NULL CHECK (action IN ('Backup', 'Restore')),
                initiated_by TEXT,
                details TEXT,
                file_path TEXT,
                file_size INTEGER,
                status TEXT DEFAULT 'Completed'
            )`
        ];

        for (const table of tables) {
            await this.companyRun(db, table);
        }

        console.log('✅ Company database tables created/verified');
    }

    // Master database operations
    masterRun(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.masterDb.run(sql, params, function(err) {
                if (err) reject(err);
                else resolve({ id: this.lastID, changes: this.changes });
            });
        });
    }

    masterGet(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.masterDb.get(sql, params, (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
    }

    masterAll(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.masterDb.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    }

    // Company database operations
    companyRun(db, sql, params = []) {
        return new Promise((resolve, reject) => {
            db.run(sql, params, function(err) {
                if (err) reject(err);
                else resolve({ id: this.lastID, changes: this.changes });
            });
        });
    }

    companyGet(db, sql, params = []) {
        return new Promise((resolve, reject) => {
            db.get(sql, params, (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
    }

    companyAll(db, sql, params = []) {
        return new Promise((resolve, reject) => {
            db.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    }

    // Transaction support for company databases
    async beginCompanyTransaction(companyId) {
        const { db } = await this.getCompanyDatabase(companyId);
        return this.companyRun(db, 'BEGIN TRANSACTION');
    }

    async commitCompanyTransaction(companyId) {
        const { db } = await this.getCompanyDatabase(companyId);
        return this.companyRun(db, 'COMMIT');
    }

    async rollbackCompanyTransaction(companyId) {
        const { db } = await this.getCompanyDatabase(companyId);
        return this.companyRun(db, 'ROLLBACK');
    }

    // Close specific company database connection
    async closeCompanyDatabase(companyId) {
        if (this.connections.has(companyId)) {
            const { db } = this.connections.get(companyId);
            return new Promise((resolve, reject) => {
                db.close((err) => {
                    if (err) reject(err);
                    else {
                        this.connections.delete(companyId);
                        console.log(`🔌 Closed database connection for company ${companyId}`);
                        resolve();
                    }
                });
            });
        }
    }

    // Close all connections
    async closeAllConnections() {
        const promises = [];
        
        // Close company databases
        for (const companyId of this.connections.keys()) {
            promises.push(this.closeCompanyDatabase(companyId));
        }

        // Close master database
        if (this.masterDb) {
            promises.push(new Promise((resolve, reject) => {
                this.masterDb.close((err) => {
                    if (err) reject(err);
                    else {
                        console.log('🔌 Master database connection closed');
                        resolve();
                    }
                });
            }));
        }

        await Promise.all(promises);
    }

    // Get all companies
    async getAllCompanies() {
        return this.masterAll('SELECT id, company_name as name FROM companies WHERE is_active = true ORDER BY company_name');
    }

    // Get company by ID with complete data
    async getCompanyData(companyId) {
        const { db, company } = await this.getCompanyDatabase(companyId);
        
        // Get company info
        const companyInfo = await this.companyGet(db, 'SELECT * FROM company_info');
        
        // Get assets
        const assets = await this.companyAll(db, `
            SELECT 
                id as assetDbId,
                record_id as recordId,
                asset_particulars as assetParticulars,
                book_entry_date as bookEntryDate,
                put_to_use_date as putToUseDate,
                basic_amount as basicAmount,
                duties_taxes as dutiesTaxes,
                gross_amount as grossAmount,
                vendor,
                invoice_no as invoiceNo,
                model_make as modelMake,
                location,
                asset_id as assetId,
                remarks,
                ledger_name_in_books as ledgerNameInBooks,
                asset_group as assetGroup,
                asset_sub_group as assetSubGroup,
                schedule_iii_classification as scheduleIIIClassification,
                disposal_date as disposalDate,
                disposal_amount as disposalAmount,
                salvage_percentage as salvagePercentage,
                wdv_of_adoption_date as wdvOfAdoptionDate,
                is_leasehold as isLeasehold,
                depreciation_method as depreciationMethod,
                life_in_years as lifeInYears,
                lease_period as leasePeriod,
                scrap_it as scrapIt
            FROM assets ORDER BY record_id
        `);
        
        // Get financial years
        const financialYears = await this.companyAll(db, 'SELECT year_range FROM financial_years ORDER BY year_range');
        
        // Get statutory rates
        const statutoryRates = await this.companyAll(db, `
            SELECT 
                is_statutory as isStatutory,
                tangibility,
                asset_group as assetGroup,
                asset_sub_group as assetSubGroup,
                extra_shift_depreciation as extraShiftDepreciation,
                useful_life_years as usefulLifeYears,
                schedule_ii_classification as scheduleIIClassification
            FROM statutory_rates
        `);
        
        // Get extra ledgers
        const extraLedgers = await this.companyAll(db, 'SELECT ledger_name FROM extra_ledgers');
        
        // Get license history
        const licenseHistory = await this.companyAll(db, `
            SELECT 
                id,
                license_key as key,
                valid_from as validFrom,
                valid_upto as validUpto,
                activated_at as activatedAt
            FROM license_history ORDER BY activated_at DESC
        `);

        return {
            id: companyId,
            info: {
                companyName: companyInfo.company_name,
                pan: companyInfo.pan,
                cin: companyInfo.cin,
                dateOfIncorporation: companyInfo.date_of_incorporation,
                financialYearStart: companyInfo.financial_year_start,
                financialYearEnd: companyInfo.financial_year_end,
                firstDateOfAdoption: companyInfo.first_date_of_adoption,
                dataFolderPath: company.data_folder_path,
                addressLine1: companyInfo.address_line1,
                addressLine2: companyInfo.address_line2,
                city: companyInfo.city,
                pin: companyInfo.pin,
                email: companyInfo.email,
                mobile: companyInfo.mobile,
                contactPerson: companyInfo.contact_person,
                licenseValidUpto: companyInfo.license_valid_upto
            },
            assets: assets,
            financialYears: financialYears.map(fy => fy.year_range),
            statutoryRates: statutoryRates,
            extraLedgers: extraLedgers.map(el => el.ledger_name),
            licenseHistory: licenseHistory
        };
    }
}

// Export singleton instance
const multiDbService = new MultiDatabaseService();

export default multiDbService;