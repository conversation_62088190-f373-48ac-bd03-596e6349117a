/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { api } from './lib/api';
import type { Asset } from './lib/db-server';
import { DataViewProps } from './lib/types';
import { sortData, exportToExcel, formatIndianNumber, exportTemplateToExcel } from './lib/utils';
import { PlusIcon, UploadIcon, FileIcon, DownloadIcon, SaveIcon, XIcon, TrashIcon } from './Icons';
import { Highlight } from './pages/PlaceholderViews';

interface LedgerRow {
    originalName: string; // For new rows, this will be a temporary unique ID
    currentName: string;
    assetCount: number;
    isNew: boolean;
}

export function LedgerMaster({ companyId, companyName, year, showAlert, showConfirmation, loggedInUser }: DataViewProps) {
    const [originalLedgers, setOriginalLedgers] = useState<LedgerRow[]>([]);
    const [editedLedgers, setEditedLedgers] = useState<LedgerRow[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isDirty, setIsDirty] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [sortConfig, setSortConfig] = useState<{ key: keyof LedgerRow; direction: 'asc' | 'desc' } | null>({ key: 'currentName', direction: 'asc' });
    const [selectedRowKey, setSelectedRowKey] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);
    const [filterText, setFilterText] = useState('');
    const fileInputRef = useRef<HTMLInputElement>(null);

    const hasNewRow = useMemo(() => editedLedgers.some(l => l.isNew), [editedLedgers]);

    const fetchData = useCallback(async () => {
        if (!companyId) {
            setLoading(false);
            setEditedLedgers([]);
            setOriginalLedgers([]);
            return;
        }
        setLoading(true);
        setError(null);
        setIsDirty(false);

        try {
            const companyData = await api.getCompanyData(companyId);
            if (!companyData) throw new Error("Company data not found");
            const { assets, extraLedgers } = companyData;

            const ledgerCounts = new Map<string, number>();
            assets.forEach(asset => {
                if (asset.ledgerNameInBooks) {
                    ledgerCounts.set(asset.ledgerNameInBooks, (ledgerCounts.get(asset.ledgerNameInBooks) || 0) + 1);
                }
            });

            extraLedgers?.forEach(ledger => {
                if (!ledgerCounts.has(ledger)) {
                    ledgerCounts.set(ledger, 0);
                }
            });

            const ledgerData: LedgerRow[] = Array.from(ledgerCounts.entries()).map(([name, count]) => ({
                originalName: name,
                currentName: name,
                assetCount: count,
                isNew: false,
            }));

            setOriginalLedgers(JSON.parse(JSON.stringify(ledgerData)));
            setEditedLedgers(ledgerData);

        } catch (err) {
            setError('Failed to fetch ledger data.');
            console.error(err);
        } finally {
            setLoading(false);
        }
    }, [companyId]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const sortedLedgers = useMemo(() => {
        return sortData(editedLedgers, sortConfig);
    }, [editedLedgers, sortConfig]);

    const filteredLedgers = useMemo(() => {
        if (!filterText) return sortedLedgers;
        const searchTerm = filterText.trim().toLowerCase();
        return sortedLedgers.filter(ledger =>
            ledger.currentName.toLowerCase().includes(searchTerm)
        );
    }, [sortedLedgers, filterText]);

    const requestSort = (key: keyof LedgerRow) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') direction = 'desc';
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: keyof LedgerRow) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };
    
    const getColumnClass = (key: keyof LedgerRow | 'actions') => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if (key === 'assetCount') classes.push('text-right');
        return classes.join(' ');
    };
    
    const getHeaderClass = (key: keyof LedgerRow | 'actions') => {
        const classes = [];
        if(key !== 'actions') classes.push('sortable');
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        if (key === 'assetCount') classes.push('text-right');
        return classes.join(' ');
    };
    
    const handleAddLedger = () => {
        if (hasNewRow) {
            showAlert("Info", "Please fill in and save the current new ledger before adding another.", 'info');
            return;
        }
        const newLedger: LedgerRow = {
            originalName: `new_${Date.now()}`,
            currentName: '',
            assetCount: 0,
            isNew: true,
        };
        setEditedLedgers(prev => [newLedger, ...prev]);
        setIsDirty(true);
    };
    
    const handleNameChange = (originalName: string, newName: string) => {
        setEditedLedgers(prev => prev.map(l =>
            l.originalName === originalName ? { ...l, currentName: newName } : l
        ));
        setIsDirty(true);
    };
    
    const handleDelete = (originalName: string) => {
        const ledgerToDelete = editedLedgers.find(l => l.originalName === originalName);
        if (!ledgerToDelete) return;
        
        if (ledgerToDelete.isNew) {
            setEditedLedgers(prev => prev.filter(l => l.originalName !== originalName));
            return;
        }

        showConfirmation(
            'Confirm Deletion',
            `Are you sure you want to delete the ledger "${ledgerToDelete.currentName}"? This cannot be undone.`,
            () => {
                setEditedLedgers(prev => prev.filter(l => l.originalName !== originalName));
                setIsDirty(true);
            }
        );
    };

    const handleSaveChanges = async () => {
        if (!companyId) return;

        // --- VALIDATION ---
        const seenNames = new Set<string>();
        for (const ledger of editedLedgers) {
            const trimmedName = ledger.currentName.trim();
            if (!trimmedName) {
                showAlert("Validation Error", "Ledger names cannot be empty. Please fill in or remove the blank ledger.", "error");
                return;
            }
            const lowerCaseName = trimmedName.toLowerCase();
            if (seenNames.has(lowerCaseName)) {
                showAlert("Validation Error", `Duplicate ledger name found: "${trimmedName}". Ledger names must be unique.`, "error");
                return;
            }
            seenNames.add(lowerCaseName);
        }
        // --- END VALIDATION ---

        setIsSaving(true);
        try {
            if (loggedInUser) {
                const added = editedLedgers.filter(l => l.isNew);
                const deleted = originalLedgers.filter(l => !editedLedgers.some(el => el.originalName === l.originalName));
                const renamed = editedLedgers.filter(l => !l.isNew && l.originalName !== l.currentName);

                const details: string[] = [];
                if (added.length > 0) details.push(`Added ${added.length} ledger(s): ${added.map(l => `'${l.currentName}'`).join(', ')}.`);
                if (renamed.length > 0) details.push(`Renamed ${renamed.length} ledger(s): ${renamed.map(l => `'${l.originalName}' to '${l.currentName}'`).join(', ')}.`);
                if (deleted.length > 0) details.push(`Deleted ${deleted.length} ledger(s): ${deleted.map(l => `'${l.originalName}'`).join(', ')}.`);

                if (details.length > 0) {
                    await api.addAuditLog(companyId, {
                        userId: loggedInUser.id,
                        username: loggedInUser.username,
                        action: 'UPDATE_LEDGER_MASTER',
                        details: details.join(' \n')
                    });
                }
            }

            const renames = new Map<string, string>();
            editedLedgers.forEach(l => {
                if (l.originalName !== l.currentName && !l.isNew) {
                    renames.set(l.originalName, l.currentName);
                }
            });

            if (renames.size > 0) {
                const assets = await api.getAssets(companyId);
                const updatedAssets = assets.map(asset => {
                    if (asset.ledgerNameInBooks && renames.has(asset.ledgerNameInBooks)) {
                        return { ...asset, ledgerNameInBooks: renames.get(asset.ledgerNameInBooks)! };
                    }
                    return asset;
                });
                await api.updateAssets(companyId, updatedAssets);
            }

            const currentAssets = await api.getAssets(companyId); // refetch to be safe
            const usedLedgers = new Set(currentAssets.map(a => a.ledgerNameInBooks).filter(Boolean));
            const newExtraLedgers = editedLedgers
                .map(l => l.currentName)
                .filter(name => !usedLedgers.has(name));

            await api.updateExtraLedgers(companyId, newExtraLedgers);
            
            showAlert("Success", 'Ledger master saved successfully!', 'success');
            await fetchData();

        } catch (error) {
            console.error(error);
            showAlert("Error", `Failed to save changes: ${error instanceof Error ? error.message : "Unknown error"}`, 'error');
        } finally {
            setIsSaving(false);
        }
    };

    const handleCancelChanges = () => {
        showConfirmation(
            'Discard Changes',
            'Are you sure you want to discard all unsaved changes?',
            () => {
                setEditedLedgers(JSON.parse(JSON.stringify(originalLedgers)));
                setIsDirty(false);
            }
        );
    };
    
    const handleExport = () => {
        if (!companyId || !companyName || !year) return;
        const dataToExport = filteredLedgers.map(l => ({
            "Ledger Name": l.currentName,
            "Asset Count": l.assetCount,
        }));
        if (!exportToExcel({ data: dataToExport, companyName, year, reportName: 'Ledger_Master' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };
    
    const handleDownloadTemplate = useCallback(() => {
        if (!companyName) {
            showAlert("Error", "Company name is not available for naming the template file.", 'error');
            return;
        }
        
        const headers = ["Ledger Name*"];

        exportTemplateToExcel({
            headers: headers,
            companyName: companyName,
            reportName: 'Ledger_Import_Template',
            sheetName: 'Ledger Import Template'
        });
    }, [companyName, showAlert]);
    
    const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file || !companyId) return;
        
        const text = await file.text();
        const lines = text.trim().split('\n').slice(1); // Skip header
        const newLedgers = lines.map(line => line.trim().replace(/,$/, '')).filter(Boolean);
        
        setEditedLedgers(prev => {
            const existingNames = new Set(prev.map(l => l.currentName.toLowerCase()));
            const additions = newLedgers
                .filter(name => !existingNames.has(name.toLowerCase()))
                .map(name => ({ originalName: name, currentName: name, assetCount: 0, isNew: true }));
            return [...additions, ...prev];
        });
        setIsDirty(true);
        showAlert("Import Success", `${newLedgers.length} ledgers parsed from file. Click "Save Changes" to finalize.`, 'info');
        event.target.value = ''; // Reset file input
    };
    
    if (!companyId) return null;
    if (loading) return <div className="loading-indicator">Loading Ledger Master...</div>;
    if (error) return <div className="error-message">{error}</div>;

    return (
        <>
            <div className="view-header">
                <h2>Ledger Master</h2>
                 <div className="actions">
                    <button className="btn btn-primary" onClick={handleAddLedger} disabled={isSaving || loading}><PlusIcon/> Add Ledger</button>
                    <input type="file" ref={fileInputRef} onChange={handleFileImport} style={{ display: 'none' }} accept=".csv" />
                    <button className="btn btn-info" onClick={() => fileInputRef.current?.click()} disabled={isSaving || hasNewRow || loading}><UploadIcon/> Import</button>
                    <button className="btn btn-excel" onClick={handleDownloadTemplate} disabled={isSaving || hasNewRow || loading}><FileIcon/> Template</button>
                    <button className="btn btn-excel" onClick={handleExport} disabled={isSaving || hasNewRow || loading}><DownloadIcon/> Export</button>
                    {isDirty && <button className="btn btn-warning" onClick={handleCancelChanges} disabled={isSaving}><XIcon/> Cancel</button>}
                    {isDirty && <button className="btn btn-primary" onClick={handleSaveChanges} disabled={isSaving}><SaveIcon/> {isSaving ? 'Saving...' : 'Save Changes'}</button>}
                </div>
            </div>
             <div className="filter-container">
                <div className="filter-input-wrapper">
                    <input
                        type="text"
                        placeholder="Filter ledgers..."
                        className="filter-input"
                        value={filterText}
                        onChange={e => setFilterText(e.target.value)}
                        disabled={isSaving}
                        aria-label="Filter ledger names"
                    />
                     <button
                        className={`filter-clear-btn ${!filterText ? 'hidden' : ''}`}
                        onClick={() => setFilterText('')}
                        aria-label="Clear filter"
                        title="Clear filter"
                        disabled={isSaving}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </div>
                 <div className="filter-info">
                    <span className="filter-info-icon">ⓘ</span>
                    <div className="filter-info-tooltip">
                        <strong>Filtering on:</strong>
                        <ul>
                            <li>Ledger Name</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div className="table-container">
                <table>
                    <thead>
                        <tr>
                            <th className={getHeaderClass('currentName')} onClick={() => requestSort('currentName')}>Ledger Name{getSortIndicator('currentName')}</th>
                            <th className={getHeaderClass('assetCount')} onClick={() => requestSort('assetCount')}>Asset Count{getSortIndicator('assetCount')}</th>
                            <th className="text-right">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredLedgers.map(ledger => {
                             const rowKey = ledger.originalName;
                             return (
                                <tr key={rowKey} onClick={() => setSelectedRowKey(rowKey)} className={`${rowKey === selectedRowKey ? 'selected-row' : ''} ${ledger.isNew ? 'new-row' : ''}`}>
                                    <td className={getColumnClass('currentName')}>
                                        <input
                                            type="text"
                                            className="table-input"
                                            value={ledger.currentName}
                                            onChange={(e) => handleNameChange(ledger.originalName, e.target.value)}
                                            disabled={isSaving}
                                        />
                                    </td>
                                    <td className={`${getColumnClass('assetCount')} text-right`}>{formatIndianNumber(ledger.assetCount)}</td>
                                    <td className="text-right">
                                        <div className="action-buttons-cell" style={{ justifyContent: 'flex-end' }}>
                                            <button
                                                className="btn-icon btn-delete"
                                                title="Delete ledger"
                                                onClick={() => handleDelete(ledger.originalName)}
                                                disabled={ledger.assetCount > 0 || isSaving}
                                            >
                                               <TrashIcon />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            );
                        })}
                    </tbody>
                </table>
            </div>
        </>
    );
}
