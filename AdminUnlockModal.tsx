
/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, FC } from 'react';
import { UnlockIcon, XIcon, AlertTriangleIcon } from './Icons';

interface AdminUnlockModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: (password: string) => void;
    isVerifying: boolean;
    error: string | null;
}

export const AdminUnlockModal: FC<AdminUnlockModalProps> = ({ isOpen, onClose, onConfirm, isVerifying, error }) => {
    const [password, setPassword] = useState('');

    if (!isOpen) return null;

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onConfirm(password);
    };

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" style={{ maxWidth: '500px' }} onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <div className="modal-header-content">
                        <AlertTriangleIcon className="icon-warning" />
                        <h2>Administrator Confirmation</h2>
                    </div>
                    <button className="modal-close-btn" onClick={onClose}>&times;</button>
                </div>
                <form onSubmit={handleSubmit}>
                    <p style={{ padding: '1.5rem 0', fontSize: '1.1rem', lineHeight: '1.5' }}>
                        You are about to unlock a finalized financial year. This will allow editing of asset records for this period.
                        <br /><br />
                        An automatic backup will be created before proceeding. This is a high-risk operation.
                        <br /><br />
                        Please enter your password to continue.
                    </p>
                    <div className="form-group full-width">
                        <label htmlFor="admin-password">Password</label>
                        <input
                            id="admin-password"
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                            autoFocus
                        />
                    </div>
                    {error && <p style={{ color: 'var(--accent-danger)', marginTop: '1rem' }}>{error}</p>}
                    <div className="modal-actions">
                        <button type="button" className="btn btn-secondary" onClick={onClose} disabled={isVerifying}>
                            <XIcon /> Cancel
                        </button>
                        <button type="submit" className="btn btn-warning" disabled={isVerifying}>
                            <UnlockIcon /> {isVerifying ? 'Verifying...' : 'Unlock Year'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};
