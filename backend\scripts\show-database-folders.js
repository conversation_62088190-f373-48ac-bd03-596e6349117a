#!/usr/bin/env node

/**
 * Show Database Folder Structure
 * Displays current vs intended database folder locations for each company
 */

import sqlite3 from 'sqlite3';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database path
const dbPath = path.join(__dirname, '../database/far_sighted.db');

// Helper function to get data
const getData = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath);
        db.all(sql, params, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
        db.close();
    });
};

// Check if path exists
const pathExists = (filePath) => {
    try {
        return fs.existsSync(filePath);
    } catch (error) {
        return false;
    }
};

// Get folder size
const getFolderSize = (folderPath) => {
    try {
        if (!fs.existsSync(folderPath)) return 0;
        
        let totalSize = 0;
        const files = fs.readdirSync(folderPath);
        
        for (const file of files) {
            const filePath = path.join(folderPath, file);
            const stats = fs.statSync(filePath);
            
            if (stats.isDirectory()) {
                totalSize += getFolderSize(filePath);
            } else {
                totalSize += stats.size;
            }
        }
        
        return totalSize;
    } catch (error) {
        return 0;
    }
};

// Format bytes
const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

async function showDatabaseFolders() {
    console.log('📁 DATABASE FOLDER STRUCTURE ANALYSIS');
    console.log('=====================================\n');

    try {
        // Get all companies
        const companies = await getData(`
            SELECT 
                id,
                company_name,
                data_folder_path,
                created_at
            FROM companies 
            ORDER BY company_name
        `);

        console.log(`📊 Found ${companies.length} companies\n`);

        // Current single database info
        const currentDbPath = path.resolve(dbPath);
        const currentDbExists = pathExists(currentDbPath);
        const currentDbSize = currentDbExists ? fs.statSync(currentDbPath).size : 0;

        console.log('🗄️  CURRENT DATABASE STRUCTURE (Single Database)');
        console.log('================================================');
        console.log(`📍 Location: ${currentDbPath}`);
        console.log(`📊 Status: ${currentDbExists ? '✅ EXISTS' : '❌ NOT FOUND'}`);
        console.log(`📏 Size: ${formatBytes(currentDbSize)}`);
        console.log(`🏢 Contains: All ${companies.length} companies in single database\n`);

        console.log('🎯 INTENDED DATABASE STRUCTURE (Multi-Database)');
        console.log('===============================================');

        // Show intended structure for each company
        companies.forEach((company, index) => {
            const companyCode = company.id;
            const sanitizedName = company.company_name.replace(/[^A-Za-z0-9]/g, '_');
            
            // Intended paths
            const intendedDbFolder = path.join(__dirname, '../database/companies', companyCode);
            const intendedDbFile = path.join(intendedDbFolder, `${sanitizedName}.db`);
            const intendedDataFolder = company.data_folder_path || `C:\\FAR_Data\\${sanitizedName}`;
            
            console.log(`${index + 1}. 🏢 ${company.company_name} (${companyCode})`);
            console.log(`   📁 Database Folder: ${intendedDbFolder}`);
            console.log(`   📄 Database File: ${intendedDbFile}`);
            console.log(`   💾 Data Folder: ${intendedDataFolder}`);
            
            // Check if intended paths exist
            const dbFolderExists = pathExists(intendedDbFolder);
            const dbFileExists = pathExists(intendedDbFile);
            const dataFolderExists = pathExists(intendedDataFolder);
            
            console.log(`   📊 Status:`);
            console.log(`      Database Folder: ${dbFolderExists ? '✅ EXISTS' : '❌ NOT CREATED'}`);
            console.log(`      Database File: ${dbFileExists ? '✅ EXISTS' : '❌ NOT CREATED'}`);
            console.log(`      Data Folder: ${dataFolderExists ? '✅ EXISTS' : '❌ NOT CREATED'}`);
            
            if (dbFileExists) {
                const dbSize = fs.statSync(intendedDbFile).size;
                console.log(`      Database Size: ${formatBytes(dbSize)}`);
            }
            
            if (dataFolderExists) {
                const folderSize = getFolderSize(intendedDataFolder);
                console.log(`      Data Folder Size: ${formatBytes(folderSize)}`);
            }
            
            console.log('');
        });

        console.log('📋 SUMMARY');
        console.log('==========');
        console.log(`Current System: Single database with ${companies.length} companies`);
        console.log(`Intended System: ${companies.length} separate databases`);
        console.log(`Migration Status: ${companies.every(c => pathExists(path.join(__dirname, '../database/companies', c.id))) ? '✅ COMPLETE' : '❌ PENDING'}`);

        console.log('\n💡 RECOMMENDATIONS');
        console.log('==================');
        console.log('1. Run database migration to create company-specific databases');
        console.log('2. Create data folders for each company');
        console.log('3. Update Company Info display to show actual database locations');
        console.log('4. Implement backup procedures for each company database');

        console.log('\n🔧 MIGRATION COMMANDS');
        console.log('=====================');
        console.log('npm run migrate          # Run database migration');
        console.log('npm run status           # Check migration status');
        console.log('npm run system-info      # View system information');

    } catch (error) {
        console.error('❌ Error analyzing database folders:', error);
    }
}

// Run the analysis
showDatabaseFolders().then(() => {
    console.log('\n🏁 Analysis completed');
}).catch(error => {
    console.error('❌ Analysis failed:', error);
});
