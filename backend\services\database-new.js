import DatabaseManager from './database-manager/DatabaseManager.js';
import DatabaseMigrator from './database-manager/DatabaseMigrator.js';

/**
 * Updated Database Service that uses DatabaseManager for multi-company databases
 * Maintains compatibility with existing API while supporting company-specific databases
 */
class DatabaseService {
    constructor() {
        this.databaseManager = new DatabaseManager();
        this.migrator = new DatabaseMigrator();
        this.currentCompanyId = null;
        this.currentCompanyDb = null;
        
        this.initialize();
    }

    async initialize() {
        // Check if migration is needed
        const migrationStatus = await this.migrator.getMigrationStatus();
        if (migrationStatus.needsMigration) {
            console.log('🔄 Migration required. Please run migration first.');
            console.log('💡 Call dbService.runMigration() to migrate data.');
        }
    }

    /**
     * Run database migration from old structure to new structure
     */
    async runMigration() {
        return await this.migrator.migrate();
    }

    /**
     * Get migration status
     */
    async getMigrationStatus() {
        return await this.migrator.getMigrationStatus();
    }

    /**
     * Set current company context for database operations
     */
    async setCompanyContext(companyId) {
        if (this.currentCompanyId === companyId && this.currentCompanyDb) {
            return; // Already set to this company
        }

        this.currentCompanyId = companyId;
        this.currentCompanyDb = await this.databaseManager.getCompanyDatabase(companyId);
    }

    /**
     * Ensure company context is set
     */
    ensureCompanyContext() {
        if (!this.currentCompanyDb) {
            throw new Error('No company context set. Call setCompanyContext(companyId) first.');
        }
    }

    // === MASTER DATABASE OPERATIONS ===

    /**
     * Get all companies
     */
    async getAllCompanies() {
        return await this.databaseManager.getAllCompanies();
    }

    /**
     * Get company info
     */
    async getCompanyInfo(companyId) {
        return await this.databaseManager.getCompanyInfo(companyId);
    }

    /**
     * Create new company
     */
    async createCompany(companyData) {
        return await this.databaseManager.createCompany(companyData);
    }

    /**
     * Update company info
     */
    async updateCompanyInfo(companyId, companyData) {
        await this.databaseManager.updateCompanyInfo(companyId, companyData);
    }

    /**
     * Get users (from master database)
     */
    async getUsers() {
        return await this.databaseManager.getUsers();
    }

    /**
     * Create user (in master database)
     */
    async createUser(userData) {
        return await this.databaseManager.createUser(userData);
    }

    /**
     * Update user (in master database)
     */
    async updateUser(userId, userData) {
        await this.databaseManager.updateUser(userId, userData);
    }

    /**
     * Authenticate user (from master database)
     */
    async authenticateUser(username, password) {
        return await this.databaseManager.authenticateUser(username, password);
    }

    // === COMPANY-SPECIFIC DATABASE OPERATIONS ===
    // These operations work on the currently set company database

    /**
     * Run query on current company database
     */
    async run(sql, params = []) {
        this.ensureCompanyContext();
        return await this.currentCompanyDb.run(sql, params);
    }

    /**
     * Get single row from current company database
     */
    async get(sql, params = []) {
        this.ensureCompanyContext();
        return await this.currentCompanyDb.get(sql, params);
    }

    /**
     * Get multiple rows from current company database
     */
    async all(sql, params = []) {
        this.ensureCompanyContext();
        return await this.currentCompanyDb.all(sql, params);
    }

    /**
     * Begin transaction on current company database
     */
    async beginTransaction() {
        this.ensureCompanyContext();
        return await this.currentCompanyDb.beginTransaction();
    }

    /**
     * Commit transaction on current company database
     */
    async commit() {
        this.ensureCompanyContext();
        return await this.currentCompanyDb.commit();
    }

    /**
     * Rollback transaction on current company database
     */
    async rollback() {
        this.ensureCompanyContext();
        return await this.currentCompanyDb.rollback();
    }

    /**
     * Add audit log to current company database
     */
    async addAuditLog(entry) {
        this.ensureCompanyContext();
        return await this.currentCompanyDb.addAuditLog(entry);
    }

    /**
     * Get audit logs from current company database
     */
    async getAuditLogs(limit = 100) {
        this.ensureCompanyContext();
        return await this.currentCompanyDb.getAuditLogs(limit);
    }

    /**
     * Create backup of current company
     */
    async createCompanyBackup() {
        this.ensureCompanyContext();
        return await this.currentCompanyDb.createBackup();
    }

    /**
     * Restore current company from backup
     */
    async restoreCompanyFromBackup(backupData) {
        this.ensureCompanyContext();
        return await this.currentCompanyDb.restoreFromBackup(backupData);
    }

    // === CONVENIENCE METHODS FOR BACKWARD COMPATIBILITY ===

    /**
     * Get company with full data (backward compatibility)
     */
    async getCompanyWithData(companyId) {
        await this.setCompanyContext(companyId);
        
        // Get company info from master DB
        const company = await this.getCompanyInfo(companyId);
        
        // Get company-specific data
        const assets = await this.all(`
            SELECT 
                id as assetDbId,
                record_id as recordId,
                asset_particulars as assetParticulars,
                book_entry_date as bookEntryDate,
                put_to_use_date as putToUseDate,
                basic_amount as basicAmount,
                duties_taxes as dutiesTaxes,
                gross_amount as grossAmount,
                vendor,
                invoice_no as invoiceNo,
                model_make as modelMake,
                location,
                asset_id as assetId,
                remarks,
                ledger_name_in_books as ledgerNameInBooks,
                asset_group as assetGroup,
                asset_sub_group as assetSubGroup,
                schedule_iii_classification as scheduleIIIClassification,
                disposal_date as disposalDate,
                disposal_amount as disposalAmount,
                salvage_percentage as salvagePercentage,
                wdv_of_adoption_date as wdvOfAdoptionDate,
                is_leasehold as isLeasehold,
                depreciation_method as depreciationMethod,
                life_in_years as lifeInYears,
                lease_period as leasePeriod,
                scrap_it as scrapIt
            FROM assets ORDER BY record_id
        `);
        
        const financialYears = await this.all('SELECT year_range FROM financial_years ORDER BY year_range');
        
        const statutoryRates = await this.all(`
            SELECT 
                is_statutory as isStatutory,
                tangibility,
                asset_group as assetGroup,
                asset_sub_group as assetSubGroup,
                extra_shift_depreciation as extraShiftDepreciation,
                useful_life_years as usefulLifeYears,
                schedule_ii_classification as scheduleIIClassification
            FROM statutory_rates ORDER BY asset_group, asset_sub_group
        `);
        
        const extraLedgers = await this.all('SELECT ledger_name FROM extra_ledgers ORDER BY ledger_name');
        
        const licenseHistory = await this.all(`
            SELECT 
                id,
                license_key as key,
                valid_from as validFrom,
                valid_upto as validUpto,
                activated_at as activatedAt
            FROM license_history ORDER BY activated_at DESC
        `);

        return {
            id: company.id,
            info: company,
            assets: assets,
            financialYears: financialYears.map(fy => fy.year_range),
            statutoryRates: statutoryRates,
            extraLedgers: extraLedgers.map(el => el.ledger_name),
            licenseHistory: licenseHistory
        };
    }

    /**
     * Close all database connections
     */
    async close() {
        await this.databaseManager.closeAll();
        this.currentCompanyDb = null;
        this.currentCompanyId = null;
    }

    // === MIGRATION HELPER METHODS ===

    /**
     * Check if system is ready (migrated)
     */
    async isSystemReady() {
        const status = await this.getMigrationStatus();
        return !status.needsMigration;
    }

    /**
     * Get current company context
     */
    getCurrentCompanyId() {
        return this.currentCompanyId;
    }

    /**
     * Get database manager (for advanced operations)
     */
    getDatabaseManager() {
        return this.databaseManager;
    }
}

// Export singleton instance
const dbService = new DatabaseService();

export default dbService;