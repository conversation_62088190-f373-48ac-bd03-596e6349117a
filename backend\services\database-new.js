import DatabaseManager from './database-manager/DatabaseManager.js';
import DatabaseMigrator from './database-manager/DatabaseMigrator.js';

/**
 * Updated Database Service that uses DatabaseManager for multi-company databases
 * Maintains compatibility with existing API while supporting company-specific databases
 */
class DatabaseService {
    constructor() {
        this.databaseManager = new DatabaseManager();
        this.migrator = new DatabaseMigrator();
        this.currentCompanyId = null;
        this.currentCompanyDb = null;
        this.isInitialized = false;
        this.initializationError = null;
        
        // Don't start initialization in constructor to avoid blocking
        console.log('📊 DatabaseService created, initialization will be done on demand');
    }

    async initialize() {
        if (this.isInitialized) {
            return;
        }

        if (this.initializationError) {
            throw this.initializationError;
        }

        try {
            console.log('🔄 DatabaseService: Starting initialization...');
            
            // Initialize database manager with timeout
            const initTimeout = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('DatabaseManager initialization timeout')), 15000);
            });
            
            await Promise.race([
                this.databaseManager.init(),
                initTimeout
            ]);
            
            console.log('✅ DatabaseManager initialized successfully');
            
            // Check migration status with timeout
            console.log('🔄 Checking migration status...');
            const statusTimeout = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Migration status check timeout')), 10000);
            });
            
            try {
                const migrationStatus = await Promise.race([
                    this.migrator.getMigrationStatus(),
                    statusTimeout
                ]);
                
                if (migrationStatus.needsMigration) {
                    console.log('🔄 Migration required. System ready for migration.');
                } else {
                    console.log('✅ No migration needed. System ready.');
                }
            } catch (migrationError) {
                console.warn('⚠️  Could not check migration status:', migrationError.message);
                console.log('📊 System will operate in basic mode');
            }
            
            this.isInitialized = true;
            console.log('✅ DatabaseService initialization complete');
            
        } catch (error) {
            this.initializationError = error;
            console.error('❌ DatabaseService initialization failed:', error);
            throw error;
        }
    }

    /**
     * Ensure initialization is complete with timeout
     */
    async ensureInitialized() {
        if (this.isInitialized) {
            return;
        }

        if (this.initializationError) {
            throw this.initializationError;
        }

        // Initialize with timeout
        const timeout = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('DatabaseService initialization timeout')), 20000);
        });

        await Promise.race([
            this.initialize(),
            timeout
        ]);
    }

    /**
     * Run database migration from old structure to new structure
     */
    async runMigration() {
        await this.ensureInitialized();
        return await this.migrator.migrate();
    }

    /**
     * Get migration status with fallback
     */
    async getMigrationStatus() {
        try {
            await this.ensureInitialized();
            return await this.migrator.getMigrationStatus();
        } catch (error) {
            console.error('Error getting migration status:', error);
            // Return a safe default status
            return {
                needsMigration: false,
                hasOldDatabase: false,
                companiesInNewStructure: 0,
                oldDatabasePath: null,
                systemReady: false,
                error: error.message
            };
        }
    }

    /**
     * Set current company context for database operations
     */
    async setCompanyContext(companyId) {
        await this.ensureInitialized();
        
        if (this.currentCompanyId === companyId && this.currentCompanyDb) {
            return; // Already set to this company
        }

        this.currentCompanyId = companyId;
        this.currentCompanyDb = await this.databaseManager.getCompanyDatabase(companyId);
    }

    /**
     * Ensure company context is set
     */
    ensureCompanyContext() {
        if (!this.currentCompanyDb) {
            throw new Error('No company context set. Call setCompanyContext(companyId) first.');
        }
    }

    // === MASTER DATABASE OPERATIONS ===

    /**
     * Get all companies with error handling
     */
    async getAllCompanies() {
        try {
            await this.ensureInitialized();
            return await this.databaseManager.getAllCompanies();
        } catch (error) {
            console.error('Error getting companies:', error);
            return []; // Return empty array as fallback
        }
    }

    /**
     * Get company info
     */
    async getCompanyInfo(companyId) {
        await this.ensureInitialized();
        return await this.databaseManager.getCompanyInfo(companyId);
    }

    /**
     * Create new company
     */
    async createCompany(companyData) {
        await this.ensureInitialized();
        return await this.databaseManager.createCompany(companyData);
    }

    /**
     * Update company info
     */
    async updateCompanyInfo(companyId, companyData) {
        await this.ensureInitialized();
        await this.databaseManager.updateCompanyInfo(companyId, companyData);
    }

    /**
     * Delete company and all associated data
     */
    async deleteCompany(companyId) {
        await this.ensureInitialized();
        await this.databaseManager.deleteCompany(companyId);
    }

    /**
     * Get users (from master database)
     */
    async getUsers() {
        try {
            await this.ensureInitialized();
            return await this.databaseManager.getUsers();
        } catch (error) {
            console.error('Error getting users:', error);
            return []; // Return empty array as fallback
        }
    }

    /**
     * Create user (in master database)
     */
    async createUser(userData) {
        await this.ensureInitialized();
        return await this.databaseManager.createUser(userData);
    }

    /**
     * Update user (in master database)
     */
    async updateUser(userId, userData) {
        await this.ensureInitialized();
        await this.databaseManager.updateUser(userId, userData);
    }

    /**
     * Authenticate user (from master database)
     */
    async authenticateUser(username, password) {
        await this.ensureInitialized();
        return await this.databaseManager.authenticateUser(username, password);
    }

    // === COMPANY-SPECIFIC DATABASE OPERATIONS ===
    // These operations work on the currently set company database

    /**
     * Run query on current company database
     */
    async run(sql, params = []) {
        await this.ensureInitialized();
        this.ensureCompanyContext();
        return await this.currentCompanyDb.run(sql, params);
    }

    /**
     * Get single row from current company database
     */
    async get(sql, params = []) {
        await this.ensureInitialized();
        this.ensureCompanyContext();
        return await this.currentCompanyDb.get(sql, params);
    }

    /**
     * Get multiple rows from current company database
     */
    async all(sql, params = []) {
        await this.ensureInitialized();
        this.ensureCompanyContext();
        return await this.currentCompanyDb.all(sql, params);
    }

    /**
     * Begin transaction on current company database
     */
    async beginTransaction() {
        await this.ensureInitialized();
        this.ensureCompanyContext();
        return await this.currentCompanyDb.beginTransaction();
    }

    /**
     * Commit transaction on current company database
     */
    async commit() {
        await this.ensureInitialized();
        this.ensureCompanyContext();
        return await this.currentCompanyDb.commit();
    }

    /**
     * Rollback transaction on current company database
     */
    async rollback() {
        await this.ensureInitialized();
        this.ensureCompanyContext();
        return await this.currentCompanyDb.rollback();
    }

    /**
     * Add audit log to current company database
     */
    async addAuditLog(entry) {
        await this.ensureInitialized();
        this.ensureCompanyContext();
        return await this.currentCompanyDb.addAuditLog(entry);
    }

    /**
     * Get audit logs from current company database
     */
    async getAuditLogs(limit = 100) {
        await this.ensureInitialized();
        this.ensureCompanyContext();
        return await this.currentCompanyDb.getAuditLogs(limit);
    }

    /**
     * Create backup of current company
     */
    async createCompanyBackup() {
        await this.ensureInitialized();
        this.ensureCompanyContext();
        return await this.currentCompanyDb.createBackup();
    }

    /**
     * Restore current company from backup
     */
    async restoreCompanyFromBackup(backupData) {
        await this.ensureInitialized();
        this.ensureCompanyContext();
        return await this.currentCompanyDb.restoreFromBackup(backupData);
    }

    // === CONVENIENCE METHODS FOR BACKWARD COMPATIBILITY ===

    /**
     * Get company with full data (backward compatibility)
     */
    async getCompanyWithData(companyId) {
        await this.ensureInitialized();
        await this.setCompanyContext(companyId);
        
        // Get company info from master DB
        const company = await this.getCompanyInfo(companyId);
        
        // Get company-specific data
        const assets = await this.all(`
            SELECT 
                id as assetDbId,
                record_id as recordId,
                asset_particulars as assetParticulars,
                book_entry_date as bookEntryDate,
                put_to_use_date as putToUseDate,
                basic_amount as basicAmount,
                duties_taxes as dutiesTaxes,
                gross_amount as grossAmount,
                vendor,
                invoice_no as invoiceNo,
                model_make as modelMake,
                location,
                asset_id as assetId,
                remarks,
                ledger_name_in_books as ledgerNameInBooks,
                asset_group as assetGroup,
                asset_sub_group as assetSubGroup,
                schedule_iii_classification as scheduleIIIClassification,
                disposal_date as disposalDate,
                disposal_amount as disposalAmount,
                salvage_percentage as salvagePercentage,
                wdv_of_adoption_date as wdvOfAdoptionDate,
                is_leasehold as isLeasehold,
                depreciation_method as depreciationMethod,
                life_in_years as lifeInYears,
                lease_period as leasePeriod,
                scrap_it as scrapIt
            FROM assets ORDER BY record_id
        `);
        
        const financialYears = await this.all('SELECT year_range FROM financial_years ORDER BY year_range');
        
        const statutoryRates = await this.all(`
            SELECT 
                is_statutory as isStatutory,
                tangibility,
                asset_group as assetGroup,
                asset_sub_group as assetSubGroup,
                extra_shift_depreciation as extraShiftDepreciation,
                useful_life_years as usefulLifeYears,
                schedule_ii_classification as scheduleIIClassification
            FROM statutory_rates ORDER BY asset_group, asset_sub_group
        `);
        
        const extraLedgers = await this.all('SELECT ledger_name FROM extra_ledgers ORDER BY ledger_name');
        
        const licenseHistory = await this.all(`
            SELECT 
                id,
                license_key as key,
                valid_from as validFrom,
                valid_upto as validUpto,
                activated_at as activatedAt
            FROM license_history ORDER BY activated_at DESC
        `);

        return {
            id: company.id,
            info: company,
            assets: assets,
            financialYears: financialYears.map(fy => fy.year_range),
            statutoryRates: statutoryRates,
            extraLedgers: extraLedgers.map(el => el.ledger_name),
            licenseHistory: licenseHistory
        };
    }

    /**
     * Close all database connections
     */
    async close() {
        try {
            console.log('🔌 Closing DatabaseService...');
            await this.databaseManager.closeAll();
            this.currentCompanyDb = null;
            this.currentCompanyId = null;
            console.log('✅ All database connections closed');
        } catch (error) {
            console.error('Error closing database connections:', error);
        }
    }

    // === MIGRATION HELPER METHODS ===

    /**
     * Check if system is ready (migrated) with fallback
     */
    async isSystemReady() {
        try {
            await this.ensureInitialized();
            const status = await this.getMigrationStatus();
            return !status.needsMigration && status.systemReady !== false;
        } catch (error) {
            console.error('Error checking system readiness:', error);
            return false;
        }
    }

    /**
     * Get current company context
     */
    getCurrentCompanyId() {
        return this.currentCompanyId;
    }

    /**
     * Get database manager (for advanced operations)
     */
    getDatabaseManager() {
        return this.databaseManager;
    }

    /**
     * Get initialization status
     */
    getInitializationStatus() {
        return {
            isInitialized: this.isInitialized,
            hasError: !!this.initializationError,
            error: this.initializationError?.message
        };
    }
}

// Export singleton instance
const dbService = new DatabaseService();

export default dbService;