import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Import NEW route handlers (multi-database structure)
import companiesRoutes from './routes/companies-new.js';
import usersRoutes from './routes/users-new.js';
import assetsRoutes from './routes/assets-new.js';
import adminRoutes from './routes/admin.js';

// Import existing routes that may still work
import assetImportRoutes from './routes/asset-import.js';
import settingsRoutes from './routes/settings.js';
import auditRoutes from './routes/audit.js';
import backupRoutes from './routes/backup.js';

// Import new database service
import dbService from './services/database-new.js';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));

// CORS configuration
app.use(cors({
    origin: [
        'http://localhost:5173',
        'http://localhost:5174', 
        'http://localhost:5175',
        'http://localhost:5176',
        'http://localhost:5177'
    ],
    credentials: true,
    optionsSuccessStatus: 200
}));

// Logging
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// API Routes - Multi-Database Structure
app.use('/api/companies', companiesRoutes);
app.use('/api/users', usersRoutes);
app.use('/api/assets', assetsRoutes);
app.use('/api/admin', adminRoutes);

// Legacy routes (may need updating for multi-database)
app.use('/api/assets', assetImportRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/audit', auditRoutes);
app.use('/api/backup', backupRoutes);

// Health check endpoint
app.get('/api/health', async (req, res) => {
    try {
        const isReady = await dbService.isSystemReady();
        const migrationStatus = await dbService.getMigrationStatus();

        res.json({
            status: 'ok',
            message: 'FAR Sighted Backend API is running',
            timestamp: new Date().toISOString(),
            version: '2.0.0',
            database: {
                multiDatabaseStructure: true,
                systemReady: isReady,
                migrationStatus
            }
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Health check failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Migration status endpoint
app.get('/api/migration-status', async (req, res) => {
    try {
        const status = await dbService.getMigrationStatus();
        res.json(status);
    } catch (error) {
        res.status(500).json({
            error: 'Failed to get migration status',
            message: error.message
        });
    }
});

// System info endpoint
app.get('/api/system-info', async (req, res) => {
    try {
        const companies = await dbService.getAllCompanies();
        const users = await dbService.getUsers();
        const migrationStatus = await dbService.getMigrationStatus();

        res.json({
            system: {
                version: '2.0.0',
                multiDatabaseStructure: true,
                timestamp: new Date().toISOString()
            },
            migration: migrationStatus,
            statistics: {
                totalCompanies: companies.length,
                totalUsers: users.length
            },
            companies: companies.map(c => ({
                id: c.id,
                name: c.company_name,
                databasePath: c.database_path
            }))
        });
    } catch (error) {
        res.status(500).json({
            error: 'Failed to get system info',
            message: error.message
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Error:', err.stack);
    res.status(500).json({
        error: 'Internal Server Error',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`
    });
});

// Start server
app.listen(PORT, () => {
    console.log('🚀 FAR Sighted Backend Server Started');
    console.log(`📡 Server running on port: ${PORT}`);
    console.log(`🌐 API URL: http://localhost:${PORT}/api`);
    console.log(`🏥 Health check: http://localhost:${PORT}/api/health`);
    console.log(`📅 Started at: ${new Date().toLocaleString()}`);
    
    if (process.env.NODE_ENV === 'development') {
        console.log('🔧 Running in development mode');
    }
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('⏹️  SIGTERM received, shutting down gracefully');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('⏹️  SIGINT received, shutting down gracefully');
    process.exit(0);
});
