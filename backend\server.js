import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Import NEW route handlers (multi-database structure)
import companiesRoutes from './routes/companies-new.js';
import usersRoutes from './routes/users-new.js';
import assetsRoutes from './routes/assets-new.js';
import adminRoutes from './routes/admin.js';

// Import existing routes that may still work
import assetImportRoutes from './routes/asset-import.js';
import settingsRoutes from './routes/settings.js';
import auditRoutes from './routes/audit.js';
import backupRoutes from './routes/backup.js';

// Import new database service
import dbService from './services/database-new.js';

// Import port manager
import { getPortConfiguration, generateCorsOrigins, savePortConfiguration, checkCommonPorts } from './utils/port-manager.js';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();

// Port configuration will be set dynamically
let PORT = process.env.BACKEND_PORT || process.env.PORT || null;
let FRONTEND_PORT = process.env.FRONTEND_PORT || null;

// Security middleware
app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));

// CORS configuration will be set dynamically in startServer() function

// Logging
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Simple authentication middleware (for demo purposes)
// In production, use proper JWT authentication
app.use((req, res, next) => {
    // For now, we'll just set a default user context
    // This should be replaced with proper JWT token verification
    req.user = {
        id: 'admin-001',
        username: 'admin',
        role: 'Admin'
    };
    next();
});

// System status check middleware with better error handling
app.use('/api', async (req, res, next) => {
    try {
        // Skip status check for admin endpoints
        if (req.path.startsWith('/admin/')) {
            return next();
        }

        // Skip status check for health endpoint to avoid circular dependency
        if (req.path === '/health' || req.path === '/migration-status') {
            return next();
        }

        const isReady = await dbService.isSystemReady();
        if (!isReady) {
            const migrationStatus = await dbService.getMigrationStatus();
            return res.status(503).json({
                error: 'System migration required',
                message: 'Database migration is needed before using the application.',
                migrationStatus,
                migrationEndpoint: '/api/admin/migrate',
                instructions: 'Please run the migration script or contact your administrator.'
            });
        }
        next();
    } catch (error) {
        console.error('Error checking system status:', error);
        // Continue anyway - let specific endpoints handle database errors
        next();
    }
});

// API Routes - New multi-database structure
app.use('/api/admin', adminRoutes);          // Migration and admin functions
app.use('/api/companies', companiesRoutes);  // Company management with separate DBs
app.use('/api/users', usersRoutes);          // User management in master DB
app.use('/api/assets', assetsRoutes);        // Asset management per company

// Existing routes (may need updates for new structure)
app.use('/api/assets', assetImportRoutes);   // Asset import functionality
app.use('/api/settings', settingsRoutes);    // Application settings
app.use('/api/audit', auditRoutes);          // Audit trail
app.use('/api/backup', backupRoutes);        // Backup and restore

// Health check endpoint with better error handling
app.get('/api/health', async (req, res) => {
    try {
        // Ensure database service is initialized
        await dbService.ensureInitialized();
        
        const isReady = await dbService.isSystemReady();
        const migrationStatus = await dbService.getMigrationStatus();
        
        res.json({
            status: 'ok',
            message: 'FAR Sighted Backend API is running',
            timestamp: new Date().toISOString(),
            version: '2.0.0',
            database: {
                multiDatabaseStructure: true,
                systemReady: isReady,
                migrationStatus
            }
        });
    } catch (error) {
        console.error('Health check error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Health check failed',
            error: error.message,
            timestamp: new Date().toISOString(),
            database: {
                multiDatabaseStructure: true,
                systemReady: false,
                initializationError: true
            }
        });
    }
});

// System info endpoint with error handling
app.get('/api/system-info', async (req, res) => {
    try {
        await dbService.ensureInitialized();
        
        const companies = await dbService.getAllCompanies();
        const users = await dbService.getUsers();
        const migrationStatus = await dbService.getMigrationStatus();
        
        res.json({
            version: '2.0.0',
            databaseStructure: 'Multi-Database',
            companies: {
                total: companies.length,
                list: companies.map(c => ({ id: c.id, name: c.name }))
            },
            users: {
                total: users.length
            },
            migration: migrationStatus,
            features: [
                'Separate database per company',
                'Enhanced data isolation',
                'Company-specific backups',
                'Improved scalability',
                'Granular audit trails'
            ]
        });
    } catch (error) {
        console.error('System info error:', error);
        res.status(500).json({
            error: 'Failed to get system info',
            message: error.message,
            version: '2.0.0',
            databaseStructure: 'Multi-Database',
            initializationError: true
        });
    }
});

// Migration status endpoint (public) with error handling
app.get('/api/migration-status', async (req, res) => {
    try {
        await dbService.ensureInitialized();
        
        const status = await dbService.getMigrationStatus();
        const isReady = await dbService.isSystemReady();
        
        res.json({
            ...status,
            systemReady: isReady,
            message: isReady 
                ? 'System is ready for use' 
                : 'Migration required before system can be used'
        });
    } catch (error) {
        console.error('Migration status error:', error);
        res.status(500).json({
            error: 'Failed to check migration status',
            message: error.message,
            systemReady: false,
            needsMigration: true,
            hasOldDatabase: false,
            companiesInNewStructure: 0,
            initializationError: true
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Error:', err.stack);
    
    // Check if it's a database context error
    if (err.message && err.message.includes('No company context set')) {
        return res.status(400).json({
            error: 'Company context required',
            message: 'A company must be selected for this operation',
            code: 'NO_COMPANY_CONTEXT'
        });
    }
    
    // Check if it's a database initialization error
    if (err.message && err.message.includes('not initialized')) {
        return res.status(503).json({
            error: 'Database not ready',
            message: 'Database is still initializing. Please try again in a moment.',
            code: 'DATABASE_INITIALIZING'
        });
    }
    
    res.status(500).json({
        error: 'Internal Server Error',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong',
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`,
        availableEndpoints: [
            'GET /api/health - Health check',
            'GET /api/system-info - System information',
            'GET /api/migration-status - Migration status',
            'POST /api/admin/migrate - Run migration',
            'GET /api/companies - List companies',
            'POST /api/companies - Create company',
            'GET /api/users - List users',
            'POST /api/users/login - User login'
        ]
    });
});

// Graceful shutdown handler
async function gracefulShutdown(signal) {
    console.log(`⏹️  ${signal} received, shutting down gracefully`);
    
    try {
        // Close all database connections
        await dbService.close();
        console.log('🔌 Database connections closed');
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
    }
}

// Dynamic port configuration and server startup
async function startServer() {
    try {
        // Check common ports and get available configuration
        await checkCommonPorts();

        let portConfig;
        if (!PORT || !FRONTEND_PORT) {
            console.log('\n🔧 Configuring ports automatically...');
            portConfig = await getPortConfiguration();
            PORT = portConfig.backend.port;
            FRONTEND_PORT = portConfig.frontend.port;

            // Save configuration for future use
            await savePortConfiguration(portConfig);
        } else {
            console.log('\n🔧 Using configured ports...');
            portConfig = {
                backend: { port: PORT, url: `http://localhost:${PORT}`, apiUrl: `http://localhost:${PORT}/api` },
                frontend: { port: FRONTEND_PORT, url: `http://localhost:${FRONTEND_PORT}` }
            };
        }

        // Update CORS with dynamic frontend port
        const corsOrigins = generateCorsOrigins(FRONTEND_PORT);
        app.use(cors({
            origin: corsOrigins,
            credentials: true,
            optionsSuccessStatus: 200
        }));

        // Start server with LAN access support
        const server = app.listen(PORT, '0.0.0.0', async () => {
            console.log('\n🚀 FAR SIGHTED BACKEND SERVER STARTED');
            console.log('====================================');
            console.log(`📡 Server running on port: ${PORT}`);
            console.log(`🌐 Local API URL: http://localhost:${PORT}/api`);
            console.log(`🌐 LAN API URL: http://<your-ip>:${PORT}/api`);
            console.log(`🏥 Health check: http://localhost:${PORT}/api/health`);
            console.log(`📊 System info: http://localhost:${PORT}/api/system-info`);
            console.log(`🔄 Migration status: http://localhost:${PORT}/api/migration-status`);
            console.log(`📅 Started at: ${new Date().toLocaleString()}`);
            console.log('🔗 Configured for multi-PC LAN access');
            console.log(`🖥️  Frontend expected on: http://localhost:${FRONTEND_PORT}`);

            // Initialize database service and check migration status
            try {
                console.log('\n🔄 Initializing database service...');
                await dbService.ensureInitialized();

                const migrationStatus = await dbService.getMigrationStatus();
                const isReady = await dbService.isSystemReady();

                console.log('\n📊 DATABASE STATUS');
                console.log('==================');
                console.log(`Structure: Multi-Database`);
                console.log(`System Ready: ${isReady ? '✅' : '❌'}`);
                console.log(`Migration Needed: ${migrationStatus.needsMigration ? '⚠️  Yes' : '✅ No'}`);
                console.log(`Companies: ${migrationStatus.companiesInNewStructure}`);

                if (!isReady) {
                    console.log('\n⚠️  MIGRATION REQUIRED:');
                    console.log('   Run: npm run migrate');
                    console.log('   Or:  POST /api/admin/migrate');
                    console.log('   Or:  node backend/scripts/migrate-database.js');
                } else {
                    console.log('\n✅ System ready for use!');
                }

            } catch (error) {
                console.error('❌ Error during database initialization:', error.message);
                console.log('\n⚠️  Database initialization failed, but server is running');
                console.log('   The system may require migration or manual setup');
                console.log('   Check the migration status: GET /api/migration-status');
            }

            if (process.env.NODE_ENV === 'development') {
                console.log('\n🔧 DEVELOPMENT MODE');
                console.log('===================');
                console.log('📁 Database location: backend/database/');
                console.log('🏢 Company databases: backend/database/companies/');
            }
        });

        return server;

    } catch (error) {
        console.error('❌ Failed to start server:', error.message);
        process.exit(1);
    }
}

// Start the server
startServer().catch(error => {
    console.error('❌ Server startup failed:', error);
    process.exit(1);
});

// Graceful shutdown handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    gracefulShutdown('UNCAUGHT_EXCEPTION');
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    gracefulShutdown('UNHANDLED_REJECTION');
});

export default app;