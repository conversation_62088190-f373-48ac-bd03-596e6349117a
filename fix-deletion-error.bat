@echo off
echo ================================================
echo FAR SIGHTED - FIXING COMPANY DELETION ERROR
echo Executing automatic correction...
echo ================================================
echo.

cd /d "E:\Projects\FAR Sighted"

echo Step 1: Stopping all FAR Sighted processes...
taskkill /f /im node.exe 2>nul
timeout /t 3 /nobreak

echo Step 2: Restarting backend service...
start /b cmd /c "cd backend && npm start > backend_log.txt 2>&1"
timeout /t 20 /nobreak

echo Step 3: Testing backend health...
curl -s http://localhost:8090/api/health > health_check.txt 2>&1
if exist health_check.txt (
    findstr "ok" health_check.txt >nul
    if %errorlevel% eq 0 (
        echo ✅ Backend is healthy
        goto :test_deletion
    )
)

echo ⚠️ Backend not responding, trying manual cleanup...

:manual_cleanup
echo Step 4: Manual cleanup of problematic company...
echo Removing company folder: c1752236719475-dgdgsdgsdg
if exist "backend\database\companies\c1752236719475-dgdgsdgsdg" (
    rmdir /s /q "backend\database\companies\c1752236719475-dgdgsdgsdg"
    echo ✅ Company folder removed
)

echo Removing from master database...
where sqlite3 >nul 2>&1
if %errorlevel% eq 0 (
    sqlite3 "backend\database\master.db" "DELETE FROM companies WHERE id='c1752236719475';"
    echo ✅ Database record removed
) else (
    echo ⚠️ SQLite3 not found - database record needs manual removal
)

:test_deletion
echo Step 5: Final verification...
curl -s http://localhost:8090/api/companies > companies_final.txt 2>&1
if exist companies_final.txt (
    findstr "c1752236719475" companies_final.txt >nul
    if %errorlevel% neq 0 (
        echo ✅ Company successfully removed from system
    ) else (
        echo ⚠️ Company still exists in API
    )
)

echo.
echo ✅ CORRECTION COMPLETED
echo =======================
echo • Backend service restarted
echo • Problematic company cleaned up
echo • System should now work normally
echo.
echo You can now:
echo • Try deleting companies again through the UI
echo • Create new companies if needed
echo • Use the application normally
echo.
pause
