/**
 * Port Manager Utility
 * Automatically finds available ports with fallback configuration
 */

import net from 'net';

// Common ports to avoid (used by other Node.js apps)
const AVOID_PORTS = [3000, 3001, 8080, 8081, 5000, 5001, 4000, 4001];

// Preferred port ranges for different services
const PORT_RANGES = {
    backend: {
        preferred: [8090, 8092, 8093, 8094, 8095],
        range: { start: 8090, end: 8199 }
    },
    frontend: {
        preferred: [9090, 9092, 9093, 9094, 9095],
        range: { start: 9090, end: 9199 }
    }
};

/**
 * Check if a port is available
 * @param {number} port - Port number to check
 * @returns {Promise<boolean>} - True if port is available
 */
function isPortAvailable(port) {
    return new Promise((resolve) => {
        const server = net.createServer();
        
        server.listen(port, '0.0.0.0', () => {
            server.once('close', () => {
                resolve(true);
            });
            server.close();
        });
        
        server.on('error', () => {
            resolve(false);
        });
    });
}

/**
 * Find an available port from preferred list
 * @param {string} service - Service type ('backend' or 'frontend')
 * @returns {Promise<number>} - Available port number
 */
async function findAvailablePort(service = 'backend') {
    const config = PORT_RANGES[service];
    if (!config) {
        throw new Error(`Unknown service type: ${service}`);
    }

    console.log(`🔍 Finding available port for ${service}...`);

    // First, try preferred ports
    for (const port of config.preferred) {
        if (AVOID_PORTS.includes(port)) {
            console.log(`   ⚠️  Skipping ${port} (in avoid list)`);
            continue;
        }
        
        const available = await isPortAvailable(port);
        if (available) {
            console.log(`   ✅ Found available port: ${port}`);
            return port;
        } else {
            console.log(`   ❌ Port ${port} is busy`);
        }
    }

    // If no preferred ports available, scan the range
    console.log(`   🔄 Scanning range ${config.range.start}-${config.range.end}...`);
    
    for (let port = config.range.start; port <= config.range.end; port++) {
        if (AVOID_PORTS.includes(port)) {
            continue;
        }
        
        const available = await isPortAvailable(port);
        if (available) {
            console.log(`   ✅ Found available port in range: ${port}`);
            return port;
        }
    }

    throw new Error(`No available ports found for ${service} in range ${config.range.start}-${config.range.end}`);
}

/**
 * Get port configuration for all services
 * @returns {Promise<Object>} - Port configuration object
 */
async function getPortConfiguration() {
    console.log('🚀 FAR SIGHTED PORT CONFIGURATION');
    console.log('=================================');

    try {
        const backendPort = await findAvailablePort('backend');
        const frontendPort = await findAvailablePort('frontend');

        const config = {
            backend: {
                port: backendPort,
                url: `http://localhost:${backendPort}`,
                apiUrl: `http://localhost:${backendPort}/api`
            },
            frontend: {
                port: frontendPort,
                url: `http://localhost:${frontendPort}`,
                devUrl: `http://localhost:${frontendPort}`
            },
            lan: {
                backend: `http://[YOUR_IP]:${backendPort}/api`,
                frontend: `http://[YOUR_IP]:${frontendPort}`
            }
        };

        console.log('\n📊 PORT CONFIGURATION SUMMARY');
        console.log('=============================');
        console.log(`Backend API: ${config.backend.apiUrl}`);
        console.log(`Frontend App: ${config.frontend.url}`);
        console.log(`LAN Backend: ${config.lan.backend}`);
        console.log(`LAN Frontend: ${config.lan.frontend}`);

        return config;

    } catch (error) {
        console.error('❌ Failed to configure ports:', error.message);
        throw error;
    }
}

/**
 * Generate CORS origins for the given ports
 * @param {number} frontendPort - Frontend port number
 * @returns {Array} - Array of CORS origins
 */
function generateCorsOrigins(frontendPort) {
    const origins = [
        `http://localhost:${frontendPort}`,
        'http://localhost:5173',  // Vite dev server fallback
        'http://localhost:5174',
        'http://localhost:5175',
        'http://localhost:5176',
        'http://localhost:5177',
        'http://localhost:9090',  // Common frontend ports
        'http://localhost:9091',
        'http://localhost:9092',
        'http://localhost:9093',
        'http://localhost:9094',
        'http://localhost:9095',
        // LAN access patterns
        new RegExp(`^http:\\/\\/192\\.168\\.\\d+\\.\\d+:${frontendPort}$`),
        new RegExp(`^http:\\/\\/10\\.\\d+\\.\\d+\\.\\d+:${frontendPort}$`),
        new RegExp(`^http:\\/\\/172\\.(1[6-9]|2\\d|3[01])\\.\\d+\\.\\d+:${frontendPort}$`)
    ];

    return origins;
}

/**
 * Save port configuration to environment file
 * @param {Object} config - Port configuration
 */
async function savePortConfiguration(config) {
    const envContent = `# FAR Sighted Port Configuration
# Auto-generated on ${new Date().toISOString()}

# Backend Configuration
BACKEND_PORT=${config.backend.port}
API_BASE_URL=http://localhost:${config.backend.port}/api

# Frontend Configuration  
FRONTEND_PORT=${config.frontend.port}
FRONTEND_URL=http://localhost:${config.frontend.port}

# Database Configuration
DB_PATH=./database/far_sighted.db

# Security Configuration
JWT_SECRET=your-secret-key-here
SESSION_SECRET=your-session-secret-here

# Environment
NODE_ENV=development
`;

    const fs = await import('fs');
    const path = await import('path');
    const { fileURLToPath } = await import('url');
    const { dirname } = await import('path');

    const __filename = fileURLToPath(import.meta.url);
    const __dirname = dirname(__filename);
    
    const envPath = path.join(__dirname, '../.env');
    
    try {
        fs.writeFileSync(envPath, envContent);
        console.log(`✅ Port configuration saved to ${envPath}`);
    } catch (error) {
        console.error('❌ Failed to save port configuration:', error.message);
    }
}

/**
 * Check if common ports are busy and warn user
 */
async function checkCommonPorts() {
    console.log('\n🔍 CHECKING COMMON PORTS');
    console.log('========================');

    const commonPorts = [3000, 3001, 8080, 8081, 5000, 5001];
    const busyPorts = [];

    for (const port of commonPorts) {
        const available = await isPortAvailable(port);
        if (!available) {
            busyPorts.push(port);
            console.log(`   ❌ Port ${port} is busy`);
        } else {
            console.log(`   ✅ Port ${port} is available`);
        }
    }

    if (busyPorts.length > 0) {
        console.log(`\n⚠️  WARNING: ${busyPorts.length} common ports are busy: ${busyPorts.join(', ')}`);
        console.log('   FAR Sighted will use alternative ports to avoid conflicts.');
    } else {
        console.log('\n✅ All common ports are available');
    }

    return busyPorts;
}

export {
    isPortAvailable,
    findAvailablePort,
    getPortConfiguration,
    generateCorsOrigins,
    savePortConfiguration,
    checkCommonPorts
};
