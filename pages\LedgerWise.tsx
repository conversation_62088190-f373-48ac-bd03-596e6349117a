/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo } from 'react';
import { api } from '../lib/api';
import type { CompanyData } from '../lib/db-server';
import { DataViewProps, LedgerWiseRow, LedgerWiseDetailRow } from '../lib/types';
import { formatIndianNumber, calculateYearlyMetrics, sortData, exportToExcel } from '../lib/utils';
import { LedgerWiseDetailModal } from './LedgerWiseDetailModal';
import { DownloadIcon } from '../Icons';

export function LedgerWise({ companyId, companyName, year, showAlert }: DataViewProps) {
    const [reportData, setReportData] = useState<LedgerWiseRow[]>([]);
    const [totals, setTotals] = useState<LedgerWiseRow | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [sortConfig, setSortConfig] = useState<{ key: keyof LedgerWiseRow; direction: 'asc' | 'desc' } | null>({ key: 'ledgerName', direction: 'asc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);

    // For drill-down modal
    const [companyData, setCompanyData] = useState<CompanyData | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalData, setModalData] = useState<LedgerWiseDetailRow[]>([]);
    const [modalTitle, setModalTitle] = useState('');
    
    useEffect(() => {
        const calculateReport = async () => {
            if (!companyId || !year) {
                setLoading(false);
                setReportData([]);
                setTotals(null);
                setCompanyData(null);
                return;
            }

            setLoading(true);
            setError(null);
            setSelectedRowId(null);
            setSelectedColumnKey(null);

            try {
                const data = await api.getCompanyData(companyId);
                setCompanyData(data);
                
                if (!data || !data.assets || data.assets.length === 0) {
                    setReportData([]);
                    setTotals(null);
                    setLoading(false);
                    return;
                }

                const reportMap: Record<string, LedgerWiseRow> = {};

                for (const asset of data.assets) {
                    const ledgerName = asset.ledgerNameInBooks;
                    if (!ledgerName) continue;

                    if (!reportMap[ledgerName]) {
                        reportMap[ledgerName] = { ledgerName, openingGross: 0, additions: 0, deletionsGross: 0, closingGross: 0, depreciation: 0, deletionAccumulatedDepreciation: 0, deletionSaleAmount: 0, deletionWDV: 0, profitOnDeletion: 0 };
                    }
                    
                    const metrics = calculateYearlyMetrics(asset, year, data);

                    reportMap[ledgerName].openingGross += metrics.openingGross;
                    reportMap[ledgerName].additions += metrics.additionsGross;
                    reportMap[ledgerName].deletionsGross += metrics.deletionsGross;
                    reportMap[ledgerName].closingGross += metrics.closingGross;
                    reportMap[ledgerName].depreciation += metrics.additionsDepreciation;
                    reportMap[ledgerName].deletionAccumulatedDepreciation += metrics.deletionsDepreciation;
                    reportMap[ledgerName].deletionSaleAmount += metrics.deletionSaleAmount;
                    reportMap[ledgerName].deletionWDV += metrics.deletionWDV;
                    reportMap[ledgerName].profitOnDeletion += metrics.profitOnDeletion;
                }
                
                let finalReportData = Object.values(reportMap);
                finalReportData = finalReportData.filter(row => 
                    row.openingGross !== 0 || row.additions !== 0 || row.deletionsGross !== 0 || row.depreciation !== 0
                );

                const totalRow: LedgerWiseRow = { ledgerName: 'Total', openingGross: 0, additions: 0, deletionsGross: 0, closingGross: 0, depreciation: 0, deletionAccumulatedDepreciation: 0, deletionSaleAmount: 0, deletionWDV: 0, profitOnDeletion: 0 };
                finalReportData.forEach(row => {
                    (Object.keys(row) as Array<keyof LedgerWiseRow>).forEach(key => {
                        if (key !== 'ledgerName') {
                            (totalRow as any)[key] += row[key] as number;
                        }
                    });
                });
                
                setReportData(finalReportData);
                setTotals(totalRow);

            } catch (err) {
                setError('Failed to generate Ledger-wise report.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        calculateReport();
    }, [companyId, year]);

    const handleRowDoubleClick = (ledgerName: string) => {
        if (!ledgerName || ledgerName === 'Total' || !companyData) return;

        const filtered = companyData.assets.filter(a => a.ledgerNameInBooks === ledgerName);

        const details: LedgerWiseDetailRow[] = filtered.map(asset => {
             const metrics = calculateYearlyMetrics(asset, year, companyData);
             return {
                recordId: asset.recordId,
                assetParticulars: asset.assetParticulars,
                ledgerName,
                openingGross: metrics.openingGross,
                additions: metrics.additionsGross,
                deletionsGross: metrics.deletionsGross,
                closingGross: metrics.closingGross,
                depreciation: metrics.additionsDepreciation,
                deletionAccumulatedDepreciation: metrics.deletionsDepreciation,
                deletionSaleAmount: metrics.deletionSaleAmount,
                deletionWDV: metrics.deletionWDV,
                profitOnDeletion: metrics.profitOnDeletion,
             };
        });
        
        setModalData(details);
        setModalTitle(`Details for Ledger: ${ledgerName}`);
        setIsModalOpen(true);
    };

    const sortedReportData = useMemo(() => {
        return sortData(reportData, sortConfig);
    }, [reportData, sortConfig]);

    const requestSort = (key: keyof LedgerWiseRow) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: keyof LedgerWiseRow) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };
    
    const getHeaderClass = (key: keyof LedgerWiseRow) => {
        const classes = ['sortable'];
        if (key !== 'ledgerName') classes.push('text-right');
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        return classes.join(' ');
    };

    const getColumnClass = (key: keyof LedgerWiseRow) => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if (key !== 'ledgerName') classes.push('text-right');
        return classes.join(' ');
    };
    
    const handleExport = () => {
        if (!companyId || !companyName) return;

        const dataToExport = [...sortedReportData, totals]
            .filter(Boolean)
            .map(row => ({
                "Ledger Name": row!.ledgerName,
                "Gross Block - Opening": row!.openingGross,
                "Gross Block - Additions": row!.additions,
                "Gross Block - Deletions": row!.deletionsGross,
                "Gross Block - Closing": row!.closingGross,
                "Depreciation for Year": row!.depreciation,
                "Disposal - Sale Amount": row!.deletionSaleAmount,
                "Disposal - Accumulated Depr.": row!.deletionAccumulatedDepreciation,
                "Disposal - WDV": row!.deletionWDV,
                "Disposal - Profit / (Loss)": row!.profitOnDeletion,
            }));

        if (!exportToExcel({ data: dataToExport, companyName, year, reportName: 'Ledger_Wise_Report' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    if (loading) return <div className="loading-indicator">Generating Ledger-wise Report...</div>;
    if (error) return <div className="error-message">{error}</div>;
    if (!companyId) return null;
    if (reportData.length === 0) return <div className="company-info-container"><p>No asset data available to generate the report for the selected year.</p></div>;


    return (
        <div className="report-view">
            <div className="view-header">
                <h2>Ledger-wise Report for FY {year}</h2>
                <div className="actions">
                    <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export to Excel</button>
                </div>
            </div>
            <div className="table-container">
                <table>
                    <thead>
                        <tr>
                            <th className={getHeaderClass('ledgerName')} onClick={() => requestSort('ledgerName')}>Ledger Name{getSortIndicator('ledgerName')}</th>
                            <th className={getHeaderClass('openingGross')} onClick={() => requestSort('openingGross')}>Gross Block - Opening{getSortIndicator('openingGross')}</th>
                            <th className={getHeaderClass('additions')} onClick={() => requestSort('additions')}>Gross Block - Additions{getSortIndicator('additions')}</th>
                            <th className={getHeaderClass('deletionsGross')} onClick={() => requestSort('deletionsGross')}>Gross Block - Deletions{getSortIndicator('deletionsGross')}</th>
                            <th className={getHeaderClass('closingGross')} onClick={() => requestSort('closingGross')}>Gross Block - Closing{getSortIndicator('closingGross')}</th>
                            <th className={getHeaderClass('depreciation')} onClick={() => requestSort('depreciation')}>Depreciation for Year{getSortIndicator('depreciation')}</th>
                            <th className={getHeaderClass('deletionSaleAmount')} onClick={() => requestSort('deletionSaleAmount')}>Disposal - Sale Amount{getSortIndicator('deletionSaleAmount')}</th>
                            <th className={getHeaderClass('deletionAccumulatedDepreciation')} onClick={() => requestSort('deletionAccumulatedDepreciation')}>Disposal - Accumulated Depr.{getSortIndicator('deletionAccumulatedDepreciation')}</th>
                            <th className={getHeaderClass('deletionWDV')} onClick={() => requestSort('deletionWDV')}>Disposal - WDV{getSortIndicator('deletionWDV')}</th>
                            <th className={getHeaderClass('profitOnDeletion')} onClick={() => requestSort('profitOnDeletion')}>Disposal - Profit / (Loss){getSortIndicator('profitOnDeletion')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {sortedReportData.map((row) => (
                            <tr key={row.ledgerName} onDoubleClick={() => handleRowDoubleClick(row.ledgerName)} onClick={() => setSelectedRowId(row.ledgerName)} className={`clickable-row ${row.ledgerName === selectedRowId ? 'selected-row' : ''}`}>
                                <td className={getColumnClass('ledgerName')}>{row.ledgerName}</td>
                                <td className={getColumnClass('openingGross')}>{formatIndianNumber(row.openingGross)}</td>
                                <td className={getColumnClass('additions')}>{formatIndianNumber(row.additions)}</td>
                                <td className={getColumnClass('deletionsGross')}>{formatIndianNumber(row.deletionsGross)}</td>
                                <td className={getColumnClass('closingGross')}>{formatIndianNumber(row.closingGross)}</td>
                                <td className={getColumnClass('depreciation')}>{formatIndianNumber(row.depreciation)}</td>
                                <td className={getColumnClass('deletionSaleAmount')}>{formatIndianNumber(row.deletionSaleAmount)}</td>
                                <td className={getColumnClass('deletionAccumulatedDepreciation')}>{formatIndianNumber(row.deletionAccumulatedDepreciation)}</td>
                                <td className={getColumnClass('deletionWDV')}>{formatIndianNumber(row.deletionWDV)}</td>
                                <td className={getColumnClass('profitOnDeletion')}>{formatIndianNumber(row.profitOnDeletion)}</td>
                            </tr>
                        ))}
                    </tbody>
                    {totals && (
                        <tfoot>
                            <tr>
                                <td className={getColumnClass('ledgerName')}>{totals.ledgerName}</td>
                                <td className={getColumnClass('openingGross')}>{formatIndianNumber(totals.openingGross)}</td>
                                <td className={getColumnClass('additions')}>{formatIndianNumber(totals.additions)}</td>
                                <td className={getColumnClass('deletionsGross')}>{formatIndianNumber(totals.deletionsGross)}</td>
                                <td className={getColumnClass('closingGross')}>{formatIndianNumber(totals.closingGross)}</td>
                                <td className={getColumnClass('depreciation')}>{formatIndianNumber(totals.depreciation)}</td>
                                <td className={getColumnClass('deletionSaleAmount')}>{formatIndianNumber(totals.deletionSaleAmount)}</td>
                                <td className={getColumnClass('deletionAccumulatedDepreciation')}>{formatIndianNumber(totals.deletionAccumulatedDepreciation)}</td>
                                <td className={getColumnClass('deletionWDV')}>{formatIndianNumber(totals.deletionWDV)}</td>
                                <td className={getColumnClass('profitOnDeletion')}>{formatIndianNumber(totals.profitOnDeletion)}</td>
                            </tr>
                        </tfoot>
                    )}
                </table>
            </div>
            <LedgerWiseDetailModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                title={modalTitle}
                data={modalData}
                year={year}
                companyId={companyId}
                companyName={companyName}
                showAlert={showAlert}
            />
        </div>
    );
}

