#!/usr/bin/env node

/**
 * Fix First Year Depreciation Calculation
 * Updates depreciation calculation to use Adoption Date WDV for first year
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database path
const dbPath = join(__dirname, '../database/far_sighted.db');

// Helper function to run queries
const runQuery = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath);
        db.run(sql, params, function(err) {
            if (err) {
                reject(err);
            } else {
                resolve({ id: this.lastID, changes: this.changes });
            }
        });
        db.close();
    });
};

// Helper function to get data
const getData = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath);
        db.all(sql, params, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
        db.close();
    });
};

// Calculate proper first year depreciation using Adoption Date WDV
const calculateFirstYearDepreciation = (asset, yearRange) => {
    const [startYearStr, endYearStr] = yearRange.split('-');
    const fyStart = new Date(`${startYearStr}-04-01`);
    const fyEnd = new Date(`${endYearStr}-03-31`);
    
    const putToUseDate = new Date(asset.put_to_use_date);
    const adoptionDate = new Date(asset.first_date_of_adoption || '2018-04-01');
    const disposalDate = asset.disposal_date ? new Date(asset.disposal_date) : null;
    const grossAmount = asset.gross_amount || 0;
    const lifeInYears = asset.life_in_years || 0;
    const salvagePercentage = asset.salvage_percentage || 0;
    const salvageValue = Math.round(grossAmount * (salvagePercentage / 100));
    const wdvOfAdoptionDate = asset.wdv_of_adoption_date;

    // Skip if asset not in use during this year
    if (putToUseDate > fyEnd || (disposalDate && disposalDate < fyStart)) {
        return {
            openingWdv: 0,
            useDays: 0,
            depreciationAmount: 0,
            closingWdv: 0
        };
    }

    // Determine opening WDV for first year calculation
    let openingWdv;
    
    if (putToUseDate <= adoptionDate) {
        // Asset was in use before adoption date - use Adoption Date WDV
        openingWdv = wdvOfAdoptionDate || grossAmount;
        console.log(`   📅 Asset ${asset.record_id}: Using Adoption Date WDV ₹${openingWdv.toLocaleString()}`);
    } else {
        // Asset acquired after adoption date - use gross amount
        openingWdv = grossAmount;
        console.log(`   📅 Asset ${asset.record_id}: New asset, using Gross Amount ₹${openingWdv.toLocaleString()}`);
    }

    // Calculate use days in financial year
    const startOfUseInFY = putToUseDate > fyStart ? putToUseDate : fyStart;
    const endOfUseInFY = disposalDate && disposalDate < fyEnd ? disposalDate : fyEnd;
    
    let useDays = 0;
    if (endOfUseInFY >= startOfUseInFY && putToUseDate <= endOfUseInFY) {
        useDays = Math.ceil((endOfUseInFY - startOfUseInFY) / (1000 * 60 * 60 * 24)) + 1;
    }

    // Calculate depreciation
    let depreciationAmount = 0;
    if (lifeInYears > 0 && openingWdv > salvageValue && useDays > 0) {
        if (asset.depreciation_method === 'SLM') {
            // For SLM, depreciation is based on original cost minus salvage
            const depreciableAmount = grossAmount - salvageValue;
            const yearlyDepreciation = depreciableAmount / lifeInYears;
            depreciationAmount = (yearlyDepreciation / 365.25) * useDays;
        } else { // WDV
            // For WDV, depreciation is based on opening WDV
            if (grossAmount > 0 && salvageValue < grossAmount) {
                const rate = 1 - Math.pow((salvageValue / grossAmount), (1 / lifeInYears));
                const yearlyDepreciation = openingWdv * rate;
                depreciationAmount = (yearlyDepreciation / 365.25) * useDays;
            }
        }
    }

    const closingWdv = Math.max(openingWdv - depreciationAmount, salvageValue);

    return {
        openingWdv: Math.round(openingWdv),
        useDays: Math.round(useDays),
        depreciationAmount: Math.round(depreciationAmount),
        closingWdv: Math.round(closingWdv)
    };
};

async function fixFirstYearDepreciation() {
    console.log('🔧 FIXING FIRST YEAR DEPRECIATION CALCULATION');
    console.log('==============================================\n');

    try {
        // First, let's add some test adoption date WDV values to demonstrate the concept
        console.log('📝 Setting up test Adoption Date WDV values...\n');
        
        // Update TEST001 to have an adoption date WDV (simulate old asset)
        await runQuery(`
            UPDATE assets 
            SET wdv_of_adoption_date = 120000,
                put_to_use_date = '2018-06-01'  -- Before adoption date
            WHERE record_id = 'TEST001' AND company_id = 'c1001'
        `);
        
        // Update TEST003 to have an adoption date WDV (simulate old asset)
        await runQuery(`
            UPDATE assets 
            SET wdv_of_adoption_date = 2200000,
                put_to_use_date = '2018-04-15'  -- Before adoption date
            WHERE record_id = 'TEST003' AND company_id = 'c1001'
        `);
        
        console.log('✅ Updated test assets with Adoption Date WDV values');
        console.log('   TEST001: ₹1,20,000 (old asset)');
        console.log('   TEST003: ₹22,00,000 (old asset)');
        console.log('   TEST002: No adoption WDV (new asset)\n');

        // Get all assets with company info
        const assets = await getData(`
            SELECT a.*, c.first_date_of_adoption
            FROM assets a
            JOIN companies c ON a.company_id = c.id
            WHERE a.company_id = 'c1001'
            ORDER BY a.record_id
        `);

        console.log(`📊 Processing ${assets.length} assets for first year depreciation...\n`);

        // Recalculate first year (2022-2023) depreciation
        for (const asset of assets) {
            console.log(`🏭 Asset: ${asset.record_id} - ${asset.asset_particulars}`);
            
            const firstYearData = calculateFirstYearDepreciation(asset, '2022-2023');
            
            // Update the yearly data for first year
            await runQuery(`
                UPDATE asset_yearly_data 
                SET opening_wdv = ?, use_days = ?, depreciation_amount = ?, closing_wdv = ?
                WHERE asset_id = ? AND year_range = '2022-2023'
            `, [
                firstYearData.openingWdv, firstYearData.useDays,
                firstYearData.depreciationAmount, firstYearData.closingWdv,
                asset.id
            ]);

            console.log(`   📊 First Year (2022-2023):`);
            console.log(`      Opening WDV: ₹${firstYearData.openingWdv.toLocaleString()}`);
            console.log(`      Use Days: ${firstYearData.useDays}`);
            console.log(`      Depreciation: ₹${firstYearData.depreciationAmount.toLocaleString()}`);
            console.log(`      Closing WDV: ₹${firstYearData.closingWdv.toLocaleString()}\n`);
        }

        // Now recalculate subsequent years to maintain continuity
        console.log('🔄 Recalculating subsequent years for continuity...\n');
        
        const financialYears = ['2023-2024', '2024-2025'];
        
        for (const asset of assets) {
            let previousClosingWdv = null;
            
            // Get first year closing WDV
            const firstYearResult = await getData(`
                SELECT closing_wdv FROM asset_yearly_data 
                WHERE asset_id = ? AND year_range = '2022-2023'
            `, [asset.id]);
            
            if (firstYearResult.length > 0) {
                previousClosingWdv = firstYearResult[0].closing_wdv;
            }
            
            for (const fy of financialYears) {
                if (previousClosingWdv !== null) {
                    // Calculate with proper opening WDV
                    const yearlyData = calculateFirstYearDepreciation({
                        ...asset,
                        wdv_of_adoption_date: previousClosingWdv  // Use previous closing as opening
                    }, fy);
                    
                    // Update the yearly data
                    await runQuery(`
                        UPDATE asset_yearly_data 
                        SET opening_wdv = ?, use_days = ?, depreciation_amount = ?, closing_wdv = ?
                        WHERE asset_id = ? AND year_range = ?
                    `, [
                        previousClosingWdv, yearlyData.useDays,
                        yearlyData.depreciationAmount, yearlyData.closingWdv,
                        asset.id, fy
                    ]);
                    
                    previousClosingWdv = yearlyData.closingWdv;
                }
            }
        }

        // Verify the corrections
        console.log('🎯 VERIFICATION - Updated First Year Depreciation:');
        const verificationData = await getData(`
            SELECT 
                a.record_id,
                a.asset_particulars,
                a.wdv_of_adoption_date,
                a.gross_amount,
                ayd.opening_wdv,
                ayd.depreciation_amount,
                ayd.closing_wdv
            FROM assets a
            JOIN asset_yearly_data ayd ON a.id = ayd.asset_id AND ayd.year_range = '2022-2023'
            WHERE a.company_id = 'c1001'
            ORDER BY a.record_id
        `);

        verificationData.forEach(row => {
            const adoptionWdv = row.wdv_of_adoption_date || 'N/A';
            console.log(`   📋 ${row.record_id}:`);
            console.log(`      Gross Amount: ₹${row.gross_amount.toLocaleString()}`);
            console.log(`      Adoption WDV: ${adoptionWdv === 'N/A' ? 'N/A' : '₹' + adoptionWdv.toLocaleString()}`);
            console.log(`      Opening WDV: ₹${row.opening_wdv.toLocaleString()}`);
            console.log(`      Depreciation: ₹${row.depreciation_amount.toLocaleString()}`);
            console.log('');
        });

        console.log('✅ First year depreciation calculation fixed successfully!');
        console.log('💡 Assets with Adoption Date WDV now use that value for first year depreciation.');

    } catch (error) {
        console.error('❌ Error fixing first year depreciation:', error);
        throw error;
    }
}

// Run the fix
fixFirstYearDepreciation().then(() => {
    console.log('\n🏁 Script completed successfully');
}).catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
});
