@echo off
echo =============================================
echo FAR SIGHTED - QUICK API TEST
echo Professional Advisory Services - CA
echo =============================================
echo.

echo 🔍 Quick test of companies API...
echo.

echo 📡 Testing backend health...
curl -s http://localhost:8090/api/health
echo.
echo ========================

echo.
echo 🏢 Testing companies API...
curl -s -w "Response Code: %%{http_code}\n" http://localhost:8090/api/companies
echo.
echo ========================

echo.
echo 📊 Raw companies API response:
curl -s http://localhost:8090/api/companies
echo.
echo ========================

echo.
echo 🔄 Testing migration status...
curl -s http://localhost:8090/api/migration-status
echo.
echo ========================

echo.
echo 💡 If you see:
echo    • "[]" = Empty database, need to add companies
echo    • "migration required" = Need to run: cd backend && npm run migrate  
echo    • Company data = Backend working, check frontend
echo    • No response = Backend not running
echo.

pause
