@echo off
setlocal enabledelayedexpansion

echo ================================================
echo FAR SIGHTED v2.0 - FINAL VERIFICATION REPORT
echo Multi-Database Architecture Validation
echo Professional Advisory Services - CA
echo ================================================
echo.

REM Navigate to project directory
cd /d "E:\Projects\FAR Sighted"

echo 🔍 Running comprehensive system verification...
echo.
echo ==========================================
echo 📁 FILE STRUCTURE VERIFICATION
echo ==========================================

echo.
echo 🏗️  Core System Files:
set "core_files_complete=true"

if exist "package.json" (
    echo    ✅ Frontend package.json
) else (
    echo    ❌ Frontend package.json MISSING
    set "core_files_complete=false"
)

if exist "backend\package.json" (
    echo    ✅ Backend package.json
) else (
    echo    ❌ Backend package.json MISSING
    set "core_files_complete=false"
)

if exist "backend\server.js" (
    echo    ✅ Multi-Database backend server
) else (
    echo    ❌ Multi-Database backend server MISSING
    set "core_files_complete=false"
)

if exist "lib\api.ts" (
    echo    ✅ Updated API client
) else (
    echo    ❌ Updated API client MISSING
    set "core_files_complete=false"
)

echo.
echo 🛠️  Multi-Database Components:
set "multidb_complete=true"

if exist "backend\services\database-new.js" (
    echo    ✅ Multi-Database service
) else (
    echo    ❌ Multi-Database service MISSING
    set "multidb_complete=false"
)

if exist "backend\services\database-manager\DatabaseManager.js" (
    echo    ✅ Database manager
) else (
    echo    ❌ Database manager MISSING
    set "multidb_complete=false"
)

if exist "backend\routes\companies-new.js" (
    echo    ✅ Updated company routes
) else (
    echo    ❌ Updated company routes MISSING
    set "multidb_complete=false"
)

if exist "backend\routes\assets-new.js" (
    echo    ✅ Updated asset routes
) else (
    echo    ❌ Updated asset routes MISSING
    set "multidb_complete=false"
)

if exist "backend\scripts\migrate-database.js" (
    echo    ✅ Migration script
) else (
    echo    ❌ Migration script MISSING
    set "multidb_complete=false"
)

echo.
echo 🎯 Management Scripts:
set "scripts_complete=true"

if exist "restart-far-app.bat" (
    echo    ✅ restart-far-app.bat
) else (
    echo    ❌ restart-far-app.bat MISSING
    set "scripts_complete=false"
)

if exist "kill-all-processes.bat" (
    echo    ✅ kill-all-processes.bat
) else (
    echo    ❌ kill-all-processes.bat MISSING
    set "scripts_complete=false"
)

if exist "start-far-app.bat" (
    echo    ✅ start-far-app.bat
) else (
    echo    ❌ start-far-app.bat MISSING
    set "scripts_complete=false"
)

if exist "migrate-database.bat" (
    echo    ✅ migrate-database.bat
) else (
    echo    ❌ migrate-database.bat MISSING
    set "scripts_complete=false"
)

if exist "check-status.bat" (
    echo    ✅ check-status.bat
) else (
    echo    ❌ check-status.bat MISSING
    set "scripts_complete=false"
)

if exist "update-system.bat" (
    echo    ✅ update-system.bat
) else (
    echo    ❌ update-system.bat MISSING
    set "scripts_complete=false"
)

echo.
echo 📚 Documentation Files:
set "docs_complete=true"

if exist "README.md" (
    echo    ✅ README.md (Updated for v2.0)
) else (
    echo    ❌ README.md MISSING
    set "docs_complete=false"
)

if exist "MULTI_DATABASE_IMPLEMENTATION.md" (
    echo    ✅ MULTI_DATABASE_IMPLEMENTATION.md
) else (
    echo    ❌ MULTI_DATABASE_IMPLEMENTATION.md MISSING
    set "docs_complete=false"
)

if exist "MIGRATION_DEPLOYMENT_GUIDE.md" (
    echo    ✅ MIGRATION_DEPLOYMENT_GUIDE.md
) else (
    echo    ❌ MIGRATION_DEPLOYMENT_GUIDE.md MISSING
    set "docs_complete=false"
)

if exist "BATCH_SCRIPTS_GUIDE.md" (
    echo    ✅ BATCH_SCRIPTS_GUIDE.md
) else (
    echo    ❌ BATCH_SCRIPTS_GUIDE.md MISSING
    set "docs_complete=false"
)

if exist "PRODUCTION_DEPLOYMENT_GUIDE.md" (
    echo    ✅ PRODUCTION_DEPLOYMENT_GUIDE.md
) else (
    echo    ❌ PRODUCTION_DEPLOYMENT_GUIDE.md MISSING
    set "docs_complete=false"
)

echo.
echo ==========================================
echo 🔧 CONFIGURATION VERIFICATION
echo ==========================================

echo.
echo 🌐 Environment Configuration:
if exist "backend\.env" (
    echo    ✅ Backend environment file (Updated for Multi-DB)
) else (
    echo    ❌ Backend environment file MISSING
)

if exist ".env.local" (
    echo    ✅ Frontend environment file (Updated)
) else (
    echo    ❌ Frontend environment file MISSING
)

echo.
echo 📦 Dependencies Check:
if exist "node_modules\" (
    echo    ✅ Frontend dependencies installed
) else (
    echo    ⚠️  Frontend dependencies not installed
)

if exist "backend\node_modules\" (
    echo    ✅ Backend dependencies installed
) else (
    echo    ⚠️  Backend dependencies not installed
)

echo.
echo ==========================================
echo 🗄️  DATABASE STRUCTURE VERIFICATION
echo ==========================================

echo.
echo 📊 Database Architecture:
if exist "backend\database\far_sighted.db" (
    echo    ✅ Original database found (Available for migration)
) else (
    echo    ℹ️  Original database not found (Clean installation)
)

if exist "backend\database\master.db" (
    echo    ✅ Master database exists (Multi-DB structure)
    set "master_db_exists=true"
) else (
    echo    ⚠️  Master database not found (Migration may be needed)
    set "master_db_exists=false"
)

if exist "backend\database\companies\" (
    echo    ✅ Company databases directory exists
    set "company_dirs=0"
    for /d %%i in ("backend\database\companies\*") do (
        set /a "company_dirs+=1"
        echo       📁 Company: %%~nxi
    )
    echo       📊 Total company databases: !company_dirs!
) else (
    echo    ⚠️  Company databases directory not found
    set "company_dirs=0"
)

echo.
echo ==========================================
echo 🌐 SERVICE STATUS VERIFICATION
echo ==========================================

echo.
echo 🔍 Checking running services...

REM Check frontend port 9090
netstat -an 2>nul | find ":9090" | find "LISTENING" >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Frontend service running (Port 9090)
    set "frontend_running=true"
) else (
    echo    ❌ Frontend service not running (Port 9090 available)
    set "frontend_running=false"
)

REM Check backend port 3001
netstat -an 2>nul | find ":3001" | find "LISTENING" >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Backend service running (Port 3001)
    set "backend_running=true"
) else (
    echo    ❌ Backend service not running (Port 3001 available)
    set "backend_running=false"
)

echo.
echo 🏥 Backend Health Check:
if "%backend_running%"=="true" (
    echo    🔍 Testing backend health...
    curl -s http://localhost:3001/api/health >nul 2>&1
    if %errorlevel% == 0 (
        echo    ✅ Backend health check PASSED
        
        echo    📊 Getting system information...
        curl -s http://localhost:3001/api/system-info 2>nul | node -e "
        try {
            const data = JSON.parse(require('fs').readFileSync(0));
            console.log('       ✅ Version:', data.version);
            console.log('       ✅ Structure:', data.databaseStructure);
            console.log('       ✅ Companies:', data.companies.total);
        } catch(e) {
            console.log('       ⚠️  Unable to parse system info');
        }" 2>nul
        
        echo    🔄 Getting migration status...
        curl -s http://localhost:3001/api/migration-status 2>nul | node -e "
        try {
            const data = JSON.parse(require('fs').readFileSync(0));
            console.log('       ✅ System Ready:', data.systemReady ? 'Yes' : 'No');
            console.log('       📋 Migration Needed:', data.needsMigration ? 'Yes' : 'No');
        } catch(e) {
            console.log('       ⚠️  Unable to parse migration status');
        }" 2>nul
        
        set "backend_healthy=true"
    ) else (
        echo    ❌ Backend health check FAILED
        set "backend_healthy=false"
    )
) else (
    echo    ⚠️  Backend not running - cannot perform health check
    set "backend_healthy=false"
)

echo.
echo ==========================================
echo 📋 SYSTEM READINESS ASSESSMENT
echo ==========================================

echo.
echo 🎯 Component Status Summary:
echo    • Core Files: %core_files_complete%
echo    • Multi-DB Components: %multidb_complete%
echo    • Management Scripts: %scripts_complete%
echo    • Documentation: %docs_complete%
echo    • Services Running: Frontend=%frontend_running%, Backend=%backend_running%
echo    • Backend Health: %backend_healthy%
echo    • Master Database: %master_db_exists%
echo    • Company Databases: !company_dirs! found

echo.
echo 🏆 Overall System Status:
set "system_ready=true"

if "%core_files_complete%"=="false" (
    set "system_ready=false"
)
if "%multidb_complete%"=="false" (
    set "system_ready=false"
)
if "%scripts_complete%"=="false" (
    set "system_ready=false"
)

if "%system_ready%"=="true" (
    echo    🟢 SYSTEM READY - All components installed correctly
) else (
    echo    🔴 SYSTEM NOT READY - Missing critical components
)

echo.
echo 📊 Service Status:
if "%frontend_running%"=="true" (
    if "%backend_running%"=="true" (
        if "%backend_healthy%"=="true" (
            echo    🟢 SERVICES OPERATIONAL - All services running and healthy
        ) else (
            echo    🟡 SERVICES PARTIAL - Running but health check failed
        )
    ) else (
        echo    🟡 SERVICES PARTIAL - Frontend running, backend down
    )
) else (
    if "%backend_running%"=="true" (
        echo    🟡 SERVICES PARTIAL - Backend running, frontend down
    ) else (
        echo    🔴 SERVICES DOWN - No services currently running
    )
)

echo.
echo 🗄️  Database Status:
if "%master_db_exists%"=="true" (
    if !company_dirs! GTR 0 (
        echo    🟢 DATABASE READY - Multi-Database structure operational
    ) else (
        echo    🟡 DATABASE PARTIAL - Master exists but no companies
    )
) else (
    if exist "backend\database\far_sighted.db" (
        echo    🟡 MIGRATION REQUIRED - Old database found, needs migration
    ) else (
        echo    🟠 DATABASE NEW - Fresh installation, no existing data
    )
)

echo.
echo ==========================================
echo 💡 RECOMMENDED ACTIONS
echo ==========================================

echo.
if "%system_ready%"=="false" (
    echo 🚨 CRITICAL: Missing system components
    echo    • Run: update-system.bat
    echo    • This will install all missing components
    echo.
)

if "%frontend_running%"=="false" (
    if "%backend_running%"=="false" (
        echo 🚀 START SERVICES:
        echo    • Run: restart-far-app.bat
        echo    • Or: start-far-app.bat
        echo.
    )
)

if "%master_db_exists%"=="false" (
    if exist "backend\database\far_sighted.db" (
        echo 🔄 MIGRATION REQUIRED:
        echo    • Run: migrate-database.bat
        echo    • This will convert to Multi-Database structure
        echo.
    )
)

if "%system_ready%"=="true" (
    if "%frontend_running%"=="true" (
        if "%backend_running%"=="true" (
            if "%backend_healthy%"=="true" (
                echo ✅ SYSTEM FULLY OPERATIONAL!
                echo    • Access: http://localhost:9090
                echo    • All components working correctly
                echo    • Ready for professional use
                echo.
            )
        )
    )
)

echo ==========================================
echo 🔗 QUICK ACCESS LINKS
echo ==========================================
echo.
echo 🌐 Application URLs:
echo    • Frontend: http://localhost:9090
echo    • Backend API: http://localhost:3001/api
echo    • Health Check: http://localhost:3001/api/health
echo    • Migration Status: http://localhost:3001/api/migration-status
echo    • System Info: http://localhost:3001/api/system-info
echo.
echo 🛠️  Management Commands:
echo    • Check Status: check-status.bat
echo    • Start System: start-far-app.bat
echo    • Restart System: restart-far-app.bat
echo    • Stop All: kill-all-processes.bat
echo    • Run Migration: migrate-database.bat
echo    • Full Update: update-system.bat
echo.
echo 📚 Documentation:
echo    • System Guide: README.md
echo    • Scripts Guide: BATCH_SCRIPTS_GUIDE.md
echo    • Technical Details: MULTI_DATABASE_IMPLEMENTATION.md
echo    • Migration Guide: MIGRATION_DEPLOYMENT_GUIDE.md
echo    • Production Guide: PRODUCTION_DEPLOYMENT_GUIDE.md

echo.
echo ================================================
echo ✨ FAR SIGHTED v2.0 VERIFICATION COMPLETE
echo ================================================
echo.
echo 🏆 Professional Asset Management System
echo    Multi-Database Architecture for Enhanced Security
echo    Professional Advisory Services - CA
echo.
echo 📅 Verification Date: %date% %time%
echo 💻 System: %computername%
echo 👤 User: %username%
echo.

if "%system_ready%"=="true" (
    echo 🎯 RESULT: ✅ SYSTEM VERIFICATION PASSED
    echo    FAR Sighted v2.0 is ready for professional use!
) else (
    echo 🎯 RESULT: ⚠️  SYSTEM REQUIRES ATTENTION
    echo    Please follow the recommended actions above.
)

echo.
echo Press any key to close this verification report...
pause >nul