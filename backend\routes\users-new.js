import express from 'express';
import bcrypt from 'bcrypt';
import dbService from '../services/database-new.js';

const router = express.Router();

// Debug endpoint to check master database users
router.get('/debug/master-users', async (req, res) => {
    try {
        await dbService.ensureInitialized();
        const dbManager = dbService.getDatabaseManager();

        const users = await dbManager.getAllMasterQuery(
            'SELECT id, username, role, created_at FROM users'
        );

        res.json({
            message: 'Master database users',
            users: users,
            count: users.length
        });
    } catch (error) {
        console.error('Error fetching master users:', error);
        res.status(500).json({ error: 'Failed to fetch master users', details: error.message });
    }
});

// Debug endpoint to reset admin password
router.post('/debug/reset-admin-password', async (req, res) => {
    try {
        await dbService.ensureInitialized();
        const dbManager = dbService.getDatabaseManager();

        // Generate new hash for 'admin123'
        const hashedPassword = await bcrypt.hash('admin123', 10);

        // Update admin user password
        await dbManager.runMasterQuery(
            'UPDATE users SET password_hash = ?, must_change_password = 0 WHERE username = ?',
            [hashedPassword, 'admin']
        );

        res.json({
            message: 'Admin password reset to admin123',
            success: true
        });
    } catch (error) {
        console.error('Error resetting admin password:', error);
        res.status(500).json({ error: 'Failed to reset admin password', details: error.message });
    }
});

// Global admin login (no company required)
router.post('/login', async (req, res) => {
    try {
        const { username, password, companyId } = req.body;

        // If no companyId provided, try global admin login
        if (!companyId) {
            return await handleGlobalAdminLogin(req, res, username, password);
        }

        // Otherwise, proceed with company-specific login
        return await handleCompanyLogin(req, res, username, password, companyId);
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: 'An error occurred during login'
        });
    }
});

// Global admin login handler
async function handleGlobalAdminLogin(req, res, username, password) {
    if (!username || !password) {
        return res.status(400).json({
            error: 'Missing credentials',
            message: 'Username and password are required'
        });
    }

    await dbService.ensureInitialized();
    const dbManager = dbService.getDatabaseManager();

    // Check for global admin user in master database
    const users = await dbManager.getAllMasterQuery(
        'SELECT * FROM users WHERE username = ?',
        [username]
    );

    const user = users.length > 0 ? users[0] : null;

    if (!user) {
        return res.status(401).json({
            error: 'Invalid credentials',
            message: 'Username or password is incorrect'
        });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);

    if (!isValidPassword) {
        return res.status(401).json({
            error: 'Invalid credentials',
            message: 'Username or password is incorrect'
        });
    }

    // Only allow admin users for global login
    if (user.role !== 'Admin') {
        return res.status(403).json({
            error: 'Access denied',
            message: 'Only administrators can login without selecting a company'
        });
    }

    // Return user info (without password)
    const userInfo = {
        id: user.id,
        username: user.username,
        role: user.role,
        mustChangePassword: user.must_change_password || false,
        hasSavedRecoveryKey: user.has_saved_recovery_key || false
    };

    // Add to global audit log
    await dbManager.runMasterQuery(
        `INSERT INTO global_audit_logs (user_id, username, action, details)
         VALUES (?, ?, ?, ?)`,
        [user.id, user.username, 'GLOBAL_ADMIN_LOGIN', `Global admin ${user.username} logged in`]
    );

    res.json({
        message: 'Login successful',
        user: userInfo,
        showAdminWelcome: true, // Always show admin welcome for global login
        mustChangePassword: user.must_change_password || false
    });
}

// Company-specific login handler
async function handleCompanyLogin(req, res, username, password, companyId) {
    if (!username || !password || !companyId) {
        return res.status(400).json({
            error: 'Missing credentials',
            message: 'Username, password, and company selection are required'
        });
    }

    // Get company database service
    const companyDbService = await dbService.getDatabaseManager().getCompanyDatabase(companyId);

    // Get user from company-specific database
    const user = await companyDbService.get(
        'SELECT * FROM users WHERE username = ?',
        [username]
    );

    if (!user) {
        return res.status(401).json({
            error: 'Invalid credentials',
            message: 'Username or password is incorrect for this company'
        });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);

    if (!isValidPassword) {
        return res.status(401).json({
            error: 'Invalid credentials',
            message: 'Username or password is incorrect'
        });
    }

    // Check if this is the only admin in this company (for admin welcome modal)
    const adminCount = await companyDbService.all(
        'SELECT COUNT(*) as count FROM users WHERE role = ?',
        ['Admin']
    );

    const showAdminWelcome = user.role === 'Admin' && adminCount[0]?.count < 2;

    // Return user info (without password)
    const userInfo = {
        id: user.id,
        username: user.username,
        role: user.role,
        companyId: companyId,
        mustChangePassword: user.must_change_password || false,
        hasSavedRecoveryKey: user.has_saved_recovery_key || false
    };

    // Add to company-specific audit log
    await companyDbService.run(
        `INSERT INTO audit_logs (id, user_id, username, action, details)
         VALUES (?, ?, ?, ?, ?)`,
        [`log_${Date.now()}`, user.id, user.username, 'USER_LOGIN', `User ${user.username} logged in`]
    );

    res.json({
        message: 'Login successful',
        user: userInfo,
        showAdminWelcome,
        mustChangePassword: user.must_change_password || false
    });
}

// Get single user by ID (cross-company lookup)
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        console.log(`🔍 Fetching user by ID: ${id}`);

        await dbService.ensureInitialized();
        const dbManager = dbService.getDatabaseManager();

        // Extract company ID from user ID format: companyId-username-timestamp
        const parts = id.split('-');
        if (parts.length < 3) {
            console.error(`❌ Invalid user ID format: ${id} (expected: companyId-username-timestamp)`);
            return res.status(400).json({
                error: 'Invalid user ID format',
                message: 'User ID must be in format: companyId-username-timestamp',
                received: id,
                expectedFormat: 'companyId-username-timestamp'
            });
        }

        const companyId = parts[0];
        console.log(`🔍 Extracted company ID: ${companyId}`);

        // Get company database service
        const companyDbService = await dbManager.getCompanyDatabase(companyId);
        
        if (!companyDbService) {
            console.error(`❌ Company database not found for: ${companyId}`);
            return res.status(404).json({
                error: 'Company not found',
                message: `Company database not accessible: ${companyId}`
            });
        }

        // Get user from company-specific database
        const user = await companyDbService.get(
            'SELECT id, username, role, is_active, must_change_password, has_saved_recovery_key, created_at FROM users WHERE id = ?',
            [id]
        );

        if (!user) {
            console.log(`❌ User not found: ${id}`);
            return res.status(404).json({ 
                error: 'User not found',
                message: `User with ID ${id} not found in company ${companyId}`
            });
        }

        console.log(`✅ User found: ${user.username} (${user.role})`);
        res.json(user);
    } catch (error) {
        console.error('❌ Error fetching user:', error);
        res.status(500).json({ 
            error: 'Failed to fetch user',
            message: error.message || 'Unknown error occurred'
        });
    }
});

// Get all users for a specific company (company-specific database)
router.get('/', async (req, res) => {
    try {
        const { companyId } = req.query;

        if (!companyId) {
            return res.status(400).json({
                error: 'Missing company ID',
                message: 'Company ID is required to fetch users'
            });
        }

        // Get company database service
        const companyDbService = await dbService.getDatabaseManager().getCompanyDatabase(companyId);

        // Get users from company-specific database
        const users = await companyDbService.all(
            'SELECT id, username, role, is_active, created_at FROM users ORDER BY username'
        );

        res.json(users);
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ error: 'Failed to fetch users' });
    }
});



// Create new user (company-specific)
router.post('/', async (req, res) => {
    try {
        console.log('📝 Creating user with data:', req.body);
        const { username, password, role, companyId } = req.body;

        // Validate required fields
        if (!username || !password || !role || !companyId) {
            console.error('❌ Missing required fields:', { username: !!username, password: !!password, role: !!role, companyId: !!companyId });
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'Username, password, role, and company ID are required',
                received: { username: !!username, password: !!password, role: !!role, companyId: !!companyId }
            });
        }

        // Validate role
        const validRoles = ['Admin', 'Data Entry', 'Report Viewer'];
        if (!validRoles.includes(role)) {
            console.error('❌ Invalid role:', role);
            return res.status(400).json({
                error: 'Invalid role',
                message: `Role must be one of: ${validRoles.join(', ')}`
            });
        }

        console.log('🔄 Ensuring database service is initialized...');
        await dbService.ensureInitialized();

        console.log(`🔄 Getting company database for: ${companyId}`);
        // Get company database service
        const companyDbService = await dbService.getDatabaseManager().getCompanyDatabase(companyId);
        
        if (!companyDbService) {
            console.error('❌ Failed to get company database service');
            return res.status(500).json({
                error: 'Company database not available',
                message: `Unable to access database for company: ${companyId}`
            });
        }

        console.log('🔄 Checking for existing username...');
        // Check if username already exists in this company
        const existingUser = await companyDbService.get(
            'SELECT id FROM users WHERE username = ?',
            [username]
        );

        if (existingUser) {
            console.error('❌ Username already exists:', username);
            return res.status(409).json({
                error: 'Username already exists',
                message: 'Please choose a different username for this company'
            });
        }

        console.log('🔄 Hashing password...');
        // Hash password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        console.log('🔄 Generating recovery key...');
        // Generate recovery key
        const recoveryKey = `rk_${companyId}_${username}_${Math.random().toString(36).substring(2, 15)}`;
        const recoveryKeyHash = await bcrypt.hash(recoveryKey, 10);

        // Create user ID
        const userId = `${companyId}-${username}-${Date.now()}`;
        console.log('🔄 Creating user with ID:', userId);

        // Create user in company-specific database
        await companyDbService.run(
            `INSERT INTO users (id, username, password_hash, role, recovery_key_hash, has_saved_recovery_key, is_active, must_change_password)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [userId, username, hashedPassword, role, recoveryKeyHash, 0, 1, 1]
        );

        console.log('✅ User created successfully, adding audit log...');
        // Add to company-specific audit log
        try {
            await companyDbService.run(
                `INSERT INTO audit_logs (user_id, username, action, details, timestamp)
                 VALUES (?, ?, ?, ?, ?)`,
                [userId, username, 'USER_CREATED', `User ${username} created with role: ${role}`, new Date().toISOString()]
            );
            console.log('✅ Audit log added successfully');
        } catch (auditError) {
            console.warn('⚠️ Failed to add audit log (but user was created):', auditError.message);
            // Continue despite audit log failure
        }

        console.log('✅ User creation completed successfully');
        res.status(201).json({
            message: 'User created successfully',
            user: {
                id: userId,
                username,
                role
            },
            recoveryKey: recoveryKey
        });
    } catch (error) {
        console.error('❌ Error creating user:', error);
        console.error('❌ Error stack:', error.stack);
        res.status(500).json({ 
            error: 'Failed to create user',
            message: error.message || 'Unknown error occurred',
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

// Update user
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { username, role, password } = req.body;
        
        // Get existing user
        const existingUser = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT * FROM users WHERE id = ?',
            [id]
        );

        if (!existingUser) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Validate role if provided
        if (role) {
            const validRoles = ['Admin', 'Data Entry', 'Report Viewer'];
            if (!validRoles.includes(role)) {
                return res.status(400).json({ 
                    error: 'Invalid role',
                    message: `Role must be one of: ${validRoles.join(', ')}`
                });
            }
        }

        // Check if new username already exists (if username is being changed)
        if (username && username !== existingUser.username) {
            const duplicateUser = await dbService.getDatabaseManager().getMasterQuery(
                'SELECT id FROM users WHERE username = ? AND id != ?',
                [username, id]
            );

            if (duplicateUser) {
                return res.status(409).json({ 
                    error: 'Username already exists',
                    message: 'Please choose a different username'
                });
            }
        }

        // Prepare update data
        const updateData = {
            username: username || existingUser.username,
            role: role || existingUser.role
        };

        // Hash new password if provided
        let newPassword = existingUser.password;
        if (password) {
            const saltRounds = 10;
            newPassword = await bcrypt.hash(password, saltRounds);
        }

        // Update user
        await dbService.getDatabaseManager().runMasterQuery(
            'UPDATE users SET username = ?, role = ?, password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [updateData.username, updateData.role, newPassword, id]
        );

        // Add to global audit log
        const changes = [];
        if (username && username !== existingUser.username) changes.push(`username: ${existingUser.username} → ${username}`);
        if (role && role !== existingUser.role) changes.push(`role: ${existingUser.role} → ${role}`);
        if (password) changes.push('password updated');

        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [id, req.user?.username || 'system', 'UPDATE_USER', `Updated user ${updateData.username}: ${changes.join(', ')}`]
        );

        res.json({
            message: 'User updated successfully',
            user: {
                id,
                username: updateData.username,
                role: updateData.role
            }
        });
    } catch (error) {
        console.error('Error updating user:', error);
        res.status(500).json({ error: 'Failed to update user' });
    }
});

// Delete user
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        console.log(`🗑️ Deleting user by ID: ${id}`);

        await dbService.ensureInitialized();
        const dbManager = dbService.getDatabaseManager();

        // Extract company ID from user ID format: companyId-username-timestamp
        const parts = id.split('-');
        if (parts.length < 3) {
            console.error(`❌ Invalid user ID format: ${id} (expected: companyId-username-timestamp)`);
            return res.status(400).json({
                error: 'Invalid user ID format',
                message: 'User ID must be in format: companyId-username-timestamp',
                received: id,
                expectedFormat: 'companyId-username-timestamp'
            });
        }

        const companyId = parts[0];
        console.log(`🔍 Extracted company ID: ${companyId}`);

        // Get company database service
        const companyDbService = await dbManager.getCompanyDatabase(companyId);
        
        if (!companyDbService) {
            console.error(`❌ Company database not found for: ${companyId}`);
            return res.status(404).json({
                error: 'Company not found',
                message: `Company database not accessible: ${companyId}`
            });
        }

        // Get existing user from company-specific database
        const existingUser = await companyDbService.get(
            'SELECT * FROM users WHERE id = ?',
            [id]
        );

        if (!existingUser) {
            console.log(`❌ User not found: ${id}`);
            return res.status(404).json({ 
                error: 'User not found',
                message: `User with ID ${id} not found in company ${companyId}`
            });
        }

        // Prevent deletion of the last admin user in the company
        if (existingUser.role === 'Admin') {
            const adminCount = await companyDbService.get(
                'SELECT COUNT(*) as count FROM users WHERE role = ? AND is_active = 1',
                ['Admin']
            );

            if (adminCount.count <= 1) {
                return res.status(400).json({ 
                    error: 'Cannot delete last admin user',
                    message: 'At least one admin user must exist in the company'
                });
            }
        }

        // Delete user from company-specific database
        const result = await companyDbService.run(
            'DELETE FROM users WHERE id = ?',
            [id]
        );

        if (result.changes === 0) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Add to global audit log
        await dbManager.runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [id, req.user?.username || 'system', 'DELETE_USER', `Deleted user: ${existingUser.username} (${existingUser.role}) from company ${companyId}`]
        );

        console.log(`✅ User deleted successfully: ${existingUser.username} (${existingUser.role})`);
        res.json({ 
            message: 'User deleted successfully',
            deletedUser: {
                id,
                username: existingUser.username,
                role: existingUser.role
            }
        });
    } catch (error) {
        console.error('❌ Error deleting user:', error);
        res.status(500).json({ 
            error: 'Failed to delete user',
            message: error.message || 'Unknown error occurred'
        });
    }
});

// Change password (for current user)
router.post('/change-password', async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        const userId = req.user?.id; // This would come from JWT middleware in production
        
        if (!userId) {
            return res.status(401).json({ error: 'User not authenticated' });
        }

        if (!currentPassword || !newPassword) {
            return res.status(400).json({ 
                error: 'Missing required fields',
                message: 'Current password and new password are required'
            });
        }

        // Get user
        const user = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT * FROM users WHERE id = ?',
            [userId]
        );

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Verify current password
        const isValidPassword = await bcrypt.compare(currentPassword, user.password);
        
        if (!isValidPassword) {
            return res.status(401).json({ 
                error: 'Invalid current password',
                message: 'Current password is incorrect'
            });
        }

        // Hash new password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

        // Update password
        await dbService.getDatabaseManager().runMasterQuery(
            'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [hashedPassword, userId]
        );

        // Add to global audit log
        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [userId, user.username, 'CHANGE_PASSWORD', `User ${user.username} changed their password`]
        );

        res.json({ message: 'Password changed successfully' });
    } catch (error) {
        console.error('Error changing password:', error);
        res.status(500).json({ error: 'Failed to change password' });
    }
});

// Get user profile (current user)
router.get('/profile', async (req, res) => {
    try {
        const userId = req.user?.id; // This would come from JWT middleware in production
        
        if (!userId) {
            return res.status(401).json({ error: 'User not authenticated' });
        }

        const user = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT id, username, role, created_at FROM users WHERE id = ?',
            [userId]
        );

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        res.json(user);
    } catch (error) {
        console.error('Error fetching user profile:', error);
        res.status(500).json({ error: 'Failed to fetch user profile' });
    }
});

// Reset user password (admin only)
router.post('/:id/reset-password', async (req, res) => {
    try {
        const { id } = req.params;
        const { newPassword } = req.body;
        const adminUser = req.user; // This would come from JWT middleware
        
        // Check if current user is admin (in production, this would be enforced by middleware)
        if (!adminUser || adminUser.role !== 'Admin') {
            return res.status(403).json({ 
                error: 'Insufficient permissions',
                message: 'Only administrators can reset user passwords'
            });
        }

        if (!newPassword) {
            return res.status(400).json({ 
                error: 'Missing required field',
                message: 'New password is required'
            });
        }

        // Get target user
        const user = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT * FROM users WHERE id = ?',
            [id]
        );

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Hash new password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

        // Update password
        await dbService.getDatabaseManager().runMasterQuery(
            'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [hashedPassword, id]
        );

        // Add to global audit log
        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [id, adminUser.username, 'RESET_PASSWORD', `Admin ${adminUser.username} reset password for user ${user.username}`]
        );

        res.json({ 
            message: 'Password reset successfully',
            user: {
                id,
                username: user.username,
                role: user.role
            }
        });
    } catch (error) {
        console.error('Error resetting password:', error);
        res.status(500).json({ error: 'Failed to reset password' });
    }
});

// Change password endpoint
router.post('/change-password', async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;

        if (!currentPassword || !newPassword) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'Current password and new password are required'
            });
        }

        // For now, we'll use a simple approach - in a real app, you'd get the user from session/token
        // This is a simplified implementation for the demo
        const users = await dbService.getDatabaseManager().getAllMasterQuery(
            'SELECT * FROM users WHERE username = ?',
            ['admin'] // Assuming we're changing the admin password
        );

        if (users.length === 0) {
            return res.status(404).json({
                error: 'User not found',
                message: 'User not found'
            });
        }

        const user = users[0];

        // Verify current password
        const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);

        if (!isValidPassword) {
            return res.status(401).json({
                error: 'Invalid current password',
                message: 'Current password is incorrect'
            });
        }

        // Hash new password
        const hashedNewPassword = await bcrypt.hash(newPassword, 10);

        // Generate new recovery key
        const recoveryKey = `rk_${user.id}_${Math.random().toString(36).substring(2, 15)}`;
        const recoveryKeyHash = await bcrypt.hash(recoveryKey, 10);

        // Update password and recovery key, clear must_change_password flag
        await dbService.getDatabaseManager().runMasterQuery(
            `UPDATE users SET
                password_hash = ?,
                recovery_key_hash = ?,
                has_saved_recovery_key = FALSE,
                must_change_password = FALSE,
                updated_at = CURRENT_TIMESTAMP
             WHERE id = ?`,
            [hashedNewPassword, recoveryKeyHash, user.id]
        );

        // Add to audit log
        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [user.id, user.username, 'PASSWORD_CHANGE', `Password changed for user ${user.username}`]
        );

        res.json({
            message: 'Password changed successfully',
            recoveryKey: recoveryKey
        });

    } catch (error) {
        console.error('Password change error:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: 'An error occurred while changing password'
        });
    }
});

// Confirm recovery key saved
router.post('/:id/confirm-recovery-key', async (req, res) => {
    try {
        const { id } = req.params;
        console.log(`🔐 Confirming recovery key for user ID: ${id}`);

        await dbService.ensureInitialized();
        const dbManager = dbService.getDatabaseManager();

        // Extract company ID from user ID format: companyId-username-timestamp
        const parts = id.split('-');
        if (parts.length < 3) {
            console.error(`❌ Invalid user ID format: ${id} (expected: companyId-username-timestamp)`);
            return res.status(400).json({
                error: 'Invalid user ID format',
                message: 'User ID must be in format: companyId-username-timestamp',
                received: id,
                expectedFormat: 'companyId-username-timestamp'
            });
        }

        const companyId = parts[0];
        console.log(`🔍 Extracted company ID: ${companyId}`);

        // Get company database service
        const companyDbService = await dbManager.getCompanyDatabase(companyId);
        
        if (!companyDbService) {
            console.error(`❌ Company database not found for: ${companyId}`);
            return res.status(404).json({
                error: 'Company not found',
                message: `Company database not accessible: ${companyId}`
            });
        }

        // Check if user exists
        const user = await companyDbService.get(
            'SELECT id FROM users WHERE id = ?',
            [id]
        );

        if (!user) {
            console.log(`❌ User not found: ${id}`);
            return res.status(404).json({ 
                error: 'User not found',
                message: `User with ID ${id} not found in company ${companyId}`
            });
        }

        // Update has_saved_recovery_key flag
        await companyDbService.run(
            'UPDATE users SET has_saved_recovery_key = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [1, id]
        );

        // Add to company-specific audit log
        await companyDbService.run(
            `INSERT INTO audit_logs (user_id, username, action, details, timestamp)
             VALUES (?, ?, ?, ?, ?)`,
            [id, 'system', 'RECOVERY_KEY_CONFIRMED', `Recovery key confirmed for user ${id}`, new Date().toISOString()]
        );

        console.log(`✅ Recovery key confirmed for user: ${id}`);
        res.json({ message: 'Recovery key confirmation updated' });
    } catch (error) {
        console.error('❌ Error confirming recovery key:', error);
        res.status(500).json({ 
            error: 'Failed to confirm recovery key',
            message: error.message || 'Unknown error occurred'
        });
    }
});

export default router;