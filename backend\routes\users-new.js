import express from 'express';
import bcrypt from 'bcrypt';
import dbService from '../services/database-new.js';

const router = express.Router();

// Get all users (from master database)
router.get('/', async (req, res) => {
    try {
        const users = await dbService.getUsers();
        
        // Remove password hashes from response
        const safeUsers = users.map(user => ({
            id: user.id,
            username: user.username,
            role: user.role,
            created_at: user.created_at
        }));
        
        res.json(safeUsers);
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ error: 'Failed to fetch users' });
    }
});

// User login
router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        
        if (!username || !password) {
            return res.status(400).json({ 
                error: 'Missing credentials',
                message: 'Username and password are required'
            });
        }

        // Get user from master database
        const user = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT * FROM users WHERE username = ?',
            [username]
        );

        if (!user) {
            return res.status(401).json({ 
                error: 'Invalid credentials',
                message: 'Username or password is incorrect'
            });
        }

        // Verify password
        const isValidPassword = await bcrypt.compare(password, user.password_hash);

        if (!isValidPassword) {
            return res.status(401).json({
                error: 'Invalid credentials',
                message: 'Username or password is incorrect'
            });
        }

        // Check if this is the only admin (for admin welcome modal)
        const adminCount = await dbService.getDatabaseManager().getAllMasterQuery(
            'SELECT COUNT(*) as count FROM users WHERE role = ?',
            ['Admin']
        );

        const showAdminWelcome = user.role === 'Admin' && adminCount[0]?.count < 2;

        // Return user info (without password)
        const userInfo = {
            id: user.id,
            username: user.username,
            role: user.role,
            mustChangePassword: user.must_change_password || false,
            hasSavedRecoveryKey: user.has_saved_recovery_key || false
        };

        // Add to global audit log
        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [user.id, user.username, 'USER_LOGIN', `User ${user.username} logged in`]
        );

        res.json({
            message: 'Login successful',
            user: userInfo,
            showAdminWelcome,
            mustChangePassword: user.must_change_password || false
        });
    } catch (error) {
        console.error('Error during login:', error);
        res.status(500).json({ error: 'Login failed' });
    }
});

// Create new user
router.post('/', async (req, res) => {
    try {
        const { username, password, role } = req.body;
        
        // Validate required fields
        if (!username || !password || !role) {
            return res.status(400).json({ 
                error: 'Missing required fields',
                message: 'Username, password, and role are required'
            });
        }

        // Validate role
        const validRoles = ['Admin', 'Data Entry', 'Report Viewer'];
        if (!validRoles.includes(role)) {
            return res.status(400).json({ 
                error: 'Invalid role',
                message: `Role must be one of: ${validRoles.join(', ')}`
            });
        }

        // Check if username already exists
        const existingUser = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT id FROM users WHERE username = ?',
            [username]
        );

        if (existingUser) {
            return res.status(409).json({ 
                error: 'Username already exists',
                message: 'Please choose a different username'
            });
        }

        // Hash password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Create user
        const userId = await dbService.createUser({
            username,
            password: hashedPassword,
            role
        });

        // Add to global audit log
        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [userId, req.user?.username || 'system', 'CREATE_USER', `Created user: ${username} with role: ${role}`]
        );

        res.status(201).json({
            message: 'User created successfully',
            user: {
                id: userId,
                username,
                role
            }
        });
    } catch (error) {
        console.error('Error creating user:', error);
        res.status(500).json({ error: 'Failed to create user' });
    }
});

// Update user
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { username, role, password } = req.body;
        
        // Get existing user
        const existingUser = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT * FROM users WHERE id = ?',
            [id]
        );

        if (!existingUser) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Validate role if provided
        if (role) {
            const validRoles = ['Admin', 'Data Entry', 'Report Viewer'];
            if (!validRoles.includes(role)) {
                return res.status(400).json({ 
                    error: 'Invalid role',
                    message: `Role must be one of: ${validRoles.join(', ')}`
                });
            }
        }

        // Check if new username already exists (if username is being changed)
        if (username && username !== existingUser.username) {
            const duplicateUser = await dbService.getDatabaseManager().getMasterQuery(
                'SELECT id FROM users WHERE username = ? AND id != ?',
                [username, id]
            );

            if (duplicateUser) {
                return res.status(409).json({ 
                    error: 'Username already exists',
                    message: 'Please choose a different username'
                });
            }
        }

        // Prepare update data
        const updateData = {
            username: username || existingUser.username,
            role: role || existingUser.role
        };

        // Hash new password if provided
        let newPassword = existingUser.password;
        if (password) {
            const saltRounds = 10;
            newPassword = await bcrypt.hash(password, saltRounds);
        }

        // Update user
        await dbService.getDatabaseManager().runMasterQuery(
            'UPDATE users SET username = ?, role = ?, password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [updateData.username, updateData.role, newPassword, id]
        );

        // Add to global audit log
        const changes = [];
        if (username && username !== existingUser.username) changes.push(`username: ${existingUser.username} → ${username}`);
        if (role && role !== existingUser.role) changes.push(`role: ${existingUser.role} → ${role}`);
        if (password) changes.push('password updated');

        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [id, req.user?.username || 'system', 'UPDATE_USER', `Updated user ${updateData.username}: ${changes.join(', ')}`]
        );

        res.json({
            message: 'User updated successfully',
            user: {
                id,
                username: updateData.username,
                role: updateData.role
            }
        });
    } catch (error) {
        console.error('Error updating user:', error);
        res.status(500).json({ error: 'Failed to update user' });
    }
});

// Delete user
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        // Get existing user
        const existingUser = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT * FROM users WHERE id = ?',
            [id]
        );

        if (!existingUser) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Prevent deletion of the last admin user
        if (existingUser.role === 'Admin') {
            const adminCount = await dbService.getDatabaseManager().getMasterQuery(
                'SELECT COUNT(*) as count FROM users WHERE role = ?',
                ['Admin']
            );

            if (adminCount.count <= 1) {
                return res.status(400).json({ 
                    error: 'Cannot delete last admin user',
                    message: 'At least one admin user must exist in the system'
                });
            }
        }

        // Delete user
        const result = await dbService.getDatabaseManager().runMasterQuery(
            'DELETE FROM users WHERE id = ?',
            [id]
        );

        if (result.changes === 0) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Add to global audit log
        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [id, req.user?.username || 'system', 'DELETE_USER', `Deleted user: ${existingUser.username} (${existingUser.role})`]
        );

        res.json({ 
            message: 'User deleted successfully',
            deletedUser: {
                id,
                username: existingUser.username,
                role: existingUser.role
            }
        });
    } catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({ error: 'Failed to delete user' });
    }
});

// Change password (for current user)
router.post('/change-password', async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        const userId = req.user?.id; // This would come from JWT middleware in production
        
        if (!userId) {
            return res.status(401).json({ error: 'User not authenticated' });
        }

        if (!currentPassword || !newPassword) {
            return res.status(400).json({ 
                error: 'Missing required fields',
                message: 'Current password and new password are required'
            });
        }

        // Get user
        const user = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT * FROM users WHERE id = ?',
            [userId]
        );

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Verify current password
        const isValidPassword = await bcrypt.compare(currentPassword, user.password);
        
        if (!isValidPassword) {
            return res.status(401).json({ 
                error: 'Invalid current password',
                message: 'Current password is incorrect'
            });
        }

        // Hash new password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

        // Update password
        await dbService.getDatabaseManager().runMasterQuery(
            'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [hashedPassword, userId]
        );

        // Add to global audit log
        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [userId, user.username, 'CHANGE_PASSWORD', `User ${user.username} changed their password`]
        );

        res.json({ message: 'Password changed successfully' });
    } catch (error) {
        console.error('Error changing password:', error);
        res.status(500).json({ error: 'Failed to change password' });
    }
});

// Get user profile (current user)
router.get('/profile', async (req, res) => {
    try {
        const userId = req.user?.id; // This would come from JWT middleware in production
        
        if (!userId) {
            return res.status(401).json({ error: 'User not authenticated' });
        }

        const user = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT id, username, role, created_at FROM users WHERE id = ?',
            [userId]
        );

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        res.json(user);
    } catch (error) {
        console.error('Error fetching user profile:', error);
        res.status(500).json({ error: 'Failed to fetch user profile' });
    }
});

// Reset user password (admin only)
router.post('/:id/reset-password', async (req, res) => {
    try {
        const { id } = req.params;
        const { newPassword } = req.body;
        const adminUser = req.user; // This would come from JWT middleware
        
        // Check if current user is admin (in production, this would be enforced by middleware)
        if (!adminUser || adminUser.role !== 'Admin') {
            return res.status(403).json({ 
                error: 'Insufficient permissions',
                message: 'Only administrators can reset user passwords'
            });
        }

        if (!newPassword) {
            return res.status(400).json({ 
                error: 'Missing required field',
                message: 'New password is required'
            });
        }

        // Get target user
        const user = await dbService.getDatabaseManager().getMasterQuery(
            'SELECT * FROM users WHERE id = ?',
            [id]
        );

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Hash new password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

        // Update password
        await dbService.getDatabaseManager().runMasterQuery(
            'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [hashedPassword, id]
        );

        // Add to global audit log
        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [id, adminUser.username, 'RESET_PASSWORD', `Admin ${adminUser.username} reset password for user ${user.username}`]
        );

        res.json({ 
            message: 'Password reset successfully',
            user: {
                id,
                username: user.username,
                role: user.role
            }
        });
    } catch (error) {
        console.error('Error resetting password:', error);
        res.status(500).json({ error: 'Failed to reset password' });
    }
});

// Change password endpoint
router.post('/change-password', async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;

        if (!currentPassword || !newPassword) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'Current password and new password are required'
            });
        }

        // For now, we'll use a simple approach - in a real app, you'd get the user from session/token
        // This is a simplified implementation for the demo
        const users = await dbService.getDatabaseManager().getAllMasterQuery(
            'SELECT * FROM users WHERE username = ?',
            ['admin'] // Assuming we're changing the admin password
        );

        if (users.length === 0) {
            return res.status(404).json({
                error: 'User not found',
                message: 'User not found'
            });
        }

        const user = users[0];

        // Verify current password
        const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);

        if (!isValidPassword) {
            return res.status(401).json({
                error: 'Invalid current password',
                message: 'Current password is incorrect'
            });
        }

        // Hash new password
        const hashedNewPassword = await bcrypt.hash(newPassword, 10);

        // Generate new recovery key
        const recoveryKey = `rk_${user.id}_${Math.random().toString(36).substring(2, 15)}`;
        const recoveryKeyHash = await bcrypt.hash(recoveryKey, 10);

        // Update password and recovery key, clear must_change_password flag
        await dbService.getDatabaseManager().runMasterQuery(
            `UPDATE users SET
                password_hash = ?,
                recovery_key_hash = ?,
                has_saved_recovery_key = FALSE,
                must_change_password = FALSE,
                updated_at = CURRENT_TIMESTAMP
             WHERE id = ?`,
            [hashedNewPassword, recoveryKeyHash, user.id]
        );

        // Add to audit log
        await dbService.getDatabaseManager().runMasterQuery(
            `INSERT INTO global_audit_logs (user_id, username, action, details)
             VALUES (?, ?, ?, ?)`,
            [user.id, user.username, 'PASSWORD_CHANGE', `Password changed for user ${user.username}`]
        );

        res.json({
            message: 'Password changed successfully',
            recoveryKey: recoveryKey
        });

    } catch (error) {
        console.error('Password change error:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: 'An error occurred while changing password'
        });
    }
});

export default router;