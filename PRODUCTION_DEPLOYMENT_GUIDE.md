# FAR Sighted v2.0 - Production Deployment Guide
## Multi-Database Architecture Production Setup

### 🎯 **Production Deployment Overview**

This guide covers deploying FAR Sighted v2.0 with Multi-Database Architecture in a production environment for professional accounting practices.

---

## 📋 **Pre-Deployment Checklist**

### **System Requirements**
- [ ] **Server OS**: Windows Server 2019 or higher
- [ ] **RAM**: 8GB minimum, 16GB recommended for 50+ companies
- [ ] **Storage**: 10GB minimum, SSD recommended
- [ ] **Network**: Stable internet connection, firewall configuration
- [ ] **Backup**: Network attached storage or cloud backup solution

### **Software Prerequisites**
- [ ] **Node.js**: v18.0.0 or higher (LTS version)
- [ ] **npm**: Latest version
- [ ] **Windows Service Manager**: For service installation
- [ ] **SSL Certificate**: For HTTPS (recommended)
- [ ] **Antivirus Exclusions**: For database directories

---

## 🚀 **Production Deployment Steps**

### **Step 1: Server Preparation**
```batch
# Create production directory
mkdir C:\FAR-Sighted-Production
cd C:\FAR-Sighted-Production

# Clone/copy application files
# Copy entire FAR Sighted directory to production location
```

### **Step 2: Environment Configuration**
Create production environment files:

**`backend\.env.production`**
```env
# Production Configuration
NODE_ENV=production
PORT=443
FRONTEND_URL=https://your-domain.com

# Security Settings
JWT_SECRET=your-super-secure-jwt-secret-key
SESSION_SECRET=your-super-secure-session-secret
BCRYPT_ROUNDS=12

# Database Configuration
DB_MASTER_PATH=C:\FAR-Sighted-Data\database\master.db
DB_COMPANIES_BASE_PATH=C:\FAR-Sighted-Data\database\companies
DB_BACKUP_PATH=C:\FAR-Sighted-Data\backups

# SSL Configuration
SSL_CERT_PATH=C:\SSL\certificate.crt
SSL_KEY_PATH=C:\SSL\private.key

# Security Headers
HELMET_ENABLED=true
RATE_LIMIT_ENABLED=true
CORS_STRICT_MODE=true

# Logging
LOG_LEVEL=error
LOG_FILE_PATH=C:\FAR-Sighted-Logs\application.log
ENABLE_AUDIT_LOGGING=true

# Performance
MAX_COMPANY_CONNECTIONS=100
CONNECTION_TIMEOUT=10000
QUERY_TIMEOUT=5000

# Backup Configuration
AUTO_BACKUP_INTERVAL=6h
BACKUP_RETENTION_DAYS=90
ENABLE_CLOUD_BACKUP=true
CLOUD_BACKUP_PATH=\\backup-server\far-sighted

# Monitoring
HEALTH_CHECK_INTERVAL=30000
ENABLE_PERFORMANCE_MONITORING=true
MEMORY_USAGE_ALERT_THRESHOLD=85
```

**`.env.production`**
```env
# Frontend Production Configuration
VITE_API_BASE_URL=https://your-domain.com/api
VITE_NODE_ENV=production
VITE_ENABLE_DEBUG_MODE=false
VITE_ENABLE_SOURCEMAPS=false
VITE_SESSION_TIMEOUT=1800000
```

### **Step 3: Database Setup**
```batch
# Create production database directories
mkdir C:\FAR-Sighted-Data\database
mkdir C:\FAR-Sighted-Data\database\companies
mkdir C:\FAR-Sighted-Data\backups

# Set proper permissions
icacls C:\FAR-Sighted-Data /grant "IIS_IUSRS:(OI)(CI)F"
icacls C:\FAR-Sighted-Data /grant "NETWORK SERVICE:(OI)(CI)F"
```

### **Step 4: Install Dependencies**
```batch
cd C:\FAR-Sighted-Production

# Install production dependencies
npm ci --only=production

cd backend
npm ci --only=production

cd ..
```

### **Step 5: Build Application**
```batch
# Build frontend for production
npm run build

# Verify build
dir dist
```

### **Step 6: SSL Certificate Setup**
```batch
# Install SSL certificate
# Copy certificate files to C:\SSL\
# Update firewall rules for HTTPS (port 443)

# Windows Firewall rules
netsh advfirewall firewall add rule name="FAR Sighted HTTPS" dir=in action=allow protocol=TCP localport=443
```

---

## 🛠️ **Windows Service Installation**

### **Create Service Script**
Create `install-service.bat`:
```batch
@echo off
echo Installing FAR Sighted as Windows Service...

# Install PM2 globally for service management
npm install -g pm2
npm install -g pm2-windows-service

# Configure PM2
pm2 install pm2-windows-service
pm2-service-install

# Create ecosystem file for PM2
```

### **PM2 Ecosystem Configuration**
Create `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [
    {
      name: 'far-sighted-backend',
      script: './backend/server.js',
      cwd: 'C:\\FAR-Sighted-Production',
      env: {
        NODE_ENV: 'production',
        PORT: 443
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '2G',
      log_file: 'C:\\FAR-Sighted-Logs\\combined.log',
      out_file: 'C:\\FAR-Sighted-Logs\\out.log',
      error_file: 'C:\\FAR-Sighted-Logs\\error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
    }
  ]
};
```

### **Start Service**
```batch
# Start the service
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

---

## 🔒 **Security Configuration**

### **Firewall Rules**
```batch
# Allow HTTPS traffic
netsh advfirewall firewall add rule name="FAR Sighted HTTPS" dir=in action=allow protocol=TCP localport=443

# Block direct backend access from external
netsh advfirewall firewall add rule name="Block FAR Backend External" dir=in action=block protocol=TCP localport=3001
```

### **File Permissions**
```batch
# Set secure file permissions
icacls C:\FAR-Sighted-Production /inheritance:r
icacls C:\FAR-Sighted-Production /grant "Administrators:(OI)(CI)F"
icacls C:\FAR-Sighted-Production /grant "SYSTEM:(OI)(CI)F"
icacls C:\FAR-Sighted-Production /grant "IIS_IUSRS:(OI)(CI)RX"

# Database directory permissions
icacls C:\FAR-Sighted-Data /inheritance:r
icacls C:\FAR-Sighted-Data /grant "Administrators:(OI)(CI)F"
icacls C:\FAR-Sighted-Data /grant "SYSTEM:(OI)(CI)F"
icacls C:\FAR-Sighted-Data /grant "NETWORK SERVICE:(OI)(CI)F"
```

### **Antivirus Exclusions**
Add these paths to antivirus exclusions:
- `C:\FAR-Sighted-Production\`
- `C:\FAR-Sighted-Data\`
- `C:\FAR-Sighted-Logs\`

---

## 📊 **IIS Configuration (Alternative Deployment)**

### **Install IIS with Node.js Support**
```batch
# Enable IIS features
dism /online /enable-feature /featurename:IIS-WebServerRole /all
dism /online /enable-feature /featurename:IIS-WebServer /all
dism /online /enable-feature /featurename:IIS-CommonHttpFeatures /all
dism /online /enable-feature /featurename:IIS-HttpErrors /all
dism /online /enable-feature /featurename:IIS-HttpLogging /all

# Install iisnode
# Download and install iisnode from https://github.com/azure/iisnode
```

### **Create web.config**
```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <handlers>
      <add name="iisnode" path="backend/server.js" verb="*" modules="iisnode"/>
    </handlers>
    <rewrite>
      <rules>
        <rule name="DynamicContent">
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True"/>
          </conditions>
          <action type="Rewrite" url="backend/server.js"/>
        </rule>
      </rules>
    </rewrite>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="52428800"/>
      </requestFiltering>
    </security>
    <iisnode node_env="production"/>
  </system.webServer>
</configuration>
```

---

## 🔄 **Database Migration in Production**

### **Production Migration Script**
Create `production-migrate.bat`:
```batch
@echo off
echo FAR Sighted Production Migration
echo ================================

# Backup current production database
mkdir C:\FAR-Sighted-Data\migration-backups\%date%_%time%
copy C:\FAR-Sighted-Data\database\*.db C:\FAR-Sighted-Data\migration-backups\%date%_%time%\

# Stop services
pm2 stop all

# Run migration
cd C:\FAR-Sighted-Production\backend
npm run migrate

# Start services
pm2 start all

echo Migration completed. Check logs for any issues.
```

---

## 📋 **Monitoring and Maintenance**

### **Health Check Script**
Create `health-check.bat`:
```batch
@echo off
echo FAR Sighted Production Health Check
echo ===================================

# Check service status
pm2 status

# Check application health
curl -s https://localhost/api/health

# Check disk space
wmic logicaldisk get size,freespace,caption

# Check memory usage
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value

# Check database sizes
dir C:\FAR-Sighted-Data\database\*.db
```

### **Automated Backup Script**
Create `backup.bat`:
```batch
@echo off
set BACKUP_DATE=%date:~-4,4%-%date:~-10,2%-%date:~-7,2%
set BACKUP_PATH=\\backup-server\far-sighted\%BACKUP_DATE%

# Create backup directory
mkdir %BACKUP_PATH%

# Backup databases
xcopy C:\FAR-Sighted-Data\database %BACKUP_PATH%\database /E /Y

# Backup logs
xcopy C:\FAR-Sighted-Logs %BACKUP_PATH%\logs /E /Y

# Backup configuration
xcopy C:\FAR-Sighted-Production\backend\.env.production %BACKUP_PATH%\config\ /Y

echo Backup completed: %BACKUP_PATH%
```

### **Performance Monitoring**
Create `monitor.bat`:
```batch
@echo off
echo Performance Monitoring Report
echo ============================

# CPU Usage
wmic cpu get loadpercentage /value

# Memory Usage
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value

# Disk Usage
wmic logicaldisk get size,freespace,caption

# Active Connections
netstat -an | find ":443" | find "ESTABLISHED" | find /c /v ""

# Service Uptime
pm2 monit

# Database File Sizes
for %%f in (C:\FAR-Sighted-Data\database\companies\*\*.db) do echo %%f: %%~zf bytes
```

---

## 🔧 **Maintenance Procedures**

### **Weekly Maintenance**
```batch
# Run every Sunday at 2 AM via Task Scheduler
@echo off

# Stop services
pm2 stop all

# Backup databases
call C:\FAR-Sighted-Production\scripts\backup.bat

# Optimize databases
cd C:\FAR-Sighted-Production\backend
npm run optimize-databases

# Clear old logs
forfiles /p C:\FAR-Sighted-Logs /s /m *.log /d -30 /c "cmd /c del @path"

# Start services
pm2 start all

# Generate health report
call C:\FAR-Sighted-Production\scripts\health-check.bat > C:\FAR-Sighted-Reports\weekly-health-%date%.txt
```

### **Monthly Maintenance**
```batch
# Security updates
npm audit --audit-level high
npm update

# Database maintenance
npm run vacuum-databases
npm run analyze-databases

# Performance review
call C:\FAR-Sighted-Production\scripts\monitor.bat > C:\FAR-Sighted-Reports\monthly-performance-%date%.txt
```

---

## 🚨 **Troubleshooting Production Issues**

### **Service Won't Start**
```batch
# Check PM2 status
pm2 status
pm2 logs

# Check Windows Event Log
eventvwr.msc

# Check file permissions
icacls C:\FAR-Sighted-Production
icacls C:\FAR-Sighted-Data
```

### **Database Connectivity Issues**
```batch
# Check database file permissions
icacls C:\FAR-Sighted-Data\database\*.db

# Check disk space
wmic logicaldisk get size,freespace,caption

# Test database connectivity
cd C:\FAR-Sighted-Production\backend
npm run test-database
```

### **Performance Issues**
```batch
# Check resource usage
tasklist /svc | findstr node

# Monitor database locks
npm run check-database-locks

# Review slow queries
npm run slow-query-log
```

---

## 📞 **Production Support**

### **Support Contacts**
- **Technical Support**: [Your support email]
- **Emergency Contact**: [Emergency phone number]
- **Documentation**: https://your-docs-site.com

### **Support Information to Collect**
```batch
# Generate support bundle
mkdir C:\FAR-Sighted-Support\%date%_%time%

# Copy logs
xcopy C:\FAR-Sighted-Logs C:\FAR-Sighted-Support\%date%_%time%\logs /E

# System information
systeminfo > C:\FAR-Sighted-Support\%date%_%time%\system-info.txt

# Service status
pm2 status > C:\FAR-Sighted-Support\%date%_%time%\service-status.txt

# Database information
dir C:\FAR-Sighted-Data\database /s > C:\FAR-Sighted-Support\%date%_%time%\database-info.txt
```

---

## ✅ **Production Deployment Checklist**

### **Pre-Go-Live**
- [ ] All components installed and configured
- [ ] SSL certificate installed and tested
- [ ] Database migration completed successfully
- [ ] Backup procedures tested
- [ ] Monitoring systems configured
- [ ] Security measures implemented
- [ ] Performance testing completed
- [ ] User acceptance testing passed

### **Go-Live**
- [ ] Services started and monitored
- [ ] Health checks passing
- [ ] User access verified
- [ ] Backup processes running
- [ ] Support team notified
- [ ] Documentation updated

### **Post-Go-Live**
- [ ] Monitor system for 24-48 hours
- [ ] Verify all features working
- [ ] Check backup completion
- [ ] Review performance metrics
- [ ] User feedback collected
- [ ] Support documentation updated

---

**🏆 FAR Sighted v2.0 is now ready for professional production deployment!**

*Professional deployment guide for enterprise-grade accounting practice management*