#!/usr/bin/env node

/**
 * Populate Default Statutory Rates for Existing Companies
 * This script adds the default statutory asset classification data to existing company databases
 */

import dbService from '../services/database-new.js';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Default statutory rates data (from lib/db-server.ts)
const defaultStatutoryRates = [
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Buildings', assetSubGroup: 'Buildings (other than factory buildings) RCC Frame Structure', extraShiftDepreciation: 'No', usefulLifeYears: '60', scheduleIIClassification: 'Buildings - Freehold' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Buildings', assetSubGroup: 'Buildings (other than factory buildings) other than RCC Frame Structure', extraShiftDepreciation: 'No', usefulLifeYears: '30', scheduleIIClassification: 'Buildings - Freehold' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Buildings', assetSubGroup: 'Factory building', extraShiftDepreciation: 'No', usefulLifeYears: '30', scheduleIIClassification: 'Buildings - Freehold' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Buildings', assetSubGroup: 'Fences, wells, tube wells', extraShiftDepreciation: 'No', usefulLifeYears: '5', scheduleIIClassification: 'Buildings - Freehold' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Buildings', assetSubGroup: 'Others (including temporary structure, etc.)', extraShiftDepreciation: 'No', usefulLifeYears: '3', scheduleIIClassification: 'Buildings - Freehold' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Bridges, culverts, bunders, etc.', assetSubGroup: 'Bridges, culverts, bunders, etc.', extraShiftDepreciation: 'No', usefulLifeYears: '30', scheduleIIClassification: 'Buildings - Freehold' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Plant and Machinery', assetSubGroup: 'General plant and machinery', extraShiftDepreciation: 'Yes', usefulLifeYears: '15', scheduleIIClassification: 'Plant and machinery - General' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Plant and Machinery', assetSubGroup: 'Continuous process plant', extraShiftDepreciation: 'Yes', usefulLifeYears: '25', scheduleIIClassification: 'Plant and machinery - Continuous process' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Plant and Machinery', assetSubGroup: 'Dies, jigs, patterns, etc.', extraShiftDepreciation: 'No', usefulLifeYears: '8', scheduleIIClassification: 'Plant and machinery - Dies, jigs, patterns' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Furniture and fittings', assetSubGroup: 'Furniture and fittings', extraShiftDepreciation: 'No', usefulLifeYears: '10', scheduleIIClassification: 'Furniture and fittings' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Office equipment', assetSubGroup: 'Office equipment', extraShiftDepreciation: 'No', usefulLifeYears: '5', scheduleIIClassification: 'Office equipment' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Computer and data processing units', assetSubGroup: 'Computer including computer software', extraShiftDepreciation: 'No', usefulLifeYears: '3', scheduleIIClassification: 'Computer including computer software' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Computer and data processing units', assetSubGroup: 'Computer software', extraShiftDepreciation: 'No', usefulLifeYears: '3', scheduleIIClassification: 'Computer including computer software' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Motor car', assetSubGroup: 'Motor car', extraShiftDepreciation: 'No', usefulLifeYears: '8', scheduleIIClassification: 'Motor car' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Motor vehicles other than motor car', assetSubGroup: 'Motor vehicles other than motor car', extraShiftDepreciation: 'No', usefulLifeYears: '8', scheduleIIClassification: 'Motor vehicles other than motor car' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Ships', assetSubGroup: 'Ships', extraShiftDepreciation: 'No', usefulLifeYears: '20', scheduleIIClassification: 'Ships' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Aircraft', assetSubGroup: 'Aircraft', extraShiftDepreciation: 'No', usefulLifeYears: '20', scheduleIIClassification: 'Aircraft' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Railway sidings', assetSubGroup: 'Railway sidings', extraShiftDepreciation: 'No', usefulLifeYears: '15', scheduleIIClassification: 'Railway sidings' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Laboratory equipment', assetSubGroup: 'Laboratory equipment', extraShiftDepreciation: 'No', usefulLifeYears: '10', scheduleIIClassification: 'Laboratory equipment' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Medical equipment', assetSubGroup: 'Medical equipment', extraShiftDepreciation: 'No', usefulLifeYears: '13', scheduleIIClassification: 'Medical equipment' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Pollution control equipment', assetSubGroup: 'Pollution control equipment', extraShiftDepreciation: 'No', usefulLifeYears: '15', scheduleIIClassification: 'Pollution control equipment' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Fire fighting equipment', assetSubGroup: 'Fire fighting equipment', extraShiftDepreciation: 'No', usefulLifeYears: '15', scheduleIIClassification: 'Fire fighting equipment' },
    { isStatutory: 'Yes', tangibility: 'Tangible', assetGroup: 'Electrical installations and equipments', assetSubGroup: 'Electrical installations and equipments', extraShiftDepreciation: 'No', usefulLifeYears: '10', scheduleIIClassification: 'Electrical installations and equipments' },
    { isStatutory: 'Yes', tangibility: 'Intangible', assetGroup: 'Patents', assetSubGroup: 'Patents', extraShiftDepreciation: 'No', usefulLifeYears: '8', scheduleIIClassification: 'Patents' },
    { isStatutory: 'Yes', tangibility: 'Intangible', assetGroup: 'Copyrights', assetSubGroup: 'Copyrights', extraShiftDepreciation: 'No', usefulLifeYears: '6', scheduleIIClassification: 'Copyrights' },
    { isStatutory: 'Yes', tangibility: 'Intangible', assetGroup: 'Trademarks', assetSubGroup: 'Trademarks', extraShiftDepreciation: 'No', usefulLifeYears: '5', scheduleIIClassification: 'Trademarks' },
    { isStatutory: 'Yes', tangibility: 'Intangible', assetGroup: 'Licenses and franchise', assetSubGroup: 'Licenses and franchise', extraShiftDepreciation: 'No', usefulLifeYears: '10', scheduleIIClassification: 'Licenses and franchise' }
];

async function populateStatutoryRates() {
    console.log('📊 POPULATING DEFAULT STATUTORY RATES');
    console.log('====================================\n');

    try {
        // Initialize database service
        await dbService.ensureInitialized();

        // Get all companies
        const companies = await dbService.getAllCompanies();
        console.log(`📊 Found ${companies.length} companies to process\n`);

        for (const company of companies) {
            console.log(`🏢 Processing company: ${company.name} (${company.id})`);
            
            try {
                // Get company database service
                const companyDbService = await dbService.databaseManager.getCompanyDatabase(company.id);
                
                // Check if statutory rates already exist
                const existingRates = await companyDbService.all(
                    'SELECT COUNT(*) as count FROM statutory_rates');
                
                if (existingRates[0].count > 0) {
                    console.log(`   ✅ Statutory rates already exist (${existingRates[0].count} records)`);
                } else {
                    console.log(`   ➕ Adding default statutory rates (${defaultStatutoryRates.length} records)`);
                    
                    // Insert default statutory rates
                    for (const rate of defaultStatutoryRates) {
                        await companyDbService.run(
                            `INSERT INTO statutory_rates (
                                is_statutory, tangibility, asset_group, asset_sub_group,
                                extra_shift_depreciation, useful_life_years, schedule_ii_classification
                            ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
                            [
                                rate.isStatutory, rate.tangibility, rate.assetGroup, 
                                rate.assetSubGroup, rate.extraShiftDepreciation, 
                                rate.usefulLifeYears, rate.scheduleIIClassification
                            ]
                        );
                    }
                    
                    console.log(`   ✅ Added ${defaultStatutoryRates.length} statutory rates`);
                }

                console.log(`   ✅ Company ${company.name} processed successfully\n`);

            } catch (error) {
                console.error(`   ❌ Error processing company ${company.name}:`, error.message);
                console.log('');
            }
        }

        console.log('🎉 STATUTORY RATES POPULATION COMPLETED');
        console.log('======================================\n');

        // Test one company to verify the fix
        if (companies.length > 0) {
            console.log('🧪 TESTING STATUTORY RATES');
            console.log('=========================');
            
            const testCompany = companies[0];
            console.log(`Testing company: ${testCompany.name} (${testCompany.id})`);
            
            try {
                const companyData = await dbService.getCompanyWithData(testCompany.id);
                console.log(`✅ Statutory rates found: ${companyData.statutoryRates.length}`);
                
                if (companyData.statutoryRates.length > 0) {
                    console.log(`   Sample rates:`);
                    companyData.statutoryRates.slice(0, 3).forEach(rate => {
                        console.log(`   - ${rate.assetGroup} > ${rate.assetSubGroup} (${rate.usefulLifeYears} years)`);
                    });
                }
                
                console.log(`✅ Assets count: ${companyData.assets.length}`);
                console.log(`✅ Financial years: ${companyData.financialYears.join(', ')}`);
            } catch (error) {
                console.error(`❌ Test failed:`, error.message);
            }
        }

    } catch (error) {
        console.error('❌ Fatal error:', error);
        process.exit(1);
    }
}

// Run the population
populateStatutoryRates().then(() => {
    console.log('\n✅ Script completed successfully');
    process.exit(0);
}).catch(error => {
    console.error('\n❌ Script failed:', error);
    process.exit(1);
});
