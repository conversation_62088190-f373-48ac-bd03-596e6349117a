@echo off
setlocal enabledelayedexpansion

echo =====================================
echo FAR SIGHTED - START APPLICATION v2.0
echo Professional Advisory Services - CA
echo Multi-Database Architecture
echo =====================================
echo.

REM Navigate to project directory
cd /d "E:\Projects\FAR Sighted"

REM Verify we're in correct directory
if not exist "package.json" (
    echo ❌ Error: package.json not found in current directory
    echo    Current directory: %cd%
    echo    Please ensure you're in the correct project directory
    pause
    exit /b 1
)

REM Check if backend directory exists
if not exist "backend\" (
    echo ❌ Error: backend directory not found
    echo    Current directory: %cd%
    pause
    exit /b 1
)

echo 🚀 Starting FAR Sighted Application v2.0...
echo.

REM Check for port conflicts
echo 🔍 Checking for port conflicts...
set "port_conflicts="

for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":3001" ^| find "LISTENING"') do (
    set "port_conflicts=!port_conflicts! 3001"
)

for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":9090" ^| find "LISTENING"') do (
    set "port_conflicts=!port_conflicts! 9090"
)

if defined port_conflicts (
    echo    ⚠️  Port conflicts detected on:!port_conflicts!
    echo    Consider running kill-all-processes.bat first
    echo.
    choice /c YN /m "Continue anyway (Y/N)?"
    if errorlevel 2 (
        echo    Cancelled by user
        pause
        exit /b 1
    )
)

echo.
echo 📡 Starting backend server (Multi-Database Architecture)...
cd backend

if exist "package.json" (
    echo    ✅ Backend configuration found
    echo    🏗️  Database: Multi-Database Structure
    echo    📊 Features: Company isolation, Enhanced security, Scalable architecture
    echo.
    echo    🚀 Starting backend on port 3001...
    start "FAR Backend v2.0" cmd /k "title FAR Backend v2.0 && echo ======================================= && echo FAR SIGHTED BACKEND v2.0 && echo Multi-Database Architecture && echo ======================================= && echo. && echo Starting server... && echo. && npm start"
    echo    ✅ Backend server starting...
    
    REM Wait for backend to initialize
    echo    ⏳ Waiting for backend initialization (8 seconds)...
    timeout /t 8 /nobreak >nul
    
) else (
    echo    ❌ Backend package.json not found
    pause
    exit /b 1
)

REM Go back to root and start frontend
cd ..
echo.
echo 🖥️  Starting frontend application...

REM Check if node_modules exists
if not exist "node_modules\" (
    echo    📦 Installing frontend dependencies (this may take a moment)...
    call npm install
    echo    ✅ Dependencies installed
)

echo    🎨 Starting Vite development server on port 9090...
start "FAR Frontend v2.0" cmd /k "title FAR Frontend v2.0 && echo ======================================= && echo FAR SIGHTED FRONTEND v2.0 && echo React + TypeScript + Vite && echo ======================================= && echo. && echo Starting development server... && echo. && npm run dev"

echo.
echo ✅ FAR Sighted v2.0 Application Starting!
echo.
echo 📊 Application Information:
echo    • Backend URL:  http://localhost:3001/api
echo    • Frontend URL: http://localhost:9090
echo    • Health Check: http://localhost:3001/api/health
echo    • Migration:    http://localhost:3001/api/migration-status
echo    • System Info:  http://localhost:3001/api/system-info
echo.
echo 🏗️  Architecture Features:
echo    • Multi-Database Structure (Separate DB per company)
echo    • Enhanced Data Isolation
echo    • Company-specific Audit Trails
echo    • Granular Backup/Restore
echo    • Horizontal Scalability
echo.

REM Wait for services to start
echo ⏳ Waiting for services to initialize (10 seconds)...
timeout /t 10 /nobreak >nul

REM Check backend health
echo 🔍 Checking backend status...
curl -s http://localhost:3001/api/health >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Backend is responding correctly
    echo.
    echo 📊 Checking migration status...
    curl -s http://localhost:3001/api/migration-status >nul 2>&1
    if %errorlevel% == 0 (
        echo    ✅ Migration status endpoint accessible
    ) else (
        echo    ⚠️  Migration status check failed (may still be starting)
    )
) else (
    echo    ⚠️  Backend health check failed
    echo    This may be normal if the backend is still starting
    echo    Wait a few more seconds and check manually: http://localhost:3001/api/health
)

echo.
echo 🌐 Opening application in browser...
timeout /t 3 /nobreak >nul
start http://localhost:9090

echo.
echo 🎯 Quick Access Commands:
echo    • Health Check:     curl http://localhost:3001/api/health
echo    • Migration Status: curl http://localhost:3001/api/migration-status
echo    • System Info:      curl http://localhost:3001/api/system-info
echo    • Companies List:   curl http://localhost:3001/api/companies
echo.
echo 💡 Migration & Database:
echo    • If first run, check migration status above
echo    • Run migration: cd backend && npm run migrate
echo    • Dry run test: cd backend && npm run migrate:dry-run
echo    • Force migration: cd backend && npm run migrate:force
echo.
echo 🔧 Development Tools:
echo    • Backend logs: Check "FAR Backend v2.0" window
echo    • Frontend logs: Check "FAR Frontend v2.0" window
echo    • Kill all processes: kill-all-processes.bat
echo    • Restart app: restart-far-app.bat
echo.

echo ✨ FAR Sighted v2.0 is now running!
echo    Professional Asset Management with Multi-Database Architecture
echo.
echo    Press any key to close this startup window...
pause >nul