import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';
import DatabaseManager from './DatabaseManager.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * DatabaseMigrator - Migrates data from old single-database structure to new multi-database structure
 */
class DatabaseMigrator {
    constructor() {
        this.oldDbPath = join(__dirname, '../../database/far_sighted.db');
        this.databaseManager = new DatabaseManager();
    }

    /**
     * Check if old database exists
     */
    async hasOldDatabase() {
        try {
            await fs.access(this.oldDbPath);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Migrate from old database structure to new structure
     */
    async migrate() {
        console.log('🔄 Starting database migration...');

        if (!await this.hasOldDatabase()) {
            console.log('ℹ️  No old database found. Migration not needed.');
            return { success: true, message: 'No migration needed' };
        }

        try {
            // Connect to old database
            const oldDb = await this.connectToOldDatabase();
            
            // Read existing data
            const migrationData = await this.extractDataFromOldDatabase(oldDb);
            
            // Create companies with their own databases
            const migrationResults = await this.createCompaniesFromData(migrationData);
            
            // Close old database
            await this.closeOldDatabase(oldDb);
            
            // Backup old database
            await this.backupOldDatabase();
            
            console.log('✅ Migration completed successfully');
            return { 
                success: true, 
                message: 'Migration completed successfully',
                results: migrationResults 
            };

        } catch (error) {
            console.error('❌ Migration failed:', error);
            return { 
                success: false, 
                message: `Migration failed: ${error.message}`,
                error: error 
            };
        }
    }

    /**
     * Connect to old database
     */
    connectToOldDatabase() {
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.oldDbPath, (err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log('✅ Connected to old database');
                    resolve(db);
                }
            });
        });
    }

    /**
     * Extract data from old database
     */
    async extractDataFromOldDatabase(oldDb) {
        console.log('📤 Extracting data from old database...');

        const data = {
            companies: await this.queryOldDb(oldDb, 'SELECT * FROM companies ORDER BY company_name'),
            users: await this.queryOldDb(oldDb, 'SELECT * FROM users'),
            assets: await this.queryOldDb(oldDb, 'SELECT * FROM assets'),
            financial_years: await this.queryOldDb(oldDb, 'SELECT * FROM financial_years'),
            asset_yearly_data: await this.queryOldDb(oldDb, 'SELECT * FROM asset_yearly_data'),
            statutory_rates: await this.queryOldDb(oldDb, 'SELECT * FROM statutory_rates'),
            extra_ledgers: await this.queryOldDb(oldDb, 'SELECT * FROM extra_ledgers'),
            license_history: await this.queryOldDb(oldDb, 'SELECT * FROM license_history'),
            audit_logs: await this.queryOldDb(oldDb, 'SELECT * FROM audit_logs'),
            backup_logs: await this.queryOldDb(oldDb, 'SELECT * FROM backup_logs'),
            app_settings: await this.queryOldDb(oldDb, 'SELECT * FROM app_settings')
        };

        console.log(`📊 Extracted data summary:
        - Companies: ${data.companies.length}
        - Users: ${data.users.length}
        - Assets: ${data.assets.length}
        - Financial Years: ${data.financial_years.length}
        - Asset Yearly Data: ${data.asset_yearly_data.length}`);

        return data;
    }

    /**
     * Query old database
     */
    queryOldDb(db, sql, params = []) {
        return new Promise((resolve, reject) => {
            db.all(sql, params, (err, rows) => {
                if (err) {
                    console.warn(`⚠️  Query failed (table may not exist): ${sql}`);
                    resolve([]); // Return empty array if table doesn't exist
                } else {
                    resolve(rows || []);
                }
            });
        });
    }

    /**
     * Create companies from migrated data
     */
    async createCompaniesFromData(data) {
        console.log('🏗️  Creating companies with separate databases...');
        
        const results = {
            companies: [],
            errors: [],
            summary: {
                companiesCreated: 0,
                assetsTransferred: 0,
                usersTransferred: 0
            }
        };

        // First, migrate users to master database
        if (data.users && data.users.length > 0) {
            console.log('👥 Migrating users...');
            for (const user of data.users) {
                try {
                    await this.databaseManager.runMasterQuery(
                        `INSERT OR REPLACE INTO users (id, username, password_hash, role, recovery_key_hash, has_saved_recovery_key, is_active, created_at, updated_at)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                            user.id || `u${Date.now()}_${Math.random()}`,
                            user.username,
                            user.password_hash || user.password, // Handle both field names
                            user.role,
                            user.recovery_key_hash || null,
                            user.has_saved_recovery_key || false,
                            true, // is_active
                            user.created_at || new Date().toISOString(),
                            user.updated_at || new Date().toISOString()
                        ]
                    );
                    results.summary.usersTransferred++;
                } catch (error) {
                    console.error(`Failed to migrate user ${user.username}:`, error);
                    results.errors.push(`User migration failed: ${user.username} - ${error.message}`);
                }
            }
        }

        // Create companies and their databases
        if (data.companies && data.companies.length > 0) {
            for (const company of data.companies) {
                try {
                    console.log(`🏢 Creating company: ${company.company_name}`);
                    
                    // Prepare company data
                    const companyData = {
                        companyName: company.company_name,
                        pan: company.pan,
                        cin: company.cin,
                        dateOfIncorporation: company.date_of_incorporation,
                        financialYearStart: company.financial_year_start,
                        financialYearEnd: company.financial_year_end,
                        firstDateOfAdoption: company.first_date_of_adoption,
                        addressLine1: company.address_line1,
                        addressLine2: company.address_line2,
                        city: company.city,
                        pin: company.pin,
                        email: company.email,
                        mobile: company.mobile,
                        contactPerson: company.contact_person,
                        licenseValidUpto: company.license_valid_upto
                    };

                    // Create company with its own database
                    const newCompany = await this.databaseManager.createCompany(companyData);
                    
                    // Get the company database service
                    const companyDb = await this.databaseManager.getCompanyDatabase(newCompany.id);
                    
                    // Migrate company-specific data
                    const companyAssets = await this.migrateCompanyData(
                        companyDb, 
                        company.id, 
                        newCompany.id,
                        data
                    );

                    results.companies.push({
                        oldId: company.id,
                        newId: newCompany.id,
                        name: company.company_name,
                        assetsCount: companyAssets
                    });

                    results.summary.companiesCreated++;
                    results.summary.assetsTransferred += companyAssets;

                } catch (error) {
                    console.error(`❌ Failed to create company ${company.company_name}:`, error);
                    results.errors.push(`Company creation failed: ${company.company_name} - ${error.message}`);
                }
            }
        } else {
            console.log('ℹ️  No companies found in old database');
        }

        return results;
    }

    /**
     * Migrate company-specific data
     */
    async migrateCompanyData(companyDb, oldCompanyId, newCompanyId, data) {
        let assetsCount = 0;

        try {
            await companyDb.beginTransaction();

            // Migrate assets
            const companyAssets = data.assets.filter(asset => asset.company_id === oldCompanyId);
            for (const asset of companyAssets) {
                await companyDb.run(
                    `INSERT INTO assets (
                        record_id, asset_particulars, book_entry_date, put_to_use_date,
                        basic_amount, duties_taxes, gross_amount, vendor, invoice_no,
                        model_make, location, asset_id, remarks, ledger_name_in_books,
                        asset_group, asset_sub_group, schedule_iii_classification,
                        disposal_date, disposal_amount, salvage_percentage, wdv_of_adoption_date,
                        is_leasehold, depreciation_method, life_in_years, lease_period, scrap_it,
                        created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        asset.record_id, asset.asset_particulars, asset.book_entry_date, asset.put_to_use_date,
                        asset.basic_amount, asset.duties_taxes, asset.gross_amount, asset.vendor, asset.invoice_no,
                        asset.model_make, asset.location, asset.asset_id, asset.remarks, asset.ledger_name_in_books,
                        asset.asset_group, asset.asset_sub_group, asset.schedule_iii_classification,
                        asset.disposal_date, asset.disposal_amount, asset.salvage_percentage, asset.wdv_of_adoption_date,
                        asset.is_leasehold, asset.depreciation_method, asset.life_in_years, asset.lease_period, asset.scrap_it,
                        asset.created_at, asset.updated_at
                    ]
                );
                assetsCount++;
            }

            // Migrate financial years
            const companyFinancialYears = data.financial_years.filter(fy => fy.company_id === oldCompanyId);
            for (const fy of companyFinancialYears) {
                await companyDb.run(
                    'INSERT INTO financial_years (year_range, is_locked, created_at) VALUES (?, ?, ?)',
                    [fy.year_range, fy.is_locked || 0, fy.created_at]
                );
            }

            // Migrate statutory rates
            const companyRates = data.statutory_rates.filter(rate => rate.company_id === oldCompanyId);
            for (const rate of companyRates) {
                await companyDb.run(
                    `INSERT INTO statutory_rates (
                        is_statutory, tangibility, asset_group, asset_sub_group,
                        extra_shift_depreciation, useful_life_years, schedule_ii_classification, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        rate.is_statutory, rate.tangibility, rate.asset_group, rate.asset_sub_group,
                        rate.extra_shift_depreciation, rate.useful_life_years, rate.schedule_ii_classification,
                        rate.created_at
                    ]
                );
            }

            // Migrate extra ledgers
            const companyLedgers = data.extra_ledgers.filter(ledger => ledger.company_id === oldCompanyId);
            for (const ledger of companyLedgers) {
                await companyDb.run(
                    'INSERT INTO extra_ledgers (ledger_name, created_at) VALUES (?, ?)',
                    [ledger.ledger_name, ledger.created_at]
                );
            }

            // Migrate license history
            const companyLicenses = data.license_history.filter(license => license.company_id === oldCompanyId);
            for (const license of companyLicenses) {
                await companyDb.run(
                    'INSERT INTO license_history (id, license_key, valid_from, valid_upto, activated_at) VALUES (?, ?, ?, ?, ?)',
                    [license.id, license.license_key, license.valid_from, license.valid_upto, license.activated_at]
                );
            }

            // Migrate audit logs
            const companyAuditLogs = data.audit_logs.filter(log => log.company_id === oldCompanyId);
            for (const log of companyAuditLogs) {
                await companyDb.run(
                    `INSERT INTO audit_logs (timestamp, user_id, username, action, details, table_name, record_id, old_values, new_values)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        log.timestamp, log.user_id, log.username, log.action, log.details,
                        log.table_name, log.record_id, log.old_values, log.new_values
                    ]
                );
            }

            await companyDb.commit();
            console.log(`✅ Company data migrated: ${assetsCount} assets`);

        } catch (error) {
            await companyDb.rollback();
            throw error;
        }

        return assetsCount;
    }

    /**
     * Close old database connection
     */
    closeOldDatabase(oldDb) {
        return new Promise((resolve) => {
            oldDb.close((err) => {
                if (err) {
                    console.error('Error closing old database:', err);
                } else {
                    console.log('🔌 Old database connection closed');
                }
                resolve();
            });
        });
    }

    /**
     * Backup old database file
     */
    async backupOldDatabase() {
        const backupPath = this.oldDbPath + '.migration-backup.' + Date.now();
        try {
            await fs.copyFile(this.oldDbPath, backupPath);
            console.log(`📋 Old database backed up to: ${backupPath}`);
        } catch (error) {
            console.error('Failed to backup old database:', error);
        }
    }

    /**
     * Get migration status
     */
    async getMigrationStatus() {
        const hasOldDb = await this.hasOldDatabase();
        const companies = await this.databaseManager.getAllCompanies();
        
        return {
            needsMigration: hasOldDb && companies.length === 0,
            hasOldDatabase: hasOldDb,
            companiesInNewStructure: companies.length,
            oldDatabasePath: this.oldDbPath
        };
    }
}

export default DatabaseMigrator;