/**
 * Enhanced Mock Data for Testing All Schedule III Scenarios
 * Professional Advisory Services - Chartered Accountant
 * 
 * This script creates comprehensive test data covering:
 * - Historical assets (pre-adoption)
 * - Multi-year depreciation scenarios  
 * - Fully depreciated assets
 * - Disposed assets
 * - Current year additions
 * - Extra shift depreciation
 * - Different depreciation methods (SLM vs WDV)
 * - Various asset life spans
 */

import sqlite3 from 'sqlite3';
import bcrypt from 'bcrypt';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const dbPath = join(__dirname, '../database/far_sighted.db');

console.log('🎯 Populating Enhanced Mock Data for Schedule III Testing...');

const populateEnhancedMockData = async () => {
    const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
            console.error('❌ Error opening database:', err.message);
            process.exit(1);
        }
        console.log('✅ Connected to SQLite database');
    });

    const runQuery = (sql, params = []) => {
        return new Promise((resolve, reject) => {
            db.run(sql, params, function(err) {
                if (err) reject(err);
                else resolve({ id: this.lastID, changes: this.changes });
            });
        });
    };

    try {
        // Clear existing data for Tech Innovations to add comprehensive test data
        await runQuery('DELETE FROM assets WHERE company_id = ?', ['c1001']);
        await runQuery('DELETE FROM asset_yearly_data WHERE asset_id IN (SELECT id FROM assets WHERE company_id = ?)', ['c1001']);

        console.log('🏭 Creating comprehensive test assets for Tech Innovations...');

        // Enhanced Assets covering all test scenarios
        const enhancedAssets = [
            // SCENARIO 1: Historical Asset (Pre-adoption) - Should use WDV at adoption
            {
                company_id: 'c1001',
                record_id: 'HIST001',
                asset_particulars: 'Historical Plant & Machinery (Pre-Adoption)',
                book_entry_date: '2017-05-15',
                put_to_use_date: '2017-06-01',
                basic_amount: 1000000,
                duties_taxes: 180000,
                gross_amount: 1180000,
                vendor: 'Historical Equipment Ltd',
                invoice_no: 'HIST/2017/001',
                model_make: 'Legacy Machine Model',
                location: 'Production Floor A',
                asset_id: 'HIST001',
                remarks: 'Historical asset existing before system adoption',
                ledger_name_in_books: 'Plant & Machinery',
                asset_group: 'Plant & Machinery',
                asset_sub_group: 'General Plant & Machinery',
                schedule_iii_classification: 'Plant & Machinery - General',
                salvage_percentage: 5,
                wdv_of_adoption_date: 850000, // Already depreciated by ₹330,000 at adoption
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 15,
                scrap_it: false
            },

            // SCENARIO 2: Multi-Year Asset with Consistent Depreciation
            {
                company_id: 'c1001',
                record_id: 'MULTI001',
                asset_particulars: 'Multi-Year Production Equipment',
                book_entry_date: '2022-04-05',
                put_to_use_date: '2022-04-10',
                basic_amount: 500000,
                duties_taxes: 90000,
                gross_amount: 590000,
                vendor: 'Production Systems Ltd',
                invoice_no: 'PSL/2022/045',
                model_make: 'ProSys-5000',
                location: 'Production Floor B',
                asset_id: 'MULTI001',
                remarks: 'Asset with multiple years of depreciation',
                ledger_name_in_books: 'Production Equipment',
                asset_group: 'Plant & Machinery',
                asset_sub_group: 'General Plant & Machinery',
                schedule_iii_classification: 'Plant & Machinery - General',
                salvage_percentage: 5,
                wdv_of_adoption_date: null,
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 10,
                scrap_it: false
            },

            // SCENARIO 3: Fully Depreciated Asset (Zero Depreciation Expected)
            {
                company_id: 'c1001',
                record_id: 'FULLY001',
                asset_particulars: 'Fully Depreciated Old Computer',
                book_entry_date: '2018-01-15',
                put_to_use_date: '2018-02-01',
                basic_amount: 50000,
                duties_taxes: 9000,
                gross_amount: 59000,
                vendor: 'Computer Solutions',
                invoice_no: 'CS/2018/012',
                model_make: 'OldTech Desktop',
                location: 'Admin Office',
                asset_id: 'FULLY001',
                remarks: 'Asset that should be fully depreciated by now',
                ledger_name_in_books: 'Computer Equipment',
                asset_group: 'Computer & Data Processing',
                asset_sub_group: 'Computers',
                schedule_iii_classification: 'Computer including computer software',
                salvage_percentage: 5,
                wdv_of_adoption_date: 55000, // Slightly depreciated at adoption
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 3,
                scrap_it: false
            },

            // SCENARIO 4: Asset Disposed in Previous Year
            {
                company_id: 'c1001',
                record_id: 'DISP001',
                asset_particulars: 'Disposed Vehicle in Previous Year',
                book_entry_date: '2021-03-10',
                put_to_use_date: '2021-04-01',
                basic_amount: 800000,
                duties_taxes: 120000,
                gross_amount: 920000,
                vendor: 'Auto Dealers Ltd',
                invoice_no: 'AD/2021/078',
                model_make: 'Sedan Model 2021',
                location: 'Disposed',
                asset_id: 'DISP001',
                remarks: 'Vehicle disposed in FY 2023-24',
                ledger_name_in_books: 'Motor Vehicles',
                asset_group: 'Motor Vehicles',
                asset_sub_group: 'Motor Cars',
                schedule_iii_classification: 'Motor cars other than those used in a business of running them on hire',
                salvage_percentage: 5,
                wdv_of_adoption_date: null,
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 8,
                disposal_date: '2024-01-15',
                disposal_amount: 450000,
                scrap_it: false
            },

            // SCENARIO 5: Current Year Addition
            {
                company_id: 'c1001',
                record_id: 'NEW001',
                asset_particulars: 'New Equipment Added This Year',
                book_entry_date: '2024-08-15',
                put_to_use_date: '2024-09-01',
                basic_amount: 300000,
                duties_taxes: 54000,
                gross_amount: 354000,
                vendor: 'Modern Equipment Co',
                invoice_no: 'MEC/2024/156',
                model_make: 'ModernTech-2024',
                location: 'New Production Line',
                asset_id: 'NEW001',
                remarks: 'Equipment added in current financial year',
                ledger_name_in_books: 'New Equipment',
                asset_group: 'Plant & Machinery',
                asset_sub_group: 'General Plant & Machinery',
                schedule_iii_classification: 'Plant & Machinery - General',
                salvage_percentage: 5,
                wdv_of_adoption_date: null,
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 12,
                scrap_it: false
            },

            // SCENARIO 6: Asset with Extra Shift Depreciation
            {
                company_id: 'c1001',
                record_id: 'SHIFT001',
                asset_particulars: 'Manufacturing Machine with Extra Shifts',
                book_entry_date: '2023-02-10',
                put_to_use_date: '2023-03-01',
                basic_amount: 750000,
                duties_taxes: 135000,
                gross_amount: 885000,
                vendor: 'Heavy Machinery Corp',
                invoice_no: 'HMC/2023/089',
                model_make: 'ShiftMax-3000',
                location: 'Production Floor C',
                asset_id: 'SHIFT001',
                remarks: 'Machine eligible for extra shift depreciation',
                ledger_name_in_books: 'Manufacturing Machinery',
                asset_group: 'Plant & Machinery',
                asset_sub_group: 'General Plant & Machinery',
                schedule_iii_classification: 'Plant & Machinery - General',
                salvage_percentage: 5,
                wdv_of_adoption_date: null,
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 15,
                scrap_it: false
            },

            // SCENARIO 7: Asset with SLM Depreciation Method
            {
                company_id: 'c1001',
                record_id: 'SLM001',
                asset_particulars: 'Office Building (SLM Method)',
                book_entry_date: '2022-10-15',
                put_to_use_date: '2022-11-01',
                basic_amount: 2000000,
                duties_taxes: 200000,
                gross_amount: 2200000,
                vendor: 'Construction Company Ltd',
                invoice_no: 'CCL/2022/234',
                model_make: 'Commercial Building',
                location: 'Head Office',
                asset_id: 'SLM001',
                remarks: 'Building using straight line method depreciation',
                ledger_name_in_books: 'Buildings',
                asset_group: 'Buildings',
                asset_sub_group: 'Factory Building',
                schedule_iii_classification: 'Buildings',
                salvage_percentage: 5,
                wdv_of_adoption_date: null,
                is_leasehold: false,
                depreciation_method: 'SLM',
                life_in_years: 30,
                scrap_it: false
            },

            // SCENARIO 8: Leasehold Asset
            {
                company_id: 'c1001',
                record_id: 'LEASE001',
                asset_particulars: 'Leasehold Office Space Improvements',
                book_entry_date: '2023-06-10',
                put_to_use_date: '2023-07-01',
                basic_amount: 400000,
                duties_taxes: 72000,
                gross_amount: 472000,
                vendor: 'Interior Design Solutions',
                invoice_no: 'IDS/2023/167',
                model_make: 'Office Renovation',
                location: 'Leased Office Premises',
                asset_id: 'LEASE001',
                remarks: 'Leasehold improvements with limited lease period',
                ledger_name_in_books: 'Leasehold Improvements',
                asset_group: 'Office Equipment',
                asset_sub_group: 'Leasehold Improvements',
                schedule_iii_classification: 'Office equipment',
                salvage_percentage: 0,
                wdv_of_adoption_date: null,
                is_leasehold: true,
                depreciation_method: 'SLM',
                life_in_years: 5,
                lease_period: 5,
                scrap_it: false
            },

            // SCENARIO 9: Asset Marked for Scrapping
            {
                company_id: 'c1001',
                record_id: 'SCRAP001',
                asset_particulars: 'Old Equipment Marked for Scrapping',
                book_entry_date: '2019-03-15',
                put_to_use_date: '2019-04-01',
                basic_amount: 200000,
                duties_taxes: 36000,
                gross_amount: 236000,
                vendor: 'Equipment Supplier Ltd',
                invoice_no: 'ESL/2019/089',
                model_make: 'OldEquip-2019',
                location: 'Storage Area',
                asset_id: 'SCRAP001',
                remarks: 'Equipment marked for scrapping',
                ledger_name_in_books: 'Old Equipment',
                asset_group: 'Plant & Machinery',
                asset_sub_group: 'General Plant & Machinery',
                schedule_iii_classification: 'Plant & Machinery - General',
                salvage_percentage: 5,
                wdv_of_adoption_date: null,
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 8,
                scrap_it: true
            },

            // SCENARIO 10: High-Value Asset with Long Life
            {
                company_id: 'c1001',
                record_id: 'HVLONG001',
                asset_particulars: 'High-Value Long-Life Industrial Equipment',
                book_entry_date: '2021-06-20',
                put_to_use_date: '2021-07-01',
                basic_amount: 5000000,
                duties_taxes: 900000,
                gross_amount: 5900000,
                vendor: 'Industrial Solutions Global',
                invoice_no: 'ISG/2021/345',
                model_make: 'IndustrialMax-5000',
                location: 'Main Production Facility',
                asset_id: 'HVLONG001',
                remarks: 'High-value equipment with extended depreciation schedule',
                ledger_name_in_books: 'Heavy Industrial Equipment',
                asset_group: 'Plant & Machinery',
                asset_sub_group: 'General Plant & Machinery',
                schedule_iii_classification: 'Plant & Machinery - General',
                salvage_percentage: 10,
                wdv_of_adoption_date: null,
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 25,
                scrap_it: false
            }
        ];

        // Insert enhanced assets
        for (const asset of enhancedAssets) {
            await runQuery(
                `INSERT OR REPLACE INTO assets (
                    company_id, record_id, asset_particulars, book_entry_date, put_to_use_date,
                    basic_amount, duties_taxes, gross_amount, vendor, invoice_no, model_make,
                    location, asset_id, remarks, ledger_name_in_books, asset_group,
                    asset_sub_group, schedule_iii_classification, salvage_percentage,
                    wdv_of_adoption_date, is_leasehold, depreciation_method, life_in_years,
                    disposal_date, disposal_amount, lease_period, scrap_it
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    asset.company_id, asset.record_id, asset.asset_particulars, asset.book_entry_date,
                    asset.put_to_use_date, asset.basic_amount, asset.duties_taxes, asset.gross_amount,
                    asset.vendor, asset.invoice_no, asset.model_make, asset.location, asset.asset_id,
                    asset.remarks, asset.ledger_name_in_books, asset.asset_group, asset.asset_sub_group,
                    asset.schedule_iii_classification, asset.salvage_percentage, asset.wdv_of_adoption_date,
                    asset.is_leasehold, asset.depreciation_method, asset.life_in_years,
                    asset.disposal_date || null, asset.disposal_amount || null, asset.lease_period || null, asset.scrap_it
                ]
            );
        }

        console.log('📈 Creating comprehensive yearly depreciation data...');

        // Get all asset IDs for the enhanced data
        const allAssetIds = await new Promise((resolve, reject) => {
            db.all('SELECT id, record_id, company_id, gross_amount, life_in_years, salvage_percentage, depreciation_method FROM assets WHERE company_id = ?', ['c1001'], (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        // Financial years for comprehensive testing
        const testingYears = ['2022-2023', '2023-2024', '2024-2025'];

        // Add extra shift statutory rate for testing
        await runQuery(
            `INSERT OR REPLACE INTO statutory_rates (
                company_id, is_statutory, tangibility, asset_group, asset_sub_group,
                extra_shift_depreciation, useful_life_years, schedule_ii_classification
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            ['c1001', 'Yes', 'Tangible', 'Plant & Machinery', 'General Plant & Machinery', 'Yes', '15', 'Plant & Machinery - General']
        );

        // Create detailed yearly data for each asset
        for (const assetRow of allAssetIds) {
            for (let yearIndex = 0; yearIndex < testingYears.length; yearIndex++) {
                const year = testingYears[yearIndex];
                const yearStart = new Date(`${year.split('-')[0]}-04-01`);
                const yearEnd = new Date(`${year.split('-')[1]}-03-31`);
                
                // Determine if asset was active in this year
                const putToUseDate = new Date(assetRow.record_id.includes('2024') ? '2024-09-01' : 
                                             assetRow.record_id.includes('2023') ? '2023-03-01' : 
                                             assetRow.record_id.includes('2022') ? '2022-04-10' : '2018-04-01');
                
                // Skip if asset wasn't purchased yet
                if (putToUseDate > yearEnd) continue;
                
                // Handle disposal
                const isDisposed = assetRow.record_id === 'DISP001' && year === '2023-2024';
                
                // Calculate opening WDV based on scenario
                let openingWdv = 0;
                let depreciationAmount = 0;
                
                if (assetRow.record_id === 'HIST001') {
                    // Historical asset
                    if (yearIndex === 0) openingWdv = 850000; // WDV at adoption
                    else if (yearIndex === 1) openingWdv = 769250; // After first year
                    else openingWdv = 696383; // After second year
                } else if (assetRow.record_id === 'FULLY001') {
                    // Fully depreciated asset
                    if (yearIndex === 0) openingWdv = 55000;
                    else if (yearIndex === 1) openingWdv = 2950; // Salvage value reached
                    else openingWdv = 2950; // No more depreciation
                } else if (assetRow.record_id === 'DISP001') {
                    // Disposed asset
                    if (yearIndex === 0) openingWdv = assetRow.gross_amount;
                    else if (yearIndex === 1) openingWdv = 741600; // After first year
                    else openingWdv = 0; // Disposed
                } else {
                    // Regular calculation for other assets
                    if (yearIndex === 0) {
                        openingWdv = assetRow.gross_amount;
                    } else {
                        // Calculate based on previous year's closing
                        const rate = assetRow.depreciation_method === 'WDV' ? 
                            (1 - Math.pow((assetRow.salvage_percentage / 100), (1 / assetRow.life_in_years))) : 
                            (1 / assetRow.life_in_years);
                        
                        // Simplified calculation for mock data
                        openingWdv = assetRow.gross_amount * Math.pow((1 - rate), yearIndex);
                    }
                }
                
                // Calculate depreciation for the year
                if (assetRow.record_id === 'FULLY001' && yearIndex > 1) {
                    depreciationAmount = 0; // Fully depreciated
                } else if (isDisposed) {
                    depreciationAmount = openingWdv - 450000; // Disposal calculation
                } else {
                    const rate = assetRow.depreciation_method === 'WDV' ? 
                        (1 - Math.pow((assetRow.salvage_percentage / 100), (1 / assetRow.life_in_years))) : 
                        (1 / assetRow.life_in_years);
                    
                    if (assetRow.depreciation_method === 'SLM') {
                        depreciationAmount = (assetRow.gross_amount * (1 - assetRow.salvage_percentage / 100)) / assetRow.life_in_years;
                    } else {
                        depreciationAmount = openingWdv * rate;
                    }
                }
                
                const closingWdv = isDisposed ? 0 : Math.max(openingWdv - depreciationAmount, assetRow.gross_amount * (assetRow.salvage_percentage / 100));
                
                // Add extra shift data for SHIFT001
                const secondShiftDays = assetRow.record_id === 'SHIFT001' ? 100 : 0;
                const thirdShiftDays = assetRow.record_id === 'SHIFT001' ? 50 : 0;
                
                await runQuery(
                    `INSERT OR REPLACE INTO asset_yearly_data (
                        asset_id, year_range, opening_wdv, use_days, depreciation_amount, 
                        closing_wdv, second_shift_days, third_shift_days
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        assetRow.id, year, Math.round(openingWdv), 365, Math.round(depreciationAmount),
                        Math.round(closingWdv), secondShiftDays, thirdShiftDays
                    ]
                );
            }
        }

        console.log('✅ Enhanced mock data creation completed successfully!');
        console.log('');
        console.log('🧪 COMPREHENSIVE TEST SCENARIOS CREATED:');
        console.log('   📊 HIST001: Historical Asset (Pre-adoption with WDV)');
        console.log('   🔄 MULTI001: Multi-year Asset with Consistent Depreciation');
        console.log('   ⚡ FULLY001: Fully Depreciated Asset (Zero depreciation expected)');
        console.log('   ❌ DISP001: Asset Disposed in Previous Year');
        console.log('   🆕 NEW001: Current Year Addition (Opening WDV = 0)');
        console.log('   🔄 SHIFT001: Asset with Extra Shift Depreciation');
        console.log('   📏 SLM001: Asset using Straight Line Method');
        console.log('   🏢 LEASE001: Leasehold Asset with Limited Period');
        console.log('   🗑️ SCRAP001: Asset Marked for Scrapping');
        console.log('   💰 HVLONG001: High-Value Long-Life Asset');
        console.log('');
        console.log('🎯 KEY VALIDATION POINTS:');
        console.log('   ✓ Opening WDV accuracy for all scenarios');
        console.log('   ✓ Zero depreciation handling (FULLY001)');
        console.log('   ✓ Historical asset WDV preservation (HIST001)');
        console.log('   ✓ Current year addition handling (NEW001)');
        console.log('   ✓ Inter-year continuity validation');
        console.log('   ✓ Extra shift depreciation calculation (SHIFT001)');
        console.log('   ✓ SLM vs WDV method differences');
        console.log('   ✓ Disposal impact on Schedule III');
        console.log('');
        console.log('🚀 Run Schedule III reports to validate all corrections!');

    } catch (error) {
        console.error('❌ Error creating enhanced mock data:', error);
    } finally {
        db.close((err) => {
            if (err) {
                console.error('❌ Error closing database:', err.message);
            } else {
                console.log('🔌 Database connection closed');
            }
        });
    }
};

populateEnhancedMockData();