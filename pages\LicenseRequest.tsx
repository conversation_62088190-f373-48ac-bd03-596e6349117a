/**
 * License Request Generator
 * Generates license request files for sending to developers
 */

import React, { useState, useEffect } from 'react';
import { api } from '../lib/api';
import type { CompanyInfo } from '../lib/db-server';
import { DownloadIcon, FileTextIcon, SendIcon } from '../lib/Icons';

interface LicenseRequestProps {
    companyId: string;
    companyName: string;
    showAlert: (title: string, message: string, type: 'success' | 'error' | 'info') => void;
}

export function LicenseRequest({ companyId, companyName, showAlert }: LicenseRequestProps) {
    const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isGenerating, setIsGenerating] = useState(false);
    const [requestData, setRequestData] = useState<any>(null);

    useEffect(() => {
        loadCompanyInfo();
    }, [companyId]);

    const loadCompanyInfo = async () => {
        try {
            setIsLoading(true);
            const data = await api.getCompanyInfo(companyId);
            setCompanyInfo(data);
        } catch (error) {
            console.error('Error loading company info:', error);
            showAlert('Error', 'Failed to load company information', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    const generateLicenseRequest = async () => {
        if (!companyInfo) return;

        try {
            setIsGenerating(true);

            // Generate unique request ID
            const requestId = `LR-${Date.now()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
            
            // Calculate suggested validity period (1 year from current FY end)
            const fyEnd = new Date(companyInfo.financialYearEnd);
            const suggestedValidUpto = new Date(fyEnd.getFullYear() + 1, fyEnd.getMonth(), fyEnd.getDate());

            // Create license request data
            const licenseRequest = {
                requestId,
                requestDate: new Date().toISOString(),
                applicationName: 'FAR Sighted Asset Management System',
                applicationVersion: '2.0.0',
                
                // Company Information
                company: {
                    name: companyInfo.companyName,
                    pan: companyInfo.pan,
                    cin: companyInfo.cin,
                    dateOfIncorporation: companyInfo.dateOfIncorporation,
                    financialYearStart: companyInfo.financialYearStart,
                    financialYearEnd: companyInfo.financialYearEnd,
                    firstDateOfAdoption: companyInfo.firstDateOfAdoption,
                    address: {
                        line1: companyInfo.addressLine1,
                        line2: companyInfo.addressLine2,
                        city: companyInfo.city,
                        pin: companyInfo.pin
                    },
                    contact: {
                        email: companyInfo.email,
                        mobile: companyInfo.mobile,
                        person: companyInfo.contactPerson
                    }
                },

                // License Requirements
                license: {
                    requestedValidFrom: companyInfo.financialYearStart,
                    requestedValidUpto: suggestedValidUpto.toISOString().split('T')[0],
                    maxUsers: 5, // Default, can be customized
                    maxCompanies: 1,
                    features: {
                        multiDatabase: true,
                        reports: true,
                        backup: true,
                        audit: true,
                        extraShiftCalculation: true,
                        scheduleIIIReports: true
                    }
                },

                // System Information
                system: {
                    platform: 'Windows',
                    architecture: 'x64',
                    nodeVersion: process.version || 'Unknown',
                    requestedBy: 'Company Administrator',
                    installationPath: companyInfo.dataFolderPath || 'Default'
                },

                // Request Metadata
                metadata: {
                    generatedBy: 'FAR Sighted License Request Generator v1.0',
                    requestType: 'New License',
                    priority: 'Normal',
                    notes: 'Generated automatically from FAR Sighted application'
                }
            };

            setRequestData(licenseRequest);
            showAlert('Success', 'License request generated successfully!', 'success');

        } catch (error) {
            console.error('Error generating license request:', error);
            showAlert('Error', 'Failed to generate license request', 'error');
        } finally {
            setIsGenerating(false);
        }
    };

    const downloadLicenseRequest = () => {
        if (!requestData) return;

        try {
            const fileName = `FAR_LICENSE_REQUEST_${requestData.company.name.replace(/[^A-Za-z0-9]/g, '_')}_${Date.now()}.json`;
            const dataStr = JSON.stringify(requestData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            showAlert('Success', `License request file downloaded: ${fileName}`, 'success');
        } catch (error) {
            console.error('Error downloading file:', error);
            showAlert('Error', 'Failed to download license request file', 'error');
        }
    };

    const copyToClipboard = async () => {
        if (!requestData) return;

        try {
            const dataStr = JSON.stringify(requestData, null, 2);
            await navigator.clipboard.writeText(dataStr);
            showAlert('Success', 'License request data copied to clipboard', 'success');
        } catch (error) {
            console.error('Error copying to clipboard:', error);
            showAlert('Error', 'Failed to copy to clipboard', 'error');
        }
    };

    if (isLoading) {
        return (
            <div className="license-request-container">
                <div className="loading-spinner">Loading company information...</div>
            </div>
        );
    }

    if (!companyInfo) {
        return (
            <div className="license-request-container">
                <div className="error-message">Failed to load company information</div>
            </div>
        );
    }

    return (
        <div className="license-request-container">
            <div className="page-header">
                <h1><FileTextIcon /> License Request Generator</h1>
                <p>Generate license request file for {companyName}</p>
            </div>

            <div className="license-request-content">
                <div className="company-summary">
                    <h3>Company Information</h3>
                    <div className="info-grid">
                        <div><strong>Company:</strong> {companyInfo.companyName}</div>
                        <div><strong>PAN:</strong> {companyInfo.pan}</div>
                        <div><strong>CIN:</strong> {companyInfo.cin}</div>
                        <div><strong>Financial Year:</strong> {companyInfo.financialYearStart} to {companyInfo.financialYearEnd}</div>
                        <div><strong>Contact:</strong> {companyInfo.contactPerson}</div>
                        <div><strong>Email:</strong> {companyInfo.email}</div>
                    </div>
                </div>

                <div className="license-actions">
                    <button 
                        className="btn btn-primary"
                        onClick={generateLicenseRequest}
                        disabled={isGenerating}
                    >
                        <FileTextIcon />
                        {isGenerating ? 'Generating...' : 'Generate License Request'}
                    </button>

                    {requestData && (
                        <div className="request-actions">
                            <button 
                                className="btn btn-success"
                                onClick={downloadLicenseRequest}
                            >
                                <DownloadIcon />
                                Download Request File
                            </button>

                            <button 
                                className="btn btn-secondary"
                                onClick={copyToClipboard}
                            >
                                <SendIcon />
                                Copy to Clipboard
                            </button>
                        </div>
                    )}
                </div>

                {requestData && (
                    <div className="request-preview">
                        <h3>License Request Preview</h3>
                        <div className="request-summary">
                            <div><strong>Request ID:</strong> {requestData.requestId}</div>
                            <div><strong>Generated:</strong> {new Date(requestData.requestDate).toLocaleString()}</div>
                            <div><strong>Application:</strong> {requestData.applicationName} v{requestData.applicationVersion}</div>
                            <div><strong>Requested Validity:</strong> {requestData.license.requestedValidFrom} to {requestData.license.requestedValidUpto}</div>
                        </div>

                        <div className="instructions">
                            <h4>Next Steps:</h4>
                            <ol>
                                <li>Download the license request file</li>
                                <li>Send the file to your FAR Sighted license provider</li>
                                <li>Wait for the license file to be generated</li>
                                <li>Install the received license file in your application</li>
                            </ol>
                        </div>
                    </div>
                )}
            </div>

            <style jsx>{`
                .license-request-container {
                    padding: 20px;
                    max-width: 800px;
                    margin: 0 auto;
                }

                .page-header {
                    margin-bottom: 30px;
                    text-align: center;
                }

                .page-header h1 {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 10px;
                    margin-bottom: 10px;
                }

                .company-summary {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom: 20px;
                }

                .info-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    margin-top: 15px;
                }

                .license-actions {
                    text-align: center;
                    margin: 30px 0;
                }

                .request-actions {
                    margin-top: 20px;
                    display: flex;
                    gap: 15px;
                    justify-content: center;
                }

                .request-preview {
                    background: #e8f5e8;
                    padding: 20px;
                    border-radius: 8px;
                    margin-top: 20px;
                }

                .request-summary {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    margin: 15px 0;
                }

                .instructions {
                    margin-top: 20px;
                }

                .instructions ol {
                    text-align: left;
                    max-width: 500px;
                    margin: 0 auto;
                }

                .loading-spinner, .error-message {
                    text-align: center;
                    padding: 50px;
                    font-size: 18px;
                }

                .error-message {
                    color: #dc3545;
                }
            `}</style>
        </div>
    );
}
