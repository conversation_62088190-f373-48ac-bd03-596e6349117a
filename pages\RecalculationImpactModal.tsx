/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { FC, useMemo } from 'react';
import type { AssetImpact } from '../lib/db-server';
import { DownloadIcon, XIcon, CheckCircleIcon } from '../Icons';
import { formatIndianNumber, exportToExcel } from '../lib/utils';

interface RecalculationImpactModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    impactData: AssetImpact[] | null;
    companyName: string | null;
    year: string | null;
    showAlert: (title: string, message: string, type: 'success' | 'error' | 'info') => void;
}

const DeltaValue: FC<{ value: number }> = ({ value }) => {
    const formattedValue = formatIndianNumber(value);
    const color = value > 0 ? 'var(--accent-success)' : value < 0 ? 'var(--accent-danger)' : 'var(--text-secondary)';
    const sign = value > 0 ? '+' : '';
    return <span style={{ color, fontWeight: 500 }}>({sign}{formattedValue})</span>;
};


export const RecalculationImpactModal: FC<RecalculationImpactModalProps> = ({
    isOpen,
    onClose,
    onConfirm,
    impactData,
    companyName,
    year,
    showAlert
}) => {
    if (!isOpen || !impactData) return null;

    const handleConfirmClick = () => {
        onConfirm();
        onClose(); // Close after confirming
    };

    const handleExport = () => {
        if (!companyName || !year) return;
        
        const exportData: any[] = [];
        impactData.forEach(assetImpact => {
            exportData.push(
                { 'Asset ID': assetImpact.recordId, 'Particulars': assetImpact.assetParticulars },
                { 'Asset ID': 'Year', 'Particulars': 'Old Depr.', 'Old WDV': 'Old WDV', 'New Depr.': 'New Depr.', 'New WDV': 'New WDV' }
            );
            assetImpact.yearImpacts.forEach(yearImpact => {
                exportData.push({
                    'Asset ID': yearImpact.year,
                    'Particulars': yearImpact.oldDepreciation,
                    'Old WDV': yearImpact.oldClosingWdv,
                    'New Depr.': yearImpact.newDepreciation,
                    'New WDV': yearImpact.newClosingWdv,
                });
            });
            exportData.push({}); // Add a blank row between assets
        });

        if (!exportToExcel({ data: exportData, companyName, year, reportName: 'Recalculation_Impact_Analysis' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content modal-xl" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <div className="modal-header-content">
                        <CheckCircleIcon className="icon-info" />
                        <h2>Recalculation Impact Analysis</h2>
                    </div>
                     <div className="actions">
                         <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export Summary</button>
                         <button className="modal-close-btn" style={{marginLeft: '1rem'}} onClick={onClose}>&times;</button>
                    </div>
                </div>

                <div className="recalc-impact-container">
                    <p style={{ marginBottom: '1.5rem', lineHeight: 1.6 }}>
                        Review the changes below. The system will recalculate all data from the unlocked year ({year}) forward. This action is irreversible.
                    </p>
                    {impactData.map((assetImpact) => (
                        <details key={assetImpact.recordId} className="recalc-impact-asset-card" open>
                            <summary>
                                <div className="recalc-impact-summary-header">
                                    <span>{assetImpact.assetParticulars}</span>
                                    <span className="recalc-impact-record-id">{assetImpact.recordId}</span>
                                </div>
                            </summary>
                            <div className="table-container">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Year</th>
                                            <th className="text-right">Old Depreciation</th>
                                            <th className="text-right">New Depreciation</th>
                                            <th className="text-right">Old Closing WDV</th>
                                            <th className="text-right">New Closing WDV</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {assetImpact.yearImpacts.map(yearImpact => (
                                            <tr key={yearImpact.year}>
                                                <td>{yearImpact.year}</td>
                                                <td className="text-right">{formatIndianNumber(yearImpact.oldDepreciation)}</td>
                                                <td className="text-right">
                                                    {formatIndianNumber(yearImpact.newDepreciation)}
                                                    {' '}
                                                    <DeltaValue value={yearImpact.newDepreciation - yearImpact.oldDepreciation} />
                                                </td>
                                                <td className="text-right">{formatIndianNumber(yearImpact.oldClosingWdv)}</td>
                                                <td className="text-right">
                                                    {formatIndianNumber(yearImpact.newClosingWdv)}
                                                    {' '}
                                                    <DeltaValue value={yearImpact.newClosingWdv - yearImpact.oldClosingWdv} />
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </details>
                    ))}
                </div>

                <div className="modal-actions">
                    <button type="button" className="btn btn-secondary" onClick={onClose}><XIcon /> Cancel</button>
                    <button type="button" className="btn btn-primary" onClick={handleConfirmClick}><CheckCircleIcon /> Confirm & Recalculate</button>
                </div>
            </div>
        </div>
    );
};

