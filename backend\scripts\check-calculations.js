import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const dbPath = join(__dirname, '../database/far_sighted.db');

console.log('🔍 Checking Depreciation Calculation Issues...\n');

const checkCalculations = async () => {
    const db = new sqlite3.Database(dbPath);

    // Helper function to query database
    const query = (sql, params = []) => {
        return new Promise((resolve, reject) => {
            db.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    };

    try {
        // Get all assets with their yearly data
        const yearlyData = await query(`
            SELECT 
                a.record_id,
                a.asset_particulars,
                a.gross_amount,
                a.wdv_of_adoption_date,
                ayd.year_range,
                ayd.opening_wdv,
                ayd.depreciation_amount,
                ayd.closing_wdv
            FROM assets a 
            JOIN asset_yearly_data ayd ON a.id = ayd.asset_id 
            ORDER BY a.record_id, ayd.year_range
        `);

        console.log('📊 Current Yearly Data Analysis:\n');

        // Group by asset
        const assetGroups = {};
        yearlyData.forEach(row => {
            if (!assetGroups[row.record_id]) {
                assetGroups[row.record_id] = [];
            }
            assetGroups[row.record_id].push(row);
        });

        // Check each asset for calculation errors
        let errorsFound = false;

        for (const [recordId, years] of Object.entries(assetGroups)) {
            console.log(`🏭 Asset: ${recordId} - ${years[0].asset_particulars}`);
            console.log(`   Purchase Value: ₹${(years[0].gross_amount/100000).toFixed(2)}L`);
            console.log(`   WDV at Adoption: ₹${(years[0].wdv_of_adoption_date/100000).toFixed(2)}L\n`);

            for (let i = 0; i < years.length; i++) {
                const year = years[i];
                const prevYear = i > 0 ? years[i-1] : null;

                console.log(`   📅 ${year.year_range}:`);
                console.log(`      Opening WDV: ₹${(year.opening_wdv/100000).toFixed(2)}L`);
                console.log(`      Depreciation: ₹${(year.depreciation_amount/100000).toFixed(2)}L`);
                console.log(`      Closing WDV: ₹${(year.closing_wdv/100000).toFixed(2)}L`);

                // Check for errors
                if (prevYear && Math.abs(year.opening_wdv - prevYear.closing_wdv) > 1) {
                    console.log(`      ❌ ERROR: Opening WDV (₹${(year.opening_wdv/100000).toFixed(2)}L) != Previous Closing WDV (₹${(prevYear.closing_wdv/100000).toFixed(2)}L)`);
                    errorsFound = true;
                }

                // Check closing calculation
                const calculatedClosing = year.opening_wdv - year.depreciation_amount;
                if (Math.abs(year.closing_wdv - calculatedClosing) > 1) {
                    console.log(`      ❌ ERROR: Closing WDV calculation wrong. Should be ₹${(calculatedClosing/100000).toFixed(2)}L`);
                    errorsFound = true;
                }

                console.log('');
            }
            console.log('---\n');
        }

        if (errorsFound) {
            console.log('💡 ISSUES DETECTED: WDV carry-forward calculations are incorrect!');
            console.log('🔧 Need to recalculate with proper year-over-year WDV continuity.\n');
        } else {
            console.log('✅ All calculations appear correct.\n');
        }

    } catch (error) {
        console.error('❌ Error checking calculations:', error);
    } finally {
        db.close();
    }
};

checkCalculations();