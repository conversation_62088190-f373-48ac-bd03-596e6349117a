/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import type { User, Asset } from './db-server';

export interface DataViewProps {
    companyId: string | null;
    companyName: string | null;
    year: string;
    financialYears: string[];
    theme?: 'light' | 'dark';
    onThemeChange?: (theme: 'light' | 'dark') => void;
    
    // For user management and logging
    loggedInUser: User | null;
    editingUserId?: string | null;
    onEditUser?: (userId: string) => void;
    onAddUserNav?: () => void;
    onUserFormSuccess?: (recoveryKey: string | null, userId: string, username: string) => void;

    // Modal triggers
    showAlert: (title: string, message: string, type: 'success' | 'error' | 'info') => void;
    showConfirmation: (title: string, message: string, onConfirm: () => void) => void;

    // Admin unlock functionality
    unlockedYear: string | null;
    onUnlockYear?: (year: string) => void;
    onLockAndRecalculate?: () => void;

    // Data refresh trigger
    onDataChange?: () => void;
}

export interface AssetValidationErrors {
    [key: string]: string; // keyof Asset -> error message
}

export interface ValidatedAsset extends Asset {
    rowNumber: number; // original row number from CSV
    errors: AssetValidationErrors;
    warnings?: AssetValidationErrors;
}

export interface ScheduleIIIRow {
    assetType: string;
    openingGross: number;
    additionsGross: number;
    deletionsGross: number;
    closingGross: number;
    openingDepreciation: number;
    additionsDepreciation: number;
    deletionsDepreciation: number;
    closingDepreciation: number;
    openingNetBlock: number;
    closingNetBlock: number;
    assetCount: number;
}

export interface ScheduleIIIDetailRow extends ScheduleIIIRow {
    recordId: string;
    assetParticulars: string;
}

export interface CalculationRow {
    recordId: string;
    assetParticulars: string;
    depreciationMethod: 'SLM' | 'WDV';
    depreciationRate: number;
    endOfLifeDate: string;
    salvageAmount: number;
    depreciableAmount: number;
    openingWDV: number;
    useDays: number;
    depreciationForYear: number;
    disposalWDV: number | null;
    closingWDV: number;
    // Added for modals
    putToUseDate: string;
    grossAmount: number;
    lifeInYears: number;
    disposalDate: string | null;
    disposalAmount: number | null;
    gainLossOnDisposal: number | null;
    isExtraShiftApplicable: boolean;
    baseDepreciation: number;
    extraShiftDepreciation: number;
    extraShiftDays2nd: number;
    extraShiftDays3rd: number;
    totalAccumulatedDepreciationOnDisposal: number | null;
    scrapIt?: boolean;
    zeroDepreciationReason: string | null;
}

export interface AssetGroupReportRow {
    assetGroup: string;
    assetSubGroup: string;
    openingGross: number;
    additionsGross: number;
    deletionsGross: number;
    closingGross: number;
    openingDepreciation: number;
    additionsDepreciation: number;
    deletionsDepreciation: number;
    closingDepreciation: number;
    openingNetBlock: number;
    closingNetBlock: number;
}

export interface LedgerWiseRow {
    ledgerName: string;
    openingGross: number;
    additions: number;
    deletionsGross: number;
    closingGross: number;
    depreciation: number;
    deletionAccumulatedDepreciation: number;
    deletionSaleAmount: number;
    deletionWDV: number;
    profitOnDeletion: number;
}

export interface LedgerWiseDetailRow extends LedgerWiseRow {
    recordId: string;
    assetParticulars: string;
}

export interface ScrapReportRow {
    recordId: string;
    assetParticulars: string;
    assetGroup: string;
    assetSubGroup: string;
    putToUseDate: string;
    endOfLifeDate: string;
    lifeInYears: number;
    status: string;
}