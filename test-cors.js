// Test CORS connectivity from frontend to backend
console.log('🧪 Testing CORS connectivity...');

fetch('http://localhost:8090/api/companies')
    .then(response => {
        console.log('✅ Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('✅ Companies received:', data.length);
        data.forEach(company => {
            console.log(`   - ${company.name} (ID: ${company.id})`);
        });
        console.log('🎯 CORS Issue Fixed! Companies should now appear in dropdown.');
    })
    .catch(error => {
        console.error('❌ CORS Error still exists:', error.message);
        console.log('💡 Try refreshing the frontend page.');
    });