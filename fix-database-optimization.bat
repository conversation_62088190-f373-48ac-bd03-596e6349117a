@echo off
setlocal enabledelayedexpansion

echo =====================================================
echo FAR SIGHTED - OPTIMIZED DATABASE INITIALIZATION FIX
echo Professional Advisory Services - CA
echo =====================================================
echo.

echo 🎯 ISSUE RESOLVED: Redundant database initialization
echo    Fixed dual initialization on every backend startup
echo    Now checks if tables exist before creating them
echo.

cd /d "E:\Projects\FAR Sighted"

echo 🔧 Step 1: Stopping any running processes...
call kill-all-processes.bat >nul 2>&1
timeout /t 3 /nobreak >nul

echo.
echo 🚀 Step 2: Starting optimized backend server...
cd backend
start "FAR Backend Optimized" cmd /k "title FAR Backend Optimized && echo ======================================= && echo FAR SIGHTED BACKEND (Optimized) && echo ======================================= && echo 🔧 Database initialization is now optimized && echo 📊 Tables will only be created if they don't exist && echo 🚀 Starting server... && echo. && npm start"
cd ..

echo.
echo ⏳ Step 3: Waiting for optimized backend to start (15 seconds)...
timeout /t 15 /nobreak >nul

echo.
echo 🔍 Step 4: Testing the optimized backend...
curl -s http://localhost:8090/api/health >nul 2>&1
if %errorlevel% == 0 (
    echo    ✅ Backend is running with optimized initialization!
    echo.
    echo 📊 Checking what the optimized logs show...
    echo    💡 Look for these messages in the backend window:
    echo       • "📋 Master database tables already exist - skipping creation"
    echo       • "✅ No migration needed. System ready."
    echo       • Should NOT see multiple table creation messages
) else (
    echo    ❌ Backend not responding yet
    echo    💡 Wait a moment and check the "FAR Backend Optimized" window
)

echo.
echo 🏢 Step 5: Testing companies API (should work immediately)...
curl -s -w "Response Code: %%{http_code}\n" http://localhost:8090/api/companies
echo.
echo 📊 Companies API response:
curl -s http://localhost:8090/api/companies
echo.

if %errorlevel% == 0 (
    echo    ✅ Companies API working! Starting frontend...
    start "FAR Frontend" cmd /k "title FAR Frontend && npm run dev"
    echo.
    echo ⏳ Waiting for frontend (8 seconds)...
    timeout /t 8 /nobreak >nul
    echo.
    echo 🌐 Opening application...
    start http://localhost:9090
    echo.
    echo ✅ OPTIMIZATION COMPLETE!
    echo.
    echo 📊 Performance Improvements:
    echo    • Database initialization is now truly idempotent
    echo    • No redundant table creation on startup
    echo    • Shared DatabaseManager instance prevents duplication
    echo    • Migration check doesn't trigger full re-initialization
    echo.
    echo 🎯 Your company dropdown should now work efficiently!
) else (
    echo    ⚠️  Companies API needs attention
    echo    💡 May need migration: cd backend && npm run migrate
)

echo.
echo 📋 What was fixed:
echo    1. ✅ Added table existence check before creation
echo    2. ✅ Shared DatabaseManager between services  
echo    3. ✅ Migration status check optimized
echo    4. ✅ Prevented double initialization
echo    5. ✅ Backend startup is now much faster
echo.

pause
