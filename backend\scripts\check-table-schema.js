/**
 * Check table schema to understand column names
 */

import DatabaseManager from '../services/database-manager/DatabaseManager.js';

async function checkSchema() {
    console.log('🔍 Checking asset_yearly_data table schema...\n');
    
    try {
        const dbManager = new DatabaseManager();
        const companies = await dbManager.getAllCompanies();
        
        if (companies.length > 0) {
            const company = companies[0];
            console.log(`📊 Checking schema for: ${company.name}`);
            
            const companyDb = await dbManager.getCompanyDatabase(company.id);
            
            // Get table schema
            const schema = await companyDb.all("PRAGMA table_info(asset_yearly_data)");
            
            console.log('\n📋 asset_yearly_data table columns:');
            schema.forEach(col => {
                console.log(`   ${col.name} (${col.type}) ${col.notnull ? 'NOT NULL' : ''} ${col.dflt_value ? `DEFAULT ${col.dflt_value}` : ''}`);
            });
            
            // Also check assets table to see what columns are available
            const assetsSchema = await companyDb.all("PRAGMA table_info(assets)");
            
            console.log('\n📋 assets table columns:');
            assetsSchema.forEach(col => {
                console.log(`   ${col.name} (${col.type}) ${col.notnull ? 'NOT NULL' : ''} ${col.dflt_value ? `DEFAULT ${col.dflt_value}` : ''}`);
            });
            
        } else {
            console.log('❌ No companies found');
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

checkSchema();
