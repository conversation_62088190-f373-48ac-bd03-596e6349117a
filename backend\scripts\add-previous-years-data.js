/**
 * <PERSON><PERSON><PERSON> to add previous years data for Schedule III testing
 * This script adds historical asset data to test carry-forward functionality
 */

import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import DatabaseManager from '../services/database-manager/DatabaseManager.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database paths
const companiesDir = path.join(__dirname, '../database/companies');

// Previous years to add data for
const previousYears = ['2022-23', '2023-24'];

// Sample historical assets with different scenarios
const historicalAssets = [
    {
        recordId: 'HIST001',
        assetParticulars: 'Manufacturing Plant - Line 1',
        bookEntryDate: '2020-05-15',
        putToUseDate: '2020-06-01',
        basicAmount: 5000000,
        dutiesTaxes: 500000,
        grossAmount: 5500000,
        wdvOfAdoptionDate: 4200000, // Asset purchased before adoption
        vendor: 'Industrial Equipment Ltd',
        invoiceNo: 'INV-2020-001',
        location: 'Factory Floor A',
        assetId: 'PLT-001',
        ledgerNameInBooks: 'Plant & Machinery',
        assetGroup: 'Plant & Machinery',
        assetSubGroup: 'Manufacturing Equipment',
        scheduleIIIClassification: 'Plant and machinery',
        depreciationMethod: 'WDV',
        lifeInYears: 15,
        salvagePercentage: 5,
        isLeasehold: false,
        disposalDate: null,
        disposalAmount: null,
        scrapIt: false
    },
    {
        recordId: 'HIST002',
        assetParticulars: 'Office Building - Main Block',
        bookEntryDate: '2019-03-20',
        putToUseDate: '2019-04-01',
        basicAmount: 15000000,
        dutiesTaxes: 1500000,
        grossAmount: 16500000,
        wdvOfAdoptionDate: 14200000, // Asset purchased before adoption
        vendor: 'Real Estate Developers',
        invoiceNo: 'SALE-2019-005',
        location: 'Corporate Office',
        assetId: 'BLDG-001',
        ledgerNameInBooks: 'Buildings',
        assetGroup: 'Buildings',
        assetSubGroup: 'Factory buildings',
        scheduleIIIClassification: 'Buildings',
        depreciationMethod: 'SLM',
        lifeInYears: 30,
        salvagePercentage: 5,
        isLeasehold: false,
        disposalDate: null,
        disposalAmount: null,
        scrapIt: false
    },
    {
        recordId: 'HIST003',
        assetParticulars: 'Computer Server - Data Center',
        bookEntryDate: '2021-08-10',
        putToUseDate: '2021-09-01',
        basicAmount: 800000,
        dutiesTaxes: 144000,
        grossAmount: 944000,
        wdvOfAdoptionDate: 750000, // Asset purchased before adoption
        vendor: 'Tech Solutions Inc',
        invoiceNo: 'INV-2021-089',
        location: 'Data Center',
        assetId: 'SRV-001',
        ledgerNameInBooks: 'Computer Equipment',
        assetGroup: 'Office equipment',
        assetSubGroup: 'Computers',
        scheduleIIIClassification: 'Office equipment',
        depreciationMethod: 'WDV',
        lifeInYears: 6,
        salvagePercentage: 10,
        isLeasehold: false,
        disposalDate: '2023-12-31', // Disposed in 2023-24
        disposalAmount: 200000,
        scrapIt: false
    },
    {
        recordId: 'HIST004',
        assetParticulars: 'Delivery Vehicle - Truck',
        bookEntryDate: '2022-01-15',
        putToUseDate: '2022-02-01',
        basicAmount: 1200000,
        dutiesTaxes: 180000,
        grossAmount: 1380000,
        wdvOfAdoptionDate: null, // Asset purchased after adoption
        vendor: 'Commercial Vehicles Ltd',
        invoiceNo: 'VEH-2022-003',
        location: 'Transport Yard',
        assetId: 'VEH-001',
        ledgerNameInBooks: 'Motor Vehicles',
        assetGroup: 'Motor vehicles',
        assetSubGroup: 'Motor cars',
        scheduleIIIClassification: 'Motor vehicles',
        depreciationMethod: 'WDV',
        lifeInYears: 8,
        salvagePercentage: 5,
        isLeasehold: false,
        disposalDate: null,
        disposalAmount: null,
        scrapIt: false
    }
];

// Function to calculate depreciation for a given year
function calculateDepreciation(asset, year, openingWDV) {
    const [startYearStr, endYearStr] = year.split('-');
    const fyStart = new Date(`${startYearStr}-04-01T00:00:00.000Z`);
    const fyEnd = new Date(`${endYearStr}-03-31T23:59:59.999Z`);
    
    const putToUseDate = new Date(asset.putToUseDate);
    const disposalDate = asset.disposalDate ? new Date(asset.disposalDate) : null;
    
    // Calculate use days in this financial year
    const startOfUseInFY = putToUseDate > fyStart ? putToUseDate : fyStart;
    const endOfUseInFY = disposalDate && disposalDate < fyEnd ? disposalDate : fyEnd;
    
    let useDays = 0;
    if (startOfUseInFY <= endOfUseInFY) {
        useDays = Math.ceil((endOfUseInFY - startOfUseInFY) / (1000 * 60 * 60 * 24)) + 1;
    }
    
    if (useDays <= 0) return { depreciation: 0, useDays: 0, closingWDV: openingWDV };
    
    const salvageValue = Math.round(asset.grossAmount * (asset.salvagePercentage / 100));
    let depreciation = 0;
    
    if (asset.depreciationMethod === 'SLM') {
        const depreciableAmount = asset.grossAmount - salvageValue;
        const yearlyDepreciation = depreciableAmount / asset.lifeInYears;
        depreciation = Math.round((yearlyDepreciation / 365.25) * useDays);
    } else { // WDV
        if (openingWDV > salvageValue) {
            const rate = 1 - Math.pow((salvageValue / asset.grossAmount), (1 / asset.lifeInYears));
            const yearlyDepreciation = openingWDV * rate;
            depreciation = Math.round((yearlyDepreciation / 365.25) * useDays);
        }
    }
    
    const closingWDV = Math.max(openingWDV - depreciation, salvageValue);
    
    return { depreciation, useDays, closingWDV };
}

// Function to add historical data to a company database
async function addHistoricalDataToCompany(companyDir) {
    const dbPath = path.join(companyDir, 'company.db');

    if (!fs.existsSync(dbPath)) {
        console.log(`⚠️  Database not found: ${dbPath}`);
        return;
    }

    // Extract company ID from directory name
    const companyId = path.basename(companyDir).split('-')[0];

    try {
        console.log(`\n📊 Adding historical data to: ${path.basename(companyDir)}`);

        // Get company database directly
        const dbManager = new DatabaseManager();
        let companyDb;

        try {
            companyDb = await dbManager.getCompanyDatabase(companyId);
        } catch (error) {
            // If company not found in master, try to access database file directly
            console.log(`   ⚠️  Company not in master database, accessing file directly...`);
            const Database = (await import('better-sqlite3')).default;
            companyDb = new Database(dbPath);
        }
        
        // Add historical assets
        const insertAssetSql = `
            INSERT OR REPLACE INTO assets (
                record_id, asset_particulars, book_entry_date, put_to_use_date,
                basic_amount, duties_taxes, gross_amount, wdv_of_adoption_date,
                vendor, invoice_no, location, asset_id, ledger_name_in_books,
                asset_group, asset_sub_group, schedule_iii_classification,
                depreciation_method, life_in_years, salvage_percentage, is_leasehold,
                disposal_date, disposal_amount, scrap_it, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        for (const asset of historicalAssets) {
            const now = new Date().toISOString();
            await companyDb.run(insertAssetSql, [
                asset.recordId, asset.assetParticulars, asset.bookEntryDate, asset.putToUseDate,
                asset.basicAmount, asset.dutiesTaxes, asset.grossAmount, asset.wdvOfAdoptionDate,
                asset.vendor, asset.invoiceNo, asset.location, asset.assetId, asset.ledgerNameInBooks,
                asset.assetGroup, asset.assetSubGroup, asset.scheduleIIIClassification,
                asset.depreciationMethod, asset.lifeInYears, asset.salvagePercentage, asset.isLeasehold ? 1 : 0,
                asset.disposalDate, asset.disposalAmount, asset.scrapIt ? 1 : 0, now, now
            ]);
        }
        
        // Add yearly data for previous years
        const insertYearlySql = `
            INSERT OR REPLACE INTO asset_yearly_data (
                asset_record_id, financial_year, opening_wdv, depreciation_for_year,
                use_days, closing_wdv, disposal_wdv, disposal_amount, gain_loss_on_disposal,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        for (const year of previousYears) {
            console.log(`  📅 Processing year: ${year}`);
            
            for (const asset of historicalAssets) {
                // Calculate opening WDV for this year
                let openingWDV;
                if (year === '2022-23') {
                    // First year - use adoption WDV or gross amount
                    openingWDV = asset.wdvOfAdoptionDate || asset.grossAmount;
                } else {
                    // Get closing WDV from previous year
                    const prevYear = previousYears[previousYears.indexOf(year) - 1];
                    const prevData = await companyDb.get(
                        'SELECT closing_wdv FROM asset_yearly_data WHERE asset_record_id = ? AND financial_year = ?',
                        [asset.recordId, prevYear]
                    );
                    openingWDV = prevData ? prevData.closing_wdv : (asset.wdvOfAdoptionDate || asset.grossAmount);
                }
                
                // Calculate depreciation for this year
                const { depreciation, useDays, closingWDV } = calculateDepreciation(asset, year, openingWDV);
                
                // Calculate disposal values if asset was disposed in this year
                let disposalWDV = null;
                let disposalAmount = null;
                let gainLossOnDisposal = null;
                
                if (asset.disposalDate) {
                    const disposalYear = new Date(asset.disposalDate).getFullYear();
                    const [, endYearStr] = year.split('-');
                    
                    if (disposalYear.toString() === `20${endYearStr}`) {
                        disposalWDV = closingWDV;
                        disposalAmount = asset.disposalAmount;
                        gainLossOnDisposal = disposalAmount - disposalWDV;
                    }
                }
                
                const now = new Date().toISOString();
                await companyDb.run(insertYearlySql, [
                    asset.recordId, year, openingWDV, depreciation, useDays, closingWDV,
                    disposalWDV, disposalAmount, gainLossOnDisposal, now, now
                ]);
            }
        }
        
        // Add extra shift days for applicable assets
        const insertExtraShiftSql = `
            INSERT OR REPLACE INTO extra_shift_days (
                asset_record_id, financial_year, second_shift_days, third_shift_days,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?)
        `;

        // Add some extra shift days for manufacturing equipment
        for (const year of previousYears) {
            const now = new Date().toISOString();
            await companyDb.run(insertExtraShiftSql, ['HIST001', year, 120, 60, now, now]); // Manufacturing plant worked extra shifts
        }

        console.log(`  ✅ Added ${historicalAssets.length} historical assets`);
        console.log(`  ✅ Added yearly data for ${previousYears.length} years`);
        console.log(`  ✅ Added extra shift data for manufacturing equipment`);

    } catch (error) {
        console.error(`❌ Error adding historical data: ${error.message}`);
        throw error;
    }
}

// Main execution
async function main() {
    console.log('🚀 Adding Previous Years Data for Schedule III Testing');
    console.log('====================================================\n');
    
    if (!fs.existsSync(companiesDir)) {
        console.error('❌ Companies directory not found:', companiesDir);
        process.exit(1);
    }
    
    const companyDirs = fs.readdirSync(companiesDir)
        .map(name => path.join(companiesDir, name))
        .filter(dir => fs.statSync(dir).isDirectory());
    
    if (companyDirs.length === 0) {
        console.error('❌ No company directories found');
        process.exit(1);
    }
    
    console.log(`📁 Found ${companyDirs.length} company database(s)`);
    
    // Only process the first 3 companies to avoid issues with test companies
    const mainCompanies = companyDirs.slice(0, 3);

    for (const companyDir of mainCompanies) {
        try {
            await addHistoricalDataToCompany(companyDir);
        } catch (error) {
            console.error(`❌ Failed to add data to ${path.basename(companyDir)}: ${error.message}`);
            console.log('   Continuing with next company...\n');
        }
    }
    
    console.log('\n🎉 Historical data addition completed successfully!');
    console.log('\n📋 Summary of added data:');
    console.log(`   • ${historicalAssets.length} historical assets`);
    console.log(`   • ${previousYears.length} previous financial years (${previousYears.join(', ')})`);
    console.log('   • Depreciation calculations for all years');
    console.log('   • Asset disposal scenario (HIST003 disposed in 2023-24)');
    console.log('   • Extra shift days for manufacturing equipment');
    console.log('\n🧪 Test scenarios included:');
    console.log('   • Assets purchased before adoption date');
    console.log('   • Assets purchased after adoption date');
    console.log('   • Both WDV and SLM depreciation methods');
    console.log('   • Asset disposal with gain/loss calculation');
    console.log('   • Extra shift depreciation');
    console.log('   • Different asset categories (Plant, Building, Office, Vehicle)');
}

// Run the script
main().catch(error => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
});
