<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Companies API Call Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Direct Companies API Call Test</h1>
        <p>This test makes a direct call to the companies API without any frontend framework interference.</p>
        
        <div id="status" class="result loading">
            ⏳ Ready to test...
        </div>
        
        <button onclick="testDirectCall()">Test Direct Call</button>
        <button onclick="testWithoutCredentials()">Test Without Credentials</button>
        <button onclick="testWithMinimalHeaders()">Test Minimal Headers</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:8090/api/companies';
        
        function updateStatus(message, type = 'loading') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `result ${type}`;
        }
        
        function showResults(data) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <h3>📊 Results:</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        }
        
        async function testDirectCall() {
            updateStatus('🔍 Testing direct call with credentials...', 'loading');
            
            try {
                console.log('Making direct call to:', API_URL);
                
                const response = await fetch(API_URL, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                if (response.ok) {
                    const companies = await response.json();
                    console.log('Companies received:', companies);
                    
                    updateStatus(`✅ Success! Found ${companies.length} companies`, 'success');
                    showResults(companies);
                } else {
                    const errorText = await response.text();
                    console.error('Error response:', errorText);
                    
                    updateStatus(`❌ Failed with status: ${response.status}`, 'error');
                    showResults({ status: response.status, error: errorText });
                }
                
            } catch (error) {
                console.error('Request failed:', error);
                updateStatus(`❌ Request failed: ${error.message}`, 'error');
                showResults({ error: error.message, stack: error.stack });
            }
        }
        
        async function testWithoutCredentials() {
            updateStatus('🔍 Testing without credentials...', 'loading');
            
            try {
                const response = await fetch(API_URL, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                    // No credentials
                });
                
                if (response.ok) {
                    const companies = await response.json();
                    updateStatus(`✅ Without credentials: Found ${companies.length} companies`, 'success');
                    showResults(companies);
                } else {
                    const errorText = await response.text();
                    updateStatus(`❌ Without credentials failed: ${response.status}`, 'error');
                    showResults({ status: response.status, error: errorText });
                }
                
            } catch (error) {
                updateStatus(`❌ Without credentials error: ${error.message}`, 'error');
                showResults({ error: error.message });
            }
        }
        
        async function testWithMinimalHeaders() {
            updateStatus('🔍 Testing with minimal headers...', 'loading');
            
            try {
                const response = await fetch(API_URL);
                
                if (response.ok) {
                    const companies = await response.json();
                    updateStatus(`✅ Minimal headers: Found ${companies.length} companies`, 'success');
                    showResults(companies);
                } else {
                    const errorText = await response.text();
                    updateStatus(`❌ Minimal headers failed: ${response.status}`, 'error');
                    showResults({ status: response.status, error: errorText });
                }
                
            } catch (error) {
                updateStatus(`❌ Minimal headers error: ${error.message}`, 'error');
                showResults({ error: error.message });
            }
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(testDirectCall, 1000);
        });
    </script>
</body>
</html>
