@echo off
echo =============================================
echo FAR SIGHTED - QUICK CORS FIX RESTART
echo Professional Advisory Services - CA
echo =============================================
echo.

echo 🔧 Quick restart with CORS fix...

REM Stop processes
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

REM Start backend
cd /d "E:\Projects\FAR Sighted\backend"
start "Backend (CORS Fixed)" cmd /k "npm start"

REM Start frontend  
cd /d "E:\Projects\FAR Sighted"
start "Frontend" cmd /k "npm run dev"

echo ✅ Both services starting with CORS fix!
echo 🌐 Opening app in 10 seconds...
timeout /t 10 /nobreak >nul
start http://localhost:9090

echo.
echo 🎯 Company dropdown should now work!
echo    No more CORS errors in browser console.
echo.

pause
