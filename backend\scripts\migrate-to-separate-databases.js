/**
 * DATABASE MIGRATION SCRIPT
 * Professional Advisory Services - Chartered Accountant
 * 
 * Migrates from single database architecture to separate database per company
 * This script ensures data integrity and zero data loss during migration.
 */

import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';
import dbManager from '../services/multi-company-database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class DatabaseMigrator {
    constructor() {
        this.oldDbPath = join(__dirname, '../database/far_sighted.db');
        this.backupPath = join(__dirname, '../database/backups');
        this.oldDb = null;
    }

    async migrate() {
        console.log('🔄 STARTING DATABASE MIGRATION TO SEPARATE DATABASES');
        console.log('=====================================================');

        try {
            // Step 1: Backup original database
            await this.backupOriginalDatabase();

            // Step 2: Connect to original database
            await this.connectToOriginalDatabase();

            // Step 3: Migrate global data to master database
            await this.migrateGlobalData();

            // Step 4: Get all companies and migrate their data
            const companies = await this.getCompaniesFromOldDb();
            console.log(`\n📊 Found ${companies.length} companies to migrate:`);
            companies.forEach(c => console.log(`   - ${c.company_name} (${c.id})`));

            for (const company of companies) {
                await this.migrateCompanyData(company);
            }

            // Step 5: Verify migration
            await this.verifyMigration(companies);

            // Step 6: Cleanup
            await this.cleanup();

            console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY!');
            console.log('✅ All data migrated to separate company databases');
            console.log('✅ Original database backed up safely');
            console.log('✅ New architecture is ready for use');

        } catch (error) {
            console.error('❌ Migration failed:', error);
            console.log('🔄 Rolling back changes...');
            await this.rollback();
            throw error;
        }
    }

    async backupOriginalDatabase() {
        console.log('\n💾 Step 1: Backing up original database...');
        
        try {
            // Ensure backup directory exists
            await fs.mkdir(this.backupPath, { recursive: true });

            // Create timestamped backup
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupFile = join(this.backupPath, `far_sighted_backup_${timestamp}.db`);

            // Check if original database exists
            try {
                await fs.access(this.oldDbPath);
            } catch {
                console.log('⚠️ Original database not found - this might be a fresh installation');
                return;
            }

            // Copy database file
            await fs.copyFile(this.oldDbPath, backupFile);
            console.log(`✅ Original database backed up to: ${backupFile}`);

        } catch (error) {
            console.error('❌ Backup failed:', error);
            throw error;
        }
    }

    async connectToOriginalDatabase() {
        console.log('\n🔌 Step 2: Connecting to original database...');

        return new Promise((resolve, reject) => {
            this.oldDb = new sqlite3.Database(this.oldDbPath, (err) => {
                if (err) {
                    if (err.code === 'SQLITE_CANTOPEN') {
                        console.log('⚠️ Original database not found - creating fresh setup');
                        resolve();
                    } else {
                        reject(err);
                    }
                } else {
                    console.log('✅ Connected to original database');
                    resolve();
                }
            });
        });
    }

    async migrateGlobalData() {
        console.log('\n👥 Step 3: Migrating global data to master database...');

        if (!this.oldDb) {
            console.log('⚠️ No original database found - skipping global data migration');
            return;
        }

        try {
            // Migrate users
            const users = await this.getAllFromOldDb('SELECT * FROM users');
            console.log(`📊 Migrating ${users.length} users...`);

            for (const user of users) {
                await dbManager.runOnMaster(
                    `INSERT OR IGNORE INTO users (
                        id, username, password_hash, role, recovery_key_hash, 
                        has_saved_recovery_key, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        user.id, user.username, user.password_hash, user.role,
                        user.recovery_key_hash, user.has_saved_recovery_key,
                        user.created_at, user.updated_at
                    ]
                );
            }

            // Migrate system audit logs
            const systemLogs = await this.getAllFromOldDb('SELECT * FROM audit_logs');
            console.log(`📊 Migrating ${systemLogs.length} system audit logs...`);

            for (const log of systemLogs) {
                await dbManager.runOnMaster(
                    `INSERT OR IGNORE INTO system_audit_logs (
                        id, user_id, username, company_id, action, details, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
                    [
                        log.id, log.user_id, log.username, null, // company_id will be null for old logs
                        log.action, log.details, log.timestamp
                    ]
                );
            }

            console.log('✅ Global data migration completed');

        } catch (error) {
            console.error('❌ Global data migration failed:', error);
            throw error;
        }
    }

    async getCompaniesFromOldDb() {
        if (!this.oldDb) {
            // Return default company for fresh installation
            return [{
                id: 'c1001',
                company_name: 'Sample Company Ltd',
                pan: '**********',
                cin: 'U72200DL2018PTC334567',
                date_of_incorporation: '2018-03-15',
                financial_year_start: '2024-04-01',
                financial_year_end: '2025-03-31',
                first_date_of_adoption: '2018-04-01',
                address_line1: 'Sample Address',
                city: 'Sample City',
                pin: '110001',
                email: '<EMAIL>',
                mobile: '**********',
                contact_person: 'Sample Person',
                license_valid_upto: '2025-12-31'
            }];
        }

        return this.getAllFromOldDb('SELECT * FROM companies ORDER BY company_name');
    }

    async migrateCompanyData(company) {
        console.log(`\n🏢 Step 4: Migrating data for ${company.company_name}...`);

        try {
            // Create company database
            await dbManager.createCompanyDatabase(company);

            if (!this.oldDb) {
                console.log('⚠️ No original database - creating empty company database');
                return;
            }

            // Migrate financial years
            const financialYears = await this.getAllFromOldDb(
                'SELECT * FROM financial_years WHERE company_id = ?', 
                [company.id]
            );
            console.log(`   📅 Migrating ${financialYears.length} financial years...`);

            for (const fy of financialYears) {
                await dbManager.runOnCompanyDb(company.id,
                    'INSERT OR IGNORE INTO financial_years (year_range, is_locked, created_at) VALUES (?, ?, ?)',
                    [fy.year_range, fy.is_locked, fy.created_at]
                );
            }

            // Migrate statutory rates
            const statutoryRates = await this.getAllFromOldDb(
                'SELECT * FROM statutory_rates WHERE company_id = ?',
                [company.id]
            );
            console.log(`   📊 Migrating ${statutoryRates.length} statutory rates...`);

            for (const rate of statutoryRates) {
                await dbManager.runOnCompanyDb(company.id,
                    `INSERT OR IGNORE INTO statutory_rates (
                        is_statutory, tangibility, asset_group, asset_sub_group,
                        extra_shift_depreciation, useful_life_years, schedule_ii_classification
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
                    [
                        rate.is_statutory, rate.tangibility, rate.asset_group, rate.asset_sub_group,
                        rate.extra_shift_depreciation, rate.useful_life_years, rate.schedule_ii_classification
                    ]
                );
            }

            // Migrate assets
            const assets = await this.getAllFromOldDb(
                'SELECT * FROM assets WHERE company_id = ?',
                [company.id]
            );
            console.log(`   🏭 Migrating ${assets.length} assets...`);

            for (const asset of assets) {
                await dbManager.runOnCompanyDb(company.id,
                    `INSERT OR IGNORE INTO assets (
                        record_id, asset_particulars, book_entry_date, put_to_use_date,
                        basic_amount, duties_taxes, gross_amount, vendor, invoice_no, model_make,
                        location, asset_id, remarks, ledger_name_in_books, asset_group,
                        asset_sub_group, schedule_iii_classification, disposal_date, disposal_amount,
                        salvage_percentage, wdv_of_adoption_date, is_leasehold, depreciation_method,
                        life_in_years, lease_period, scrap_it, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        asset.record_id, asset.asset_particulars, asset.book_entry_date, asset.put_to_use_date,
                        asset.basic_amount, asset.duties_taxes, asset.gross_amount, asset.vendor, asset.invoice_no,
                        asset.model_make, asset.location, asset.asset_id, asset.remarks, asset.ledger_name_in_books,
                        asset.asset_group, asset.asset_sub_group, asset.schedule_iii_classification,
                        asset.disposal_date, asset.disposal_amount, asset.salvage_percentage, asset.wdv_of_adoption_date,
                        asset.is_leasehold, asset.depreciation_method, asset.life_in_years, asset.lease_period,
                        asset.scrap_it, asset.created_at, asset.updated_at
                    ]
                );
            }

            // Migrate asset yearly data
            const assetYearlyData = await this.getAllFromOldDb(
                `SELECT ayd.* FROM asset_yearly_data ayd 
                 JOIN assets a ON ayd.asset_id = a.id 
                 WHERE a.company_id = ?`,
                [company.id]
            );
            console.log(`   📈 Migrating ${assetYearlyData.length} asset yearly data records...`);

            for (const yearlyData of assetYearlyData) {
                // Get the new asset ID in the company database
                const newAsset = await dbManager.getFromCompanyDb(company.id,
                    'SELECT id FROM assets WHERE record_id = (SELECT record_id FROM assets WHERE id = ? LIMIT 1)',
                    [yearlyData.asset_id]
                );

                if (newAsset) {
                    await dbManager.runOnCompanyDb(company.id,
                        `INSERT OR IGNORE INTO asset_yearly_data (
                            asset_id, year_range, opening_wdv, use_days, depreciation_amount,
                            closing_wdv, second_shift_days, third_shift_days, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                            newAsset.id, yearlyData.year_range, yearlyData.opening_wdv, yearlyData.use_days,
                            yearlyData.depreciation_amount, yearlyData.closing_wdv, yearlyData.second_shift_days,
                            yearlyData.third_shift_days, yearlyData.created_at, yearlyData.updated_at
                        ]
                    );
                }
            }

            // Migrate extra ledgers
            const extraLedgers = await this.getAllFromOldDb(
                'SELECT * FROM extra_ledgers WHERE company_id = ?',
                [company.id]
            );
            console.log(`   📚 Migrating ${extraLedgers.length} extra ledgers...`);

            for (const ledger of extraLedgers) {
                await dbManager.runOnCompanyDb(company.id,
                    'INSERT OR IGNORE INTO extra_ledgers (ledger_name, created_at) VALUES (?, ?)',
                    [ledger.ledger_name, ledger.created_at]
                );
            }

            // Grant admin access to all users for this company (temporary)
            const users = await dbManager.allFromMaster('SELECT id FROM users');
            for (const user of users) {
                await dbManager.grantUserAccess(user.id, company.id, 'admin', 'system_migration');
            }

            console.log(`✅ Migration completed for ${company.company_name}`);

        } catch (error) {
            console.error(`❌ Failed to migrate ${company.company_name}:`, error);
            throw error;
        }
    }

    async verifyMigration(companies) {
        console.log('\n🔍 Step 5: Verifying migration...');

        try {
            // Verify master database
            const masterUsers = await dbManager.allFromMaster('SELECT COUNT(*) as count FROM users');
            const masterCompanies = await dbManager.allFromMaster('SELECT COUNT(*) as count FROM companies');
            
            console.log(`✅ Master database verification:`);
            console.log(`   - Users: ${masterUsers[0].count}`);
            console.log(`   - Companies: ${masterCompanies[0].count}`);

            // Verify each company database
            for (const company of companies) {
                const assets = await dbManager.allFromCompanyDb(company.id, 'SELECT COUNT(*) as count FROM assets');
                const financialYears = await dbManager.allFromCompanyDb(company.id, 'SELECT COUNT(*) as count FROM financial_years');
                
                console.log(`✅ ${company.company_name} verification:`);
                console.log(`   - Assets: ${assets[0].count}`);
                console.log(`   - Financial Years: ${financialYears[0].count}`);
            }

            console.log('✅ Migration verification completed successfully');

        } catch (error) {
            console.error('❌ Migration verification failed:', error);
            throw error;
        }
    }

    async cleanup() {
        console.log('\n🧹 Step 6: Cleanup...');

        try {
            // Close old database connection
            if (this.oldDb) {
                await new Promise((resolve) => {
                    this.oldDb.close((err) => {
                        if (err) console.error('Warning: Error closing old database:', err);
                        resolve();
                    });
                });
            }

            // Rename old database file
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const renamedPath = join(__dirname, `../database/far_sighted_old_${timestamp}.db`);
            
            try {
                await fs.rename(this.oldDbPath, renamedPath);
                console.log(`✅ Original database renamed to: ${renamedPath}`);
            } catch (error) {
                if (error.code !== 'ENOENT') {
                    console.log('⚠️ Warning: Could not rename original database file');
                }
            }

        } catch (error) {
            console.error('⚠️ Warning: Cleanup had issues:', error);
        }
    }

    async rollback() {
        console.log('🔄 Rolling back migration...');
        // Implementation would restore from backup if needed
        console.log('⚠️ Manual rollback required - restore from backup if needed');
    }

    // Helper methods
    async getAllFromOldDb(sql, params = []) {
        if (!this.oldDb) return [];
        
        return new Promise((resolve, reject) => {
            this.oldDb.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });
    }
}

// Main migration execution
const runMigration = async () => {
    const migrator = new DatabaseMigrator();
    
    try {
        await migrator.migrate();
        console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY!');
        console.log('\n📋 NEXT STEPS:');
        console.log('1. Update your application to use the new multi-company database service');
        console.log('2. Test all functionality with the new architecture');
        console.log('3. Update your backup procedures for per-company databases');
        console.log('4. Configure user access permissions per company');
    } catch (error) {
        console.error('\n❌ MIGRATION FAILED!');
        console.error('Error:', error.message);
        console.log('\n🔧 RECOVERY STEPS:');
        console.log('1. Check the backup files in database/backups/');
        console.log('2. Restore from backup if needed');
        console.log('3. Review error logs and fix issues');
        console.log('4. Re-run migration after fixing issues');
        process.exit(1);
    }
};

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runMigration();
}

export default DatabaseMigrator;