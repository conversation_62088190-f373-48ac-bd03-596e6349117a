<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend-Backend Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .companies-list {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .company-item {
            padding: 8px;
            margin: 5px 0;
            background-color: white;
            border-radius: 3px;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔗 Frontend-Backend Connection Test</h1>
        <p>This page tests if the frontend (port 9090) can successfully connect to the backend (port 8090) and retrieve the companies list.</p>
        
        <div id="status" class="test-result loading">
            ⏳ Ready to test connection...
        </div>
        
        <button onclick="testHealthEndpoint()">Test Health Endpoint</button>
        <button onclick="testCompaniesEndpoint()">Test Companies API</button>
        <button onclick="testFullConnection()">Test Full Connection</button>
        
        <div id="companies-container"></div>
        
        <div id="details" style="margin-top: 30px;">
            <h3>Connection Details:</h3>
            <ul>
                <li><strong>Frontend URL:</strong> http://localhost:9090</li>
                <li><strong>Backend URL:</strong> http://localhost:8090</li>
                <li><strong>API Base URL:</strong> http://localhost:8090/api</li>
                <li><strong>Health Endpoint:</strong> http://localhost:8090/api/health</li>
                <li><strong>Companies Endpoint:</strong> http://localhost:8090/api/companies</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8090/api';
        
        function updateStatus(message, type = 'loading') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `test-result ${type}`;
        }
        
        function showCompanies(companies) {
            const container = document.getElementById('companies-container');
            if (companies && companies.length > 0) {
                container.innerHTML = `
                    <div class="companies-list">
                        <h3>📊 Companies Found (${companies.length}):</h3>
                        ${companies.map(company => `
                            <div class="company-item">
                                <strong>${company.name}</strong> (ID: ${company.id})
                            </div>
                        `).join('')}
                    </div>
                `;
            } else {
                container.innerHTML = `
                    <div class="companies-list">
                        <h3>⚠️ No companies found</h3>
                    </div>
                `;
            }
        }
        
        async function testHealthEndpoint() {
            updateStatus('🔍 Testing health endpoint...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                
                if (response.ok) {
                    const data = await response.json();
                    updateStatus(`✅ Health endpoint working! Status: ${data.status}`, 'success');
                    return true;
                } else {
                    updateStatus(`❌ Health endpoint failed with status: ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                updateStatus(`❌ Health endpoint error: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function testCompaniesEndpoint() {
            updateStatus('🔍 Testing companies endpoint...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE}/companies`);
                
                if (response.ok) {
                    const companies = await response.json();
                    updateStatus(`✅ Companies endpoint working! Found ${companies.length} companies`, 'success');
                    showCompanies(companies);
                    return companies;
                } else {
                    updateStatus(`❌ Companies endpoint failed with status: ${response.status}`, 'error');
                    return null;
                }
            } catch (error) {
                updateStatus(`❌ Companies endpoint error: ${error.message}`, 'error');
                return null;
            }
        }
        
        async function testFullConnection() {
            updateStatus('🔍 Testing full connection...', 'loading');
            
            // Test health first
            const healthOk = await testHealthEndpoint();
            if (!healthOk) {
                return;
            }
            
            // Wait a moment
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Test companies
            const companies = await testCompaniesEndpoint();
            if (companies) {
                updateStatus(`🎉 Full connection test successful! Backend and frontend are properly connected.`, 'success');
            }
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(testFullConnection, 1000);
        });
    </script>
</body>
</html>
