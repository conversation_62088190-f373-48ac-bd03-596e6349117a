/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React from 'react';
import type { Company, User } from './db-server';
import { PlusIcon, EditIcon, ChevronsRightIcon, LockIcon, AlertTriangleIcon } from './Icons';

interface AppHeaderProps {
    companies: Company[];
    selectedCompanyId: string | null;
    onCompanyChange: (id: string) => void;
    selectedYear: string;
    onYearChange: (year: string) => void;
    years: string[];
    onAddCompany: () => void;
    onEditCompany: () => void;
    onCreateNextFY: () => void;
    activeView: string;
    
    // Admin Unlock props
    loggedInUser: User | null;
    unlockedYear: string | null;
    onLockAndRecalculate?: () => void;
    isCalculatingImpact?: boolean;

    // License Info
    licenseValidUpto: string | null;
}

const LicenseStatusIcon = ({ licenseValidUpto }: { licenseValidUpto: string | null }) => {
    if (!licenseValidUpto) {
        return <AlertTriangleIcon size={18} className="icon-danger" title="License is invalid or missing!" />;
    }
    const expiryDate = new Date(licenseValidUpto);
    const today = new Date();
    const daysUntilExpiry = (expiryDate.getTime() - today.getTime()) / (1000 * 3600 * 24);

    if (daysUntilExpiry < 0) {
        return <AlertTriangleIcon size={18} className="icon-danger" title={`License Expired on ${expiryDate.toLocaleDateString()}`} />;
    }
    if (daysUntilExpiry <= 30) {
        return <AlertTriangleIcon size={18} className="icon-warning" title={`License Expires Soon: ${expiryDate.toLocaleDateString()}`} />;
    }
    return null;
}


export function AppHeader({ 
    companies, selectedCompanyId, onCompanyChange, selectedYear, onYearChange, years, onAddCompany, onEditCompany, onCreateNextFY, activeView,
    loggedInUser, unlockedYear, onLockAndRecalculate, isCalculatingImpact, licenseValidUpto
 }: AppHeaderProps) {
    const isLatestYear = selectedYear === years[years.length - 1];
    const isAdmin = loggedInUser?.role === 'Admin';
    
    return (
        <header className="app-header">
            <div className="header-actions">
                {activeView === 'CompanyInfo' && (
                    <>
                        <button className="btn btn-primary" onClick={onAddCompany}><PlusIcon /> Add Company</button>
                        <button className="btn btn-success" onClick={onEditCompany} disabled={!selectedCompanyId}><EditIcon /> Edit Company</button>
                        <button className="btn btn-warning" onClick={onCreateNextFY} disabled={!selectedCompanyId || !isLatestYear} title={!isLatestYear ? 'Can only create from the most recent financial year' : ''}><ChevronsRightIcon /> Create Next FY</button>
                    </>
                )}
                {isAdmin && selectedCompanyId && selectedYear === unlockedYear && onLockAndRecalculate && (
                    <button className="btn btn-warning" onClick={onLockAndRecalculate} disabled={isCalculatingImpact} title="Lock this year and recalculate all subsequent years">
                        <LockIcon /> {isCalculatingImpact ? 'Calculating Impact...' : 'Lock & Recalculate'}
                    </button>
                )}
            </div>
            <div className="header-controls">
                <label htmlFor="company-select">Company:</label>
                <div className="company-select-wrapper">
                    <select id="company-select" className="highlighted-control" value={selectedCompanyId ?? ''} onChange={(e) => onCompanyChange(e.target.value)} disabled={companies.length === 0 || !!unlockedYear}>
                        <option value="" disabled>Select a company</option>
                        {companies.map(c => <option key={c.id} value={c.id}>{c.name}</option>)}
                    </select>
                    <LicenseStatusIcon licenseValidUpto={licenseValidUpto} />
                </div>
                <label htmlFor="year-select">Year:</label>
                <select id="year-select" className="highlighted-control" value={selectedYear} onChange={(e) => onYearChange(e.target.value)} disabled={!selectedCompanyId || !!unlockedYear}>
                    {years.map(y => <option key={y} value={y}>{y}</option>)}
                </select>
            </div>
        </header>
    );
};
