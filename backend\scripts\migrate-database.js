#!/usr/bin/env node

/**
 * Database Migration Script
 * Migrates FAR Sighted from single database to multi-database structure
 * 
 * Usage:
 * node migrate-database.js [--dry-run] [--force]
 * 
 * Options:
 * --dry-run  : Show what would be migrated without actually doing it
 * --force    : Force migration even if new structure already exists
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import DatabaseMigrator from '../services/database-manager/DatabaseMigrator.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class MigrationScript {
    constructor() {
        this.migrator = new DatabaseMigrator();
        this.args = process.argv.slice(2);
        this.isDryRun = this.args.includes('--dry-run');
        this.isForce = this.args.includes('--force');
    }

    async run() {
        console.log('🚀 FAR Sighted Database Migration Tool');
        console.log('=====================================\n');

        try {
            // Check migration status
            const status = await this.migrator.getMigrationStatus();
            
            console.log('📊 Current Status:');
            console.log(`   Old Database: ${status.hasOldDatabase ? '✅ Found' : '❌ Not Found'}`);
            console.log(`   Companies in New Structure: ${status.companiesInNewStructure}`);
            console.log(`   Migration Needed: ${status.needsMigration ? '✅ Yes' : '❌ No'}\n`);

            if (!status.hasOldDatabase) {
                console.log('ℹ️  No old database found. Nothing to migrate.');
                return;
            }

            if (!status.needsMigration && !this.isForce) {
                console.log('ℹ️  Migration not needed. Companies already exist in new structure.');
                console.log('   Use --force to run migration anyway.');
                return;
            }

            if (this.isDryRun) {
                await this.performDryRun();
            } else {
                await this.performMigration();
            }

        } catch (error) {
            console.error('❌ Migration failed:', error.message);
            if (error.stack) {
                console.error('\nStack trace:');
                console.error(error.stack);
            }
            process.exit(1);
        }
    }

    async performDryRun() {
        console.log('🔍 DRY RUN MODE - No changes will be made\n');
        
        // This would require additional methods in the migrator to preview data
        console.log('📋 What would be migrated:');
        console.log('   - This is a dry run preview');
        console.log('   - Actual migration data analysis would be shown here');
        console.log('\n💡 Run without --dry-run to perform actual migration');
    }

    async performMigration() {
        console.log('🔄 Starting migration...\n');

        if (!this.isForce) {
            console.log('⚠️  WARNING: This operation will:');
            console.log('   1. Create separate databases for each company');
            console.log('   2. Copy all existing data to new structure');
            console.log('   3. Backup the original database');
            console.log('   4. This process cannot be easily undone\n');

            // In a real implementation, you might want to add a confirmation prompt
            console.log('▶️  Proceeding with migration...\n');
        }

        const result = await this.migrator.migrate();

        if (result.success) {
            console.log('\n✅ MIGRATION COMPLETED SUCCESSFULLY!\n');
            
            if (result.results) {
                console.log('📊 Migration Summary:');
                console.log(`   Companies Created: ${result.results.summary.companiesCreated}`);
                console.log(`   Assets Transferred: ${result.results.summary.assetsTransferred}`);
                console.log(`   Users Transferred: ${result.results.summary.usersTransferred}\n`);

                if (result.results.companies.length > 0) {
                    console.log('🏢 Companies Migrated:');
                    result.results.companies.forEach(company => {
                        console.log(`   • ${company.name} (${company.assetsCount} assets)`);
                    });
                    console.log('');
                }

                if (result.results.errors.length > 0) {
                    console.log('⚠️  Errors encountered:');
                    result.results.errors.forEach(error => {
                        console.log(`   • ${error}`);
                    });
                    console.log('');
                }
            }

            console.log('🎉 Your FAR Sighted system is now using the new multi-database structure!');
            console.log('📁 Each company now has its own database in: backend/database/companies/');
            console.log('💾 Original database backed up with .migration-backup extension');
            
        } else {
            console.log('\n❌ MIGRATION FAILED\n');
            console.log(`Error: ${result.message}`);
            
            if (result.error) {
                console.error('Details:', result.error.message);
            }
        }
    }

    showHelp() {
        console.log(`
FAR Sighted Database Migration Tool

Usage: node migrate-database.js [options]

Options:
  --dry-run    Show what would be migrated without making changes
  --force      Force migration even if new structure exists
  --help       Show this help message

Examples:
  node migrate-database.js                 # Run migration
  node migrate-database.js --dry-run       # Preview migration
  node migrate-database.js --force         # Force migration
        `);
    }
}

// Run the script
async function main() {
    const script = new MigrationScript();
    
    if (script.args.includes('--help')) {
        script.showHelp();
        return;
    }
    
    await script.run();
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

// Run the script
main();