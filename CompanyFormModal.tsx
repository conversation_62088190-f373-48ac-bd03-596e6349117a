/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect } from 'react';
import type { CompanyInfo as CompanyInfoType } from './lib/db-server';
import { SaveIcon, XIcon, TrashIcon } from './Icons';

interface CompanyFormModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (formData: CompanyInfoType) => void;
    onDelete?: (companyId: string) => void;
    mode: 'add' | 'edit';
    initialData?: CompanyInfoType | null;
    showAlert: (title: string, message: string, type: 'success' | 'error' | 'info') => void;
}

export function CompanyFormModal({ isOpen, onClose, onSubmit, onDelete, mode, initialData, showAlert }: CompanyFormModalProps) {
    const [formData, setFormData] = useState<CompanyInfoType>(initialData || {} as CompanyInfoType);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

    useEffect(() => {
        if (mode === 'add') {
            const today = new Date();
            const currentYear = today.getFullYear();
            const currentMonth = today.getMonth(); // 0-11
            const startYear = currentMonth < 3 ? currentYear - 1 : currentYear;
            const defaultStartDate = `${startYear}-04-01`;
            const defaultEndDate = `${startYear + 1}-03-31`;

            // Default adoption date to 01/04/2024 as per requirement
            const defaultAdoptionDate = '2024-04-01';

            setFormData({
                companyName: '',
                pan: '',
                cin: '',
                dateOfIncorporation: '',
                financialYearStart: defaultStartDate,
                financialYearEnd: defaultEndDate,
                firstDateOfAdoption: defaultAdoptionDate,
                dataFolderPath: '',
                addressLine1: '',
                addressLine2: '',
                city: '',
                pin: '',
                email: '',
                mobile: '',
                contactPerson: '',
                licenseValidUpto: '', // License is blank for new companies
            });
        } else {
            setFormData(initialData || {} as CompanyInfoType);
        }
    }, [initialData, mode]);

    if (!isOpen) return null;

    const isLicensed = !!(initialData?.licenseValidUpto && new Date(initialData.licenseValidUpto) > new Date());

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;

        // Auto-fill financial year end date when start date is entered
        if (name === 'financialYearStart' && value) {
            const startDate = new Date(value);
            const endDate = new Date(startDate);
            endDate.setFullYear(startDate.getFullYear() + 1);
            endDate.setDate(endDate.getDate() - 1); // Previous day to make it end of FY

            setFormData(prev => ({
                ...prev,
                [name]: value,
                financialYearEnd: endDate.toISOString().split('T')[0]
            }));
        } else {
            setFormData(prev => ({ ...prev, [name]: value }));
        }
    };

    const handleDeleteClick = () => {
        setShowDeleteConfirm(true);
    };

    const handleDeleteConfirm = () => {
        if (onDelete && formData.id) {
            onDelete(formData.id);
            setShowDeleteConfirm(false);
            onClose();
        }
    };

    const handleDeleteCancel = () => {
        setShowDeleteConfirm(false);
    };
    
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.companyName || !formData.dateOfIncorporation || !formData.firstDateOfAdoption) {
            showAlert('Validation Error', 'Please fill all the required fields marked with an asterisk.', 'error');
            return;
        }

        if (!formData.pan) {
            showAlert('Validation Error', 'PAN is required.', 'error');
            return;
        }

        // Validate First Date of Adoption minimum date (01/04/2014)
        const adoptionDate = new Date(formData.firstDateOfAdoption);
        const minAdoptionDate = new Date('2014-04-01');

        if (adoptionDate < minAdoptionDate) {
            showAlert('Invalid Adoption Date',
                'First Date of Adoption cannot be before 01/04/2014. ' +
                'Note: Depreciation rates were different before 01/04/2014.', 'error');
            return;
        }

        // Show warning about master data edit restrictions after license installation
        if (mode === 'add') {
            showAlert('Important Notice',
                'Once a license is installed, core company master details (Name, PAN, CIN, Date of Incorporation, First Date of Adoption) cannot be edited. ' +
                'Please ensure all information is correct before proceeding.', 'info');
        }

        onSubmit(formData);
    };

    const formatDateForInput = (dateString: string) => {
        if (!dateString) return '';
        return new Date(dateString).toISOString().split('T')[0];
    };

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>{showDeleteConfirm ? 'Delete Company' : (mode === 'add' ? 'Add New Company' : 'Edit Company')}</h2>
                    <button className="modal-close-btn" onClick={onClose}>&times;</button>
                </div>

                {showDeleteConfirm ? (
                    <div className="delete-confirmation">
                        <div style={{ padding: '1rem 0' }}>
                            <p><strong>Are you sure you want to delete "{formData.companyName}"?</strong></p>
                            <p>This action will permanently delete all associated data and cannot be undone.</p>
                        </div>
                        <div className="modal-actions">
                            <button type="button" className="btn btn-secondary" onClick={handleDeleteCancel}><XIcon /> Cancel</button>
                            <button type="button" className="btn btn-danger" onClick={handleDeleteConfirm}><TrashIcon /> Confirm Delete</button>
                        </div>
                    </div>
                ) : (
                    <form onSubmit={handleSubmit}>
                        <p className="form-note">Fields marked with <span className="required-asterisk">*</span> are required. PAN is mandatory.</p>
                        <div className="form-grid" style={{marginTop: '1rem'}}>
                            {/* Form inputs remain the same */}
                        <div className="form-group full-width"><label>Company Name<span className="required-asterisk">*</span></label><input type="text" name="companyName" value={formData.companyName} onChange={handleChange} required disabled={isLicensed} /></div>
                        <div className="form-group"><label>Corporate Identification Number (CIN)</label><input type="text" name="cin" value={formData.cin || ''} onChange={handleChange} disabled={isLicensed} /></div>
                        <div className="form-group"><label>Permanent Account Number (PAN)<span className="required-asterisk">*</span></label><input type="text" name="pan" value={formData.pan || ''} onChange={handleChange} disabled={isLicensed}/></div>
                        <div className="form-group"><label>Date of Incorporation<span className="required-asterisk">*</span></label><input type="date" name="dateOfIncorporation" value={formatDateForInput(formData.dateOfIncorporation)} onChange={handleChange} required disabled={isLicensed}/></div>
                        <div className="form-group"><label>Address Line 1</label><input type="text" name="addressLine1" value={formData.addressLine1} onChange={handleChange} /></div>
                        <div className="form-group"><label>Address Line 2</label><input type="text" name="addressLine2" value={formData.addressLine2} onChange={handleChange} /></div>
                        <div className="form-group"><label>City</label><input type="text" name="city" value={formData.city} onChange={handleChange} /></div>
                        <div className="form-group"><label>PIN Code</label><input type="text" name="pin" value={formData.pin} onChange={handleChange} /></div>
                        <div className="form-group"><label>Contact Person</label><input type="text" name="contactPerson" value={formData.contactPerson} onChange={handleChange} /></div>
                        <div className="form-group"><label>Email</label><input type="email" name="email" value={formData.email} onChange={handleChange} /></div>
                        <div className="form-group"><label>Mobile</label><input type="tel" name="mobile" value={formData.mobile} onChange={handleChange} /></div>
                        <div className="form-group full-width"><label>Data Folder Path</label><input type="text" name="dataFolderPath" value={formData.dataFolderPath} onChange={handleChange} /></div>
                        <div className="form-group"><label>Financial Year Start<span className="required-asterisk">*</span></label><input type="date" name="financialYearStart" value={formatDateForInput(formData.financialYearStart)} onChange={handleChange} required disabled={isLicensed}/></div>
                        <div className="form-group"><label>Financial Year End</label><input type="date" name="financialYearEnd" value={formatDateForInput(formData.financialYearEnd)} onChange={handleChange} disabled={isLicensed}/></div>
                        <div className="form-group">
                            <label>First Date of Adoption (of this app for FAR)<span className="required-asterisk">*</span></label>
                            <input
                                type="date"
                                name="firstDateOfAdoption"
                                value={formatDateForInput(formData.firstDateOfAdoption)}
                                onChange={handleChange}
                                min="2014-04-01"
                                required
                                disabled={isLicensed}
                            />
                        </div>
                        <div className="form-group">
                            <label>License Valid Upto</label>
                            <input
                                type="date"
                                name="licenseValidUpto"
                                value={formatDateForInput(formData.licenseValidUpto)}
                                readOnly
                                disabled
                            />
                        </div>
                        </div>
                        <div className="modal-actions">
                            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'space-between', width: '100%' }}>
                                <div>
                                    {mode === 'edit' && onDelete && (
                                        <button type="button" className="btn btn-danger" onClick={handleDeleteClick}>
                                            <TrashIcon /> Delete Company
                                        </button>
                                    )}
                                </div>
                                <div style={{ display: 'flex', gap: '1rem' }}>
                                    <button type="button" className="btn btn-secondary" onClick={onClose}><XIcon /> Cancel</button>
                                    <button type="submit" className="btn btn-primary"><SaveIcon /> {mode === 'add' ? 'Create Company' : 'Save Changes'}</button>
                                </div>
                            </div>
                        </div>
                    </form>
                )}
            </div>
        </div>
    );
};
