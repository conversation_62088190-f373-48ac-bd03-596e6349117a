/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect } from 'react';
import type { CompanyInfo as CompanyInfoType } from './db-server';
import { SaveIcon, XIcon } from './Icons';

interface CompanyFormModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (formData: CompanyInfoType) => void;
    mode: 'add' | 'edit';
    initialData?: CompanyInfoType | null;
    showAlert: (title: string, message: string, type: 'success' | 'error' | 'info') => void;
}

export function CompanyFormModal({ isOpen, onClose, onSubmit, mode, initialData, showAlert }: CompanyFormModalProps) {
    const [formData, setFormData] = useState<CompanyInfoType>(initialData || {} as CompanyInfoType);

    useEffect(() => {
        if (mode === 'add') {
            const today = new Date();
            const currentYear = today.getFullYear();
            const currentMonth = today.getMonth(); // 0-11
            const startYear = currentMonth < 3 ? currentYear - 1 : currentYear;
            const defaultStartDate = `${startYear}-04-01`;
            const defaultEndDate = `${startYear + 1}-03-31`;

            setFormData({
                companyName: '',
                pan: '',
                cin: '',
                dateOfIncorporation: '',
                financialYearStart: defaultStartDate,
                financialYearEnd: defaultEndDate,
                firstDateOfAdoption: defaultStartDate,
                dataFolderPath: '',
                addressLine1: '',
                addressLine2: '',
                city: '',
                pin: '',
                email: '',
                mobile: '',
                contactPerson: '',
                licenseValidUpto: '', // License is blank for new companies
            });
        } else {
            setFormData(initialData || {} as CompanyInfoType);
        }
    }, [initialData, mode]);

    if (!isOpen) return null;

    const isLicensed = !!(initialData?.licenseValidUpto && new Date(initialData.licenseValidUpto) > new Date());

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };
    
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!formData.companyName || !formData.dateOfIncorporation || !formData.firstDateOfAdoption) {
            showAlert('Validation Error', 'Please fill all the required fields marked with an asterisk.', 'error');
            return;
        }

        if (!formData.pan && !formData.cin) {
            showAlert('Validation Error', 'Either a PAN or a CIN must be provided.', 'error');
            return;
        }

        onSubmit(formData);
    };

    const formatDateForInput = (dateString: string) => {
        if (!dateString) return '';
        return new Date(dateString).toISOString().split('T')[0];
    };

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>{mode === 'add' ? 'Add New Company' : 'Edit Company'}</h2>
                    <button className="modal-close-btn" onClick={onClose}>&times;</button>
                </div>
                <form onSubmit={handleSubmit}>
                    <p className="form-note">Fields marked with <span className="required-asterisk">*</span> are required. Either PAN or CIN must be provided.</p>
                    <div className="form-grid" style={{marginTop: '1rem'}}>
                        <div className="form-group full-width"><label>Company Name<span className="required-asterisk">*</span></label><input type="text" name="companyName" value={formData.companyName} onChange={handleChange} required disabled={isLicensed} /></div>
                        <div className="form-group"><label>Corporate Identification Number (CIN)<span className="required-asterisk">*</span></label><input type="text" name="cin" value={formData.cin || ''} onChange={handleChange} disabled={isLicensed} /></div>
                        <div className="form-group"><label>Permanent Account Number (PAN)<span className="required-asterisk">*</span></label><input type="text" name="pan" value={formData.pan || ''} onChange={handleChange} disabled={isLicensed}/></div>
                        <div className="form-group"><label>Date of Incorporation<span className="required-asterisk">*</span></label><input type="date" name="dateOfIncorporation" value={formatDateForInput(formData.dateOfIncorporation)} onChange={handleChange} required disabled={isLicensed}/></div>
                        <div className="form-group"><label>Address Line 1</label><input type="text" name="addressLine1" value={formData.addressLine1} onChange={handleChange} /></div>
                        <div className="form-group"><label>Address Line 2</label><input type="text" name="addressLine2" value={formData.addressLine2} onChange={handleChange} /></div>
                        <div className="form-group"><label>City</label><input type="text" name="city" value={formData.city} onChange={handleChange} /></div>
                        <div className="form-group"><label>PIN Code</label><input type="text" name="pin" value={formData.pin} onChange={handleChange} /></div>
                        <div className="form-group"><label>Contact Person</label><input type="text" name="contactPerson" value={formData.contactPerson} onChange={handleChange} /></div>
                        <div className="form-group"><label>Email</label><input type="email" name="email" value={formData.email} onChange={handleChange} /></div>
                        <div className="form-group"><label>Mobile</label><input type="tel" name="mobile" value={formData.mobile} onChange={handleChange} /></div>
                        <div className="form-group full-width"><label>Data Folder Path</label><input type="text" name="dataFolderPath" value={formData.dataFolderPath} onChange={handleChange} /></div>
                        <div className="form-group"><label>Financial Year Start<span className="required-asterisk">*</span></label><input type="date" name="financialYearStart" value={formatDateForInput(formData.financialYearStart)} onChange={handleChange} required/></div>
                        <div className="form-group"><label>Financial Year End</label><input type="date" name="financialYearEnd" value={formatDateForInput(formData.financialYearEnd)} onChange={handleChange} /></div>
                        <div className="form-group"><label>First Date of Adoption<span className="required-asterisk">*</span></label><input type="date" name="firstDateOfAdoption" value={formatDateForInput(formData.firstDateOfAdoption)} onChange={handleChange} required/></div>
                        <div className="form-group"><label>License Valid Upto</label><input type="date" name="licenseValidUpto" value={formatDateForInput(formData.licenseValidUpto)} onChange={handleChange} /></div>
                    </div>
                     {isLicensed && <p className="form-note" style={{marginTop: '1rem', color: 'var(--accent-info)'}}>Core company details (Name, PAN, CIN, Date of Incorporation) are locked because a valid license is active.</p>}
                    <div className="modal-actions">
                        <button type="button" className="btn btn-secondary" onClick={onClose}><XIcon /> Cancel</button>
                        <button type="submit" className="btn btn-primary"><SaveIcon /> {mode === 'add' ? 'Create Company' : 'Save Changes'}</button>
                    </div>
                </form>
            </div>
        </div>
    );
};
