/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

// Updated API client to connect to SQLite backend server with Multi-Database support
import type { Company, CompanyData, CompanyInfo, Asset, StatutoryRate, User, AppSettings, UserPreferences, BackupLogEntry, AuditLogEntry, AssetImpact, License } from './db-server';

// Backend server configuration - Dynamic port detection
const FALLBACK_PORTS = [8090, 3001, 3000, 8080, 8081, 5000, 5001];
let API_BASE_URL = process.env.NODE_ENV === 'production'
    ? 'https://your-production-domain.com/api'  // Update this for production
    : 'http://localhost:3001/api'; // Default fallback

// Dynamic port detection
let detectedPort: number | null = null;
let portDetectionPromise: Promise<void> | null = null;

async function detectBackendPort(): Promise<number> {
    if (detectedPort) {
        return detectedPort;
    }

    console.log('🔍 Detecting backend port...');

    for (const port of FALLBACK_PORTS) {
        try {
            const response = await fetch(`http://localhost:${port}/api/health`, {
                method: 'GET',
                signal: AbortSignal.timeout(2000) // 2 second timeout
            });

            if (response.ok) {
                detectedPort = port;
                API_BASE_URL = `http://localhost:${port}/api`;
                console.log(`✅ Backend detected on port ${port}`);
                return port;
            }
        } catch (error) {
            // Port not available, try next
            continue;
        }
    }

    // If no port detected, use default
    console.warn('⚠️ No backend detected, using default port 3001');
    detectedPort = 3001;
    return 3001;
}

// Initialize port detection
async function initializePortDetection(): Promise<void> {
    if (process.env.NODE_ENV !== 'production') {
        try {
            await detectBackendPort();
        } catch (error) {
            console.error('❌ Port detection failed:', error);
        }
    }
}

// Start port detection immediately
if (typeof window !== 'undefined' && process.env.NODE_ENV !== 'production') {
    portDetectionPromise = initializePortDetection();
}

// HTTP client wrapper with error handling and migration support
async function apiRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    // Ensure port detection is complete before making requests
    if (portDetectionPromise && process.env.NODE_ENV !== 'production') {
        await portDetectionPromise;
    }

    const url = `${API_BASE_URL}${endpoint}`;

    const defaultOptions: RequestInit = {
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for session management
    };
    
    const requestOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers,
        },
    };
    
    try {
        console.log(`[API Client] -> ${requestOptions.method || 'GET'} ${endpoint}`);
        
        const response = await fetch(url, requestOptions);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            
            // Handle migration required error specifically
            if (response.status === 503 && errorData.error === 'System migration required') {
                console.warn('[API Client] Migration required:', errorData);
                throw new Error('MIGRATION_REQUIRED');
            }
            
            throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log(`[API Client] <- 200 OK for ${requestOptions.method || 'GET'} ${endpoint}`);
        return data;
        
    } catch (error) {
        console.error(`[API Client] <- Error for ${requestOptions.method || 'GET'} ${endpoint}:`, error);
        throw error;
    }
}

// The single, centralized API client object for the entire application with Multi-Database support.
export const api = {
    // System Information and Migration Functions
    healthCheck: (): Promise<any> => 
        apiRequest('/health'),
    
    getSystemInfo: (): Promise<any> => 
        apiRequest('/system-info'),
    
    getMigrationStatus: (): Promise<any> => 
        apiRequest('/migration-status'),
    
    runMigration: (force: boolean = false): Promise<any> => 
        apiRequest('/admin/migrate', {
            method: 'POST',
            body: JSON.stringify({ force }),
        }),

    // Company Functions - Updated for Multi-Database
    getCompanies: (): Promise<Company[]> => 
        apiRequest('/companies'),
    
    getCompanyData: (companyId: string): Promise<CompanyData | null> => 
        apiRequest(`/companies/${companyId}`),
    
    getCompanyInfo: (companyId: string): Promise<CompanyInfo> => 
        apiRequest(`/companies/${companyId}/info`),
    
    addCompany: (companyInfo: CompanyInfo): Promise<Company> => 
        apiRequest('/companies', {
            method: 'POST',
            body: JSON.stringify(companyInfo),
        }),
    
    updateCompany: (companyId: string, companyInfo: CompanyInfo): Promise<void> =>
        apiRequest(`/companies/${companyId}`, {
            method: 'PUT',
            body: JSON.stringify(companyInfo),
        }),

    deleteCompany: (companyId: string): Promise<void> =>
        apiRequest(`/companies/${companyId}`, {
            method: 'DELETE',
        }),

    activateLicense: (companyId: string, licenseData: any): Promise<CompanyInfo> =>
        apiRequest(`/companies/${companyId}/activate-license`, {
            method: 'POST',
            body: JSON.stringify({ licenseData }),
        }),

    // Asset Functions - Updated for Company-Specific Databases
    getAssets: (companyId: string): Promise<Asset[]> => 
        apiRequest(`/assets/company/${companyId}`),
    
    updateAssets: (companyId: string, updatedAssets: Asset[]): Promise<Asset[]> => 
        apiRequest(`/assets/company/${companyId}`, {
            method: 'PUT',
            body: JSON.stringify({ assets: updatedAssets }),
        }),
    
    importAssets: (companyId: string, newAssets: Asset[]): Promise<void> => 
        apiRequest(`/assets/company/${companyId}/import`, {
            method: 'POST',
            body: JSON.stringify({ assets: newAssets }),
        }),

    // Single Asset Operations
    createAsset: (companyId: string, asset: Asset): Promise<void> => 
        apiRequest(`/assets/company/${companyId}/asset`, {
            method: 'POST',
            body: JSON.stringify(asset),
        }),
    
    updateAsset: (companyId: string, recordId: string, asset: Asset): Promise<void> => 
        apiRequest(`/assets/company/${companyId}/asset/${recordId}`, {
            method: 'PUT',
            body: JSON.stringify(asset),
        }),
    
    deleteAsset: (companyId: string, recordId: string): Promise<void> => 
        apiRequest(`/assets/company/${companyId}/asset/${recordId}`, {
            method: 'DELETE',
        }),

    // Master Data Functions - Company-Specific
    getStatutoryRates: (companyId: string): Promise<StatutoryRate[]> => 
        apiRequest(`/companies/${companyId}`).then(data => data.statutoryRates),
    
    updateStatutoryRates: (companyId: string, newRates: StatutoryRate[]): Promise<void> => 
        apiRequest(`/companies/${companyId}/statutory-rates`, {
            method: 'PUT',
            body: JSON.stringify({ rates: newRates }),
        }),
    
    getExtraLedgers: (companyId: string): Promise<string[]> => 
        apiRequest(`/companies/${companyId}`).then(data => data.extraLedgers),
    
    updateExtraLedgers: (companyId: string, newLedgers: string[]): Promise<void> => 
        apiRequest(`/companies/${companyId}/extra-ledgers`, {
            method: 'PUT',
            body: JSON.stringify({ ledgers: newLedgers }),
        }),
    
    // Financial Year and Calculation Functions
    createNextFinancialYear: (companyId: string, currentYear: string): Promise<string> => 
        apiRequest(`/companies/${companyId}/financial-years`, {
            method: 'POST',
            body: JSON.stringify({ currentYear }),
        }),
    
    recalculateFromYear: (companyId: string, startRecalculationYear: string, dryRunAssets?: Asset[]): Promise<void> => 
        apiRequest(`/companies/${companyId}/recalculate`, {
            method: 'POST',
            body: JSON.stringify({ 
                startYear: startRecalculationYear, 
                assets: dryRunAssets 
            }),
        }),
    
    calculateRecalculationImpact: (companyId: string, unlockedYear: string, assetsBefore: Asset[], assetsAfter: Asset[]): Promise<AssetImpact[]> => 
        apiRequest(`/companies/${companyId}/recalculate-impact`, {
            method: 'POST',
            body: JSON.stringify({ 
                year: unlockedYear, 
                assetsBefore, 
                assetsAfter 
            }),
        }),

    // Extra Shift Days Management
    updateExtraShiftDays: (companyId: string, yearRange: string, shiftData: any[]): Promise<void> => 
        apiRequest(`/assets/company/${companyId}/extra-shift-days/${yearRange}`, {
            method: 'PUT',
            body: JSON.stringify({ shiftData }),
        }),
    
    // User Management Functions - Company-specific Database
    getUsers: (companyId: string): Promise<User[]> =>
        apiRequest(`/users?companyId=${companyId}`),
    
    getUser: (userId: string): Promise<User | null> => 
        apiRequest(`/users/${userId}`),
    
    addUser: (userData: Omit<User, 'id' | 'recoveryKeyHash' | 'hasSavedRecoveryKey'> & { companyId: string }): Promise<{user: User, recoveryKey: string}> =>
        apiRequest('/users', {
            method: 'POST',
            body: JSON.stringify(userData),
        }),
    
    updateUser: (userId: string, userData: Partial<Omit<User, 'id' | 'username'>>): Promise<{recoveryKey: string | null}> => 
        apiRequest(`/users/${userId}`, {
            method: 'PUT',
            body: JSON.stringify(userData),
        }),
    
    deleteUser: (userId: string): Promise<void> => 
        apiRequest(`/users/${userId}`, {
            method: 'DELETE',
        }),
    
    verifyAdminPassword: (userId: string, passwordAttempt: string): Promise<boolean> =>
        apiRequest(`/users/${userId}/verify-password`, {
            method: 'POST',
            body: JSON.stringify({ password: passwordAttempt }),
        }),

    changePassword: (currentPassword: string, newPassword: string): Promise<{recoveryKey: string}> =>
        apiRequest('/users/change-password', {
            method: 'POST',
            body: JSON.stringify({ currentPassword, newPassword }),
        }),
    
    loginUser: (username: string, passwordAttempt: string): Promise<{ user: User, showAdminWelcome: boolean } | null> => 
        apiRequest('/users/login', {
            method: 'POST',
            body: JSON.stringify({ username, password: passwordAttempt }),
        }),
    
    recoverUserPassword: (username: string, recoveryKey: string, newPassword: string): Promise<{recoveryKey: string}> => 
        apiRequest(`/users/${username}/recover`, {
            method: 'POST',
            body: JSON.stringify({ username, recoveryKey, newPassword }),
        }),
    
    setHasSavedRecoveryKey: (userId: string): Promise<void> => 
        apiRequest(`/users/${userId}/confirm-recovery-key`, {
            method: 'POST',
        }),

    // Settings Functions - Global Settings
    getSettings: (): Promise<AppSettings> =>
        apiRequest('/settings'),

    updateSettings: (newSettings: AppSettings): Promise<void> =>
        apiRequest('/settings', {
            method: 'PUT',
            body: JSON.stringify(newSettings),
        }),

    // User Preferences Functions
    getUserPreferences: (userId: string): Promise<UserPreferences> =>
        apiRequest(`/user-preferences/${userId}`),

    updateUserPreferences: (userId: string, preferences: Partial<UserPreferences>): Promise<void> =>
        apiRequest(`/user-preferences/${userId}`, {
            method: 'PUT',
            body: JSON.stringify(preferences),
        }),

    updateUserTheme: (userId: string, theme: 'light' | 'dark'): Promise<void> =>
        apiRequest(`/user-preferences/${userId}/theme`, {
            method: 'PATCH',
            body: JSON.stringify({ theme }),
        }),

    // Backup & Restore Functions - Company-Specific
    getBackupLogs: (): Promise<BackupLogEntry[]> => 
        apiRequest('/backup/logs'),
    
    addBackupLog: (logData: Omit<BackupLogEntry, 'id' | 'timestamp'>): Promise<void> => 
        apiRequest('/backup/logs', {
            method: 'POST',
            body: JSON.stringify(logData),
        }),
    
    createAutoBackup: (): Promise<string> => 
        apiRequest('/backup/auto', {
            method: 'POST',
        }).then(data => data.backupName),
    
    // Company-specific backup/restore
    createCompanyBackup: (companyId: string): Promise<any> => 
        apiRequest(`/companies/${companyId}/backup`, {
            method: 'POST',
        }),
    
    restoreCompanyData: (companyId: string, backupData: any): Promise<void> => 
        apiRequest(`/companies/${companyId}/restore`, {
            method: 'POST',
            body: JSON.stringify({ backupData }),
        }),

    // Legacy backup functions (may need updates)
    exportDatabase: (): Promise<string> => 
        apiRequest('/backup/export').then(data => JSON.stringify(data, null, 2)),
    
    importDatabase: (jsonString: string): Promise<void> => 
        apiRequest('/backup/import', {
            method: 'POST',
            body: jsonString,
        }),

    // Audit Log Functions - Company-Specific
    getAuditLog: (): Promise<AuditLogEntry[]> => 
        apiRequest('/audit'),
    
    addAuditLog: (logData: Omit<AuditLogEntry, 'id' | 'timestamp'>): Promise<void> => 
        apiRequest('/audit', {
            method: 'POST',
            body: JSON.stringify(logData),
        }),

    // Admin Functions for Multi-Database Management
    getDatabaseInfo: (): Promise<any> => 
        apiRequest('/admin/database-info'),
    
    createTestCompany: (): Promise<any> => 
        apiRequest('/admin/test-company', {
            method: 'POST',
        }),
    
    backupAllCompanies: (): Promise<any> => 
        apiRequest('/admin/backup-all', {
            method: 'POST',
        }),

    // Backup API functions
    getBackupSettings: (): Promise<any> =>
        apiRequest('/backup/settings'),

    setBackupPath: (backupPath: string): Promise<any> =>
        apiRequest('/backup/settings/path', {
            method: 'POST',
            body: JSON.stringify({ backupPath }),
        }),

    createBackup: (): Promise<any> =>
        apiRequest('/backup/create', {
            method: 'POST',
        }),

    checkBackupPath: (): Promise<any> =>
        apiRequest('/backup/path-check'),
};

// Utility function to check if migration is required
export const checkMigrationStatus = async (): Promise<boolean> => {
    try {
        const status = await api.getMigrationStatus();
        return !status.systemReady;
    } catch (error) {
        console.warn('Unable to check migration status:', error);
        return false;
    }
};

// Utility functions for port management
export const getApiBaseUrl = (): string => API_BASE_URL;
export const getDetectedPort = (): number | null => detectedPort;
export const refreshPortDetection = async (): Promise<void> => {
    detectedPort = null;
    portDetectionPromise = initializePortDetection();
    await portDetectionPromise;
};

// Export API base URL for external use
export { API_BASE_URL };