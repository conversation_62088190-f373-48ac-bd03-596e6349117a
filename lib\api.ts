/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

// Updated API client to connect to SQLite backend server
import type { Company, CompanyData, CompanyInfo, Asset, StatutoryRate, User, AppSettings, BackupLogEntry, AuditLogEntry, AssetImpact, License } from './db-server';

// Backend server configuration
const API_BASE_URL = process.env.NODE_ENV === 'production' 
    ? 'https://your-production-domain.com/api'  // Update this for production
    : 'http://localhost:3001/api';

// HTTP client wrapper with error handling
async function apiRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const defaultOptions: RequestInit = {
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for session management
    };
    
    const requestOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers,
        },
    };
    
    try {
        console.log(`[API Client] -> ${requestOptions.method || 'GET'} ${endpoint}`);
        
        const response = await fetch(url, requestOptions);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log(`[API Client] <- 200 OK for ${requestOptions.method || 'GET'} ${endpoint}`);
        return data;
        
    } catch (error) {
        console.error(`[API Client] <- Error for ${requestOptions.method || 'GET'} ${endpoint}:`, error);
        throw error;
    }
}

// The single, centralized API client object for the entire application.
export const api = {
    // Company Functions
    getCompanies: (): Promise<Company[]> => 
        apiRequest('/companies'),
    
    getCompanyData: (companyId: string): Promise<CompanyData | null> => 
        apiRequest(`/companies/${companyId}`),
    
    getCompanyInfo: (companyId: string): Promise<CompanyInfo> => 
        apiRequest(`/companies/${companyId}`).then(data => data.info),
    
    addCompany: (companyInfo: CompanyInfo): Promise<Company> => 
        apiRequest('/companies', {
            method: 'POST',
            body: JSON.stringify(companyInfo),
        }),
    
    updateCompany: (companyId: string, companyInfo: CompanyInfo): Promise<void> => 
        apiRequest(`/companies/${companyId}`, {
            method: 'PUT',
            body: JSON.stringify(companyInfo),
        }),
    
    activateLicense: (companyId: string, licenseData: any): Promise<CompanyInfo> => 
        apiRequest(`/companies/${companyId}/activate-license`, {
            method: 'POST',
            body: JSON.stringify({ licenseData }),
        }),

    // Asset Functions
    getAssets: (companyId: string): Promise<Asset[]> => 
        apiRequest(`/assets/company/${companyId}`),
    
    updateAssets: (companyId: string, updatedAssets: Asset[]): Promise<Asset[]> => 
        apiRequest(`/assets/company/${companyId}`, {
            method: 'PUT',
            body: JSON.stringify({ assets: updatedAssets }),
        }),
    
    importAssets: (companyId: string, newAssets: Asset[]): Promise<void> => 
        apiRequest(`/assets/company/${companyId}/import`, {
            method: 'POST',
            body: JSON.stringify({ assets: newAssets }),
        }),

    // Master Data Functions
    getStatutoryRates: (companyId: string): Promise<StatutoryRate[]> => 
        apiRequest(`/companies/${companyId}`).then(data => data.statutoryRates),
    
    updateStatutoryRates: (companyId: string, newRates: StatutoryRate[]): Promise<void> => 
        apiRequest(`/companies/${companyId}/statutory-rates`, {
            method: 'PUT',
            body: JSON.stringify({ rates: newRates }),
        }),
    
    getExtraLedgers: (companyId: string): Promise<string[]> => 
        apiRequest(`/companies/${companyId}`).then(data => data.extraLedgers),
    
    updateExtraLedgers: (companyId: string, newLedgers: string[]): Promise<void> => 
        apiRequest(`/companies/${companyId}/extra-ledgers`, {
            method: 'PUT',
            body: JSON.stringify({ ledgers: newLedgers }),
        }),
    
    // Financial Year and Calculation Functions
    createNextFinancialYear: (companyId: string, currentYear: string): Promise<string> => 
        apiRequest(`/companies/${companyId}/financial-years`, {
            method: 'POST',
            body: JSON.stringify({ currentYear }),
        }),
    
    recalculateFromYear: (companyId: string, startRecalculationYear: string, dryRunAssets?: Asset[]): Promise<void> => 
        apiRequest(`/companies/${companyId}/recalculate`, {
            method: 'POST',
            body: JSON.stringify({ 
                startYear: startRecalculationYear, 
                assets: dryRunAssets 
            }),
        }),
    
    calculateRecalculationImpact: (companyId: string, unlockedYear: string, assetsBefore: Asset[], assetsAfter: Asset[]): Promise<AssetImpact[]> => 
        apiRequest(`/companies/${companyId}/recalculate-impact`, {
            method: 'POST',
            body: JSON.stringify({ 
                year: unlockedYear, 
                assetsBefore, 
                assetsAfter 
            }),
        }),
    
    // User, Auth & Recovery Functions
    getUsers: (): Promise<User[]> => 
        apiRequest('/users'),
    
    getUser: (userId: string): Promise<User | null> => 
        apiRequest(`/users/${userId}`),
    
    addUser: (userData: Omit<User, 'id' | 'recoveryKeyHash' | 'hasSavedRecoveryKey'>): Promise<{user: User, recoveryKey: string}> => 
        apiRequest('/users', {
            method: 'POST',
            body: JSON.stringify(userData),
        }),
    
    updateUser: (userId: string, userData: Partial<Omit<User, 'id' | 'username'>>): Promise<{recoveryKey: string | null}> => 
        apiRequest(`/users/${userId}`, {
            method: 'PUT',
            body: JSON.stringify(userData),
        }),
    
    deleteUser: (userId: string): Promise<void> => 
        apiRequest(`/users/${userId}`, {
            method: 'DELETE',
        }),
    
    verifyAdminPassword: (userId: string, passwordAttempt: string): Promise<boolean> => 
        apiRequest(`/users/${userId}/verify-password`, {
            method: 'POST',
            body: JSON.stringify({ password: passwordAttempt }),
        }),
    
    loginUser: (username: string, passwordAttempt: string): Promise<{ user: User, showAdminWelcome: boolean } | null> => 
        apiRequest('/users/login', {
            method: 'POST',
            body: JSON.stringify({ username, password: passwordAttempt }),
        }),
    
    recoverUserPassword: (username: string, recoveryKey: string, newPassword: string): Promise<{recoveryKey: string}> => 
        apiRequest(`/users/${username}/recover`, {
            method: 'POST',
            body: JSON.stringify({ username, recoveryKey, newPassword }),
        }),
    
    setHasSavedRecoveryKey: (userId: string): Promise<void> => 
        apiRequest(`/users/${userId}/confirm-recovery-key`, {
            method: 'POST',
        }),

    // Settings Functions
    getSettings: (): Promise<AppSettings> => 
        apiRequest('/settings'),
    
    updateSettings: (newSettings: AppSettings): Promise<void> => 
        apiRequest('/settings', {
            method: 'PUT',
            body: JSON.stringify(newSettings),
        }),

    // Backup & Restore Functions
    getBackupLogs: (): Promise<BackupLogEntry[]> => 
        apiRequest('/backup/logs'),
    
    addBackupLog: (logData: Omit<BackupLogEntry, 'id' | 'timestamp'>): Promise<void> => 
        apiRequest('/backup/logs', {
            method: 'POST',
            body: JSON.stringify(logData),
        }),
    
    createAutoBackup: (): Promise<string> => 
        apiRequest('/backup/auto', {
            method: 'POST',
        }).then(data => data.backupName),
    
    exportDatabase: (): Promise<string> => 
        apiRequest('/backup/export').then(data => JSON.stringify(data, null, 2)),
    
    importDatabase: (jsonString: string): Promise<void> => 
        apiRequest('/backup/import', {
            method: 'POST',
            body: jsonString,
        }),

    // Audit Log Functions
    getAuditLog: (): Promise<AuditLogEntry[]> => 
        apiRequest('/audit'),
    
    addAuditLog: (logData: Omit<AuditLogEntry, 'id' | 'timestamp'>): Promise<void> => 
        apiRequest('/audit', {
            method: 'POST',
            body: JSON.stringify(logData),
        }),

    // Health Check
    healthCheck: (): Promise<any> => 
        apiRequest('/health'),
};
