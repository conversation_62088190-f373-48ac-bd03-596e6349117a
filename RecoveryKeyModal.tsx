/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, FC } from 'react';
import { AlertTriangleIcon, CopyIcon, CheckCircleIcon } from './Icons';

interface RecoveryKeyModalProps {
    isOpen: boolean;
    recoveryKey: string | null;
    onConfirm: () => void;
    isRecoveryFlow?: boolean;
    usernameFor?: string | null;
}

export const RecoveryKeyModal: FC<RecoveryKeyModalProps> = ({ isOpen, recoveryKey, onConfirm, isRecoveryFlow = false, usernameFor }) => {
    const [hasSaved, setHasSaved] = useState(false);
    const [copied, setCopied] = useState(false);

    if (!isOpen || !recoveryKey) return null;
    
    const handleCopy = () => {
        navigator.clipboard.writeText(recoveryKey);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
    };

    return (
        <div className="modal-overlay">
            <div className="modal-content" style={{ maxWidth: '600px' }} onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <div className="modal-header-content">
                        <AlertTriangleIcon className="icon-warning" size={24}/>
                        <h2>Save Your Recovery Key</h2>
                    </div>
                </div>
                <div>
                    {usernameFor && (
                         <p style={{
                            fontSize: '1.1rem',
                            fontWeight: '600',
                            backgroundColor: 'var(--background-primary)',
                            padding: '0.75rem',
                            borderRadius: '6px',
                            border: '1px solid var(--border-primary)',
                            marginBottom: '1rem',
                            textAlign: 'center',
                        }}>
                            This recovery key is for user: <span style={{color: 'var(--accent-primary)'}}>{usernameFor}</span>
                        </p>
                    )}
                    <p style={{ padding: '0.5rem 0', fontSize: '1.1rem', lineHeight: '1.6' }}>
                        This is a <strong>one-time</strong> recovery key. It is the <strong>only way</strong> to regain access to this account if the password is forgotten.
                    </p>
                    <p style={{ padding: '0.5rem 0', fontSize: '1.1rem', lineHeight: '1.6' }}>
                        Store it in a password manager or a secure physical location. <strong>You will not be shown this key again.</strong>
                    </p>
                    
                    <div className="recovery-key-box">
                        <span className="recovery-key-text">{recoveryKey}</span>
                        <button className="btn-icon" title="Copy to clipboard" onClick={handleCopy}>
                            {copied ? <CheckCircleIcon size={20} className="icon-success" /> : <CopyIcon size={20} />}
                        </button>
                    </div>

                    <div className="recovery-key-ack recovery-key-ack-prominent">
                        <input
                            type="checkbox"
                            id="ack-save"
                            checked={hasSaved}
                            onChange={(e) => setHasSaved(e.target.checked)}
                            className="recovery-key-checkbox"
                        />
                        <label htmlFor="ack-save" className="recovery-key-label">
                            <strong>I have copied and saved this recovery key in a secure place.</strong>
                        </label>
                    </div>

                </div>
                <div className="modal-actions">
                    <button type="button" className="btn btn-primary" onClick={onConfirm} disabled={!hasSaved}>
                        {isRecoveryFlow ? 'Return to Login' : 'Done'}
                    </button>
                </div>
            </div>
        </div>
    );
};
