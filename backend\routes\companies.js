import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import dbService from '../services/database.js';

const router = express.Router();

// Get all companies
router.get('/', async (req, res) => {
    try {
        const companies = await dbService.all(
            'SELECT id, company_name as name FROM companies ORDER BY company_name'
        );
        res.json(companies);
    } catch (error) {
        console.error('Error fetching companies:', error);
        res.status(500).json({ error: 'Failed to fetch companies' });
    }
});

// Get company by ID with complete data
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        // Get company info
        const company = await dbService.get(
            `SELECT * FROM companies WHERE id = ?`, [id]
        );
        
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // Get assets for this company
        const assets = await dbService.all(
            `SELECT 
                id as assetDbId,
                record_id as recordId,
                asset_particulars as assetParticulars,
                book_entry_date as bookEntryDate,
                put_to_use_date as putToUseDate,
                basic_amount as basicAmount,
                duties_taxes as dutiesTaxes,
                gross_amount as grossAmount,
                vendor,
                invoice_no as invoiceNo,
                model_make as modelMake,
                location,
                asset_id as assetId,
                remarks,
                ledger_name_in_books as ledgerNameInBooks,
                asset_group as assetGroup,
                asset_sub_group as assetSubGroup,
                schedule_iii_classification as scheduleIIIClassification,
                disposal_date as disposalDate,
                disposal_amount as disposalAmount,
                salvage_percentage as salvagePercentage,
                wdv_of_adoption_date as wdvOfAdoptionDate,
                is_leasehold as isLeasehold,
                depreciation_method as depreciationMethod,
                life_in_years as lifeInYears,
                lease_period as leasePeriod,
                scrap_it as scrapIt
            FROM assets WHERE company_id = ?`, [id]
        );
        
        // Get financial years
        const financialYears = await dbService.all(
            'SELECT year_range FROM financial_years WHERE company_id = ? ORDER BY year_range',
            [id]
        );
        
        // Get statutory rates
        const statutoryRates = await dbService.all(
            `SELECT 
                is_statutory as isStatutory,
                tangibility,
                asset_group as assetGroup,
                asset_sub_group as assetSubGroup,
                extra_shift_depreciation as extraShiftDepreciation,
                useful_life_years as usefulLifeYears,
                schedule_ii_classification as scheduleIIClassification
            FROM statutory_rates WHERE company_id = ?`, [id]
        );
        
        // Get extra ledgers
        const extraLedgers = await dbService.all(
            'SELECT ledger_name FROM extra_ledgers WHERE company_id = ?',
            [id]
        );
        
        // Get license history
        const licenseHistory = await dbService.all(
            `SELECT 
                id,
                license_key as key,
                valid_from as validFrom,
                valid_upto as validUpto,
                activated_at as activatedAt
            FROM license_history WHERE company_id = ?`, [id]
        );

        // Transform company data to match frontend interface
        const companyData = {
            id: company.id,
            info: {
                companyName: company.company_name,
                pan: company.pan,
                cin: company.cin,
                dateOfIncorporation: company.date_of_incorporation,
                financialYearStart: company.financial_year_start,
                financialYearEnd: company.financial_year_end,
                firstDateOfAdoption: company.first_date_of_adoption,
                dataFolderPath: company.data_folder_path,
                addressLine1: company.address_line1,
                addressLine2: company.address_line2,
                city: company.city,
                pin: company.pin,
                email: company.email,
                mobile: company.mobile,
                contactPerson: company.contact_person,
                licenseValidUpto: company.license_valid_upto
            },
            assets: assets,
            financialYears: financialYears.map(fy => fy.year_range),
            statutoryRates: statutoryRates,
            extraLedgers: extraLedgers.map(el => el.ledger_name),
            licenseHistory: licenseHistory
        };
        
        res.json(companyData);
    } catch (error) {
        console.error('Error fetching company data:', error);
        res.status(500).json({ error: 'Failed to fetch company data' });
    }
});

// Add new company
router.post('/', async (req, res) => {
    try {
        const companyInfo = req.body;
        
        // Validate required fields
        if (!companyInfo.companyName || !companyInfo.financialYearStart || 
            !companyInfo.financialYearEnd || !companyInfo.firstDateOfAdoption) {
            return res.status(400).json({ 
                error: 'Missing required fields: companyName, financialYearStart, financialYearEnd, firstDateOfAdoption' 
            });
        }
        
        const companyId = `c${Date.now()}`;
        
        await dbService.beginTransaction();
        
        try {
            // Insert company
            await dbService.run(
                `INSERT INTO companies (
                    id, company_name, pan, cin, date_of_incorporation,
                    financial_year_start, financial_year_end, first_date_of_adoption,
                    data_folder_path, address_line1, address_line2, city, pin,
                    email, mobile, contact_person, license_valid_upto
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    companyId, companyInfo.companyName, companyInfo.pan, companyInfo.cin,
                    companyInfo.dateOfIncorporation, companyInfo.financialYearStart, 
                    companyInfo.financialYearEnd, companyInfo.firstDateOfAdoption,
                    companyInfo.dataFolderPath, companyInfo.addressLine1, companyInfo.addressLine2,
                    companyInfo.city, companyInfo.pin, companyInfo.email, companyInfo.mobile,
                    companyInfo.contactPerson, companyInfo.licenseValidUpto
                ]
            );
            
            // Add default financial year
            const startYear = new Date(companyInfo.financialYearStart).getFullYear();
            const endYear = new Date(companyInfo.financialYearEnd).getFullYear();
            const yearRange = `${startYear}-${endYear}`;
            
            await dbService.run(
                'INSERT INTO financial_years (company_id, year_range) VALUES (?, ?)',
                [companyId, yearRange]
            );
            
            // Add default statutory rates (copy from master data)
            // This would need to be populated with the statutory rates data
            // For now, we'll skip this and let it be added separately
            
            await dbService.commit();
            
            res.status(201).json({
                id: companyId,
                name: companyInfo.companyName
            });
        } catch (error) {
            await dbService.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error creating company:', error);
        res.status(500).json({ error: 'Failed to create company' });
    }
});

// Update company
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const companyInfo = req.body;
        
        const company = await dbService.get('SELECT id FROM companies WHERE id = ?', [id]);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        await dbService.run(
            `UPDATE companies SET 
                company_name = ?, pan = ?, cin = ?, date_of_incorporation = ?,
                financial_year_start = ?, financial_year_end = ?, first_date_of_adoption = ?,
                data_folder_path = ?, address_line1 = ?, address_line2 = ?, city = ?, pin = ?,
                email = ?, mobile = ?, contact_person = ?, license_valid_upto = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?`,
            [
                companyInfo.companyName, companyInfo.pan, companyInfo.cin,
                companyInfo.dateOfIncorporation, companyInfo.financialYearStart, 
                companyInfo.financialYearEnd, companyInfo.firstDateOfAdoption,
                companyInfo.dataFolderPath, companyInfo.addressLine1, companyInfo.addressLine2,
                companyInfo.city, companyInfo.pin, companyInfo.email, companyInfo.mobile,
                companyInfo.contactPerson, companyInfo.licenseValidUpto, id
            ]
        );
        
        res.json({ message: 'Company updated successfully' });
    } catch (error) {
        console.error('Error updating company:', error);
        res.status(500).json({ error: 'Failed to update company' });
    }
});

// Get company info only
router.get('/:id/info', async (req, res) => {
    try {
        const { id } = req.params;
        
        const company = await dbService.get('SELECT * FROM companies WHERE id = ?', [id]);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        const companyInfo = {
            companyName: company.company_name,
            pan: company.pan,
            cin: company.cin,
            dateOfIncorporation: company.date_of_incorporation,
            financialYearStart: company.financial_year_start,
            financialYearEnd: company.financial_year_end,
            firstDateOfAdoption: company.first_date_of_adoption,
            dataFolderPath: company.data_folder_path,
            addressLine1: company.address_line1,
            addressLine2: company.address_line2,
            city: company.city,
            pin: company.pin,
            email: company.email,
            mobile: company.mobile,
            contactPerson: company.contact_person,
            licenseValidUpto: company.license_valid_upto
        };
        
        res.json(companyInfo);
    } catch (error) {
        console.error('Error fetching company info:', error);
        res.status(500).json({ error: 'Failed to fetch company info' });
    }
});

// Update statutory rates for a company
router.put('/:id/statutory-rates', async (req, res) => {
    try {
        const { id } = req.params;
        const { rates } = req.body;
        
        if (!Array.isArray(rates)) {
            return res.status(400).json({ error: 'Rates must be an array' });
        }
        
        const company = await dbService.get('SELECT id FROM companies WHERE id = ?', [id]);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        await dbService.beginTransaction();
        
        try {
            // Delete existing statutory rates
            await dbService.run('DELETE FROM statutory_rates WHERE company_id = ?', [id]);
            
            // Insert new rates
            for (const rate of rates) {
                await dbService.run(
                    `INSERT INTO statutory_rates (
                        company_id, is_statutory, tangibility, asset_group, asset_sub_group,
                        extra_shift_depreciation, useful_life_years, schedule_ii_classification
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        id, rate.isStatutory, rate.tangibility, rate.assetGroup, 
                        rate.assetSubGroup, rate.extraShiftDepreciation, 
                        rate.usefulLifeYears, rate.scheduleIIClassification
                    ]
                );
            }
            
            await dbService.commit();
            res.json({ message: 'Statutory rates updated successfully' });
        } catch (error) {
            await dbService.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error updating statutory rates:', error);
        res.status(500).json({ error: 'Failed to update statutory rates' });
    }
});

// Update extra ledgers for a company
router.put('/:id/extra-ledgers', async (req, res) => {
    try {
        const { id } = req.params;
        const { ledgers } = req.body;
        
        if (!Array.isArray(ledgers)) {
            return res.status(400).json({ error: 'Ledgers must be an array' });
        }
        
        const company = await dbService.get('SELECT id FROM companies WHERE id = ?', [id]);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        await dbService.beginTransaction();
        
        try {
            // Delete existing extra ledgers
            await dbService.run('DELETE FROM extra_ledgers WHERE company_id = ?', [id]);
            
            // Insert new ledgers
            for (const ledger of ledgers) {
                await dbService.run(
                    'INSERT INTO extra_ledgers (company_id, ledger_name) VALUES (?, ?)',
                    [id, ledger]
                );
            }
            
            await dbService.commit();
            res.json({ message: 'Extra ledgers updated successfully' });
        } catch (error) {
            await dbService.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error updating extra ledgers:', error);
        res.status(500).json({ error: 'Failed to update extra ledgers' });
    }
});

// Create next financial year
router.post('/:id/financial-years', async (req, res) => {
    try {
        const { id } = req.params;
        const { currentYear } = req.body;
        
        const company = await dbService.get('SELECT * FROM companies WHERE id = ?', [id]);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // Parse current year and create next year
        const [startYear, endYear] = currentYear.split('-').map(Number);
        const nextYearRange = `${startYear + 1}-${endYear + 1}`;
        
        // Check if next year already exists
        const existingYear = await dbService.get(
            'SELECT id FROM financial_years WHERE company_id = ? AND year_range = ?',
            [id, nextYearRange]
        );
        
        if (existingYear) {
            return res.status(409).json({ error: 'Financial year already exists' });
        }
        
        await dbService.run(
            'INSERT INTO financial_years (company_id, year_range) VALUES (?, ?)',
            [id, nextYearRange]
        );
        
        res.json({ yearRange: nextYearRange });
    } catch (error) {
        console.error('Error creating financial year:', error);
        res.status(500).json({ error: 'Failed to create financial year' });
    }
});

// Activate license for a company
router.post('/:id/activate-license', async (req, res) => {
    try {
        const { id } = req.params;
        const { licenseData } = req.body;
        
        const company = await dbService.get('SELECT * FROM companies WHERE id = ?', [id]);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // For this implementation, we'll just update the license valid date
        // In a real implementation, you'd validate the license key
        await dbService.run(
            'UPDATE companies SET license_valid_upto = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [licenseData.validUpto, id]
        );
        
        // Add to license history
        await dbService.run(
            `INSERT INTO license_history (id, company_id, license_key, valid_from, valid_upto, activated_at)
             VALUES (?, ?, ?, ?, ?, ?)`,
            [
                uuidv4(), id, licenseData.key, licenseData.validFrom, 
                licenseData.validUpto, new Date().toISOString()
            ]
        );
        
        // Return updated company info
        const updatedCompany = await dbService.get('SELECT * FROM companies WHERE id = ?', [id]);
        const companyInfo = {
            companyName: updatedCompany.company_name,
            pan: updatedCompany.pan,
            cin: updatedCompany.cin,
            dateOfIncorporation: updatedCompany.date_of_incorporation,
            financialYearStart: updatedCompany.financial_year_start,
            financialYearEnd: updatedCompany.financial_year_end,
            firstDateOfAdoption: updatedCompany.first_date_of_adoption,
            dataFolderPath: updatedCompany.data_folder_path,
            addressLine1: updatedCompany.address_line1,
            addressLine2: updatedCompany.address_line2,
            city: updatedCompany.city,
            pin: updatedCompany.pin,
            email: updatedCompany.email,
            mobile: updatedCompany.mobile,
            contactPerson: updatedCompany.contact_person,
            licenseValidUpto: updatedCompany.license_valid_upto
        };
        
        res.json(companyInfo);
    } catch (error) {
        console.error('Error activating license:', error);
        res.status(500).json({ error: 'Failed to activate license' });
    }
});

// Recalculate depreciation from a specific year
router.post('/:id/recalculate', async (req, res) => {
    try {
        const { id } = req.params;
        const { startYear, assets } = req.body;
        
        const company = await dbService.get('SELECT * FROM companies WHERE id = ?', [id]);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // This is a simplified implementation
        // In a full implementation, you'd need complex depreciation calculation logic
        await dbService.beginTransaction();
        
        try {
            // Delete existing yearly data from start year onwards
            const financial_years = await dbService.all(
                'SELECT year_range FROM financial_years WHERE company_id = ? AND year_range >= ? ORDER BY year_range',
                [id, startYear]
            );
            
            for (const fy of financial_years) {
                await dbService.run(
                    'DELETE FROM asset_yearly_data WHERE asset_id IN (SELECT id FROM assets WHERE company_id = ?) AND year_range = ?',
                    [id, fy.year_range]
                );
            }
            
            // Recalculate and insert new yearly data
            // This would involve complex depreciation calculations
            // For now, we'll just acknowledge the request
            
            await dbService.commit();
            res.json({ message: 'Recalculation completed successfully' });
        } catch (error) {
            await dbService.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error during recalculation:', error);
        res.status(500).json({ error: 'Failed to recalculate depreciation' });
    }
});

// Calculate recalculation impact
router.post('/:id/recalculate-impact', async (req, res) => {
    try {
        const { id } = req.params;
        const { year, assetsBefore, assetsAfter } = req.body;
        
        const company = await dbService.get('SELECT id FROM companies WHERE id = ?', [id]);
        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }
        
        // Calculate impact between before and after assets
        const impact = [];
        
        // This is a simplified implementation
        // In a real implementation, you'd calculate the actual depreciation differences
        for (const assetAfter of assetsAfter) {
            const assetBefore = assetsBefore.find(a => a.recordId === assetAfter.recordId);
            if (assetBefore) {
                // Check for differences that would impact depreciation
                if (assetBefore.basicAmount !== assetAfter.basicAmount ||
                    assetBefore.depreciationMethod !== assetAfter.depreciationMethod ||
                    assetBefore.lifeInYears !== assetAfter.lifeInYears) {
                    
                    impact.push({
                        recordId: assetAfter.recordId,
                        assetParticulars: assetAfter.assetParticulars,
                        oldDepreciation: assetBefore.basicAmount * 0.1, // Simplified calculation
                        newDepreciation: assetAfter.basicAmount * 0.1,  // Simplified calculation
                        difference: (assetAfter.basicAmount - assetBefore.basicAmount) * 0.1
                    });
                }
            }
        }
        
        res.json(impact);
    } catch (error) {
        console.error('Error calculating recalculation impact:', error);
        res.status(500).json({ error: 'Failed to calculate impact' });
    }
});

export default router;
