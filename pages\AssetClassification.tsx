/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo } from 'react';
import { api } from '../lib/api';
import type { StatutoryRate } from '../lib/db-server';
import { DataViewProps } from '../lib/types';
import { sortData, exportToExcel } from '../lib/utils';
import { DownloadIcon, SaveIcon, XIcon } from '../Icons';
import { Highlight } from './PlaceholderViews';

export function AssetClassification({ companyId, companyName, year, showAlert, showConfirmation, loggedInUser }: DataViewProps) {
    const [originalRates, setOriginalRates] = useState<StatutoryRate[]>([]);
    const [editedRates, setEditedRates] = useState<StatutoryRate[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isDirty, setIsDirty] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [sortConfig, setSortConfig] = useState<{ key: keyof StatutoryRate; direction: 'asc' | 'desc' } | null>({ key: 'assetGroup', direction: 'asc' });
    const [selectedRowKey, setSelectedRowKey] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);
    const [filterText, setFilterText] = useState('');

    useEffect(() => {
        if(!companyId) {
            setEditedRates([]);
            setLoading(false);
            setIsDirty(false);
            setSelectedRowKey(null);
            setSelectedColumnKey(null);
            setFilterText('');
            return;
        }
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                setIsDirty(false);
                setSelectedRowKey(null);
                setSelectedColumnKey(null);
                setFilterText('');
                const data = await api.getStatutoryRates(companyId);
                const deepClonedData = JSON.parse(JSON.stringify(data));
                setOriginalRates(deepClonedData);
                setEditedRates(deepClonedData);
            } catch (err) {
                setError('Failed to fetch asset classification data.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [companyId]);
    
    const sortedRates = useMemo(() => {
        return sortData(editedRates, sortConfig);
    }, [editedRates, sortConfig]);

    const searchableFields: (keyof StatutoryRate)[] = ['assetGroup', 'assetSubGroup', 'scheduleIIClassification', 'tangibility', 'isStatutory'];

    const filteredRates = useMemo(() => {
        if (!filterText) return sortedRates;
        const searchTerm = filterText.trim().toLowerCase();
        return sortedRates.filter(rate =>
            searchableFields.some(field =>
                String(rate[field] ?? '').toLowerCase().includes(searchTerm)
            )
        );
    }, [sortedRates, filterText]);

    const requestSort = (key: keyof StatutoryRate) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: keyof StatutoryRate) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };

    const getColumnClass = (key: keyof StatutoryRate) => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if (key === 'usefulLifeYears') classes.push('text-right');
        if (['assetGroup', 'assetSubGroup', 'scheduleIIClassification'].includes(key)) classes.push('td-wrap');
        return classes.join(' ');
    };

    const getHeaderClass = (key: keyof StatutoryRate) => {
        const classes = ['sortable'];
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        if (key === 'usefulLifeYears') classes.push('text-right');
        return classes.join(' ');
    };

    const handleLifeChange = (index: number, newLife: string) => {
        const newRates = [...editedRates];
        const originalIndex = editedRates.findIndex(r => r.assetSubGroup === sortedRates[index].assetSubGroup && r.assetGroup === sortedRates[index].assetGroup);
        if (originalIndex !== -1 && (newLife === '' || /^\d*$/.test(newLife))) {
             newRates[originalIndex] = { ...newRates[originalIndex], usefulLifeYears: newLife };
             setEditedRates(newRates);
             setIsDirty(true);
        }
    };
    
    const handleSaveChanges = async () => {
        if (!companyId || !companyName) return;
        setIsSaving(true);
        try {
            await api.updateStatutoryRates(companyId, editedRates);
            if (loggedInUser) {
                await api.addAuditLog({
                    userId: loggedInUser.id,
                    username: loggedInUser.username,
                    action: 'UPDATE_ASSET_CLASSIFICATION',
                    details: `Updated asset classification rates for company "${companyName}".`
                });
            }
            setOriginalRates(JSON.parse(JSON.stringify(editedRates)));
            setIsDirty(false);
            showAlert("Success", "Changes saved successfully!", 'success');
        } catch (error) {
            console.error("Failed to save changes:", error);
            showAlert("Error", "Error saving changes. Please try again.", 'error');
        } finally {
            setIsSaving(false);
        }
    };

    const handleCancelChanges = () => {
        showConfirmation(
            'Discard Changes',
            'Are you sure you want to discard all unsaved changes?',
            () => {
                setEditedRates(JSON.parse(JSON.stringify(originalRates)));
                setIsDirty(false);
            }
        );
    };

    const handleExport = () => {
        if (!companyId || !companyName) return;

        const dataToExport = filteredRates.map(rate => ({
            "Asset Group": rate.assetGroup,
            "Asset Sub-Group": rate.assetSubGroup,
            "Useful Life (Yrs)": parseInt(rate.usefulLifeYears, 10),
            "Extra Shift Depr.": rate.extraShiftDepreciation,
            "Schedule III Classification": rate.scheduleIIClassification,
            "Tangibility": rate.tangibility,
            "Is Statutory": rate.isStatutory,
        }));
        
        const success = exportToExcel({
            data: dataToExport,
            companyName,
            year,
            reportName: 'Asset_Classification'
        });

        if (!success) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    if (!companyId) return null;
    if (loading) return <div className="loading-indicator">Loading Asset Classification...</div>;
    if (error) return <div className="error-message">{error}</div>;

    return (
        <>
            <div className="view-header">
                <h2>Asset Classification</h2>
                 <div className="actions">
                    <button className="btn btn-excel" onClick={handleExport} disabled={isSaving || loading}><DownloadIcon /> Export to Excel</button>
                    {isDirty && <button className="btn btn-warning" onClick={handleCancelChanges} disabled={isSaving}><XIcon/> Cancel</button>}
                    {isDirty && <button className="btn btn-primary" onClick={handleSaveChanges} disabled={isSaving}><SaveIcon/> {isSaving ? 'Saving...' : 'Save Changes'}</button>}
                </div>
            </div>
            <div className="filter-container">
                <div className="filter-input-wrapper">
                    <input
                        type="text"
                        placeholder="Filter classifications..."
                        className="filter-input"
                        value={filterText}
                        onChange={e => setFilterText(e.target.value)}
                        disabled={isSaving || loading}
                        aria-label="Filter asset classifications"
                    />
                    <button
                        className={`filter-clear-btn ${!filterText ? 'hidden' : ''}`}
                        onClick={() => setFilterText('')}
                        aria-label="Clear filter"
                        title="Clear filter"
                        disabled={isSaving || loading}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </div>
                <div className="filter-info">
                    <span className="filter-info-icon">ⓘ</span>
                    <div className="filter-info-tooltip">
                        <strong>Filtering on:</strong>
                        <ul>
                            <li>Asset Group</li>
                            <li>Asset Sub-Group</li>
                            <li>Schedule III Classification</li>
                            <li>Tangibility</li>
                            <li>Is Statutory</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div className="table-container asset-classification-table-container">
                <table className="table-layout-auto">
                    <thead><tr>
                        <th className={getHeaderClass('assetGroup')} onClick={() => requestSort('assetGroup')}>Asset Group{getSortIndicator('assetGroup')}</th>
                        <th className={getHeaderClass('assetSubGroup')} onClick={() => requestSort('assetSubGroup')}>Asset Sub-Group{getSortIndicator('assetSubGroup')}</th>
                        <th className={getHeaderClass('usefulLifeYears')} onClick={() => requestSort('usefulLifeYears')}>Useful Life (Yrs){getSortIndicator('usefulLifeYears')}</th>
                        <th className={getHeaderClass('extraShiftDepreciation')} onClick={() => requestSort('extraShiftDepreciation')}>Extra Shift Depr.{getSortIndicator('extraShiftDepreciation')}</th>
                        <th className={getHeaderClass('scheduleIIClassification')} onClick={() => requestSort('scheduleIIClassification')}>Schedule III Classification{getSortIndicator('scheduleIIClassification')}</th>
                        <th className={getHeaderClass('tangibility')} onClick={() => requestSort('tangibility')}>Tangibility{getSortIndicator('tangibility')}</th>
                        <th className={getHeaderClass('isStatutory')} onClick={() => requestSort('isStatutory')}>Is Statutory{getSortIndicator('isStatutory')}</th>
                    </tr></thead>
                    <tbody>
                        {filteredRates.map((rate, index) => {
                            const rowKey = `${rate.assetGroup}-${rate.assetSubGroup}-${index}`;
                            return (
                                <tr key={rowKey} onClick={() => setSelectedRowKey(rowKey)} className={rowKey === selectedRowKey ? 'selected-row' : ''}>
                                    <td className={getColumnClass('assetGroup')}><Highlight text={rate.assetGroup} highlight={filterText} /></td>
                                    <td className={getColumnClass('assetSubGroup')}><Highlight text={rate.assetSubGroup} highlight={filterText} /></td>
                                    <td className={getColumnClass('usefulLifeYears')}>
                                        <input
                                            type="text"
                                            className="table-input"
                                            value={rate.usefulLifeYears}
                                            onChange={(e) => handleLifeChange(index, e.target.value)}
                                            disabled={rate.isStatutory === 'Yes' || isSaving}
                                            />
                                    </td>
                                    <td className={getColumnClass('extraShiftDepreciation')}><Highlight text={rate.extraShiftDepreciation} highlight={filterText} /></td>
                                    <td className={getColumnClass('scheduleIIClassification')}><Highlight text={rate.scheduleIIClassification} highlight={filterText} /></td>
                                    <td className={getColumnClass('tangibility')}><Highlight text={rate.tangibility} highlight={filterText} /></td>
                                    <td className={getColumnClass('isStatutory')}><Highlight text={rate.isStatutory} highlight={filterText} /></td>
                                </tr>
                            );
                        })}
                    </tbody>
                </table>
            </div>
        </>
    );
}

