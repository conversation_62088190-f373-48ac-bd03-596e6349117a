{"name": "far-sighted", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "backend": "cd backend && npm run dev", "backend:start": "cd backend && npm start", "backend:init": "cd backend && npm run init-db", "dev:full": "concurrently \"npm run backend\" \"npm run dev\"", "start:full": "concurrently \"npm run backend:start\" \"npm run dev\""}, "dependencies": {"react": "18.2.0", "react-dom": "18.2.0", "xlsx": "0.18.5"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.3.0", "concurrently": "^9.2.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}