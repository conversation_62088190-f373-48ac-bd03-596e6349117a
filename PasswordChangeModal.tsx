/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, FC } from 'react';
import { api } from './lib/api';
import { AlertTriangleIcon, EyeIcon, EyeOffIcon } from './Icons';

interface PasswordChangeModalProps {
    isOpen: boolean;
    username: string;
    isFirstLogin?: boolean;
    onSuccess: () => void;
    onCancel?: () => void;
    showAlert: (title: string, message: string, type: 'success' | 'error' | 'info') => void;
}

export const PasswordChangeModal: FC<PasswordChangeModalProps> = ({ 
    isOpen, 
    username, 
    isFirstLogin = false, 
    onSuccess, 
    onCancel,
    showAlert 
}) => {
    const [formData, setFormData] = useState({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    });
    const [showPasswords, setShowPasswords] = useState({
        current: false,
        new: false,
        confirm: false
    });
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');

    if (!isOpen) return null;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
        setError(''); // Clear error when user types
    };

    const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
        setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
    };

    const validatePassword = (password: string): string[] => {
        const errors: string[] = [];
        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long');
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        if (!/[0-9]/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }
        return errors;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');

        // Validation
        if (!formData.currentPassword && !isFirstLogin) {
            setError('Current password is required');
            return;
        }

        if (!formData.newPassword) {
            setError('New password is required');
            return;
        }

        if (formData.newPassword !== formData.confirmPassword) {
            setError('New passwords do not match');
            return;
        }

        // Password strength validation
        const passwordErrors = validatePassword(formData.newPassword);
        if (passwordErrors.length > 0) {
            setError(passwordErrors.join('. '));
            return;
        }

        // For first login, use default password as current password
        const currentPassword = isFirstLogin ? 'admin123' : formData.currentPassword;

        setIsLoading(true);
        try {
            const response = await api.changePassword(currentPassword, formData.newPassword);
            
            showAlert(
                'Password Changed Successfully', 
                'Your password has been updated. Please save your new recovery key securely.',
                'success'
            );
            
            onSuccess();
        } catch (error) {
            console.error('Password change error:', error);
            setError(error instanceof Error ? error.message : 'Failed to change password');
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        if (isFirstLogin) {
            showAlert(
                'Password Change Required',
                'You must change your password before continuing.',
                'info'
            );
            return;
        }
        if (onCancel) onCancel();
    };

    return (
        <div className="modal-overlay" onClick={handleCancel}>
            <div className="modal-content" style={{ maxWidth: '500px' }} onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <div className="modal-header-content">
                        <AlertTriangleIcon className="icon-warning" size={24}/>
                        <h2>{isFirstLogin ? 'Change Default Password' : 'Change Password'}</h2>
                    </div>
                </div>
                
                <div className="modal-body">
                    {isFirstLogin && (
                        <div className="alert-info" style={{ 
                            backgroundColor: 'var(--background-control-highlight)', 
                            padding: '1rem', 
                            borderRadius: '6px', 
                            marginBottom: '1.5rem',
                            border: '1px solid var(--accent-primary)'
                        }}>
                            <p><strong>Welcome, {username}!</strong></p>
                            <p>For security reasons, you must change your default password before continuing.</p>
                            <p>Your current password is: <code>admin123</code></p>
                        </div>
                    )}

                    <form onSubmit={handleSubmit}>
                        {!isFirstLogin && (
                            <div className="form-group">
                                <label htmlFor="currentPassword">Current Password *</label>
                                <div className="password-input-wrapper">
                                    <input
                                        type={showPasswords.current ? 'text' : 'password'}
                                        id="currentPassword"
                                        name="currentPassword"
                                        value={formData.currentPassword}
                                        onChange={handleChange}
                                        required={!isFirstLogin}
                                        autoComplete="current-password"
                                    />
                                    <button
                                        type="button"
                                        className="password-toggle"
                                        onClick={() => togglePasswordVisibility('current')}
                                        tabIndex={-1}
                                    >
                                        {showPasswords.current ? <EyeOffIcon size={16} /> : <EyeIcon size={16} />}
                                    </button>
                                </div>
                            </div>
                        )}

                        <div className="form-group">
                            <label htmlFor="newPassword">New Password *</label>
                            <div className="password-input-wrapper">
                                <input
                                    type={showPasswords.new ? 'text' : 'password'}
                                    id="newPassword"
                                    name="newPassword"
                                    value={formData.newPassword}
                                    onChange={handleChange}
                                    required
                                    autoComplete="new-password"
                                />
                                <button
                                    type="button"
                                    className="password-toggle"
                                    onClick={() => togglePasswordVisibility('new')}
                                    tabIndex={-1}
                                >
                                    {showPasswords.new ? <EyeOffIcon size={16} /> : <EyeIcon size={16} />}
                                </button>
                            </div>
                        </div>

                        <div className="form-group">
                            <label htmlFor="confirmPassword">Confirm New Password *</label>
                            <div className="password-input-wrapper">
                                <input
                                    type={showPasswords.confirm ? 'text' : 'password'}
                                    id="confirmPassword"
                                    name="confirmPassword"
                                    value={formData.confirmPassword}
                                    onChange={handleChange}
                                    required
                                    autoComplete="new-password"
                                />
                                <button
                                    type="button"
                                    className="password-toggle"
                                    onClick={() => togglePasswordVisibility('confirm')}
                                    tabIndex={-1}
                                >
                                    {showPasswords.confirm ? <EyeOffIcon size={16} /> : <EyeIcon size={16} />}
                                </button>
                            </div>
                        </div>

                        <div className="password-requirements">
                            <h4>Password Requirements:</h4>
                            <ul>
                                <li>At least 8 characters long</li>
                                <li>At least one uppercase letter (A-Z)</li>
                                <li>At least one lowercase letter (a-z)</li>
                                <li>At least one number (0-9)</li>
                                <li>At least one special character (!@#$%^&*)</li>
                            </ul>
                        </div>

                        {error && (
                            <div className="error-message" style={{ 
                                color: 'var(--accent-danger)', 
                                marginTop: '1rem',
                                padding: '0.75rem',
                                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                                borderRadius: '4px',
                                border: '1px solid var(--accent-danger)'
                            }}>
                                {error}
                            </div>
                        )}

                        <div className="modal-actions">
                            {!isFirstLogin && (
                                <button 
                                    type="button" 
                                    className="btn btn-secondary" 
                                    onClick={handleCancel}
                                    disabled={isLoading}
                                >
                                    Cancel
                                </button>
                            )}
                            <button 
                                type="submit" 
                                className="btn btn-primary" 
                                disabled={isLoading}
                            >
                                {isLoading ? 'Changing Password...' : 'Change Password'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <style jsx>{`
                .password-input-wrapper {
                    position: relative;
                    display: flex;
                    align-items: center;
                }

                .password-input-wrapper input {
                    padding-right: 2.5rem;
                    flex: 1;
                }

                .password-toggle {
                    position: absolute;
                    right: 0.75rem;
                    background: none;
                    border: none;
                    cursor: pointer;
                    color: var(--text-secondary);
                    padding: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .password-toggle:hover {
                    color: var(--text-primary);
                }

                .password-requirements {
                    margin-top: 1rem;
                    padding: 1rem;
                    background-color: var(--background-tertiary);
                    border-radius: 6px;
                    border: 1px solid var(--border-primary);
                }

                .password-requirements h4 {
                    margin: 0 0 0.5rem 0;
                    font-size: 0.9rem;
                    color: var(--text-primary);
                }

                .password-requirements ul {
                    margin: 0;
                    padding-left: 1.25rem;
                    font-size: 0.85rem;
                    color: var(--text-secondary);
                }

                .password-requirements li {
                    margin-bottom: 0.25rem;
                }

                .alert-info code {
                    background-color: var(--background-tertiary);
                    padding: 0.25rem 0.5rem;
                    border-radius: 3px;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                }
            `}</style>
        </div>
    );
};
