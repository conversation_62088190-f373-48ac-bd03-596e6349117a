@echo off
setlocal enabledelayedexpansion

echo =====================================
echo FAR SIGHTED - KILL ALL PROCESSES
echo Professional Advisory Services - CA
echo =====================================
echo.

echo 🔥 Terminating all FAR Sighted processes...
echo.

REM Kill Node.js processes
echo 📛 Killing Node.js processes...
taskkill /f /im node.exe 2>nul
if %errorlevel% == 0 (
    echo    ✅ Node.js processes terminated
) else (
    echo    ℹ️  No Node.js processes found
)

REM Kill npm processes
echo 📛 Killing npm processes...
taskkill /f /im npm.cmd 2>nul
taskkill /f /im npm 2>nul
if %errorlevel% == 0 (
    echo    ✅ npm processes terminated
) else (
    echo    ℹ️  No npm processes found
)

REM Kill nodemon processes
echo 📛 Killing nodemon processes...
taskkill /f /im nodemon 2>nul
if %errorlevel% == 0 (
    echo    ✅ nodemon processes terminated
) else (
    echo    ℹ️  No nodemon processes found
)

REM Kill cmd processes that might be running our servers
echo 📛 Killing cmd processes with FAR titles...
for /f "tokens=2 delims= " %%a in ('tasklist /fi "imagename eq cmd.exe" /fi "windowtitle eq FAR*" /fo table /nh 2^>nul') do (
    echo    Killing cmd process (PID: %%a)
    taskkill /f /PID %%a 2>nul
)

echo.
echo 📛 Killing processes on application ports...

REM Function to kill process on port
set "ports=9090 8090 5173 8080 3000 5000"

for %%p in (%ports%) do (
    echo    🔍 Checking port %%p...
    for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":%%p" ^| find "LISTENING"') do (
        echo       💀 Killing process on port %%p (PID: %%a)
        taskkill /f /PID %%a 2>nul
    )
)

echo.
echo 🧹 Additional cleanup...

REM Kill any remaining Vite processes
taskkill /f /im vite 2>nul
if %errorlevel% == 0 (
    echo    ✅ Vite processes terminated
) else (
    echo    ℹ️  No Vite processes found
)

REM Kill any React/JavaScript development processes
wmic process where "commandline like '%%npm run dev%%'" delete 2>nul
wmic process where "commandline like '%%npm start%%'" delete 2>nul
wmic process where "commandline like '%%vite%%'" delete 2>nul
wmic process where "commandline like '%%nodemon%%'" delete 2>nul

echo.
echo ⏳ Waiting for processes to fully terminate...
timeout /t 3 /nobreak >nul

echo.
echo 🧹 Final cleanup - checking ports again...
set "active_ports="
for %%p in (%ports%) do (
    for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":%%p" ^| find "LISTENING"') do (
        set "active_ports=!active_ports! %%p"
    )
)

if defined active_ports (
    echo    ⚠️  Some ports still active:!active_ports!
    echo    This may be normal for system processes
) else (
    echo    ✅ All application ports are now free
)

echo.
echo 📊 Process Termination Summary:
echo    • Node.js processes: Terminated
echo    • npm processes: Terminated  
echo    • nodemon processes: Terminated
echo    • Vite processes: Terminated
echo    • Port-specific processes: Terminated
echo    • Application ports: Available
echo.

echo ✅ All FAR Sighted processes have been terminated!
echo    You can now safely restart the application.
echo.
echo 💡 To restart the application, run: restart-far-app.bat
echo.

pause