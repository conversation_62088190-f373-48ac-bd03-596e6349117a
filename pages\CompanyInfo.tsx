
/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, FC } from 'react';
import { api } from '../lib/api';
import type { CompanyInfo as CompanyInfoType } from '../lib/db-server';
import { DataViewProps } from '../lib/types';
import { DownloadIcon, CheckCircleIcon, AlertTriangleIcon } from '../Icons';

function InfoItem({ label, value, fullWidth }: { label: string; value: React.ReactNode; fullWidth?: boolean }) {
    return (
        <div className={`info-item ${fullWidth ? 'full-width' : ''}`}>
            <label>{label}</label>
            <p>{value || 'N/A'}</p>
        </div>
    );
}

const RegistrationStatus: FC<{ licenseValidUpto: string | null }> = ({ licenseValidUpto }) => {
    let message = 'This company is unregistered. Please generate a license request file and contact support.';
    let Icon = AlertTriangleIcon;
    let colorClass = 'unregistered';

    if (licenseValidUpto) {
        const expiryDate = new Date(licenseValidUpto);
        const today = new Date();
        
        if (expiryDate < today) {
            message = `License expired on ${expiryDate.toLocaleDateString()}.`;
        } else {
            message = `Registered and valid until ${expiryDate.toLocaleDateString()}.`;
            Icon = CheckCircleIcon;
            colorClass = 'registered';
        }
    }

    return (
        <div className={`registration-status-banner ${colorClass}`}>
            <Icon size={24} />
            <span>{message}</span>
        </div>
    );
};


export function CompanyInfo({ companyId, showAlert, showConfirmation }: DataViewProps) {
    const [info, setInfo] = useState<CompanyInfoType | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!companyId) {
            setInfo(null);
            setLoading(false);
            return;
        }

        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                const data = await api.getCompanyInfo(companyId);
                setInfo(data);
            } catch (err) {
                setError('Failed to fetch company information.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [companyId]);

    const handleGenerateRequest = () => {
        if (!info) return;

        if (!info.companyName || (!info.pan && !info.cin) || !info.dateOfIncorporation || !info.firstDateOfAdoption) {
            showAlert(
                'Missing Information', 
                'Cannot generate license request file. Please edit the company and provide values for all required fields: Company Name, PAN or CIN, Date of Incorporation, and First Date of Adoption.',
                'error'
            );
            return;
        }

        const requestData = {
            companyName: info.companyName,
            pan: info.pan,
            cin: info.cin,
            dateOfIncorporation: info.dateOfIncorporation,
            firstDateOfAdoption: info.firstDateOfAdoption,
            licenseValidUpto: info.licenseValidUpto || 'N/A',
            usersLimit: "no limit"
        };
        const jsonString = JSON.stringify(requestData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        const safeCompanyName = info.companyName.replace(/[^a-z0-9]/gi, '_');
        const fileName = `FAR_Registration_Request_${safeCompanyName}.json`;
        link.download = fileName;
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        showAlert(
            'Request File Downloaded',
            `The license request file named "${fileName}" has been saved to your browser's default downloads location.\n\nPlease email this <NAME_EMAIL> to receive your license file.`,
            'success'
        );
    };


    if (loading) return <div className="loading-indicator">Loading Company Info...</div>;
    if (error) return <div className="error-message">{error}</div>;

    return (
        <>
            <div className="view-header">
                <h2>Company Info</h2>
                <div className="actions">
                    <button className="btn btn-info" onClick={handleGenerateRequest} disabled={!info}>
                        <DownloadIcon /> Generate License Request
                    </button>
                </div>
            </div>

            {!companyId && (
                 <div className="company-info-container"><p>Please select a company to view its information.</p></div>
            )}
            
            {info && (
                <div>
                    <RegistrationStatus licenseValidUpto={info.licenseValidUpto} />
                    <div className="company-info-container">
                        <div className="company-info-layout">
                            <InfoItem label="Company Name" value={info.companyName} fullWidth />
                            <InfoItem label="Corporate Identification Number (CIN)" value={info.cin} />
                            <InfoItem label="Permanent Account Number (PAN)" value={info.pan} />
                            <InfoItem label="Date of Incorporation" value={info.dateOfIncorporation ? new Date(info.dateOfIncorporation).toLocaleDateString() : 'N/A'} />
                            <InfoItem label="Address Line 1" value={info.addressLine1} />
                            <InfoItem label="Address Line 2" value={info.addressLine2} />
                            <InfoItem label="City" value={info.city} />
                            <InfoItem label="PIN Code" value={info.pin} />
                            <InfoItem label="Contact Person" value={info.contactPerson} />
                            <InfoItem label="Email" value={info.email} />
                            <InfoItem label="Mobile" value={info.mobile} />
                            <InfoItem label="Financial Year Start" value={new Date(info.financialYearStart).toLocaleDateString()} />
                            <InfoItem label="Financial Year End" value={new Date(info.financialYearEnd).toLocaleDateString()} />
                            <InfoItem label="First Date of Adoption" value={new Date(info.firstDateOfAdoption).toLocaleDateString()} />
                            <InfoItem label="Data Folder Path" value={info.dataFolderPath} fullWidth />

                            {/* Database Location Information */}
                            {info.databaseInfo && (
                                <>
                                    <InfoItem
                                        label="Current Database Location"
                                        value={
                                            <div>
                                                <div style={{ fontFamily: 'monospace', fontSize: '0.9em', marginBottom: '4px' }}>
                                                    {info.databaseInfo.currentLocation}
                                                </div>
                                                <div style={{ fontSize: '0.8em', color: '#666' }}>
                                                    Type: {info.databaseInfo.currentType}
                                                </div>
                                            </div>
                                        }
                                        fullWidth
                                    />
                                    <InfoItem
                                        label="Intended Database Location"
                                        value={
                                            <div>
                                                <div style={{ fontFamily: 'monospace', fontSize: '0.9em', marginBottom: '4px' }}>
                                                    {info.databaseInfo.intendedLocation}
                                                </div>
                                                <div style={{ fontSize: '0.8em', color: '#666' }}>
                                                    Type: {info.databaseInfo.intendedType} | Status:
                                                    <span style={{
                                                        color: info.databaseInfo.migrationStatus === 'Migrated' ? '#28a745' : '#dc3545',
                                                        fontWeight: 'bold',
                                                        marginLeft: '4px'
                                                    }}>
                                                        {info.databaseInfo.migrationStatus}
                                                    </span>
                                                </div>
                                            </div>
                                        }
                                        fullWidth
                                    />
                                </>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};