/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo, useRef, FC, useCallback } from 'react';
import * as XLSX from 'xlsx';
import { api } from '../lib/api';
import type { Asset, StatutoryRate, CompanyInfo as CompanyInfoType } from '../lib/db-server';
import { DataViewProps, ValidatedAsset, AssetValidationErrors } from '../lib/types';
import { formatIndianNumber, sortData, exportToExcel, parseAssetsFromCSV, validateAsset, exportImportErrorsToExcel, isValidDateString, exportTemplateToExcel, parseAssetsFromExcel } from '../lib/utils';
import { RotateCcwIcon, TrashIcon, PlusIcon, UploadIcon, DownloadIcon, SaveIcon, XIcon, FileIcon, EditIcon } from '../Icons';
import { Highlight } from './PlaceholderViews';
import { ColumnManager, useColumnManager } from '../components/ColumnManager';

interface NewClassification {
    group: string;
    subGroup: string;
    life: number;
    scheduleIIIClassification: string;
}

// --- Asset Import Preview Modal ---
interface AssetImportPreviewModalProps {
    isOpen: boolean;
    onClose: () => void;
    assetsToPreview: ValidatedAsset[];
    onAssetChange: (rowIndex: number, field: keyof Asset, value: any) => void;
    onConfirmImport: (assets: Asset[], newLedgers: string[], newClassifications: NewClassification[]) => void;
    newLedgers: string[];
    newClassifications: NewClassification[];
    fieldMapping: { [key: string]: keyof Asset | 'actions' };
    allHeaders: string[];
    companyName: string | null;
    year: string;
    showConfirmation: (title: string, message: string, onConfirm: () => void) => void;
    showAlert: (title: string, message: string, type: 'success' | 'error' | 'info') => void;
}

const AssetImportPreviewModal: FC<AssetImportPreviewModalProps> = ({
    isOpen, onClose, assetsToPreview, onAssetChange, onConfirmImport, newLedgers,
    newClassifications, fieldMapping, allHeaders, companyName, showAlert
}) => {
    if (!isOpen) return null;

    const errorCount = assetsToPreview.filter(a => a.errors && Object.keys(a.errors).length > 0).length;
    const warningCount = assetsToPreview.filter(a => a.warnings && Object.keys(a.warnings).length > 0).length;
    const validCount = assetsToPreview.length - errorCount;

    const handleImportClick = () => {
        const validAssets = assetsToPreview.filter(a => !a.errors || Object.keys(a.errors).length === 0);
        if (errorCount > 0) {
            showAlert(
                'Import with Errors',
                `You have ${errorCount} rows with critical errors that prevent import. Please fix them or download the error report. Only ${validCount} valid rows can be imported.`,
                'error'
            );
            return;
        }
        onConfirmImport(validAssets, newLedgers, newClassifications);
    };
    
    const handleDownloadErrors = () => {
        const invalidAssets = assetsToPreview.filter(a => a.errors && Object.keys(a.errors).length > 0);
        if (invalidAssets.length > 0 && companyName) {
            exportImportErrorsToExcel(invalidAssets, allHeaders, fieldMapping, companyName);
        } else {
            showAlert('Info', 'There are no invalid rows to download.', 'info');
        }
    };

    const renderCellContent = (asset: ValidatedAsset, header: string, rowIndex: number) => {
        const field = fieldMapping[header as keyof typeof fieldMapping] as keyof Asset;
        if (!field || field === 'actions') return null;

        const value = asset[field];
        
        if (field === 'recordId') return <span>{asset.recordId}</span>;
        if (field === 'grossAmount') return <span>{formatIndianNumber(value as number)}</span>;
        
        const isNumeric = ['basicAmount', 'dutiesTaxes', 'disposalAmount', 'salvagePercentage', 'lifeInYears', 'leasePeriod', 'wdvOfAdoptionDate'].includes(String(field));
        const isDate = ['bookEntryDate', 'putToUseDate', 'disposalDate'].includes(String(field));
        const isFloat = field === 'salvagePercentage';
        
        if (isNumeric) {
            return <input type="number" className="table-input" value={value ?? ''} onChange={e => onAssetChange(rowIndex, field, e.target.value === '' ? null : (isFloat ? parseFloat(e.target.value) : parseInt(e.target.value, 10)))} />;
        }
        if (isDate) {
            return <input type="date" className="table-input" value={value ? String(value).split('T')[0] : ''} onChange={e => onAssetChange(rowIndex, field, e.target.value)} />;
        }
        
        return <input type="text" className="table-input" value={value as any ?? ''} onChange={e => onAssetChange(rowIndex, field, e.target.value)} />;
    };

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content modal-xl import-preview-modal" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>Asset Import Preview</h2>
                    <button className="modal-close-btn" onClick={onClose}>&times;</button>
                </div>
                <div className="import-preview-summary">
                    <div className="import-preview-summary-stats">
                        <span>Total Rows: <strong>{assetsToPreview.length}</strong></span> | 
                        <span className={errorCount > 0 ? 'error-count' : ''}>Rows with Errors: <strong>{errorCount}</strong></span> |
                        <span className={warningCount > 0 ? 'warning-count' : ''}>Rows with Warnings: <strong>{warningCount}</strong></span>
                    </div>
                     <div>
                         <p style={{fontSize: '0.9rem', color: 'var(--text-secondary)'}}>Hover over red (error) or yellow (warning) cells to see details. Errors must be fixed before import.</p>
                    </div>
                </div>

                <div className="import-preview-table-container">
                    <table>
                        <thead>
                            <tr>{allHeaders.map(h => h !== 'Actions' && h !== 'Dispose' && <th key={h}>{h}</th>)}</tr>
                        </thead>
                        <tbody>
                            {assetsToPreview.map((asset, rowIndex) => (
                                <tr key={asset.recordId}>
                                    {allHeaders.map(header => {
                                        if (header === 'Actions' || header === 'Dispose') return null;
                                        const field = fieldMapping[header as keyof typeof fieldMapping] as keyof Asset;
                                        const error = asset.errors?.[field];
                                        const warning = asset.warnings?.[field];
                                        const cellClass = error ? 'cell-error' : warning ? 'cell-warning' : '';
                                        const message = error || warning;

                                        return (
                                            <td key={`${asset.recordId}-${header}`} className={cellClass} data-message={message}>
                                                {renderCellContent(asset, header, rowIndex)}
                                            </td>
                                        );
                                    })}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {(newLedgers.length > 0 || newClassifications.length > 0) && (
                    <div className="import-new-items-section">
                        {newLedgers.length > 0 && (
                            <div>
                                <h4>New Ledgers to be Added</h4>
                                <ul>{newLedgers.map(l => <li key={l}>{l}</li>)}</ul>
                            </div>
                        )}
                        {newClassifications.length > 0 && (
                             <div>
                                <h4>New Classifications to be Added</h4>
                                <ul>{newClassifications.map(c => <li key={c.subGroup}>{c.group} &gt; {c.subGroup} ({c.life} yrs, Sch.III: {c.scheduleIIIClassification})</li>)}</ul>
                            </div>
                        )}
                    </div>
                )}
                
                <div className="modal-actions">
                    <button className="btn btn-secondary" onClick={handleDownloadErrors} disabled={errorCount === 0}><DownloadIcon /> Download Error Report</button>
                    <div style={{flexGrow: 1}}></div>
                    <button className="btn btn-secondary" onClick={onClose}><XIcon /> Cancel</button>
                    <button className="btn btn-primary" onClick={handleImportClick} disabled={errorCount > 0}><SaveIcon /> Import Valid Rows</button>
                </div>
            </div>
        </div>
    );
};

// --- Disposal Modal ---
interface DisposeAssetModalProps {
    isOpen: boolean;
    onClose: () => void;
    asset: Asset | null;
    onSave: (data: { disposalDate: string | null; disposalAmount: number | null; scrapIt: boolean }) => void;
    year: string;
}

const DisposeAssetModal: FC<DisposeAssetModalProps> = ({ isOpen, onClose, asset, onSave, year }) => {
    const [formData, setFormData] = useState({
        disposalDate: '',
        disposalAmount: '',
        scrapIt: false,
    });
    const [error, setError] = useState('');

    useEffect(() => {
        if (asset) {
            setFormData({
                disposalDate: asset.disposalDate || '',
                disposalAmount: asset.disposalAmount?.toString() || '',
                scrapIt: asset.scrapIt === true, // Explicitly check for true to ensure false default
            });
            setError('');
        } else {
            // Reset form when modal is closed
            setFormData({
                disposalDate: '',
                disposalAmount: '',
                scrapIt: false,
            });
            setError('');
        }
    }, [asset]);

    if (!isOpen || !asset) return null;

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setError('');

        const disposalDate = formData.disposalDate ? new Date(formData.disposalDate + 'T00:00:00.000Z') : null;
        if (disposalDate) {
            const putToUseDate = new Date(asset.putToUseDate + 'T00:00:00.000Z');
            const [startYearStr, endYearStr] = year.split('-');
            const fyStart = new Date(`${startYearStr}-04-01T00:00:00.000Z`);
            const fyEnd = new Date(`${endYearStr}-03-31T00:00:00.000Z`);

            if (disposalDate.getTime() < putToUseDate.getTime()) {
                setError("Disposal Date cannot be earlier than the Put to Use Date.");
                return;
            }
            if (disposalDate.getTime() < fyStart.getTime() || disposalDate.getTime() > fyEnd.getTime()) {
                setError(`The Disposal Date must fall within the selected financial year (${year}).`);
                return;
            }
        }

        onSave({
            disposalDate: formData.disposalDate || null,
            disposalAmount: formData.disposalAmount ? parseInt(formData.disposalAmount, 10) : null,
            scrapIt: formData.scrapIt,
        });
    };

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" style={{maxWidth: '550px'}} onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>{asset.disposalDate ? 'Edit Disposal / Scrap Details' : 'Dispose / Scrap Asset'}</h2>
                    <button type="button" className="modal-close-btn" onClick={onClose}>&times;</button>
                </div>
                <p style={{marginBottom: '1rem'}}>Asset: <strong>{asset.assetParticulars} ({asset.recordId})</strong></p>
                <form onSubmit={handleSubmit}>
                    <div className="form-group"><label>Disposal Date</label><input type="date" value={formData.disposalDate} onChange={e => setFormData({...formData, disposalDate: e.target.value})} /></div>
                    <div className="form-group"><label>Disposal Amount</label><input type="number" value={formData.disposalAmount} onChange={e => setFormData({...formData, disposalAmount: e.target.value})} /></div>
                    <div className="recovery-key-ack" style={{marginTop: '1rem'}}>
                        <input type="checkbox" id="scrapIt" checked={formData.scrapIt} onChange={e => setFormData({...formData, scrapIt: e.target.checked})} />
                        <label htmlFor="scrapIt">Mark this asset for scrapping (e.g., for reporting purposes, even if no sale value)</label>
                    </div>
                    {error && <p className="login-error" style={{marginTop: '1rem'}}>{error}</p>}
                    <div className="modal-actions">
                        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'space-between', width: '100%' }}>
                            <div>
                                {asset.disposalDate && (
                                    <button type="button" className="btn btn-warning" onClick={() => {
                                        onSave({ disposalDate: null, disposalAmount: null, scrapIt: formData.scrapIt });
                                        onClose();
                                    }}>
                                        <XIcon /> Remove Disposal
                                    </button>
                                )}
                            </div>
                            <div style={{ display: 'flex', gap: '1rem' }}>
                                <button type="button" className="btn btn-secondary" onClick={onClose}><XIcon /> Cancel</button>
                                <button type="submit" className="btn btn-primary"><SaveIcon /> Save Disposal Info</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
};


// Component for action buttons to keep JSX cleaner
const ActionButtons: FC<{
    asset: Asset;
    originalAsset: Asset | undefined;
    onRevert: (recordId: string) => void;
    onDelete: (recordId: string) => void;
    onDispose: (asset: Asset) => void;
    disabled: boolean;
}> = ({ asset, originalAsset, onRevert, onDelete, onDispose, disabled }) => {
    const isRowDirty = originalAsset ? JSON.stringify(originalAsset) !== JSON.stringify(asset) : true;
    return (
        <div className="action-buttons-cell">
            <button className="btn-icon btn-dispose" title={asset.disposalDate ? "Edit Disposal/Scrap Details" : "Dispose/Scrap Asset"} disabled={disabled} onClick={() => onDispose(asset)} >
                 <TrashIcon />
            </button>
            <button className="btn-icon btn-revert" title="Revert row changes" disabled={!isRowDirty || disabled} onClick={() => onRevert(asset.recordId)} >
                <RotateCcwIcon />
            </button>
        </div>
    );
};

export const AssetRecords: FC<DataViewProps> = ({ companyId, companyName, year, financialYears, showAlert, showConfirmation, loggedInUser, unlockedYear }) => {
    // --- STATE MANAGEMENT ---
    const [originalAssets, setOriginalAssets] = useState<Asset[]>([]);
    const [editedAssets, setEditedAssets] = useState<Asset[]>([]);
    const [statutoryRates, setStatutoryRates] = useState<StatutoryRate[]>([]);
    const [companyInfo, setCompanyInfo] = useState<CompanyInfoType | null>(null);
    const [allLedgers, setAllLedgers] = useState<string[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isDirty, setIsDirty] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [isEditMode, setIsEditMode] = useState(false);
    const [filterText, setFilterText] = useState('');
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>({ key: 'recordId', direction: 'asc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);
    
    const [disposalModalState, setDisposalModalState] = useState<{ isOpen: boolean; asset: Asset | null }>({ isOpen: false, asset: null });
    const [validatedAssetsForPreview, setValidatedAssetsForPreview] = useState<ValidatedAsset[]>([]);
    const [newLedgersForPreview, setNewLedgersForPreview] = useState<string[]>([]);
    const [newClassificationsForPreview, setNewClassificationsForPreview] = useState<NewClassification[]>([]);
    const [isImportPreviewOpen, setIsImportPreviewOpen] = useState(false);

    // Column configuration for Asset Records table
    const defaultColumns = [
        { key: 'recordId', label: 'Record ID', visible: true, width: 120, minWidth: 80, sticky: true },
        { key: 'assetParticulars', label: 'Asset Particulars', visible: true, width: 250, minWidth: 150, sticky: true },
        { key: 'bookEntryDate', label: 'Book Entry Date', visible: true, width: 130, minWidth: 100 },
        { key: 'putToUseDate', label: 'Put to Use Date', visible: true, width: 130, minWidth: 100 },
        { key: 'basicAmount', label: 'Basic Amount', visible: true, width: 120, minWidth: 100 },
        { key: 'assetGroup', label: 'Asset Group', visible: true, width: 150, minWidth: 120 },
        { key: 'assetSubGroup', label: 'Asset Sub Group', visible: true, width: 150, minWidth: 120 },
        { key: 'depreciationMethod', label: 'Depreciation Method', visible: true, width: 140, minWidth: 120 },
        { key: 'usefulLifeYears', label: 'Useful Life (Years)', visible: true, width: 130, minWidth: 100 },
        { key: 'residualValue', label: 'Residual Value', visible: true, width: 120, minWidth: 100 },
        { key: 'disposalDate', label: 'Disposal Date', visible: false, width: 130, minWidth: 100 },
        { key: 'disposalAmount', label: 'Disposal Amount', visible: false, width: 130, minWidth: 100 },
        { key: 'scrapIt', label: 'Scrap Status', visible: false, width: 100, minWidth: 80 },
        { key: 'actions', label: 'Actions', visible: true, width: 120, minWidth: 100, resizable: false }
    ];

    // Use column manager hook
    const {
        columns,
        setColumns,
        getColumnStyles,
        getVisibleColumns,
        isColumnVisible,
        getColumnWidth
    } = useColumnManager('assetRecords', defaultColumns);

    // --- CONSTANTS & DERIVED STATE ---
    const isLatestYear = year === financialYears[financialYears.length - 1];
    const isLocked = !isLatestYear && year !== unlockedYear;
    const hasNewRow = useMemo(() => editedAssets.some(asset => asset.recordId.startsWith('new_')), [editedAssets]);

    const { headers, fieldMapping, compulsoryHeaders, searchableFields, reverseFieldMapping } = useMemo(() => {
        const headers = ["Record ID", "Asset Particulars", "Actions", "Location", "Asset ID/ Serial No", "Remarks", "Vendor/ Source", "Invoice/ Bill No.", "Book Entry Date", "Put to use Date", "Basic Amount", "Duties & Taxes", "Gross Amount", "WDV of Adoption Date", "Ledger Name in Books", "Asset Group as per Co. Act (SCH II)", "Nature of Asset as per Co. Act  (SCH II)", "Schedule III Classification", "Depreciation method", "Life in years", "Salvage %age", "Assets under leasehold", "Lease Period"];
        const fieldMapping: { [key: string]: keyof Asset | 'actions' } = { "Record ID": "recordId", "Asset Particulars": "assetParticulars", "Actions": "actions", "Book Entry Date": "bookEntryDate", "Put to use Date": "putToUseDate", "Basic Amount": "basicAmount", "Duties & Taxes": "dutiesTaxes", "Gross Amount": "grossAmount", "Vendor/ Source": "vendor", "Invoice/ Bill No.": "invoiceNo", "Model/ Make": "modelMake", "Location": "location", "Asset ID/ Serial No": "assetId", "Remarks": "remarks", "Ledger Name in Books": "ledgerNameInBooks", "Asset Group as per Co. Act (SCH II)": "assetGroup", "Nature of Asset as per Co. Act  (SCH II)": "assetSubGroup", "Schedule III Classification": "scheduleIIIClassification", "Salvage %age": "salvagePercentage", "WDV of Adoption Date": "wdvOfAdoptionDate", "Assets under leasehold": "isLeasehold", "Depreciation method": "depreciationMethod", "Life in years": "lifeInYears", "Lease Period": "leasePeriod" };
        const compulsoryHeaders = ["Asset Particulars", "Book Entry Date", "Basic Amount", "Ledger Name in Books", "Asset Group as per Co. Act (SCH II)", "Nature of Asset as per Co. Act  (SCH II)", "Schedule III Classification", "Depreciation method", "Life in years", "Salvage %age"];
        const searchableFields: (keyof Asset)[] = ['recordId', 'assetParticulars', 'location', 'assetId', 'remarks', 'vendor', 'invoiceNo', 'ledgerNameInBooks', 'assetGroup', 'assetSubGroup', 'scheduleIIIClassification'];
        const reverseFieldMapping = Object.fromEntries(Object.entries(fieldMapping).map(([key, value]) => [value, key]));
        return { headers, fieldMapping, compulsoryHeaders, searchableFields, reverseFieldMapping };
    }, []);

    // --- DATA FETCHING ---
    const fetchData = useCallback(async () => {
        if (!companyId) {
            setOriginalAssets([]); setEditedAssets([]); setStatutoryRates([]); setCompanyInfo(null); setLoading(false);
            setSelectedRowId(null);
            setSelectedColumnKey(null);
            return;
        }
        setLoading(true); setError(null); setIsDirty(false); setSelectedRowId(null); setSelectedColumnKey(null);
        try {
            const [assetData, ratesData, infoData, extraLedgers] = await Promise.all([
                api.getAssets(companyId), 
                api.getStatutoryRates(companyId),
                api.getCompanyInfo(companyId),
                api.getExtraLedgers(companyId)
            ]);
            const clonedAssets = JSON.parse(JSON.stringify(assetData));
            setOriginalAssets(clonedAssets);
            setEditedAssets(clonedAssets);
            setStatutoryRates(ratesData);
            setCompanyInfo(infoData);
            
            const usedLedgers = new Set(assetData.map(a => a.ledgerNameInBooks).filter(Boolean));
            extraLedgers.forEach(l => usedLedgers.add(l));
            setAllLedgers(Array.from(usedLedgers).sort());

        } catch (err) {
            setError('Failed to fetch asset records or classification data.');
            console.error(err);
        } finally {
            setLoading(false);
        }
    }, [companyId]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    // --- MEMOIZED LOOKUPS & DATA ---
    const statutoryInfoMap = useMemo(() => {
        const map = new Map<string, { life: string; scheduleIII: string; isStatutory: boolean }>();
        statutoryRates.forEach(rate => {
            if (rate.assetGroup && rate.assetSubGroup) {
                map.set(`${rate.assetGroup}|${rate.assetSubGroup}`, {
                    life: rate.usefulLifeYears,
                    scheduleIII: rate.scheduleIIClassification,
                    isStatutory: rate.isStatutory === 'Yes'
                });
            }
        });
        return map;
    }, [statutoryRates]);

    const dropdownOptions = useMemo(() => {
        const unique = (key: keyof (StatutoryRate)) =>
            [...new Set(statutoryRates.map(item => item[key]).filter(Boolean))].sort() as string[];
        return {
            assetGroup: unique('assetGroup'),
            scheduleIIIClassification: unique('scheduleIIClassification'),
        };
    }, [statutoryRates]);

    const assetSubGroupOptionsByGroup = useMemo(() => {
        const map = new Map<string, string[]>();
        statutoryRates.forEach(item => {
            const group = item.assetGroup;
            const subGroup = item.assetSubGroup;
            if (group && subGroup) {
                if (!map.has(group)) map.set(group, []);
                if (!map.get(group)!.includes(subGroup)) map.get(group)!.push(subGroup);
            }
        });
        map.forEach(subGroups => subGroups.sort());
        return map;
    }, [statutoryRates]);

    const sortedAssets = useMemo(() => {
        return sortData(editedAssets, sortConfig);
    }, [editedAssets, sortConfig]);

    const filteredAssets = useMemo(() => {
        if (!filterText) return sortedAssets;
        const searchTerm = filterText.trim().toLowerCase();
        return sortedAssets.filter(asset =>
            searchableFields.some(field => String(asset[field] ?? '').toLowerCase().includes(searchTerm))
        );
    }, [sortedAssets, filterText, searchableFields]);
    
    const handleDateBlur = useCallback((e: React.FocusEvent<HTMLInputElement>, recordId: string, field: keyof Asset) => {
        const { value } = e.target;
        if (!value) return; 
        
        const asset = editedAssets.find(a => a.recordId === recordId);
        if (!asset) return;

        if ((field === 'bookEntryDate' || field === 'putToUseDate')) {
            const [, endYearStr] = year.split('-');
            const fyEnd = new Date(`${endYearStr}-03-31T23:59:59.999Z`);
            const selectedDate = new Date(value + 'T00:00:00.000Z');

            if (selectedDate > fyEnd) {
                showAlert("Validation Error", `${field === 'bookEntryDate' ? 'Book Entry Date' : 'Put to Use Date'} cannot be in a future financial year.`, 'error');
            }
        }
        
        if (field === 'putToUseDate' && asset.bookEntryDate) {
            const putToUseDate = new Date(value + 'T00:00:00.000Z');
            const bookEntryDate = new Date(asset.bookEntryDate + 'T00:00:00.000Z');
            if (putToUseDate.getTime() < bookEntryDate.getTime()) {
                showAlert("Validation Error", "Put to Use Date cannot be earlier than the Book Entry Date.", 'error');
            }
        }
    }, [year, showAlert, editedAssets]);


    // --- EVENT HANDLERS ---
    const handleAssetChange = useCallback((recordId: string, field: keyof Asset, value: any) => {
        if ((field === 'bookEntryDate' || field === 'putToUseDate') && value) {
            const [, endYearStr] = year.split('-');
            const fyEnd = new Date(`${endYearStr}-03-31T23:59:59.999Z`);
            const selectedDate = new Date(value + 'T00:00:00.000Z');

            if (selectedDate > fyEnd) {
                const fieldName = field === 'bookEntryDate' ? 'Book Entry Date' : 'Put to Use Date';
                showAlert("Validation Error", `${fieldName} cannot be in a future financial year.`, 'error');
                return;
            }
        }

        if (field === 'putToUseDate' && value) {
            const assetBeingChanged = editedAssets.find(a => a.recordId === recordId);
            if (assetBeingChanged?.bookEntryDate) {
                const putToUseDate = new Date(value + 'T00:00:00.000Z');
                const bookEntryDate = new Date(assetBeingChanged.bookEntryDate + 'T00:00:00.000Z');
                if (putToUseDate.getTime() < bookEntryDate.getTime()) {
                    showAlert("Validation Error", "Put to Use Date cannot be earlier than the Book Entry Date.", 'error');
                    return; 
                }
            }
        }

        setEditedAssets(prevAssets => {
            return prevAssets.map(asset => {
                if (asset.recordId !== recordId) return asset;

                let updatedAsset = { ...asset, [field]: value };

                if (field === 'assetGroup') {
                    updatedAsset = { ...updatedAsset, assetSubGroup: '', lifeInYears: 0, scheduleIIIClassification: '' };
                } else if (field === 'assetSubGroup') {
                    const info = statutoryInfoMap.get(`${updatedAsset.assetGroup}|${value}`);
                    updatedAsset = { ...updatedAsset, lifeInYears: info ? parseInt(info.life, 10) || 0 : 0, scheduleIIIClassification: info ? info.scheduleIII : '' };
                } else if (field === 'bookEntryDate' && !asset.putToUseDate) {
                    updatedAsset.putToUseDate = value;
                } else if (field === 'basicAmount' || field === 'dutiesTaxes') {
                    const basic = field === 'basicAmount' ? Number(value) || 0 : updatedAsset.basicAmount || 0;
                    const taxes = field === 'dutiesTaxes' ? Number(value) || 0 : updatedAsset.dutiesTaxes || 0;
                    updatedAsset.grossAmount = basic + taxes;
                } else if (field === 'putToUseDate' && companyInfo) {
                    const isNewer = new Date(value) >= new Date(companyInfo.firstDateOfAdoption);
                    if (isNewer) {
                        updatedAsset.wdvOfAdoptionDate = null;
                    }
                } else if (field === 'isLeasehold') {
                    if (value === false) { 
                        updatedAsset.leasePeriod = null;
                    }
                }
                return updatedAsset;
            });
        });
        setIsDirty(true);
    }, [statutoryInfoMap, companyInfo, editedAssets, year, showAlert]);
    
    const handleSaveChanges = useCallback(async () => {
        if (!companyId || !companyInfo) return;

        const assetsToSave = editedAssets.map(asset => ({
            ...asset,
            putToUseDate: asset.putToUseDate || asset.bookEntryDate,
        }));
        
        for (const asset of assetsToSave) {
            for (const header of compulsoryHeaders) {
                const field = fieldMapping[header as keyof typeof fieldMapping];
                if (field === 'actions') continue;
                if (asset[field as keyof Asset] == null || String(asset[field as keyof Asset]).trim() === '') {
                    showAlert("Validation Error", `Error in Asset '${asset.assetParticulars || asset.recordId}': Field '${header}' is required.`, 'error');
                    return;
                }
            }

            if (asset.bookEntryDate && !isValidDateString(asset.bookEntryDate)) {
                showAlert("Validation Error", `Error in Asset '${asset.assetParticulars || asset.recordId}': Invalid format for Book Entry Date. Use YYYY-MM-DD.`, 'error');
                return;
            }
            if (asset.putToUseDate && !isValidDateString(asset.putToUseDate)) {
                showAlert("Validation Error", `Error in Asset '${asset.assetParticulars || asset.recordId}': Invalid format for Put to Use Date. Use YYYY-MM-DD.`, 'error');
                return;
            }
            if (asset.disposalDate && !isValidDateString(asset.disposalDate)) {
                showAlert("Validation Error", `Error in Asset '${asset.assetParticulars || asset.recordId}': Invalid format for Disposal Date. Use YYYY-MM-DD.`, 'error');
                return;
            }
            
            if (asset.putToUseDate && new Date(asset.putToUseDate) < new Date(companyInfo.firstDateOfAdoption)) {
                if (asset.wdvOfAdoptionDate == null) {
                    showAlert("Validation Error", `Error in Asset '${asset.assetParticulars || asset.recordId}': 'WDV of Adoption Date' is required for assets put to use before the company adoption date.`, 'error');
                    return;
                }
            }
        }

        setIsSaving(true);
        try {
            if (loggedInUser) {
                // ... Audit log logic ...
            }
            
            const savedAssets = await api.updateAssets(companyId, assetsToSave);
            const clonedAssets = JSON.parse(JSON.stringify(savedAssets));
            setOriginalAssets(clonedAssets);
            setEditedAssets(clonedAssets);
            setIsDirty(false);
            showAlert("Success", "Asset records saved successfully!", 'success');
        } catch (error) {
            console.error("Failed to save asset records:", error);
            showAlert("Error", "Error saving asset records.", 'error');
        } finally {
            setIsSaving(false);
        }
    }, [companyId, companyInfo, editedAssets, compulsoryHeaders, fieldMapping, showAlert, loggedInUser]);

    const handleCancelChanges = useCallback(() => {
        showConfirmation(
            'Discard Changes',
            'Are you sure you want to discard all unsaved changes?',
            () => {
                setEditedAssets(JSON.parse(JSON.stringify(originalAssets)));
                setIsDirty(false);
            }
        );
    }, [originalAssets, showConfirmation]);

    const handleRevertAsset = useCallback((recordId: string) => {
        const original = originalAssets.find(a => a.recordId === recordId);
        setEditedAssets(prev => {
            if (original) return prev.map(a => a.recordId === recordId ? JSON.parse(JSON.stringify(original)) : a);
            return prev.filter(a => a.recordId !== recordId);
        });
    }, [originalAssets]);

    const handleOpenDisposalModal = (asset: Asset) => setDisposalModalState({ isOpen: true, asset });
    
    const handleSaveDisposal = (data: { disposalDate: string | null; disposalAmount: number | null; scrapIt: boolean }) => {
        if (!disposalModalState.asset) return;
        const assetId = disposalModalState.asset.recordId;
        handleAssetChange(assetId, 'disposalDate', data.disposalDate);
        handleAssetChange(assetId, 'disposalAmount', data.disposalAmount);
        handleAssetChange(assetId, 'scrapIt', data.scrapIt);
        setDisposalModalState({ isOpen: false, asset: null });
    };

    const handleAddAsset = useCallback(() => {
        if (!companyId) return;
        if (hasNewRow) {
            showAlert("Info", "Please save the current new asset before adding another.", 'info');
            return;
        }
        // Automatically enable edit mode when adding an asset
        setIsEditMode(true);
        const newAsset: Asset = { recordId: `new_${Date.now()}`, assetParticulars: '', bookEntryDate: new Date().toISOString().split('T')[0], putToUseDate: '', basicAmount: 0, dutiesTaxes: 0, grossAmount: 0, vendor: '', invoiceNo: '', modelMake: '', location: '', assetId: '', remarks: '', ledgerNameInBooks: '', assetGroup: '', assetSubGroup: '', scheduleIIIClassification: '', disposalDate: null, disposalAmount: null, salvagePercentage: 5, wdvOfAdoptionDate: null, isLeasehold: false, depreciationMethod: 'WDV', lifeInYears: 0, leasePeriod: null, scrapIt: false };
        setEditedAssets(prev => [newAsset, ...prev]);
        setIsDirty(true);
    }, [companyId, hasNewRow, showAlert]);
    
    const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file || !companyId || !companyInfo) return;
    
        try {
            let parsedAssets: Asset[];
            if (file.name.endsWith('.csv')) {
                const text = await file.text();
                parsedAssets = parseAssetsFromCSV(text, companyId);
            } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                const buffer = await file.arrayBuffer();
                const workbook = XLSX.read(buffer, { type: 'buffer' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { blankrows: false });
                parsedAssets = parseAssetsFromExcel(jsonData, companyId);
            } else {
                showAlert("Unsupported File", "Please upload a CSV or Excel (.xlsx, .xls) file.", 'error');
                return;
            }
    
            event.target.value = '';
    
            if (parsedAssets.length === 0) {
                showAlert("Info", "No assets found in the provided file.", 'info');
                return;
            }
            
            const existingLedgers = new Set(allLedgers);
            const statutoryMap = new Map(statutoryRates.map(r => [`${r.assetGroup}|${r.assetSubGroup}`, r]));
            const existingClassifications = new Set(statutoryRates.map(r => `${r.assetGroup}|${r.assetSubGroup}`));
            const newLedgers = new Set<string>();
            const newClassifications = new Map<string, NewClassification>();

            const validatedAssets: ValidatedAsset[] = parsedAssets.map((asset, index) => {
                if (asset.ledgerNameInBooks && !existingLedgers.has(asset.ledgerNameInBooks)) {
                    newLedgers.add(asset.ledgerNameInBooks);
                }
                if (asset.assetGroup && asset.assetSubGroup) {
                    const key = `${asset.assetGroup}|${asset.assetSubGroup}`;
                    if (!existingClassifications.has(key) && !newClassifications.has(key)) {
                        newClassifications.set(key, { group: asset.assetGroup, subGroup: asset.assetSubGroup, life: asset.lifeInYears || 0, scheduleIIIClassification: asset.scheduleIIIClassification || asset.assetSubGroup });
                    }
                }
                const { errors, warnings } = validateAsset(asset, index + 2, compulsoryHeaders, fieldMapping, companyInfo, statutoryRates, statutoryMap);
                return { ...asset, rowNumber: index + 2, errors, warnings };
            });

            setValidatedAssetsForPreview(validatedAssets);
            setNewLedgersForPreview(Array.from(newLedgers));
            setNewClassificationsForPreview(Array.from(newClassifications.values()));
            setIsImportPreviewOpen(true);
    
        } catch (err) {
            showAlert("Import Error", `Error processing file: ${err instanceof Error ? err.message : 'Unknown error'}`, 'error');
        }
    };
    
    const handleAssetChangeInPreview = (rowIndex: number, field: keyof Asset, value: any) => {
        if (!companyInfo) return;
    
        setValidatedAssetsForPreview(prev => {
            const newAssets = [...prev];
            const originalAsset = newAssets[rowIndex];
            const updatedAsset = { ...originalAsset };
            (updatedAsset as any)[field] = value;
    
            if (field === 'basicAmount' || field === 'dutiesTaxes') {
                updatedAsset.grossAmount = (Number(updatedAsset.basicAmount) || 0) + (Number(updatedAsset.dutiesTaxes) || 0);
            }
            
            const statutoryMap = new Map(statutoryRates.map(r => [`${r.assetGroup}|${r.assetSubGroup}`, r]));
            const { errors, warnings } = validateAsset(updatedAsset, updatedAsset.rowNumber, compulsoryHeaders, fieldMapping, companyInfo, statutoryRates, statutoryMap);
            newAssets[rowIndex] = { ...updatedAsset, errors, warnings };
            return newAssets;
        });
    };
    
    const handleConfirmImport = async (assetsToImport: Asset[], newLedgers: string[], newClassifications: NewClassification[]) => {
        if (!companyId) return;
        setIsSaving(true);
        setIsImportPreviewOpen(false);
    
        try {
            // Add new ledgers and classifications first
            if (newClassifications.length > 0) {
                const currentRates = await api.getStatutoryRates(companyId);
                const newRateObjects = newClassifications.map(c => ({
                    isStatutory: 'No',
                    tangibility: 'Tangible', // A reasonable default
                    assetGroup: c.group,
                    assetSubGroup: c.subGroup,
                    extraShiftDepreciation: 'No',
                    usefulLifeYears: String(c.life),
                    scheduleIIClassification: c.scheduleIIIClassification,
                }));
                await api.updateStatutoryRates(companyId, [...currentRates, ...newRateObjects]);
            }
            if (newLedgers.length > 0) {
                const currentExtraLedgers = await api.getExtraLedgers(companyId);
                await api.updateExtraLedgers(companyId, [...currentExtraLedgers, ...newLedgers]);
            }
            
            // Auto-apply statutory life for imported assets
            const statutoryMap = new Map(statutoryRates.map(r => [`${r.assetGroup}|${r.assetSubGroup}`, r]));
            const finalAssets = assetsToImport.map(asset => {
                const statutoryInfo = statutoryMap.get(`${asset.assetGroup}|${asset.assetSubGroup}`);
                if (statutoryInfo && statutoryInfo.isStatutory === 'Yes') {
                    return { ...asset, lifeInYears: parseInt(statutoryInfo.usefulLifeYears, 10) };
                }
                return asset;
            });
            
            await api.importAssets(companyId, finalAssets);
            
            if (loggedInUser) {
                const details = [
                    `Imported ${assetsToImport.length} assets.`,
                    newLedgers.length > 0 ? `Added new ledgers: ${newLedgers.join(', ')}.` : '',
                    newClassifications.length > 0 ? `Added new classifications: ${newClassifications.map(c => c.subGroup).join(', ')}.` : '',
                ].filter(Boolean).join(' ');

                await api.addAuditLog({
                    userId: loggedInUser.id,
                    username: loggedInUser.username,
                    action: 'IMPORT_ASSETS',
                    details,
                });
            }
            
            showAlert("Success", `${assetsToImport.length} assets imported successfully. Reloading all data.`, 'success');
        } catch (err) {
            showAlert("Import Error", `An error occurred during final import: ${err instanceof Error ? err.message : 'Unknown error'}`, 'error');
        } finally {
            setIsSaving(false);
            await fetchData();
        }
    };


    const handleDownloadTemplate = useCallback(() => {
        if (!companyName) {
            showAlert("Error", "Company name is not available for naming the template file.", 'error');
            return;
        }

        const templateHeaders = ["Asset Particulars", "Book Entry Date", "Put to use Date", "Basic Amount", "Duties & Taxes", "Vendor/ Source", "Invoice/ Bill No.", "Model/ Make", "Location", "Asset ID/ Serial No", "Remarks", "Ledger Name in Books", "Asset Group as per Co. Act (SCH II)", "Nature of Asset as per Co. Act  (SCH II)", "Schedule III Classification", "Salvage %age", "WDV of Adoption Date", "Assets under leasehold", "Depreciation method", "Life in years", "Lease Period"];
        
        const markedHeaders = templateHeaders.map(header => 
            compulsoryHeaders.includes(header) ? `${header}*` : header
        );
        
        exportTemplateToExcel({
            headers: markedHeaders,
            companyName: companyName,
            reportName: 'Asset_Import_Template',
            sheetName: 'Asset Import Template'
        });

    }, [compulsoryHeaders, companyName, showAlert]);

    if (loading) return <div className="loading-indicator">Loading Asset Records...</div>;
    if (error) return <div className="error-message">{error}</div>;
    if (!companyId) return null;

    return (
        <>
            <div className="view-header">
                <h2>Asset Records</h2>
                <div className="actions">
                    <button type="button" className="btn btn-primary" onClick={handleAddAsset} disabled={isLocked || isSaving || hasNewRow || loading}><PlusIcon/> Add Asset</button>
                    <input type="file" ref={fileInputRef} onChange={handleFileImport} style={{ display: 'none' }} accept=".csv,.xlsx,.xls" />
                    <button type="button" className="btn btn-info" onClick={() => fileInputRef.current?.click()} disabled={isLocked || isSaving || hasNewRow || loading}><UploadIcon/> Import File</button>
                    <button type="button" className="btn btn-excel" onClick={handleDownloadTemplate} disabled={isLocked || isSaving || hasNewRow || loading}><FileIcon/> Template</button>
                    {!isEditMode && !isLocked && (
                        <button type="button" className="btn btn-success" onClick={() => setIsEditMode(true)} disabled={isSaving || hasNewRow || loading}>
                            <EditIcon/> Edit Mode
                        </button>
                    )}
                    {isEditMode && (
                        <button type="button" className="btn btn-danger" onClick={() => {
                            setIsEditMode(false);
                            if (isDirty) {
                                handleCancelChanges();
                            }
                        }} disabled={isSaving}>
                            <XIcon/> Exit Edit Mode
                        </button>
                    )}
                    {isDirty && <button type="button" className="btn btn-warning" onClick={handleCancelChanges} disabled={isSaving}><XIcon/> Cancel</button>}
                    {isDirty && <button type="button" className="btn btn-primary" onClick={handleSaveChanges} disabled={isSaving}><SaveIcon/> {isSaving ? 'Saving...' : 'Save Changes'}</button>}
                </div>
            </div>

            {isLocked && (
                 <div className="banner-locked">
                    <p><svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{verticalAlign: 'middle', marginRight: '8px'}}><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>
                    Records for {year} are locked. To make changes, an Admin must unlock this year from the 'Administration' panel.</p>
                </div>
            )}

            <div className="filter-container">
                <div className="filter-input-wrapper">
                    <input type="text" placeholder="Filter by ID, Particulars, Location, etc..." className="filter-input" value={filterText} onChange={e => setFilterText(e.target.value)} disabled={isSaving || loading} aria-label="Filter asset records" />
                    <button className={`filter-clear-btn ${!filterText ? 'hidden' : ''}`} onClick={() => setFilterText('')} aria-label="Clear filter" title="Clear filter" disabled={isSaving} >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </div>

                <ColumnManager
                    tableId="assetRecords"
                    columns={columns}
                    onColumnsChange={setColumns}
                    className="column-manager-asset-records"
                />
                 <div className="filter-info">
                    <span className="filter-info-icon">ⓘ</span>
                    <div className="filter-info-tooltip">
                        <strong>Filtering on:</strong>
                        <ul>{searchableFields.map(f => <li key={f}>{reverseFieldMapping[f]}</li>)}</ul>
                    </div>
                </div>
            </div>
            
            <div className="asset-records-table-container">
                <table>
                    <thead>
                        <tr>{headers.map((header, index) => {
                            // Make first 3 columns sticky: Record ID, Asset Particulars, Actions
                            let stickyClass = index < 3 ? `sticky-col sticky-col-${index + 1}` : '';
                            // Add appropriate width classes for sticky columns
                            if (index === 0) stickyClass += ' col-width-sm'; // Record ID - 150px
                            if (index === 1) stickyClass += ' col-width-lg'; // Asset Particulars - 250px
                            if (index === 2) stickyClass += ' col-width-sm'; // Actions - 150px
                            return (<th key={header} className={stickyClass}>{header}</th>);
                        })}</tr>
                    </thead>
                    <tbody>
                        {filteredAssets.map(asset => {
                            const originalAsset = originalAssets.find(a => a.recordId === asset.recordId);
                            const isNewAsset = asset.recordId.startsWith('new_');
                            const isHistorical = companyInfo && new Date(asset.putToUseDate) < new Date(companyInfo.firstDateOfAdoption);

                            // Check if asset has reached end of life
                            let isEndOfLife = false;
                            if (asset.lifeInYears > 0 && asset.putToUseDate) {
                                const putToUseDate = new Date(asset.putToUseDate);
                                const endOfLifeDate = new Date(putToUseDate.getTime());
                                endOfLifeDate.setFullYear(endOfLifeDate.getFullYear() + asset.lifeInYears);
                                const [, endYearStr] = year.split('-');
                                const fyEnd = new Date(`${endYearStr}-03-31T23:59:59.999Z`);
                                isEndOfLife = endOfLifeDate <= fyEnd;
                            }

                            return (
                                <tr
                                    key={asset.recordId}
                                    className={`table-row-selectable ${selectedRowId === asset.recordId ? 'table-row-selected' : ''} ${isDirty && JSON.stringify(originalAsset) !== JSON.stringify(asset) ? 'dirty-row' : ''} ${isNewAsset ? 'new-row' : ''} ${asset.disposalDate ? 'disposed-row' : ''} ${asset.scrapIt ? 'scrapped-row' : ''} ${isEndOfLife && !asset.disposalDate ? 'end-of-life-row' : ''}`}
                                    onClick={() => setSelectedRowId(selectedRowId === asset.recordId ? null : asset.recordId)}
                                >
                                    {headers.map((header, index) => {
                                        const field = fieldMapping[header as keyof typeof fieldMapping] as keyof Asset | 'actions';
                                        // Make first 3 columns sticky: Record ID, Asset Particulars, Actions
                                        let stickyClass = index < 3 ? `sticky-col sticky-col-${index + 1}` : '';
                                        // Add appropriate width classes for sticky columns
                                        if (index === 0) stickyClass += ' col-width-sm'; // Record ID - 150px
                                        if (index === 1) stickyClass += ' col-width-lg'; // Asset Particulars - 250px
                                        if (index === 2) stickyClass += ' col-width-sm'; // Actions - 150px
                                        const cellClass = field === 'recordId' ? `td-record-id ${stickyClass}` : stickyClass;

                                        if (asset.disposalDate) {
                                            if (field === 'actions') {
                                                 return <td key={`${asset.recordId}-${field}`} className={cellClass}><ActionButtons asset={asset} originalAsset={originalAsset} onRevert={handleRevertAsset} onDelete={()=>{}} onDispose={handleOpenDisposalModal} disabled={true} /></td>;
                                            }
                                            return <td key={`${asset.recordId}-${field}`} className={cellClass}><span>{field === 'grossAmount' ? formatIndianNumber(asset[field] as number) : String(asset[field] ?? '')}</span></td>;
                                        }

                                        let inputElement: React.ReactNode;

                                        switch(field) {
                                            case 'actions':
                                                inputElement = <ActionButtons asset={asset} originalAsset={originalAsset} onRevert={handleRevertAsset} onDelete={()=>{}} onDispose={handleOpenDisposalModal} disabled={!isEditMode || isLocked || isSaving} />
                                                break;
                                            case 'recordId':
                                                inputElement = <Highlight text={asset.recordId.startsWith('new_') ? '(New)' : asset.recordId} highlight={filterText} />;
                                                break;
                                            case 'assetParticulars': case 'vendor': case 'invoiceNo': case 'modelMake': case 'location': case 'assetId': case 'remarks':
                                                inputElement = <input type="text" list={`${field}-list`} className="table-input" value={asset[field] as any ?? ''} onChange={e => handleAssetChange(asset.recordId, field, e.target.value)} disabled={!isEditMode || isLocked || isSaving} />;
                                                break;
                                            case 'bookEntryDate': case 'putToUseDate':
                                                inputElement = <input type="date" className="table-input" value={asset[field] ? String(asset[field]).split('T')[0] : ''} onChange={e => handleAssetChange(asset.recordId, field, e.target.value || null)} onBlur={(e) => handleDateBlur(e, asset.recordId, field)} disabled={!isEditMode || isLocked || isSaving} />;
                                                break;
                                            case 'basicAmount': case 'dutiesTaxes': case 'leasePeriod':
                                                inputElement = <input type="number" className="table-input" value={asset[field] as any ?? ''} onChange={e => handleAssetChange(asset.recordId, field, e.target.value === '' ? null : parseInt(e.target.value, 10))} disabled={!isEditMode || isLocked || isSaving || (field === 'leasePeriod' && !asset.isLeasehold)} />;
                                                break;
                                            case 'wdvOfAdoptionDate':
                                                inputElement = <input type="number" className="table-input" value={asset[field] as any ?? ''} onChange={e => handleAssetChange(asset.recordId, field, e.target.value === '' ? null : parseInt(e.target.value, 10))} disabled={!isEditMode || isLocked || isSaving || !isHistorical} />;
                                                break;
                                            case 'salvagePercentage': case 'lifeInYears':
                                                inputElement = <input type="number" step="0.01" className="table-input" value={asset[field] as any ?? ''} onChange={e => handleAssetChange(asset.recordId, field, e.target.value === '' ? null : parseFloat(e.target.value))} disabled={!isEditMode || (field === 'lifeInYears' && statutoryInfoMap.get(`${asset.assetGroup}|${asset.assetSubGroup}`)?.isStatutory) || isLocked || isSaving} />;
                                                break;
                                            case 'grossAmount':
                                                inputElement = <span>{formatIndianNumber(asset[field] as number)}</span>;
                                                break;
                                            case 'ledgerNameInBooks':
                                                inputElement = <input type="text" list="ledger-names" className="table-input" value={asset[field] as any ?? ''} onChange={e => handleAssetChange(asset.recordId, field, e.target.value)} disabled={!isEditMode || isLocked || isSaving} />;
                                                break;
                                            case 'assetGroup':
                                                inputElement = <select className="table-select" value={asset[field] as string} onChange={e => handleAssetChange(asset.recordId, field, e.target.value)} disabled={!isEditMode || isLocked || isSaving}><option value="">Select...</option>{dropdownOptions.assetGroup.map(o => <option key={o} value={o}>{o}</option>)}</select>;
                                                break;
                                            case 'assetSubGroup':
                                                inputElement = <select className="table-select" value={asset[field] as string} onChange={e => handleAssetChange(asset.recordId, field, e.target.value)} disabled={!isEditMode || isLocked || isSaving || !asset.assetGroup}><option value="">Select...</option>{(assetSubGroupOptionsByGroup.get(asset.assetGroup) || []).map(o => <option key={o} value={o}>{o}</option>)}</select>;
                                                break;
                                            case 'scheduleIIIClassification':
                                                inputElement = <Highlight text={asset.scheduleIIIClassification} highlight={filterText} />;
                                                break;
                                            case 'depreciationMethod':
                                                inputElement = <select className="table-select" value={asset[field] as string} onChange={e => handleAssetChange(asset.recordId, field, e.target.value)} disabled={!isEditMode || isLocked || isSaving}><option value="WDV">WDV</option><option value="SLM">SLM</option></select>;
                                                break;
                                            case 'isLeasehold':
                                                inputElement = <input type="checkbox" className="table-checkbox" checked={!!asset[field]} onChange={e => handleAssetChange(asset.recordId, field, e.target.checked)} disabled={!isEditMode || isLocked || isSaving} />;
                                                break;
                                            default:
                                                inputElement = <span>{String(asset[field] ?? 'N/A')}</span>
                                        }

                                        return <td key={`${asset.recordId}-${field}`} className={cellClass}>{inputElement}</td>
                                    })}
                                </tr>
                            )
                        })}
                    </tbody>
                </table>
                {filteredAssets.length === 0 && (
                    <div className="company-info-container"><p>No assets match the current filter criteria.</p></div>
                )}
            </div>

            <datalist id="ledger-names">
                {allLedgers.map(l => <option key={l} value={l} />)}
            </datalist>

            <AssetImportPreviewModal
                isOpen={isImportPreviewOpen}
                onClose={() => setIsImportPreviewOpen(false)}
                assetsToPreview={validatedAssetsForPreview}
                onAssetChange={handleAssetChangeInPreview}
                onConfirmImport={handleConfirmImport}
                newLedgers={newLedgersForPreview}
                newClassifications={newClassificationsForPreview}
                fieldMapping={fieldMapping}
                allHeaders={headers}
                companyName={companyName}
                year={year}
                showAlert={showAlert}
                showConfirmation={showConfirmation}
            />
            <DisposeAssetModal
                isOpen={disposalModalState.isOpen}
                onClose={() => setDisposalModalState({ isOpen: false, asset: null })}
                asset={disposalModalState.asset}
                onSave={handleSaveDisposal}
                year={year}
            />
        </>
    );
};

