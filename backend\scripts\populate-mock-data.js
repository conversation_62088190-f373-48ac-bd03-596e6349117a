import sqlite3 from 'sqlite3';
import bcrypt from 'bcrypt';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database file path
const dbPath = join(__dirname, '../database/far_sighted.db');

console.log('🎯 Populating FAR Sighted Database with Mock Data...');
console.log('📁 Database location:', dbPath);

const populateMockData = async () => {
    const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
            console.error('❌ Error opening database:', err.message);
            process.exit(1);
        }
        console.log('✅ Connected to SQLite database');
    });

    // Helper function to run queries
    const runQuery = (sql, params = []) => {
        return new Promise((resolve, reject) => {
            db.run(sql, params, function(err) {
                if (err) reject(err);
                else resolve({ id: this.lastID, changes: this.changes });
            });
        });
    };

    try {
        console.log('🏢 Creating sample companies...');
        
        // Sample Companies
        const companies = [
            {
                id: 'c1001',
                company_name: 'Tech Innovations Pvt Ltd',
                pan: '**********',
                cin: 'U72200DL2018PTC334567',
                date_of_incorporation: '2018-03-15',
                financial_year_start: '2024-04-01',
                financial_year_end: '2025-03-31',
                first_date_of_adoption: '2018-04-01',
                data_folder_path: 'C:\\FAR_Data\\TechInnovations',
                address_line1: 'A-204, Tech Park',
                address_line2: 'Sector 62, Noida',
                city: 'Noida',
                pin: '201301',
                email: '<EMAIL>',
                mobile: '**********',
                contact_person: 'Rajesh Kumar',
                license_valid_upto: '2025-12-31'
            },
            {
                id: 'c1002', 
                company_name: 'Maharashtra Manufacturing Ltd',
                pan: '**********',
                cin: 'U25200MH2015PLC345678',
                date_of_incorporation: '2015-07-20',
                financial_year_start: '2024-04-01',
                financial_year_end: '2025-03-31',
                first_date_of_adoption: '2015-04-01',
                data_folder_path: 'C:\\FAR_Data\\MaharashtraManufacturing',
                address_line1: 'Plot No. 45, MIDC',
                address_line2: 'Aurangabad Industrial Area',
                city: 'Aurangabad',
                pin: '431001',
                email: '<EMAIL>',
                mobile: '**********',
                contact_person: 'Priya Sharma',
                license_valid_upto: '2025-12-31'
            },
            {
                id: 'c1003',
                company_name: 'Green Energy Solutions Pvt Ltd',
                pan: '**********',
                cin: 'U40300GJ2020PTC356789',
                date_of_incorporation: '2020-01-10',
                financial_year_start: '2024-04-01',
                financial_year_end: '2025-03-31',
                first_date_of_adoption: '2020-04-01',
                data_folder_path: 'C:\\FAR_Data\\GreenEnergy',
                address_line1: '23, Green Tower',
                address_line2: 'S.G. Highway, Ahmedabad',
                city: 'Ahmedabad',
                pin: '380015',
                email: '<EMAIL>',
                mobile: '**********',
                contact_person: 'Amit Patel',
                license_valid_upto: '2025-12-31'
            }
        ];

        for (const company of companies) {
            await runQuery(
                `INSERT OR REPLACE INTO companies (
                    id, company_name, pan, cin, date_of_incorporation,
                    financial_year_start, financial_year_end, first_date_of_adoption,
                    data_folder_path, address_line1, address_line2, city, pin,
                    email, mobile, contact_person, license_valid_upto
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    company.id, company.company_name, company.pan, company.cin,
                    company.date_of_incorporation, company.financial_year_start,
                    company.financial_year_end, company.first_date_of_adoption,
                    company.data_folder_path, company.address_line1, company.address_line2,
                    company.city, company.pin, company.email, company.mobile,
                    company.contact_person, company.license_valid_upto
                ]
            );
        }

        console.log('👥 Creating sample users...');
        
        // Sample Users
        const users = [
            {
                id: 'u1001',
                username: 'ca_admin',
                password: 'admin123',
                role: 'Admin'
            },
            {
                id: 'u1002',
                username: 'data_entry1',
                password: 'entry123',
                role: 'Data Entry'
            },
            {
                id: 'u1003',
                username: 'report_viewer1',
                password: 'view123',
                role: 'Report Viewer'
            },
            {
                id: 'u1004',
                username: 'senior_ca',
                password: 'senior123',
                role: 'Admin'
            }
        ];

        for (const user of users) {
            const passwordHash = await bcrypt.hash(user.password, 10);
            const recoveryKey = `rk_${Math.random().toString(36).substring(2, 15)}`;
            const recoveryKeyHash = await bcrypt.hash(recoveryKey, 10);
            
            await runQuery(
                `INSERT OR REPLACE INTO users (id, username, password_hash, role, recovery_key_hash, has_saved_recovery_key)
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [user.id, user.username, passwordHash, user.role, recoveryKeyHash, true]
            );
        }

        console.log('📅 Creating financial years...');
        
        // Financial Years for each company
        const financialYears = [
            { company_id: 'c1001', year_range: '2022-2023', is_locked: true },
            { company_id: 'c1001', year_range: '2023-2024', is_locked: true },
            { company_id: 'c1001', year_range: '2024-2025', is_locked: false },
            { company_id: 'c1002', year_range: '2022-2023', is_locked: true },
            { company_id: 'c1002', year_range: '2023-2024', is_locked: true },
            { company_id: 'c1002', year_range: '2024-2025', is_locked: false },
            { company_id: 'c1003', year_range: '2023-2024', is_locked: true },
            { company_id: 'c1003', year_range: '2024-2025', is_locked: false }
        ];

        for (const fy of financialYears) {
            await runQuery(
                'INSERT OR REPLACE INTO financial_years (company_id, year_range, is_locked) VALUES (?, ?, ?)',
                [fy.company_id, fy.year_range, fy.is_locked]
            );
        }

        console.log('📊 Creating statutory rates...');
        
        // Sample Statutory Rates
        const statutoryRates = [
            {
                company_id: 'c1001',
                is_statutory: 'Yes',
                tangibility: 'Tangible',
                asset_group: 'Plant & Machinery',
                asset_sub_group: 'General Plant & Machinery',
                extra_shift_depreciation: '25%',
                useful_life_years: '15',
                schedule_ii_classification: 'Plant & Machinery - General'
            },
            {
                company_id: 'c1001',
                is_statutory: 'Yes',
                tangibility: 'Tangible',
                asset_group: 'Computer & Data Processing',
                asset_sub_group: 'Computers',
                extra_shift_depreciation: 'NA',
                useful_life_years: '3',
                schedule_ii_classification: 'Computer including computer software'
            },
            {
                company_id: 'c1001',
                is_statutory: 'Yes',
                tangibility: 'Tangible',
                asset_group: 'Office Equipment',
                asset_sub_group: 'Furniture & Fixtures',
                extra_shift_depreciation: 'NA',
                useful_life_years: '10',
                schedule_ii_classification: 'Furniture and fittings'
            }
        ];

        for (const rate of statutoryRates) {
            await runQuery(
                `INSERT OR REPLACE INTO statutory_rates (
                    company_id, is_statutory, tangibility, asset_group, asset_sub_group,
                    extra_shift_depreciation, useful_life_years, schedule_ii_classification
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    rate.company_id, rate.is_statutory, rate.tangibility, rate.asset_group,
                    rate.asset_sub_group, rate.extra_shift_depreciation, rate.useful_life_years,
                    rate.schedule_ii_classification
                ]
            );
        }

        console.log('🏭 Creating sample assets...');        
        // Sample Assets for Tech Innovations Pvt Ltd
        const assets = [
            {
                company_id: 'c1001',
                record_id: 'P0001',
                asset_particulars: 'CNC Machining Center - VMC 850',
                book_entry_date: '2022-05-15',
                put_to_use_date: '2022-06-01',
                basic_amount: 2500000,
                duties_taxes: 450000,
                gross_amount: 2950000,
                vendor: 'Precision Machines Ltd',
                invoice_no: 'PM/2022/001',
                model_make: 'Haas VF-4SS',
                location: 'Production Floor A',
                asset_id: 'PM001',
                remarks: 'High precision machining center',
                ledger_name_in_books: 'Plant & Machinery',
                asset_group: 'Plant & Machinery',
                asset_sub_group: 'General Plant & Machinery',
                schedule_iii_classification: 'Plant & Machinery - General',
                salvage_percentage: 5,
                wdv_of_adoption_date: 2950000,
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 15,
                scrap_it: false
            },
            {
                company_id: 'c1001',
                record_id: 'C0001',
                asset_particulars: 'Dell Workstation - Precision 7760',
                book_entry_date: '2023-01-10',
                put_to_use_date: '2023-01-15',
                basic_amount: 150000,
                duties_taxes: 27000,
                gross_amount: 177000,
                vendor: 'Dell Technologies India',
                invoice_no: 'DELL/2023/0045',
                model_make: 'Dell Precision 7760',
                location: 'Design Department',
                asset_id: 'COMP001',
                remarks: 'CAD workstation for design team',
                ledger_name_in_books: 'Computer Equipment',
                asset_group: 'Computer & Data Processing',
                asset_sub_group: 'Computers',
                schedule_iii_classification: 'Computer including computer software',
                salvage_percentage: 5,
                wdv_of_adoption_date: 177000,
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 3,
                scrap_it: false
            },
            {
                company_id: 'c1001',
                record_id: 'F0001',
                asset_particulars: 'Conference Table with Chairs Set',
                book_entry_date: '2022-08-20',
                put_to_use_date: '2022-08-25',
                basic_amount: 85000,
                duties_taxes: 15300,
                gross_amount: 100300,
                vendor: 'Office Furniture Solutions',
                invoice_no: 'OFS/2022/078',
                model_make: 'Executive Series Conference Set',
                location: 'Conference Room 1',
                asset_id: 'FURN001',
                remarks: '12-seater conference table with ergonomic chairs',
                ledger_name_in_books: 'Furniture & Fixtures',
                asset_group: 'Office Equipment',
                asset_sub_group: 'Furniture & Fixtures',
                schedule_iii_classification: 'Furniture and fittings',
                salvage_percentage: 5,
                wdv_of_adoption_date: 100300,
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 10,
                scrap_it: false
            },
            // Maharashtra Manufacturing Ltd Assets
            {
                company_id: 'c1002',
                record_id: 'M0001',
                asset_particulars: 'Industrial Press Machine - 100 Ton',
                book_entry_date: '2021-03-10',
                put_to_use_date: '2021-04-01',
                basic_amount: 1800000,
                duties_taxes: 324000,
                gross_amount: 2124000,
                vendor: 'Heavy Machinery Corporation',
                invoice_no: 'HMC/2021/156',
                model_make: 'Hydraulic Press HP-100',
                location: 'Manufacturing Unit 1',
                asset_id: 'MACH001',
                remarks: '100 ton capacity hydraulic press',
                ledger_name_in_books: 'Manufacturing Equipment',
                asset_group: 'Plant & Machinery',
                asset_sub_group: 'General Plant & Machinery',
                schedule_iii_classification: 'Plant & Machinery - General',
                salvage_percentage: 5,
                wdv_of_adoption_date: 2124000,
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 20,
                scrap_it: false
            },
            {
                company_id: 'c1002',
                record_id: 'V0001',
                asset_particulars: 'Tata LPT 1613 - Goods Vehicle',
                book_entry_date: '2023-02-15',
                put_to_use_date: '2023-03-01',
                basic_amount: 1250000,
                duties_taxes: 125000,
                gross_amount: 1375000,
                vendor: 'Tata Motors Ltd',
                invoice_no: 'TATA/2023/2156',
                model_make: 'Tata LPT 1613',
                location: 'Transport Division',
                asset_id: 'VEH001',
                remarks: 'Goods transportation vehicle',
                ledger_name_in_books: 'Motor Vehicles',
                asset_group: 'Motor Vehicles',
                asset_sub_group: 'Goods Vehicle',
                schedule_iii_classification: 'Motor cars other than those used in a business of running them on hire',
                salvage_percentage: 5,
                wdv_of_adoption_date: 1375000,
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 8,
                scrap_it: false
            },
            // Green Energy Solutions Assets
            {
                company_id: 'c1003',
                record_id: 'S0001',
                asset_particulars: 'Solar Panel Array - 50KW System',
                book_entry_date: '2023-09-10',
                put_to_use_date: '2023-10-01',
                basic_amount: 3500000,
                duties_taxes: 630000,
                gross_amount: 4130000,
                vendor: 'Solar Power Systems Ltd',
                invoice_no: 'SPS/2023/089',
                model_make: 'Monocrystalline 540W Panels',
                location: 'Rooftop Installation',
                asset_id: 'SOL001',
                remarks: '50KW solar power generation system',
                ledger_name_in_books: 'Solar Equipment',
                asset_group: 'Plant & Machinery',
                asset_sub_group: 'Power Generation Equipment',
                schedule_iii_classification: 'Plant & Machinery - General',
                salvage_percentage: 10,
                wdv_of_adoption_date: 4130000,
                is_leasehold: false,
                depreciation_method: 'WDV',
                life_in_years: 25,
                scrap_it: false
            }
        ];

        for (const asset of assets) {
            await runQuery(
                `INSERT OR REPLACE INTO assets (
                    company_id, record_id, asset_particulars, book_entry_date, put_to_use_date,
                    basic_amount, duties_taxes, gross_amount, vendor, invoice_no, model_make,
                    location, asset_id, remarks, ledger_name_in_books, asset_group,
                    asset_sub_group, schedule_iii_classification, salvage_percentage,
                    wdv_of_adoption_date, is_leasehold, depreciation_method, life_in_years, scrap_it
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    asset.company_id, asset.record_id, asset.asset_particulars, asset.book_entry_date,
                    asset.put_to_use_date, asset.basic_amount, asset.duties_taxes, asset.gross_amount,
                    asset.vendor, asset.invoice_no, asset.model_make, asset.location, asset.asset_id,
                    asset.remarks, asset.ledger_name_in_books, asset.asset_group, asset.asset_sub_group,
                    asset.schedule_iii_classification, asset.salvage_percentage, asset.wdv_of_adoption_date,
                    asset.is_leasehold, asset.depreciation_method, asset.life_in_years, asset.scrap_it
                ]
            );
        }

        console.log('📈 Creating yearly depreciation data...');
        
        // Get asset IDs for yearly data
        const assetIds = await new Promise((resolve, reject) => {
            db.all('SELECT id, record_id, company_id FROM assets', (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        // Sample yearly data
        const yearlyDataSamples = [
            { year_range: '2022-2023', use_days: 365, depreciation_rate: 0.095 },
            { year_range: '2023-2024', use_days: 365, depreciation_rate: 0.095 },
            { year_range: '2024-2025', use_days: 275, depreciation_rate: 0.095 } // Partial year
        ];

        for (const assetRow of assetIds) {
            for (const yearData of yearlyDataSamples) {
                // Skip if asset was created after the year
                if (assetRow.record_id.includes('2023') && yearData.year_range === '2022-2023') continue;
                
                const openingWdv = assetRow.record_id === 'P0001' ? 
                    (yearData.year_range === '2022-2023' ? 2950000 : 
                     yearData.year_range === '2023-2024' ? 2669750 : 2416363) : 
                    100000; // Simplified calculation
                
                const depreciationAmount = openingWdv * yearData.depreciation_rate;
                const closingWdv = openingWdv - depreciationAmount;

                await runQuery(
                    `INSERT OR REPLACE INTO asset_yearly_data (
                        asset_id, year_range, opening_wdv, use_days, depreciation_amount, 
                        closing_wdv, second_shift_days, third_shift_days
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        assetRow.id, yearData.year_range, openingWdv, yearData.use_days,
                        depreciationAmount, closingWdv, 0, 0
                    ]
                );
            }
        }

        console.log('📚 Creating extra ledgers...');
        
        // Extra Ledgers
        const extraLedgers = [
            { company_id: 'c1001', ledger_name: 'Research Equipment' },
            { company_id: 'c1001', ledger_name: 'Testing Instruments' },
            { company_id: 'c1002', ledger_name: 'Safety Equipment' },
            { company_id: 'c1002', ledger_name: 'Quality Control Instruments' },
            { company_id: 'c1003', ledger_name: 'Environmental Monitoring Equipment' }
        ];

        for (const ledger of extraLedgers) {
            await runQuery(
                'INSERT OR REPLACE INTO extra_ledgers (company_id, ledger_name) VALUES (?, ?)',
                [ledger.company_id, ledger.ledger_name]
            );
        }

        console.log('🔍 Creating audit logs...');
        
        // Sample Audit Logs
        const auditLogs = [
            {
                id: 'audit_001',
                timestamp: '2024-07-01T10:30:00Z',
                user_id: 'u1001',
                username: 'ca_admin',
                action: 'LOGIN',
                details: 'User logged in successfully'
            },
            {
                id: 'audit_002',
                timestamp: '2024-07-01T10:45:00Z',
                user_id: 'u1001',
                username: 'ca_admin',
                action: 'CREATE_COMPANY',
                details: 'Created new company: Tech Innovations Pvt Ltd'
            },
            {
                id: 'audit_003',
                timestamp: '2024-07-01T11:15:00Z',
                user_id: 'u1002',
                username: 'data_entry1',
                action: 'ADD_ASSET',
                details: 'Added new asset: CNC Machining Center - VMC 850 (Record ID: P0001)'
            },
            {
                id: 'audit_004',
                timestamp: '2024-07-02T09:20:00Z',
                user_id: 'u1003',
                username: 'report_viewer1',
                action: 'GENERATE_REPORT',
                details: 'Generated Asset Group Report for Tech Innovations Pvt Ltd'
            },
            {
                id: 'audit_005',
                timestamp: '2024-07-02T14:30:00Z',
                user_id: 'u1001',
                username: 'ca_admin',
                action: 'UPDATE_ASSET',
                details: 'Updated asset depreciation method for Record ID: C0001'
            }
        ];

        for (const log of auditLogs) {
            await runQuery(
                `INSERT OR REPLACE INTO audit_logs (id, timestamp, user_id, username, action, details)
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [log.id, log.timestamp, log.user_id, log.username, log.action, log.details]
            );
        }

        console.log('💾 Creating backup logs...');
        
        // Sample Backup Logs
        const backupLogs = [
            {
                id: 'backup_001',
                timestamp: '2024-07-01T23:00:00Z',
                action: 'Backup',
                initiated_by: 'System Auto Backup',
                details: 'Scheduled backup completed successfully. File: backup_20240701.db'
            },
            {
                id: 'backup_002',
                timestamp: '2024-07-02T15:30:00Z',
                action: 'Backup',
                initiated_by: 'ca_admin',
                details: 'Manual backup created before major data import. File: manual_backup_20240702.db'
            },
            {
                id: 'backup_003',
                timestamp: '2024-07-03T09:45:00Z',
                action: 'Restore',
                initiated_by: 'ca_admin',
                details: 'Database restored from backup_20240701.db due to data corruption'
            }
        ];

        for (const log of backupLogs) {
            await runQuery(
                `INSERT OR REPLACE INTO backup_logs (id, timestamp, action, initiated_by, details)
                 VALUES (?, ?, ?, ?, ?)`,
                [log.id, log.timestamp, log.action, log.initiated_by, log.details]
            );
        }

        console.log('🏷️ Creating license history...');
        
        // License History
        const licenseHistory = [
            {
                id: 'lic_001',
                company_id: 'c1001',
                license_key: 'FAR-2024-TECH-001-ABCD',
                valid_from: '2024-01-01',
                valid_upto: '2024-12-31',
                activated_at: '2024-01-15T10:00:00Z'
            },
            {
                id: 'lic_002',
                company_id: 'c1002',
                license_key: 'FAR-2024-MFG-002-EFGH',
                valid_from: '2024-01-01',
                valid_upto: '2024-12-31',
                activated_at: '2024-02-01T14:30:00Z'
            },
            {
                id: 'lic_003',
                company_id: 'c1003',
                license_key: 'FAR-2024-GREEN-003-IJKL',
                valid_from: '2024-01-01',
                valid_upto: '2024-12-31',
                activated_at: '2024-03-10T09:15:00Z'
            }
        ];

        for (const license of licenseHistory) {
            await runQuery(
                `INSERT OR REPLACE INTO license_history (id, company_id, license_key, valid_from, valid_upto, activated_at)
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [license.id, license.company_id, license.license_key, license.valid_from, license.valid_upto, license.activated_at]
            );
        }

        console.log('✅ Mock data population completed successfully!');
        console.log('');
        console.log('📊 Summary of Mock Data Created:');
        console.log('   🏢 Companies: 3 (Tech Innovations, Maharashtra Manufacturing, Green Energy)');
        console.log('   👥 Users: 4 (ca_admin, data_entry1, report_viewer1, senior_ca)');
        console.log('   📅 Financial Years: 8 across all companies');
        console.log('   🏭 Assets: 6 with complete depreciation data');
        console.log('   📈 Yearly Data: Multiple years of depreciation calculations');
        console.log('   📚 Extra Ledgers: 5 additional ledger accounts');
        console.log('   🔍 Audit Logs: 5 sample audit trail entries');
        console.log('   💾 Backup Logs: 3 backup/restore history entries');
        console.log('   🏷️ License History: 3 license activation records');
        console.log('');
        console.log('🎯 Test Login Credentials:');
        console.log('   Admin: ca_admin / admin123');
        console.log('   Data Entry: data_entry1 / entry123');
        console.log('   Report Viewer: report_viewer1 / view123');
        console.log('   Senior Admin: senior_ca / senior123');
        console.log('');
        console.log('🚀 Database is now ready for comprehensive testing!');

    } catch (error) {
        console.error('❌ Error populating mock data:', error);
    } finally {
        // Close database connection
        db.close((err) => {
            if (err) {
                console.error('❌ Error closing database:', err.message);
            } else {
                console.log('🔌 Database connection closed');
            }
        });
    }
};

populateMockData();