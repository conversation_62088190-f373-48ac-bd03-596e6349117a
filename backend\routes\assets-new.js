import express from 'express';
import dbService from '../services/database-new.js';

const router = express.Router();

// Middleware to ensure company context is set
const requireCompanyContext = async (req, res, next) => {
    const companyId = req.params.companyId || req.body.companyId || req.query.companyId;
    
    if (!companyId) {
        return res.status(400).json({ 
            error: 'Company ID required',
            message: 'Company context must be specified for asset operations'
        });
    }

    try {
        await dbService.setCompanyContext(companyId);
        req.companyId = companyId;
        next();
    } catch (error) {
        console.error('Error setting company context:', error);
        res.status(404).json({ 
            error: 'Company not found',
            message: `Cannot set context for company: ${companyId}`
        });
    }
};

// Get all assets for a company
router.get('/company/:companyId', requireCompanyContext, async (req, res) => {
    try {
        const assets = await dbService.all(`
            SELECT 
                id as assetDbId,
                record_id as recordId,
                asset_particulars as assetParticulars,
                book_entry_date as bookEntryDate,
                put_to_use_date as putToUseDate,
                basic_amount as basicAmount,
                duties_taxes as dutiesTaxes,
                gross_amount as grossAmount,
                vendor,
                invoice_no as invoiceNo,
                model_make as modelMake,
                location,
                asset_id as assetId,
                remarks,
                ledger_name_in_books as ledgerNameInBooks,
                asset_group as assetGroup,
                asset_sub_group as assetSubGroup,
                schedule_iii_classification as scheduleIIIClassification,
                disposal_date as disposalDate,
                disposal_amount as disposalAmount,
                salvage_percentage as salvagePercentage,
                wdv_of_adoption_date as wdvOfAdoptionDate,
                is_leasehold as isLeasehold,
                depreciation_method as depreciationMethod,
                life_in_years as lifeInYears,
                lease_period as leasePeriod,
                scrap_it as scrapIt,
                created_at as createdAt,
                updated_at as updatedAt
            FROM assets 
            ORDER BY record_id
        `);

        // Convert TEXT boolean fields back to actual booleans for frontend
        const convertedAssets = assets.map(asset => ({
            ...asset,
            isLeasehold: asset.isLeasehold === 'Yes',
            scrapIt: asset.scrapIt === 'Yes'
        }));

        res.json(convertedAssets);
    } catch (error) {
        console.error('Error fetching company assets:', error);
        res.status(500).json({ error: 'Failed to fetch assets' });
    }
});

// Update assets for a company (bulk update)
router.put('/company/:companyId', requireCompanyContext, async (req, res) => {
    try {
        const { assets } = req.body;
        
        if (!Array.isArray(assets)) {
            return res.status(400).json({ error: 'Assets must be an array' });
        }

        await dbService.beginTransaction();
        
        try {
            // Get existing assets for comparison (for audit trail)
            const existingAssets = await dbService.all('SELECT * FROM assets');
            const existingAssetsMap = new Map(existingAssets.map(asset => [asset.record_id, asset]));
            
            // Clear existing assets
            await dbService.run('DELETE FROM assets');
            
            // Insert updated assets
            for (const asset of assets) {
                const existingAsset = existingAssetsMap.get(asset.recordId);
                
                await dbService.run(
                    `INSERT INTO assets (
                        record_id, asset_particulars, book_entry_date, put_to_use_date,
                        basic_amount, duties_taxes, gross_amount, vendor, invoice_no,
                        model_make, location, asset_id, remarks, ledger_name_in_books,
                        asset_group, asset_sub_group, schedule_iii_classification,
                        disposal_date, disposal_amount, salvage_percentage, wdv_of_adoption_date,
                        is_leasehold, depreciation_method, life_in_years, lease_period, scrap_it,
                        created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        asset.recordId, asset.assetParticulars, asset.bookEntryDate, asset.putToUseDate,
                        asset.basicAmount, asset.dutiesTaxes || 0, asset.grossAmount, asset.vendor, asset.invoiceNo,
                        asset.modelMake, asset.location, asset.assetId, asset.remarks, asset.ledgerNameInBooks,
                        asset.assetGroup, asset.assetSubGroup, asset.scheduleIIIClassification,
                        asset.disposalDate, asset.disposalAmount, asset.salvagePercentage || 5.0, asset.wdvOfAdoptionDate,
                        asset.isLeasehold ? 'Yes' : 'No', asset.depreciationMethod || 'WDV', asset.lifeInYears, asset.leasePeriod, asset.scrapIt ? 'Yes' : 'No',
                        existingAsset?.created_at || new Date().toISOString(),
                        new Date().toISOString()
                    ]
                );

                // Add audit log for each asset change
                if (existingAsset) {
                    await dbService.addAuditLog({
                        userId: req.user?.id || 'system',
                        username: req.user?.username || 'system',
                        action: 'UPDATE_ASSET',
                        details: `Updated asset: ${asset.assetParticulars}`,
                        tableName: 'assets',
                        recordId: asset.recordId,
                        oldValues: JSON.stringify(existingAsset),
                        newValues: JSON.stringify(asset)
                    });
                } else {
                    await dbService.addAuditLog({
                        userId: req.user?.id || 'system',
                        username: req.user?.username || 'system',
                        action: 'CREATE_ASSET',
                        details: `Created new asset: ${asset.assetParticulars}`,
                        tableName: 'assets',
                        recordId: asset.recordId,
                        newValues: JSON.stringify(asset)
                    });
                }
            }
            
            await dbService.commit();
            
            // Add summary audit log
            await dbService.addAuditLog({
                userId: req.user?.id || 'system',
                username: req.user?.username || 'system',
                action: 'BULK_UPDATE_ASSETS',
                details: `Bulk updated ${assets.length} assets`,
                tableName: 'assets'
            });
            
            res.json({ 
                message: 'Assets updated successfully',
                assetsUpdated: assets.length
            });
            
        } catch (error) {
            await dbService.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error updating assets:', error);
        res.status(500).json({ error: 'Failed to update assets' });
    }
});

// Create single asset
router.post('/company/:companyId/asset', requireCompanyContext, async (req, res) => {
    try {
        const asset = req.body;
        
        // Validate required fields
        if (!asset.recordId || !asset.assetParticulars || !asset.bookEntryDate || 
            !asset.putToUseDate || !asset.basicAmount || !asset.assetGroup || !asset.assetSubGroup) {
            return res.status(400).json({ 
                error: 'Missing required fields',
                required: ['recordId', 'assetParticulars', 'bookEntryDate', 'putToUseDate', 'basicAmount', 'assetGroup', 'assetSubGroup']
            });
        }

        // Check if record ID already exists
        const existingAsset = await dbService.get(
            'SELECT id FROM assets WHERE record_id = ?',
            [asset.recordId]
        );

        if (existingAsset) {
            return res.status(409).json({ error: 'Asset with this Record ID already exists' });
        }

        const result = await dbService.run(
            `INSERT INTO assets (
                record_id, asset_particulars, book_entry_date, put_to_use_date,
                basic_amount, duties_taxes, gross_amount, vendor, invoice_no,
                model_make, location, asset_id, remarks, ledger_name_in_books,
                asset_group, asset_sub_group, schedule_iii_classification,
                disposal_date, disposal_amount, salvage_percentage, wdv_of_adoption_date,
                is_leasehold, depreciation_method, life_in_years, lease_period, scrap_it
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                asset.recordId, asset.assetParticulars, asset.bookEntryDate, asset.putToUseDate,
                asset.basicAmount, asset.dutiesTaxes || 0, asset.grossAmount || asset.basicAmount + (asset.dutiesTaxes || 0),
                asset.vendor, asset.invoiceNo, asset.modelMake, asset.location, asset.assetId, asset.remarks,
                asset.ledgerNameInBooks, asset.assetGroup, asset.assetSubGroup, asset.scheduleIIIClassification,
                asset.disposalDate, asset.disposalAmount, asset.salvagePercentage || 5.0, asset.wdvOfAdoptionDate,
                asset.isLeasehold ? 'Yes' : 'No', asset.depreciationMethod || 'WDV', asset.lifeInYears, asset.leasePeriod, asset.scrapIt ? 'Yes' : 'No'
            ]
        );

        // Add audit log
        await dbService.addAuditLog({
            userId: req.user?.id || 'system',
            username: req.user?.username || 'system',
            action: 'CREATE_ASSET',
            details: `Created new asset: ${asset.assetParticulars}`,
            tableName: 'assets',
            recordId: asset.recordId,
            newValues: JSON.stringify(asset)
        });

        res.status(201).json({ 
            message: 'Asset created successfully',
            assetId: result.id,
            recordId: asset.recordId
        });

    } catch (error) {
        console.error('Error creating asset:', error);
        res.status(500).json({ error: 'Failed to create asset' });
    }
});

// Update single asset
router.put('/company/:companyId/asset/:recordId', requireCompanyContext, async (req, res) => {
    try {
        const { recordId } = req.params;
        const asset = req.body;

        // Get existing asset for audit trail
        const existingAsset = await dbService.get(
            'SELECT * FROM assets WHERE record_id = ?',
            [recordId]
        );

        if (!existingAsset) {
            return res.status(404).json({ error: 'Asset not found' });
        }

        await dbService.run(
            `UPDATE assets SET 
                asset_particulars = ?, book_entry_date = ?, put_to_use_date = ?,
                basic_amount = ?, duties_taxes = ?, gross_amount = ?, vendor = ?, invoice_no = ?,
                model_make = ?, location = ?, asset_id = ?, remarks = ?, ledger_name_in_books = ?,
                asset_group = ?, asset_sub_group = ?, schedule_iii_classification = ?,
                disposal_date = ?, disposal_amount = ?, salvage_percentage = ?, wdv_of_adoption_date = ?,
                is_leasehold = ?, depreciation_method = ?, life_in_years = ?, lease_period = ?, scrap_it = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE record_id = ?`,
            [
                asset.assetParticulars, asset.bookEntryDate, asset.putToUseDate,
                asset.basicAmount, asset.dutiesTaxes || 0, asset.grossAmount, asset.vendor, asset.invoiceNo,
                asset.modelMake, asset.location, asset.assetId, asset.remarks, asset.ledgerNameInBooks,
                asset.assetGroup, asset.assetSubGroup, asset.scheduleIIIClassification,
                asset.disposalDate, asset.disposalAmount, asset.salvagePercentage || 5.0, asset.wdvOfAdoptionDate,
                asset.isLeasehold ? 'Yes' : 'No', asset.depreciationMethod || 'WDV', asset.lifeInYears, asset.leasePeriod, asset.scrapIt ? 'Yes' : 'No',
                recordId
            ]
        );

        // Add audit log
        await dbService.addAuditLog({
            userId: req.user?.id || 'system',
            username: req.user?.username || 'system',
            action: 'UPDATE_ASSET',
            details: `Updated asset: ${asset.assetParticulars}`,
            tableName: 'assets',
            recordId: recordId,
            oldValues: JSON.stringify(existingAsset),
            newValues: JSON.stringify(asset)
        });

        res.json({ message: 'Asset updated successfully' });

    } catch (error) {
        console.error('Error updating asset:', error);
        res.status(500).json({ error: 'Failed to update asset' });
    }
});

// Delete asset
router.delete('/company/:companyId/asset/:recordId', requireCompanyContext, async (req, res) => {
    try {
        const { recordId } = req.params;

        // Get existing asset for audit trail
        const existingAsset = await dbService.get(
            'SELECT * FROM assets WHERE record_id = ?',
            [recordId]
        );

        if (!existingAsset) {
            return res.status(404).json({ error: 'Asset not found' });
        }

        const result = await dbService.run(
            'DELETE FROM assets WHERE record_id = ?',
            [recordId]
        );

        if (result.changes === 0) {
            return res.status(404).json({ error: 'Asset not found' });
        }

        // Add audit log
        await dbService.addAuditLog({
            userId: req.user?.id || 'system',
            username: req.user?.username || 'system',
            action: 'DELETE_ASSET',
            details: `Deleted asset: ${existingAsset.asset_particulars}`,
            tableName: 'assets',
            recordId: recordId,
            oldValues: JSON.stringify(existingAsset)
        });

        res.json({ message: 'Asset deleted successfully' });

    } catch (error) {
        console.error('Error deleting asset:', error);
        res.status(500).json({ error: 'Failed to delete asset' });
    }
});

// Get asset yearly data for a company
router.get('/company/:companyId/yearly-data/:yearRange?', requireCompanyContext, async (req, res) => {
    try {
        const { yearRange } = req.params;
        
        let query = `
            SELECT 
                ayd.*,
                a.record_id,
                a.asset_particulars,
                a.asset_group,
                a.asset_sub_group
            FROM asset_yearly_data ayd
            JOIN assets a ON ayd.asset_id = a.id
        `;
        
        let params = [];
        
        if (yearRange) {
            query += ' WHERE ayd.year_range = ?';
            params.push(yearRange);
        }
        
        query += ' ORDER BY a.record_id, ayd.year_range';
        
        const yearlyData = await dbService.all(query, params);
        
        res.json(yearlyData);
    } catch (error) {
        console.error('Error fetching yearly data:', error);
        res.status(500).json({ error: 'Failed to fetch yearly data' });
    }
});

// Update extra shift days for assets
router.put('/company/:companyId/extra-shift-days/:yearRange', requireCompanyContext, async (req, res) => {
    try {
        const { yearRange } = req.params;
        const { shiftData } = req.body;
        
        if (!Array.isArray(shiftData)) {
            return res.status(400).json({ error: 'Shift data must be an array' });
        }

        await dbService.beginTransaction();
        
        try {
            for (const data of shiftData) {
                // Update or insert extra shift days
                const existing = await dbService.get(
                    'SELECT id FROM asset_yearly_data WHERE asset_id = ? AND year_range = ?',
                    [data.assetId, yearRange]
                );

                if (existing) {
                    await dbService.run(
                        `UPDATE asset_yearly_data SET 
                            extra_shift_days_2nd = ?, 
                            extra_shift_days_3rd = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE asset_id = ? AND year_range = ?`,
                        [data.extraShiftDays2nd || 0, data.extraShiftDays3rd || 0, data.assetId, yearRange]
                    );
                } else {
                    await dbService.run(
                        `INSERT INTO asset_yearly_data (asset_id, year_range, extra_shift_days_2nd, extra_shift_days_3rd)
                         VALUES (?, ?, ?, ?)`,
                        [data.assetId, yearRange, data.extraShiftDays2nd || 0, data.extraShiftDays3rd || 0]
                    );
                }
            }
            
            await dbService.commit();
            
            // Add audit log
            await dbService.addAuditLog({
                userId: req.user?.id || 'system',
                username: req.user?.username || 'system',
                action: 'UPDATE_EXTRA_SHIFT_DAYS',
                details: `Updated extra shift days for ${shiftData.length} assets in ${yearRange}`,
                tableName: 'asset_yearly_data',
                recordId: yearRange
            });
            
            res.json({ message: 'Extra shift days updated successfully' });
            
        } catch (error) {
            await dbService.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error updating extra shift days:', error);
        res.status(500).json({ error: 'Failed to update extra shift days' });
    }
});

// Calculate depreciation for a financial year
router.post('/company/:companyId/calculate-depreciation/:yearRange', requireCompanyContext, async (req, res) => {
    try {
        const { yearRange } = req.params;
        
        // This would implement the depreciation calculation logic
        // For now, we'll return a placeholder response
        
        res.json({ 
            message: 'Depreciation calculation completed',
            yearRange: yearRange,
            note: 'Depreciation calculation logic to be implemented'
        });
        
    } catch (error) {
        console.error('Error calculating depreciation:', error);
        res.status(500).json({ error: 'Failed to calculate depreciation' });
    }
});

export default router;