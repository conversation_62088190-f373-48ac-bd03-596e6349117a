/**
 * Comprehensive Historical Data Script for Schedule III Testing
 * Adds substantial multi-year data to test all scenarios
 */

import DatabaseManager from '../services/database-manager/DatabaseManager.js';

// Historical assets with comprehensive scenarios
const comprehensiveAssets = [
    // Pre-adoption assets (purchased before 01/04/2024)
    {
        recordId: 'HIST-BLDG-001',
        assetParticulars: 'Corporate Office Building - Mumbai Main',
        bookEntryDate: '2019-03-15',
        putToUseDate: '2019-04-01',
        basicAmount: 25000000,
        dutiesTaxes: 2500000,
        grossAmount: 27500000,
        wdvOfAdoptionDate: 22000000, // Carried forward value
        vendor: 'Real Estate Developers Mumbai',
        invoiceNo: 'SALE-2019-001',
        location: 'Mumbai Corporate Office',
        assetId: 'BLDG-001',
        ledgerNameInBooks: 'Buildings',
        assetGroup: 'Buildings',
        assetSubGroup: 'Factory buildings',
        scheduleIIIClassification: 'Buildings',
        depreciationMethod: 'SLM',
        lifeInYears: 30,
        salvagePercentage: 5,
        isLeasehold: 'No'
    },
    {
        recordId: 'HIST-PLT-001',
        assetParticulars: 'Manufacturing Plant - Production Line A',
        bookEntryDate: '2020-08-10',
        putToUseDate: '2020-09-01',
        basicAmount: 15000000,
        dutiesTaxes: 2250000,
        grossAmount: 17250000,
        wdvOfAdoptionDate: 12500000, // Carried forward value
        vendor: 'Industrial Equipment Solutions',
        invoiceNo: 'IND-2020-089',
        location: 'Factory Floor A',
        assetId: 'PLT-001',
        ledgerNameInBooks: 'Plant & Machinery',
        assetGroup: 'Plant & Machinery',
        assetSubGroup: 'Manufacturing Equipment',
        scheduleIIIClassification: 'Plant and machinery',
        depreciationMethod: 'WDV',
        lifeInYears: 15,
        salvagePercentage: 5,
        isLeasehold: 'No'
    },
    {
        recordId: 'HIST-PLT-002',
        assetParticulars: 'CNC Machining Center - Haas VF-3',
        bookEntryDate: '2021-01-20',
        putToUseDate: '2021-02-15',
        basicAmount: 8000000,
        dutiesTaxes: 1440000,
        grossAmount: 9440000,
        wdvOfAdoptionDate: 7200000, // Carried forward value
        vendor: 'Precision Machinery Ltd',
        invoiceNo: 'PMC-2021-015',
        location: 'Machining Section',
        assetId: 'PLT-002',
        ledgerNameInBooks: 'Plant & Machinery',
        assetGroup: 'Plant & Machinery',
        assetSubGroup: 'Manufacturing Equipment',
        scheduleIIIClassification: 'Plant and machinery',
        depreciationMethod: 'WDV',
        lifeInYears: 10,
        salvagePercentage: 10,
        isLeasehold: 'No'
    },
    {
        recordId: 'HIST-VEH-001',
        assetParticulars: 'Toyota Innova Crysta - Executive Vehicle',
        bookEntryDate: '2022-06-15',
        putToUseDate: '2022-07-01',
        basicAmount: 1800000,
        dutiesTaxes: 324000,
        grossAmount: 2124000,
        wdvOfAdoptionDate: 1900000, // Carried forward value
        vendor: 'Toyota Motors India',
        invoiceNo: 'TOY-2022-067',
        location: 'Transport Pool',
        assetId: 'VEH-001',
        ledgerNameInBooks: 'Motor Vehicles',
        assetGroup: 'Motor vehicles',
        assetSubGroup: 'Motor cars',
        scheduleIIIClassification: 'Motor vehicles',
        depreciationMethod: 'WDV',
        lifeInYears: 8,
        salvagePercentage: 5,
        isLeasehold: 'No'
    },
    {
        recordId: 'HIST-OFF-001',
        assetParticulars: 'Dell Workstation - Precision 7000 Series',
        bookEntryDate: '2023-01-10',
        putToUseDate: '2023-02-01',
        basicAmount: 350000,
        dutiesTaxes: 63000,
        grossAmount: 413000,
        wdvOfAdoptionDate: 380000, // Carried forward value
        vendor: 'Dell Technologies India',
        invoiceNo: 'DELL-2023-012',
        location: 'IT Department',
        assetId: 'OFF-001',
        ledgerNameInBooks: 'Office Equipment',
        assetGroup: 'Office equipment',
        assetSubGroup: 'Computers',
        scheduleIIIClassification: 'Office equipment',
        depreciationMethod: 'WDV',
        lifeInYears: 6,
        salvagePercentage: 10,
        isLeasehold: 'No',
        disposalDate: '2024-12-31', // Will be disposed in current year
        disposalAmount: 150000
    },
    // Post-adoption assets (purchased after 01/04/2024)
    {
        recordId: 'NEW-PLT-001',
        assetParticulars: 'Automated Assembly Line - Robotic System',
        bookEntryDate: '2024-05-15',
        putToUseDate: '2024-06-01',
        basicAmount: 12000000,
        dutiesTaxes: 2160000,
        grossAmount: 14160000,
        wdvOfAdoptionDate: null, // New asset, no carry-forward
        vendor: 'Automation Technologies Pvt Ltd',
        invoiceNo: 'AUTO-2024-056',
        location: 'Assembly Section',
        assetId: 'PLT-003',
        ledgerNameInBooks: 'Plant & Machinery',
        assetGroup: 'Plant & Machinery',
        assetSubGroup: 'Manufacturing Equipment',
        scheduleIIIClassification: 'Plant and machinery',
        depreciationMethod: 'WDV',
        lifeInYears: 12,
        salvagePercentage: 5,
        isLeasehold: 'No'
    },
    {
        recordId: 'NEW-VEH-001',
        assetParticulars: 'Mahindra Bolero - Utility Vehicle',
        bookEntryDate: '2024-08-20',
        putToUseDate: '2024-09-01',
        basicAmount: 1200000,
        dutiesTaxes: 216000,
        grossAmount: 1416000,
        wdvOfAdoptionDate: null, // New asset, no carry-forward
        vendor: 'Mahindra & Mahindra Ltd',
        invoiceNo: 'MAH-2024-089',
        location: 'Transport Pool',
        assetId: 'VEH-002',
        ledgerNameInBooks: 'Motor Vehicles',
        assetGroup: 'Motor vehicles',
        assetSubGroup: 'Motor cars',
        scheduleIIIClassification: 'Motor vehicles',
        depreciationMethod: 'WDV',
        lifeInYears: 8,
        salvagePercentage: 5,
        isLeasehold: 'No'
    },
    {
        recordId: 'NEW-OFF-001',
        assetParticulars: 'HP Laser Printer - Enterprise Series',
        bookEntryDate: '2024-10-05',
        putToUseDate: '2024-10-15',
        basicAmount: 85000,
        dutiesTaxes: 15300,
        grossAmount: 100300,
        wdvOfAdoptionDate: null, // New asset, no carry-forward
        vendor: 'HP India Sales Pvt Ltd',
        invoiceNo: 'HP-2024-105',
        location: 'Admin Office',
        assetId: 'OFF-002',
        ledgerNameInBooks: 'Office Equipment',
        assetGroup: 'Office equipment',
        assetSubGroup: 'Office appliances',
        scheduleIIIClassification: 'Office equipment',
        depreciationMethod: 'WDV',
        lifeInYears: 5,
        salvagePercentage: 10,
        isLeasehold: 'No'
    }
];

// Extra shift data for manufacturing equipment
const extraShiftData = [
    { assetRecordId: 'HIST-PLT-001', financialYear: '2022-23', secondShiftDays: 150, thirdShiftDays: 80 },
    { assetRecordId: 'HIST-PLT-001', financialYear: '2023-24', secondShiftDays: 180, thirdShiftDays: 100 },
    { assetRecordId: 'HIST-PLT-001', financialYear: '2024-25', secondShiftDays: 200, thirdShiftDays: 120 },
    { assetRecordId: 'HIST-PLT-002', financialYear: '2022-23', secondShiftDays: 120, thirdShiftDays: 60 },
    { assetRecordId: 'HIST-PLT-002', financialYear: '2023-24', secondShiftDays: 140, thirdShiftDays: 70 },
    { assetRecordId: 'HIST-PLT-002', financialYear: '2024-25', secondShiftDays: 160, thirdShiftDays: 80 },
    { assetRecordId: 'NEW-PLT-001', financialYear: '2024-25', secondShiftDays: 100, thirdShiftDays: 50 }
];

// Financial years to create data for
const financialYears = ['2022-23', '2023-24', '2024-25'];

// Function to add comprehensive data to a company
async function addComprehensiveDataToCompany(companyId, companyName) {
    console.log(`\n📊 Adding comprehensive historical data to: ${companyName} (${companyId})`);
    
    try {
        const dbManager = new DatabaseManager();
        const companyDb = await dbManager.getCompanyDatabase(companyId);
        
        console.log(`  📝 Adding ${comprehensiveAssets.length} comprehensive assets...`);
        
        // Add all comprehensive assets
        for (const asset of comprehensiveAssets) {
            const now = new Date().toISOString();
            
            await companyDb.run(`
                INSERT OR REPLACE INTO assets (
                    record_id, asset_particulars, book_entry_date, put_to_use_date,
                    basic_amount, duties_taxes, gross_amount, wdv_of_adoption_date,
                    vendor, invoice_no, location, asset_id, ledger_name_in_books,
                    asset_group, asset_sub_group, schedule_iii_classification,
                    depreciation_method, life_in_years, salvage_percentage, is_leasehold,
                    disposal_date, disposal_amount, scrap_it, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                asset.recordId, asset.assetParticulars, asset.bookEntryDate, asset.putToUseDate,
                asset.basicAmount, asset.dutiesTaxes, asset.grossAmount, asset.wdvOfAdoptionDate,
                asset.vendor, asset.invoiceNo, asset.location, asset.assetId, asset.ledgerNameInBooks,
                asset.assetGroup, asset.assetSubGroup, asset.scheduleIIIClassification,
                asset.depreciationMethod, asset.lifeInYears, asset.salvagePercentage, asset.isLeasehold,
                asset.disposalDate, asset.disposalAmount, asset.scrapIt ? 'Yes' : 'No', now, now
            ]);
            
            console.log(`    ✅ Added: ${asset.recordId} - ${asset.assetParticulars}`);
        }
        
        console.log(`  📅 Adding extra shift data...`);
        
        // Add extra shift data
        for (const shiftData of extraShiftData) {
            const now = new Date().toISOString();
            
            await companyDb.run(`
                INSERT OR REPLACE INTO extra_shift_days (
                    asset_record_id, financial_year, second_shift_days, third_shift_days,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            `, [
                shiftData.assetRecordId, shiftData.financialYear, 
                shiftData.secondShiftDays, shiftData.thirdShiftDays, now, now
            ]);
        }
        
        console.log(`  ✅ Added extra shift data for ${extraShiftData.length} entries`);
        console.log(`  ✅ Comprehensive data added successfully for ${companyName}`);
        
    } catch (error) {
        console.error(`  ❌ Error adding comprehensive data: ${error.message}`);
        throw error;
    }
}

// Main execution
async function main() {
    console.log('🚀 Adding Comprehensive Historical Data for Schedule III Testing');
    console.log('================================================================\n');
    
    try {
        const dbManager = new DatabaseManager();
        
        // Get all companies
        const companies = await dbManager.getAllCompanies();
        
        if (!companies || companies.length === 0) {
            console.error('❌ No companies found');
            process.exit(1);
        }
        
        console.log(`📁 Found ${companies.length} company(ies)`);
        
        // Add comprehensive data to first 3 companies
        const targetCompanies = companies.slice(0, 3);
        
        for (const company of targetCompanies) {
            try {
                await addComprehensiveDataToCompany(company.id, company.name);
            } catch (error) {
                console.error(`❌ Failed to add data to ${company.name}: ${error.message}`);
                console.log('   Continuing with next company...\n');
            }
        }
        
        console.log('\n🎉 Comprehensive historical data addition completed successfully!');
        console.log('\n📋 Summary of added data:');
        console.log(`   • ${comprehensiveAssets.length} comprehensive assets per company`);
        console.log(`   • ${financialYears.length} financial years covered (${financialYears.join(', ')})`);
        console.log('   • Pre-adoption assets with carry-forward values');
        console.log('   • Post-adoption assets with full depreciation');
        console.log('   • Asset disposal scenario (HIST-OFF-001 disposed in 2024-25)');
        console.log('   • Extra shift days for manufacturing equipment');
        console.log('   • Multiple depreciation methods (WDV and SLM)');
        console.log('   • Different asset categories (Buildings, Plant, Vehicles, Office)');
        console.log('\n🧪 Test scenarios covered:');
        console.log('   • Carry-forward from previous system');
        console.log('   • New asset additions during the year');
        console.log('   • Asset disposals with gain/loss calculations');
        console.log('   • Extra shift depreciation for plant & machinery');
        console.log('   • Both WDV and SLM depreciation methods');
        console.log('   • Multiple asset groups and classifications');
        console.log('   • Schedule III opening/closing balance verification');
        console.log('\n📊 Ready for comprehensive Schedule III testing!');
        
    } catch (error) {
        console.error('❌ Script failed:', error.message);
        process.exit(1);
    }
}

// Run the script
main();
