/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect, useMemo } from 'react';
import { api } from '../lib/api';
import type { CompanyData } from '../lib/db-server';
import { DataViewProps, ScheduleIIIDetailRow } from '../lib/types';
import { formatIndianNumber, calculateYearlyMetrics, sortData, exportToExcel } from '../lib/utils';
import { MethodWiseDetailModal } from './MethodWiseDetailModal';
import { DownloadIcon } from '../Icons';

interface MethodWiseRow {
    method: string;
    openingGross: number;
    additionsGross: number;
    deletionsGross: number;
    closingGross: number;
    openingDepreciation: number;
    additionsDepreciation: number;
    deletionsDepreciation: number;
    closingDepreciation: number;
    openingNetBlock: number;
    closingNetBlock: number;
}

export function MethodWise({ companyId, companyName, year, showAlert }: DataViewProps) {
    const [reportData, setReportData] = useState<MethodWiseRow[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [sortConfig, setSortConfig] = useState<{ key: keyof MethodWiseRow; direction: 'asc' | 'desc' } | null>({ key: 'method', direction: 'asc' });
    const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
    const [selectedColumnKey, setSelectedColumnKey] = useState<string | null>(null);

    // For drill-down modal
    const [companyData, setCompanyData] = useState<CompanyData | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalData, setModalData] = useState<ScheduleIIIDetailRow[]>([]);
    const [modalTitle, setModalTitle] = useState('');
    
    useEffect(() => {
        const calculateReport = async () => {
            if (!companyId || !year) {
                setLoading(false);
                setReportData([]);
                setCompanyData(null);
                return;
            }

            setLoading(true);
            setError(null);
            setSelectedRowId(null);
            setSelectedColumnKey(null);

            try {
                const data = await api.getCompanyData(companyId);
                setCompanyData(data);
                
                if (!data || !data.assets || data.assets.length === 0) {
                    setReportData([]);
                    setLoading(false);
                    return;
                }

                const reportMap: Record<string, MethodWiseRow> = {
                    'WDV': { method: 'WDV', openingGross: 0, additionsGross: 0, deletionsGross: 0, closingGross: 0, openingDepreciation: 0, additionsDepreciation: 0, deletionsDepreciation: 0, closingDepreciation: 0, openingNetBlock: 0, closingNetBlock: 0 },
                    'SLM': { method: 'SLM', openingGross: 0, additionsGross: 0, deletionsGross: 0, closingGross: 0, openingDepreciation: 0, additionsDepreciation: 0, deletionsDepreciation: 0, closingDepreciation: 0, openingNetBlock: 0, closingNetBlock: 0 },
                };

                for (const asset of data.assets) {
                    const method = asset.depreciationMethod;
                    if (!method || !reportMap[method]) continue;

                    const metrics = calculateYearlyMetrics(asset, year, data);
                    
                    reportMap[method].openingGross += metrics.openingGross;
                    reportMap[method].additionsGross += metrics.additionsGross;
                    reportMap[method].deletionsGross += metrics.deletionsGross;
                    reportMap[method].openingDepreciation += metrics.openingDepreciation;
                    reportMap[method].additionsDepreciation += metrics.additionsDepreciation;
                    reportMap[method].deletionsDepreciation += metrics.deletionsDepreciation;
                }

                const finalReportData: MethodWiseRow[] = Object.values(reportMap).map(row => {
                    row.closingGross = row.openingGross + row.additionsGross - row.deletionsGross;
                    row.closingDepreciation = row.openingDepreciation + row.additionsDepreciation - row.deletionsDepreciation;
                    row.openingNetBlock = row.openingGross - row.openingDepreciation;
                    row.closingNetBlock = row.closingGross - row.closingDepreciation;
                    return row;
                });
                
                const totalRow: MethodWiseRow = { method: 'Total', openingGross: 0, additionsGross: 0, deletionsGross: 0, closingGross: 0, openingDepreciation: 0, additionsDepreciation: 0, deletionsDepreciation: 0, closingDepreciation: 0, openingNetBlock: 0, closingNetBlock: 0 };
                finalReportData.forEach(row => {
                    (Object.keys(row) as Array<keyof MethodWiseRow>).forEach(key => {
                        if (key !== 'method') {
                            totalRow[key] += row[key] as number;
                        }
                    });
                });
                
                const dataWithTotal = [...finalReportData];
                if (totalRow.openingGross > 0 || totalRow.additionsGross > 0 || totalRow.closingGross > 0) {
                     dataWithTotal.push(totalRow);
                }

                setReportData(dataWithTotal);

            } catch (err) {
                setError('Failed to generate Method-wise report.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        calculateReport();
    }, [companyId, year]);

    const handleRowDoubleClick = (method: string) => {
        if (!method || method === 'Total' || !companyData) return;
        
        const filtered = companyData.assets.filter(a => a.depreciationMethod === method);
        
        const details: ScheduleIIIDetailRow[] = filtered.map((asset) => {
            const metrics = calculateYearlyMetrics(asset, year, companyData);

            return {
                recordId: asset.recordId,
                assetParticulars: asset.assetParticulars,
                assetType: method,
                openingGross: metrics.openingGross,
                additionsGross: metrics.additionsGross,
                deletionsGross: metrics.deletionsGross,
                closingGross: metrics.closingGross,
                openingDepreciation: metrics.openingDepreciation,
                additionsDepreciation: metrics.additionsDepreciation,
                deletionsDepreciation: metrics.deletionsDepreciation,
                closingDepreciation: metrics.closingDepreciation,
                openingNetBlock: metrics.openingNetBlock,
                closingNetBlock: metrics.closingNetBlock,
                assetCount: 1,
            };
        });

        setModalData(details);
        setModalTitle(`Details for: ${method} Method`);
        setIsModalOpen(true);
    };

    const sortedReportData = useMemo(() => {
        const dataToSort = reportData.filter(row => row.method !== 'Total');
        return sortData(dataToSort, sortConfig);
    }, [reportData, sortConfig]);

    const totalRow = useMemo(() => reportData.find(row => row.method === 'Total'), [reportData]);

    const requestSort = (key: keyof MethodWiseRow) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
        setSelectedColumnKey(key);
    };

    const getSortIndicator = (key: keyof MethodWiseRow) => {
        if (!sortConfig || sortConfig.key !== key) return <span className="sort-indicator">↕</span>;
        return <span className="sort-indicator">{sortConfig.direction === 'asc' ? '▲' : '▼'}</span>;
    };
    
    const getHeaderClass = (key: keyof MethodWiseRow) => {
        const classes = ['sortable'];
        if (key !== 'method') classes.push('text-right');
        if (sortConfig?.key === key) classes.push('sortable-active');
        if (selectedColumnKey === key) classes.push('selected-col');
        return classes.join(' ');
    };

    const getColumnClass = (key: keyof MethodWiseRow) => {
        const classes = [];
        if (selectedColumnKey === key) classes.push('selected-col');
        if (key !== 'method') classes.push('text-right');
        return classes.join(' ');
    };

    const handleExport = () => {
        if (!companyId || !companyName || !year) return;

        const dataToExport = [...sortedReportData, totalRow]
            .filter(Boolean)
            .map(row => ({
                "Method": row!.method,
                "Gross Block - Opening": row!.openingGross,
                "Gross Block - Additions": row!.additionsGross,
                "Gross Block - Deletions": row!.deletionsGross,
                "Gross Block - Closing": row!.closingGross,
                "Depreciation - Opening": row!.openingDepreciation,
                "Depreciation - For the Year": row!.additionsDepreciation,
                "Depreciation - On Disposals": row!.deletionsDepreciation,
                "Depreciation - Closing": row!.closingDepreciation,
                "Net Block - Opening": row!.openingNetBlock,
                "Net Block - Closing": row!.closingNetBlock,
            }));

        if (!exportToExcel({ data: dataToExport, companyName, year, reportName: 'Method_Wise_Report' })) {
            showAlert("Export Failed", "There is no data to export.", 'error');
        }
    };

    if (loading) return <div className="loading-indicator">Generating Method-wise Report...</div>;
    if (error) return <div className="error-message">{error}</div>;
    if (!companyId) return null;
    if (reportData.length === 0 || (reportData.length === 1 && reportData[0].method === 'Total' && reportData[0].closingGross === 0)) {
        return <div className="company-info-container"><p>No asset data available to generate the report for the selected year.</p></div>
    }

    return (
        <div className="report-view">
            <div className="view-header">
                <h2>Method-wise Report for FY {year}</h2>
                <div className="actions">
                    <button className="btn btn-excel" onClick={handleExport}><DownloadIcon /> Export to Excel</button>
                </div>
            </div>
            <div className="table-container">
                <table>
                    <thead>
                        <tr>
                            <th className={getHeaderClass('method')} onClick={() => requestSort('method')}>Method{getSortIndicator('method')}</th>
                            <th className={getHeaderClass('openingGross')} onClick={() => requestSort('openingGross')}>Gross Block - Opening{getSortIndicator('openingGross')}</th>
                            <th className={getHeaderClass('additionsGross')} onClick={() => requestSort('additionsGross')}>Gross Block - Additions{getSortIndicator('additionsGross')}</th>
                            <th className={getHeaderClass('deletionsGross')} onClick={() => requestSort('deletionsGross')}>Gross Block - Deletions{getSortIndicator('deletionsGross')}</th>
                            <th className={getHeaderClass('closingGross')} onClick={() => requestSort('closingGross')}>Gross Block - Closing{getSortIndicator('closingGross')}</th>
                            <th className={getHeaderClass('openingDepreciation')} onClick={() => requestSort('openingDepreciation')}>Depreciation - Opening{getSortIndicator('openingDepreciation')}</th>
                            <th className={getHeaderClass('additionsDepreciation')} onClick={() => requestSort('additionsDepreciation')}>Depreciation - For the Year{getSortIndicator('additionsDepreciation')}</th>
                            <th className={getHeaderClass('deletionsDepreciation')} onClick={() => requestSort('deletionsDepreciation')}>Depreciation - On Disposals{getSortIndicator('deletionsDepreciation')}</th>
                            <th className={getHeaderClass('closingDepreciation')} onClick={() => requestSort('closingDepreciation')}>Depreciation - Closing{getSortIndicator('closingDepreciation')}</th>
                            <th className={getHeaderClass('openingNetBlock')} onClick={() => requestSort('openingNetBlock')}>Net Block - Opening{getSortIndicator('openingNetBlock')}</th>
                            <th className={getHeaderClass('closingNetBlock')} onClick={() => requestSort('closingNetBlock')}>Net Block - Closing{getSortIndicator('closingNetBlock')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {sortedReportData.map((row) => (
                             <tr key={row.method} onDoubleClick={() => handleRowDoubleClick(row.method)} onClick={() => setSelectedRowId(row.method)} className={`clickable-row ${row.method === selectedRowId ? 'selected-row' : ''}`}>
                                <td className={getColumnClass('method')}>{row.method}</td>
                                <td className={getColumnClass('openingGross')}>{formatIndianNumber(row.openingGross)}</td>
                                <td className={getColumnClass('additionsGross')}>{formatIndianNumber(row.additionsGross)}</td>
                                <td className={getColumnClass('deletionsGross')}>{formatIndianNumber(row.deletionsGross)}</td>
                                <td className={getColumnClass('closingGross')}>{formatIndianNumber(row.closingGross)}</td>
                                <td className={getColumnClass('openingDepreciation')}>{formatIndianNumber(row.openingDepreciation)}</td>
                                <td className={getColumnClass('additionsDepreciation')}>{formatIndianNumber(row.additionsDepreciation)}</td>
                                <td className={getColumnClass('deletionsDepreciation')}>{formatIndianNumber(row.deletionsDepreciation)}</td>
                                <td className={getColumnClass('closingDepreciation')}>{formatIndianNumber(row.closingDepreciation)}</td>
                                <td className={getColumnClass('openingNetBlock')}>{formatIndianNumber(row.openingNetBlock)}</td>
                                <td className={getColumnClass('closingNetBlock')}>{formatIndianNumber(row.closingNetBlock)}</td>
                            </tr>
                        ))}
                    </tbody>
                    {totalRow && (
                        <tfoot>
                            <tr>
                                <td className={getColumnClass('method')}>{totalRow.method}</td>
                                <td className={getColumnClass('openingGross')}>{formatIndianNumber(totalRow.openingGross)}</td>
                                <td className={getColumnClass('additionsGross')}>{formatIndianNumber(totalRow.additionsGross)}</td>
                                <td className={getColumnClass('deletionsGross')}>{formatIndianNumber(totalRow.deletionsGross)}</td>
                                <td className={getColumnClass('closingGross')}>{formatIndianNumber(totalRow.closingGross)}</td>
                                <td className={getColumnClass('openingDepreciation')}>{formatIndianNumber(totalRow.openingDepreciation)}</td>
                                <td className={getColumnClass('additionsDepreciation')}>{formatIndianNumber(totalRow.additionsDepreciation)}</td>
                                <td className={getColumnClass('deletionsDepreciation')}>{formatIndianNumber(totalRow.deletionsDepreciation)}</td>
                                <td className={getColumnClass('closingDepreciation')}>{formatIndianNumber(totalRow.closingDepreciation)}</td>
                                <td className={getColumnClass('openingNetBlock')}>{formatIndianNumber(totalRow.openingNetBlock)}</td>
                                <td className={getColumnClass('closingNetBlock')}>{formatIndianNumber(totalRow.closingNetBlock)}</td>
                            </tr>
                        </tfoot>
                    )}
                </table>
            </div>
            <MethodWiseDetailModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                title={modalTitle}
                data={modalData}
                year={year}
                companyId={companyId}
                companyName={companyName}
                showAlert={showAlert}
            />
        </div>
    );
}

