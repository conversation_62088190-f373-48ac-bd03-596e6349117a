@echo off
setlocal enabledelayedexpansion

echo =====================================================
echo FAR SIGHTED - COMPLETE DIAGNOSTIC FOR DROPDOWN ISSUE
echo Professional Advisory Services - CA
echo =====================================================
echo.

cd /d "E:\Projects\FAR Sighted"

echo 🔍 COMPREHENSIVE DIAGNOSIS: Why companies dropdown is still empty
echo.

echo ============================================
echo STEP 1: BACKEND CONNECTION TEST
echo ============================================
curl -s http://localhost:8090/api/health >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Backend is responding on port 8090
) else (
    echo ❌ ISSUE FOUND: Backend not responding on port 8090
    echo 💡 FIX: cd backend && npm start
    goto :end
)

echo.
echo ============================================
echo STEP 2: DETAILED COMPANIES API TEST
echo ============================================
echo 🔍 Testing: GET http://localhost:8090/api/companies
echo.

REM Get response code and body
curl -s -w "HTTP_CODE:%%{http_code}\nTIME:%%{time_total}s\n" -o response.tmp http://localhost:8090/api/companies
set /p response_code=<response.tmp
echo Response received. Analyzing...
echo.

type response.tmp
echo.
echo ============================================

REM Check if it's a 503 migration error
findstr "migration required" response.tmp >nul 2>&1
if %errorlevel% == 0 (
    echo ❌ ISSUE FOUND: Backend returning migration required error
    echo 💡 FIX: Need to run migration
    echo 🔧 RUNNING MIGRATION NOW...
    cd backend
    npm run migrate
    if %errorlevel% == 0 (
        echo ✅ Migration completed. Testing API again...
        cd ..
        curl -s http://localhost:8090/api/companies > response.tmp
        type response.tmp
    ) else (
        echo ❌ Migration failed. Check error messages above.
    )
    cd ..
    goto :step3
)

REM Check if it's an empty array
findstr "^\[\]$" response.tmp >nul 2>&1
if %errorlevel% == 0 (
    echo ❌ ISSUE FOUND: API returns empty array - No companies in database
    echo 💡 FIX: Need to add test companies
    goto :create_test_company
)

REM Check if it contains company data
findstr "\"id\":" response.tmp >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ API returns company data - Backend is working correctly
    echo 🔍 Issue must be in frontend. Checking frontend...
    goto :frontend_check
) else (
    echo ❌ ISSUE FOUND: API returns unexpected response
    echo 📋 Response content:
    type response.tmp
)

:step3
echo.
echo ============================================
echo STEP 3: MIGRATION STATUS CHECK
echo ============================================
curl -s http://localhost:8090/api/migration-status | findstr "systemReady.*true" >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Migration status: System ready
) else (
    echo ❌ Migration status: System not ready
    echo 📊 Full migration status:
    curl -s http://localhost:8090/api/migration-status
    echo.
)

echo.
echo ============================================
echo STEP 4: DATABASE INSPECTION
echo ============================================
if exist "backend\database\master.db" (
    echo ✅ Master database exists
) else (
    echo ❌ Master database missing
)

if exist "backend\database\companies\" (
    echo ✅ Companies directory exists
    echo 📁 Company folders found:
    for /d %%i in ("backend\database\companies\*") do (
        echo    • %%~nxi
    )
) else (
    echo ❌ Companies directory missing
)

echo.
echo ============================================
echo STEP 5: FRONTEND CONNECTIVITY TEST
echo ============================================
curl -s http://localhost:9090 >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Frontend is responding on port 9090
    goto :frontend_check
) else (
    echo ❌ Frontend not responding on port 9090
    echo 💡 FIX: npm run dev
    goto :end
)

:create_test_company
echo.
echo ============================================
echo CREATING TEST COMPANY
echo ============================================
echo 🏢 The database has no companies. Creating a test company...
echo.

curl -X POST http://localhost:8090/api/companies ^
  -H "Content-Type: application/json" ^
  -d "{\"companyName\":\"Test Company Ltd\",\"financialYearStart\":\"2024-04-01\",\"financialYearEnd\":\"2025-03-31\",\"firstDateOfAdoption\":\"2024-04-01\",\"pan\":\"**********\",\"addressLine1\":\"Test Address\",\"city\":\"Mumbai\",\"pin\":\"400001\"}" ^
  2>nul

if %errorlevel% == 0 (
    echo ✅ Test company created successfully
    echo 🔍 Testing companies API again...
    curl -s http://localhost:8090/api/companies
    echo.
) else (
    echo ❌ Failed to create test company
    echo 💡 Check backend logs for errors
)

goto :frontend_check

:frontend_check
echo.
echo ============================================
echo STEP 6: FRONTEND API CALL SIMULATION
echo ============================================
echo 🔍 Testing the exact API call that frontend makes...
echo.

curl -s -H "Content-Type: application/json" ^
     -H "Accept: application/json" ^
     -H "Origin: http://localhost:9090" ^
     http://localhost:8090/api/companies

echo.
echo ============================================
echo STEP 7: CORS CHECK
echo ============================================
curl -s -I -H "Origin: http://localhost:9090" http://localhost:8090/api/companies | findstr "Access-Control"
if %errorlevel% == 0 (
    echo ✅ CORS headers present
) else (
    echo ❌ ISSUE FOUND: CORS headers missing
    echo 💡 FIX: Backend CORS configuration issue
)

echo.
echo ============================================
echo STEP 8: BROWSER CONSOLE CHECK INSTRUCTIONS
echo ============================================
echo 🌐 To check frontend issues:
echo.
echo 1. Open http://localhost:9090 in browser
echo 2. Press F12 to open Developer Tools
echo 3. Go to Network tab
echo 4. Refresh page
echo 5. Look for failed requests to /api/companies
echo 6. Go to Console tab
echo 7. Look for JavaScript errors
echo.
echo Common issues to look for:
echo   • Network failed / CORS error
echo   • 503 Service Unavailable
echo   • JavaScript errors in console
echo   • Empty response []

echo.
echo ============================================
echo DIAGNOSTIC SUMMARY
echo ============================================

curl -s http://localhost:8090/api/companies > final_test.tmp
findstr "\"id\":" final_test.tmp >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ BACKEND: Companies API is working and returning data
    echo ❌ FRONTEND: Issue is likely in React component or browser
    echo.
    echo 💡 NEXT STEPS:
    echo    1. Check browser console (F12) for JavaScript errors
    echo    2. Check Network tab for failed API calls
    echo    3. Verify frontend is making calls to correct URL
    echo    4. Check if CORS is blocking the request
) else (
    findstr "^\[\]$" final_test.tmp >nul 2>&1
    if %errorlevel% == 0 (
        echo ❌ BACKEND: API returns empty array - no companies exist
        echo 💡 NEXT STEPS:
        echo    1. Run: cd backend && npm run migrate
        echo    2. Add test company using API or frontend
    ) else (
        echo ❌ BACKEND: API is returning errors
        echo 💡 NEXT STEPS:
        echo    1. Check backend logs for errors
        echo    2. Verify migration completed successfully
        echo    3. Check database files exist
    )
)

echo.
echo 🔗 QUICK TEST URLs:
echo    Backend Health: http://localhost:8090/api/health
echo    Companies API: http://localhost:8090/api/companies
echo    Frontend App: http://localhost:9090
echo.

:end
REM Cleanup
if exist response.tmp del response.tmp
if exist final_test.tmp del final_test.tmp

pause
