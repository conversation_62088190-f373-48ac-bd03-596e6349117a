@echo off
echo =============================================
echo FAR SIGHTED - DATABASE CONTENT CHECK
echo Professional Advisory Services - CA
echo =============================================
echo.

cd /d "E:\Projects\FAR Sighted\backend"

echo 🗄️ Checking database contents directly...
echo.

if exist "database\master.db" (
    echo ✅ Master database found
    echo.
    echo 📊 Checking companies in master database...
    echo SELECT COUNT(*) as company_count FROM companies; | sqlite3 database\master.db
    echo.
    echo 📋 Companies list:
    echo "SELECT id, company_name FROM companies;" | sqlite3 database\master.db
    echo.
) else (
    echo ❌ Master database not found!
    echo 💡 This explains why dropdown is empty
)

echo 📁 Company database folders:
if exist "database\companies\" (
    for /d %%i in ("database\companies\*") do (
        echo    • %%~nxi
    )
) else (
    echo ❌ No company folders found
)

echo.
echo 🔧 If no companies found, you can:
echo    1. Run migration: npm run migrate
echo    2. Create test company via API
echo    3. Check if old database exists and needs migration
echo.

if exist "database\far_sighted.db" (
    echo 📋 Old database found - checking for companies there...
    echo "SELECT COUNT(*) as old_company_count FROM companies;" | sqlite3 database\far_sighted.db 2>nul
    if %errorlevel% == 0 (
        echo "SELECT company_name FROM companies LIMIT 5;" | sqlite3 database\far_sighted.db
        echo.
        echo 💡 Companies exist in old database - migration needed!
        echo    Run: npm run migrate
    )
)

pause
